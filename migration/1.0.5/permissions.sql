

-- 机动车换牌
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657651910, '/vehicle/by/user/list', '用户机动车列表');
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657651911, '/vehicle/check/agree', '用户机动车列表');
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657651912, '/vehicle/lcWhitelist', '机动车蓝卡白名单');
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657651913, '/vehicle/plateChangeApply', '机动车蓝卡白名单');
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657651914, '/vehicle/plateChangeCheck', '机动车修改车牌');
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657651915, '/vehicle/refundCheck', '机动车退月卡审核');
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657651916, '/vehicle/monthlyCardBill', '机动车月卡订单查询');

--发票
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657652001, '/invoice/pageList', '发票分页列表');
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657652002, '/invoice/process', '发票申请处理');
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657652003, '/invoice/get', '发票详情');


--弹窗
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657652101, '/modalBanner/page', '弹窗横幅分页列表');
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657652202, '/modalBanner/save', '弹窗横幅添加或修改');
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657652303, '/modalBanner/delete', '弹窗横幅删除');


--物业单元
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657652401, '/propertyCell/pageList', '物业单元分页列表');
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657652402, '/propertyCell/add', '物业单元新增');
INSERT INTO admin_permission (id, code, name) VALUES (1731557757657652403, '/propertyCell/update', '物业单元更新');


