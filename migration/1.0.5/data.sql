-- 创建一个空白的流程
-- code:自己起
-- name:自己起
INSERT INTO public.bpm_process (id, code, name) VALUES (1500000000000004000, 'vehiclePlateChange', '机动车换牌');
-- 步骤
-- process_id:上面插入的流程的id
-- seq:步骤的顺序
-- name:步骤的名称
-- code: 自己起（用不到）
-- params:步骤的参数 （用不到）
INSERT INTO public.bpm_step (id, process_id, name, seq, code, params) VALUES (1500000000000004101, 1500000000000004000, '提交申请', 1, null, null);
INSERT INTO public.bpm_step (id, process_id, name, seq, code, params) VALUES (1500000000000004102, 1500000000000004000, '录入停车场', 2, null, null);
INSERT INTO public.bpm_step (id, process_id, name, seq, code, params) VALUES (1500000000000004103, 1500000000000004000, '结束', 3, null, null);
-- 节点
-- process_id: 上面插入的流程的id
-- step_id:步骤的id
-- name:节点的名称
-- type:节点的类型 NodeTypeEnum(END,APPROVE,DELAY,CODE,) 0-结束 1-审批 2-延时 3-自定义??
-- role:节点的角色 NodeRoleEnum(SYSTEM,SELF,USER,ROLE,) 0-系统 1-自己 2-用户 3-角色·
-- role_value:节点的角色值
-- code:自己起名字
-- params:节点的参数
INSERT INTO public.bpm_node (id, process_id, step_id, name, type, role, role_value, code, params) VALUES (1500000000000004201, 1500000000000004000, 1500000000000004101, '提交申请', 1, 1, null, null, null);
INSERT INTO public.bpm_node (id, process_id, step_id, name, type, role, role_value, code, params) VALUES (1500000000000004202, 1500000000000004000, 1500000000000004102, '录入停车场', 1, 3, 'admin', null, null);
INSERT INTO public.bpm_node (id, process_id, step_id, name, type, role, role_value, code, params) VALUES (1500000000000004203, 1500000000000004000, 1500000000000004103, '结束', 0, 0, null, null, null);
INSERT INTO public.bpm_node (id, process_id, step_id, name, type, role, role_value, code, params) VALUES (1500000000000004204, 1500000000000004000, 1500000000000004103, '失败', 0, 0, null, null, null);
-- 节点事件，生成某个节点后会触发的事件
-- event_code 自己起
-- event_name 自己起
-- params 事件的参数
-- type EventTypeEnum(NONE, PRE, POST, EXCEPTION, TIMEOUT, DELAY,) 0-无 1-前置 2-后置 3-异常 4-超时 5-延时
-- seq 事件的顺序 按从小到大执行??
-- node_id 节点的id
-- status NodeStatusEnum(WAITING, AUTO, APPROVED, FORWARDED, REJECTED, CANCELED, RECALLED, TIMEOUT,) 0-等待 1-自动 2-通过 3-转办 4-拒绝 5-取消 6-撤回 7-超时
INSERT INTO public.bpm_node_event (id, event_code, event_name, params, type, seq, node_id, status) VALUES (1500000000000004301, 'approvalMsg', '待办提醒', 'bpm', 1, 0, 1500000000000004201, 0);
INSERT INTO public.bpm_node_event (id, event_code, event_name, params, type, seq, node_id, status) VALUES (1500000000000004302, 'approvalDone', '待办完成', 'bpm', 2, 0, 1500000000000004201, 0);
INSERT INTO public.bpm_node_event (id, event_code, event_name, params, type, seq, node_id, status) VALUES (1500000000000004303, 'approvalMsg', '待办提醒', 'bpm', 1, 0, 1500000000000004202, 0);
INSERT INTO public.bpm_node_event (id, event_code, event_name, params, type, seq, node_id, status) VALUES (1500000000000004304, 'approvalDone', '待办完成', 'bpm', 2, 0, 1500000000000004202, 0);
-- INSERT INTO public.bpm_node_event (id, event_code, event_name, params, type, seq, node_id, status) VALUES (1500000000000004305, 'approvalMsg', '待办提醒', 'bpm', 1, 0, 1500000000000004203, 0);
-- INSERT INTO public.bpm_node_event (id, event_code, event_name, params, type, seq, node_id, status) VALUES (1500000000000004306, 'approvalDone', '待办完成', 'bpm', 2, 0, 1500000000000004203, 0);


-- 节点操作，节点可以执行的操作
-- type ActionTypeEnum(START, APPROVE, REJECT, RECALL, FORWARD, CANCEL, TIMEOUT) 0-开始 1-同意 2-打回 3-撤回 4-转交 5-取消 6-超时
-- code 同一个操作可以有多个执行，实际执行的哪个的判断条件。参考TricycleIllegalNotNeedTowHandle
-- name 自己起
-- to_node_id 操作完之后生成哪种节点
-- node_id 操作的当前节点
-- params 默认null
-- seq 操作的顺序
INSERT INTO public.bpm_action (id, type, code, name, node_id, to_node_id, params, seq) VALUES (1500000000000004401, 1, null,      '提交',   1500000000000004201, 1500000000000004202,  null, 99);
INSERT INTO public.bpm_action (id, type, code, name, node_id, to_node_id, params, seq) VALUES (1500000000000004402, 5, null,      '取消',   1500000000000004201, null,  null, 99);
INSERT INTO public.bpm_action (id, type, code, name, node_id, to_node_id, params, seq) VALUES (1500000000000004403, 1, null,      '录入完成',   1500000000000004202, 1500000000000004203, null, 99);
INSERT INTO public.bpm_action (id, type, code, name, node_id, to_node_id, params, seq) VALUES (1500000000000004404, 2, null,      '录入失败',   1500000000000004202, 1500000000000004204, null, 99);

-- 节点操作的事件，执行节点操作会触发的事件
-- action_id 操作的id
-- event_code 自己起
-- event_name 自己起
-- type EventTypeEnum(NONE, PRE, POST, EXCEPTION, TIMEOUT, DELAY,) 0-无 1-前置 2-后置 3-异常 4-超时 5-延时
-- seq 事件的顺序 按从小到大执行
-- params 默认null，自己起给handle传参，例如
INSERT INTO public.bpm_action_event (id, action_id, event_code, event_name, type, params, seq) VALUES (1500000000000004501, 1500000000000004401, 'vehiclePlateChangeStatusChange', '机动车改车牌状态更新', 1, '1', 0 ); -- 提交变成1
INSERT INTO public.bpm_action_event (id, action_id, event_code, event_name, type, params, seq) VALUES (1500000000000004502, 1500000000000004402, 'vehiclePlateChangeStatusChange', '机动车改车牌状态更新', 1, '3,1', 0 ); -- 取消变成3
INSERT INTO public.bpm_action_event (id, action_id, event_code, event_name, type, params, seq) VALUES (1500000000000004503, 1500000000000004403, 'vehiclePlateChangeStatusChange', '机动车改车牌状态更新', 1, '2,1', 0 ); -- 录入成功变成2
INSERT INTO public.bpm_action_event (id, action_id, event_code, event_name, type, params, seq) VALUES (1500000000000004504, 1500000000000004404, 'vehiclePlateChangeStatusChange', '机动车改车牌状态更新', 1, '4,1', 0 ); -- 录入失败变成4


INSERT INTO public.msg_template (id,  code, title, content, channel, type, category_id) VALUES (1418578764887756802, 'invoiceClient', 'XpouRa35fC7sxK_GtLmKKiS-iOVPvZxjJpe2TGMbTog', e'{"thing1":"[[${headerName}]]","amount2":"[[${amount}]]","phrase3":"[[${statusName}]]", "thing6":"[[${rejectReason}]]","time4":"[[${processedAt}]]","pages":"/pages/invoice/invoice-detail?id=[[${id}]]"}', 4, 0, 0);
INSERT INTO public.msg_template (id,  code, title, content, channel, type, category_id) VALUES (1418578764887756801, 'invoiceShop',   'gU6EZ9gciC_YTRX9Y3cjBVPqGZ7abgoGhOaLqOATn10', e'{"thing1":"[[${headerName}]]","amount2":"[[${amount}]]","phrase3":"[[${statusName}]]", "thing6":"[[${rejectReason}]]","time4":"[[${processedAt}]]","pages":"/pages/invoice/invoice-detail?id=[[${id}]]"}', 4, 0, 0);
INSERT INTO public.msg_template (id,  code, title, content, channel, type, category_id) VALUES (1418578764887756803,  'vehicleRefund', 'g4melL54occX59FfaANnPZIwoXCsXxOHDo6NmffvX1U', e'{"thing11":"[[${number}]]月卡","phrase4":"[[${statusName}]]","amount2":"[[${amount}]]", "thing12":"[[${rejectReason}]]","pages":"/pages/shop/vehicle/index?target=vehicle&numberFirst=[[${number}]]"}', 4, 0, 0);


INSERT INTO public.msg_template (id, code, title, content, channel, type, category_id) VALUES (1337149893478846471, 'vehicleReject', 'xoj6TpmoD3ESOCvoPpoaTEe7zOkB21miWxcXZmI5CZ8', '{"character_string2": "[[${id}]]","thing5": "机动车[[${number}]]审核","phrase1": "审核拒绝", "date3": "[[${#dates.format(#dates.createNow(), ''yyyy-MM-dd HH:mm:ss'')}]]","thing8": "拒绝理由：[[${checkRejectReason}]]","pages": "/pages/shop/vehicle/index?target=vehicle&numberFirst=[[${number}]]"}', 4, 0, 0);
--- TODO