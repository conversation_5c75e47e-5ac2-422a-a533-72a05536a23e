------------------------
-- add store columns
alter table if exists vehicle_monthly_card add column if not exists remind_days integer;
alter table if exists vehicle add column if not exists monthly_card_start_at timestamp;

-- modify vehicle_monthly_card columns comment
comment on column vehicle_monthly_card.remind_days is '提醒天数';


------------------------
-- modify config_allinpay columns comment
comment on column config_allinpay.cert_data is '';
comment on column config_allinpay.key_data is '';


------------------------
-- modify bpm_node_instance columns comment
comment on column bpm_node_instance.operated_reason is '';
comment on column bpm_node_instance.remark is '';


------------------------
-- modify lc_order columns comment
comment on column lc_order.match_mode is '匹配模式: 全字匹配、近似匹配、人工匹配、人工纠正,无信息时填''';
comment on column lc_order.open_gate_mode is '放行类型: 自动抬杆/手动放行,无信息时填''';


------------------------
-- modal_banner table
create table if not exists modal_banner (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    client smallint not null,
    place smallint not null,
    start_at timestamp,
    end_at timestamp,
    hide_hours integer default 0 not null,
    link_url varchar,
    image_url varchar not null,
    status smallint default 0 not null
);

-- modal_banner comment
comment on column modal_banner.id is 'ID';
comment on column modal_banner.created_by is '创建时间';
comment on column modal_banner.created_at is '创建人';
comment on column modal_banner.updated_by is '更新人';
comment on column modal_banner.updated_at is '更新时间';
comment on column modal_banner.ver is '版本号';
comment on column modal_banner.is_deleted is '是否删除 0:否 1:是';
comment on column modal_banner.client is '客户端类型';
comment on column modal_banner.place is '弹窗位置';
comment on column modal_banner.start_at is '开始时间';
comment on column modal_banner.end_at is '结束时间';
comment on column modal_banner.hide_hours is '关闭后不再弹出的时间';
comment on column modal_banner.link_url is '跳转小程序内地址';
comment on column modal_banner.image_url is '图片URL';
comment on column modal_banner.status is '0:否 1:是';
------------------------
-- add modal_banner columns
alter table if exists modal_banner add column if not exists title varchar not null;


------------------------
-- modify modal_banner columns comment
comment on column modal_banner.title is '标题';


------------------------
-- add coupon_tpl columns
alter table if exists coupon_tpl add column if not exists client_public smallint default 0 not null;


------------------------
-- add coupon_tpl columns
alter table if exists coupon_tpl add column if not exists client_receive_limit integer default 0 not null;
comment on column coupon_tpl.client_public is '游客可见';
comment on column coupon_tpl.client_receive_limit is '游客可领取个数';


------------------------
-- add vehicle columns
alter table if exists vehicle add column if not exists show smallint default 1 not null;

------------------------
-- modify vehicle columns comment
comment on column vehicle.show is '隐藏状态';



------------------------
-- vehicle_plate_change table
create table if not exists vehicle_plate_change (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    user_id bigint not null,
    vehicle_id bigint not null,
    old_plate varchar not null,
    new_plate varchar not null,
    modified_at timestamp,
    change_status smallint,
    bill_id bigint
);

-- vehicle_plate_change comment
comment on column vehicle_plate_change.id is 'ID';
comment on column vehicle_plate_change.created_by is '创建时间';
comment on column vehicle_plate_change.created_at is '创建人';
comment on column vehicle_plate_change.updated_by is '更新人';
comment on column vehicle_plate_change.updated_at is '更新时间';
comment on column vehicle_plate_change.ver is '版本号';
comment on column vehicle_plate_change.is_deleted is '是否删除 0:否 1:是';
comment on column vehicle_plate_change.user_id is '用户ID';
comment on column vehicle_plate_change.vehicle_id is '机动车ID';
comment on column vehicle_plate_change.old_plate is '变更前车牌';
comment on column vehicle_plate_change.new_plate is '变更后车牌';
comment on column vehicle_plate_change.modified_at is '变更时间';
comment on column vehicle_plate_change.change_status is '变更状态';
comment on column vehicle_plate_change.bill_id is '账单号';

------------------------
-- add vehicle_plate_change columns
alter table if exists vehicle_plate_change add column if not exists changed_at timestamp;
alter table if exists vehicle_plate_change add column if not exists reject_reason varchar;
comment on column vehicle_plate_change.changed_at is '修改时间';
comment on column vehicle_plate_change.reject_reason is '拒绝理由';



------------------------
-- add bill columns
alter table if exists bill add column if not exists invoice_id bigint;


------------------------
-- invoice table
create table if not exists invoice (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    header_type smallint not null,
    header_name varchar not null,
    tax_number varchar,
    content varchar not null,
    amount_cent integer not null,
    status smallint not null,
    reject_reason varchar,
    processed_at timestamp,
    processor_id bigint,
    remark varchar,
    download_url varchar,
    user_type smallint,
    user_id bigint,
    bill_ids varchar
);

-- invoice comment
comment on column invoice.id is 'ID';
comment on column invoice.created_by is '创建时间';
comment on column invoice.created_at is '创建人';
comment on column invoice.updated_by is '更新人';
comment on column invoice.updated_at is '更新时间';
comment on column invoice.ver is '版本号';
comment on column invoice.is_deleted is '是否删除 0:否 1:是';
comment on column invoice.header_type is '抬头类型';
comment on column invoice.header_name is '抬头名称';
comment on column invoice.tax_number is '税号';
comment on column invoice.content is '发票内容';
comment on column invoice.amount_cent is '发票金额(分)';
comment on column invoice.status is '开票状态';
comment on column invoice.reject_reason is '拒绝理由';
comment on column invoice.processed_at is '处理时间';
comment on column invoice.processor_id is '处理人';
comment on column invoice.remark is '备注';
comment on column invoice.download_url is '下载地址';
comment on column invoice.user_type is '用户类型';
comment on column invoice.user_id is '用户ID';
comment on column invoice.bill_ids is '账单ID列表';





-- modify bill columns comment
comment on column bill.invoice_id is '发票ID';


------------------------
-- add invoice columns
alter table if exists invoice add column if not exists bill_count integer default 0;


------------------------
-- modify invoice columns comment
comment on column invoice.bill_count is '订单数';


------------------------
-- add vehicle columns
alter table if exists vehicle add column if not exists refund_status smallint default 0 not null;
alter table if exists vehicle add column if not exists refund_processed_at timestamp;
alter table if exists vehicle add column if not exists refund_processor_id bigint;
alter table if exists vehicle add column if not exists refund_remark varchar;
alter table if exists vehicle add column if not exists refund_reject_reason varchar;
alter table if exists vehicle add column if not exists refund_money_cent integer;
alter table if exists vehicle add column if not exists refund_images varchar;


------------------------
-- modify vehicle columns comment
comment on column vehicle.refund_remark is '退款备注';
comment on column vehicle.refund_reject_reason is '退款拒绝理由';
comment on column vehicle.refund_status is '退款状态';
comment on column vehicle.refund_processed_at is '退款处理时间';
comment on column vehicle.refund_money_cent is '退款金额';
comment on column vehicle.refund_images is '退款图片';
comment on column vehicle.refund_processor_id is '退款处理人';



-- add coupon_tpl columns
alter table if exists coupon_tpl add column if not exists is_stackable smallint default 0 not null;


------------------------
-- add coupon columns
alter table if exists coupon add column if not exists is_stackable smallint default 0 not null;


------------------------
-- modify coupon_tpl columns comment
comment on column coupon_tpl.is_stackable is '可叠加';


------------------------
-- modify coupon columns comment
comment on column coupon.is_stackable is '可叠加';


------------------------
-- add coupon_tpl columns
alter table if exists coupon_tpl add column if not exists bound_vehicle_monthly_card_ids varchar;

------------------------
-- modify coupon_tpl columns comment
comment on column coupon_tpl.bound_vehicle_monthly_card_ids is '绑定的月卡';


------------------------
-- add vehicle columns
alter table if exists vehicle add column if not exists before_lc_white varchar;


------------------------
-- modify vehicle columns comment
comment on column vehicle.before_lc_white is '之前的蓝卡白名单';


------------------------
-- add vehicle columns
alter table if exists vehicle add column if not exists check_reject_reason varchar;
alter table if exists vehicle add column if not exists check_remark varchar;


------------------------
-- modify vehicle columns comment
comment on column vehicle.check_remark is '审核备注';
comment on column vehicle.check_reject_reason is '审核拒绝理由';


------------------------
alter table if exists job_posting add column if not exists salary_max integer;
alter table if exists job_posting add column if not exists recommend integer default 0 not null;
alter table if exists job_posting add column if not exists is_official smallint default 0 not null;
alter table if exists park_service add column if not exists price_max integer;

comment on column job_posting.salary_max is '待遇';
comment on column job_posting.is_official is '是否官方';
comment on column job_posting.recommend is '推荐值';
comment on column park_service.price_max is '价格';


ALTER TABLE store ALTER COLUMN shop_id DROP NOT NULL;


-- modify job_posting columns not null
alter table if exists job_posting alter column is_official set default 0;
alter table if exists job_posting alter column is_official set not null;
alter table if exists job_posting alter column recommend set default 0;
alter table if exists job_posting alter column recommend set not null;


------------------------
-- add vehicle columns
alter table if exists vehicle add column if not exists root_id bigint;


------------------------
-- modify vehicle columns comment
comment on column vehicle.root_id is '根ID';



------------------------
-- client_action_count table
create table if not exists client_action_count (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    key varchar not null,
    count integer default 0 not null,
    time timestamp not null,
    time_unit varchar not null
);

-- client_action_count comment
comment on column client_action_count.id is 'ID';
comment on column client_action_count.created_by is '创建时间';
comment on column client_action_count.created_at is '创建人';
comment on column client_action_count.updated_by is '更新人';
comment on column client_action_count.updated_at is '更新时间';
comment on column client_action_count.ver is '版本号';
comment on column client_action_count.is_deleted is '是否删除 0:否 1:是';
comment on column client_action_count.key is '键';
comment on column client_action_count.count is '次数';
comment on column client_action_count.time is '时间';
comment on column client_action_count.time_unit is '时间单位';



------------------------
-- energy_cell table
create table if not exists energy_cell (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    company_name varchar not null,
    company_code varchar not null,
    cell_name varchar not null,
    cell_no varchar not null,
    cell_id varchar not null,
    master_name varchar not null,
    master_mobile varchar not null,
    master_id_card_no varchar not null,
    layer_name varchar,
    layer_no varchar,
    admin_user_id bigint not null,
    subscribe smallint default 0 not null,
    remark varchar
);

-- energy_cell comment
comment on column energy_cell.id is 'ID';
comment on column energy_cell.created_by is '创建时间';
comment on column energy_cell.created_at is '创建人';
comment on column energy_cell.updated_by is '更新人';
comment on column energy_cell.updated_at is '更新时间';
comment on column energy_cell.ver is '版本号';
comment on column energy_cell.is_deleted is '是否删除 0:否 1:是';
comment on column energy_cell.company_name is '管理公司';
comment on column energy_cell.company_code is '管理公司编码';
comment on column energy_cell.cell_name is '店铺名称';
comment on column energy_cell.cell_no is '店铺编号';
comment on column energy_cell.cell_id is '店铺ID';
comment on column energy_cell.master_name is '店主姓名';
comment on column energy_cell.master_mobile is '店主手机号';
comment on column energy_cell.master_id_card_no is '店主身份证号';
comment on column energy_cell.layer_name is '店主所在层';
comment on column energy_cell.layer_no is '店铺层门牌号';
comment on column energy_cell.admin_user_id is '用户Id';
comment on column energy_cell.subscribe is '是否订阅';
comment on column energy_cell.remark is '备注';

------------------------
-- modify product_order columns comment
comment on column product_order.merge is '是否多店铺合并订单';


-- add store columns
alter table if exists store add column if not exists location varchar;

-- add energy_cell columns
alter table if exists energy_cell add column if not exists business_license varchar;

------------------------
-- modify store columns comment
comment on column store.location is '店铺坐标';


------------------------
-- modify energy_cell columns comment
comment on column energy_cell.business_license is '营业执照';




------------------------
-- add energy_cell columns
alter table if exists energy_cell add column if not exists is_verify smallint default 0 not null;
alter table if exists energy_cell add column if not exists property_cell_id bigint;


------------------------
-- property_cell table
create table if not exists property_cell (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    company_name varchar not null,
    company_code varchar not null,
    cell_name varchar not null,
    cell_no varchar not null,
    cell_id varchar not null,
    master_name varchar not null,
    master_mobile varchar not null,
    master_id_card_no varchar not null,
    layer_name varchar,
    layer_no varchar,
    business_license varchar
);

-- property_cell comment
comment on column property_cell.id is 'ID';
comment on column property_cell.created_by is '创建时间';
comment on column property_cell.created_at is '创建人';
comment on column property_cell.updated_by is '更新人';
comment on column property_cell.updated_at is '更新时间';
comment on column property_cell.ver is '版本号';
comment on column property_cell.is_deleted is '是否删除 0:否 1:是';
comment on column property_cell.company_name is '管理公司';
comment on column property_cell.company_code is '管理公司编码';
comment on column property_cell.cell_name is '店铺名称';
comment on column property_cell.cell_no is '店铺编号';
comment on column property_cell.cell_id is '店铺ID';
comment on column property_cell.master_name is '店主姓名';
comment on column property_cell.master_mobile is '店主手机号';
comment on column property_cell.master_id_card_no is '店主身份证号';
comment on column property_cell.layer_name is '店主所在层';
comment on column property_cell.layer_no is '店铺层门牌号';
comment on column property_cell.business_license is '营业执照';


-- config_aliyun_openapi table
create table if not exists config_aliyun_sms (
     id bigint primary key,
     created_by bigint default 0 not null,
     created_at timestamp default CURRENT_TIMESTAMP not null,
     updated_by bigint default 0 not null,
     updated_at timestamp default CURRENT_TIMESTAMP not null,
     ver bigint default 0 not null,
     access_key varchar,
     access_secret varchar
);

-- config_aliyun_openapi comment
comment on column config_aliyun_sms.id is 'ID';
comment on column config_aliyun_sms.created_by is '创建时间';
comment on column config_aliyun_sms.created_at is '创建人';
comment on column config_aliyun_sms.updated_by is '更新人';
comment on column config_aliyun_sms.updated_at is '更新时间';
comment on column config_aliyun_sms.ver is '版本号';


------------------------
-- modify energy_cell columns comment
comment on column energy_cell.is_verify is '是否验证';
comment on column energy_cell.property_cell_id is '物业店铺';


------------------------
-- add property_cell columns
alter table if exists property_cell add column if not exists hide smallint default 0 not null;


------------------------
-- modify property_cell columns comment
comment on column property_cell.hide is '折叠起来';



------------------------
-- add property_cell columns
alter table if exists property_cell add column if not exists wait_update smallint default 0 not null;


------------------------
-- modify property_cell columns comment
comment on column property_cell.wait_update is '是否待更新';


-- TODO prod------------------------------------------------------------------------------------------------------------------------------------------------


------------------------
-- add property_cell columns
alter table if exists property_cell add column if not exists ocr_business_license_result varchar;


------------------------
-- modify property_cell columns comment
comment on column property_cell.ocr_business_license_result is 'Ocr结果';


