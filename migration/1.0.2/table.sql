
------------------------
-- tricycle_info_record table
create table if not exists tricycle_info_record (
                                                    id bigint primary key,
                                                    created_by bigint default 0 not null,
                                                    created_at timestamp default CURRENT_TIMESTAMP not null,
                                                    updated_by bigint default 0 not null,
                                                    updated_at timestamp default CURRENT_TIMESTAMP not null,
                                                    ver bigint default 0 not null,
                                                    is_deleted smallint default 0 not null,
                                                    tricycle_id bigint,
                                                    user_id bigint,
                                                    realname varchar,
                                                    mobile varchar
);

-- tricycle_info_record comment
comment on column tricycle_info_record.id is 'ID';
comment on column tricycle_info_record.created_by is '创建时间';
comment on column tricycle_info_record.created_at is '创建人';
comment on column tricycle_info_record.updated_by is '更新人';
comment on column tricycle_info_record.updated_at is '更新时间';
comment on column tricycle_info_record.ver is '版本号';
comment on column tricycle_info_record.is_deleted is '是否删除 0:否 1:是';


------------------------
-- add tricycle_info_record columns
alter table if exists tricycle_info_record add column if not exists images varchar;


------------------------
-- bi_trading_count table
create table if not exists bi_trading_count (
                                                id bigint primary key,
                                                created_by bigint default 0 not null,
                                                created_at timestamp default CURRENT_TIMESTAMP not null,
                                                updated_by bigint default 0 not null,
                                                updated_at timestamp default CURRENT_TIMESTAMP not null,
                                                ver bigint default 0 not null,
                                                date timestamp not null,
                                                order_count integer not null,
                                                amount_count integer not null,
                                                order_count_total integer not null,
                                                amount_count_total integer not null,
                                                type smallint not null
);

-- bi_trading_count comment
comment on column bi_trading_count.id is 'ID';
comment on column bi_trading_count.created_by is '创建时间';
comment on column bi_trading_count.created_at is '创建人';
comment on column bi_trading_count.updated_by is '更新人';
comment on column bi_trading_count.updated_at is '更新时间';
comment on column bi_trading_count.ver is '版本号';
comment on column bi_trading_count.date is '日期';
comment on column bi_trading_count.order_count is '订单数';
comment on column bi_trading_count.amount_count is '订单金额';
comment on column bi_trading_count.order_count_total is '订单总数';
comment on column bi_trading_count.amount_count_total is '订单金额总数';
comment on column bi_trading_count.type is '类型';


------------------------
-- product_refund_apply table
create table if not exists product_refund_apply (
                                                    id bigint primary key,
                                                    created_by bigint default 0 not null,
                                                    created_at timestamp default CURRENT_TIMESTAMP not null,
                                                    updated_by bigint default 0 not null,
                                                    updated_at timestamp default CURRENT_TIMESTAMP not null,
                                                    ver bigint default 0 not null,
                                                    is_deleted smallint default 0 not null,
                                                    order_id bigint not null,
                                                    order_item_id bigint,
                                                    user_id bigint not null,
                                                    product_id bigint,
                                                    store_id bigint not null,
                                                    refund_amount integer not null,
                                                    reason varchar,
                                                    description varchar,
                                                    status smallint not null,
                                                    handle_time timestamp,
                                                    handle_note varchar,
                                                    refunded smallint
);

-- product_refund_apply comment
comment on column product_refund_apply.id is 'ID';
comment on column product_refund_apply.created_by is '创建时间';
comment on column product_refund_apply.created_at is '创建人';
comment on column product_refund_apply.updated_by is '更新人';
comment on column product_refund_apply.updated_at is '更新时间';
comment on column product_refund_apply.ver is '版本号';
comment on column product_refund_apply.is_deleted is '是否删除 0:否 1:是';
comment on column product_refund_apply.order_id is '订单ID';
comment on column product_refund_apply.order_item_id is '订单项ID';
comment on column product_refund_apply.user_id is '用户ID';
comment on column product_refund_apply.product_id is '商品ID';
comment on column product_refund_apply.store_id is '店铺ID';
comment on column product_refund_apply.refund_amount is '退款金额(分)';
comment on column product_refund_apply.reason is '申请原因';
comment on column product_refund_apply.description is '说明';
comment on column product_refund_apply.status is '退款状态';
comment on column product_refund_apply.handle_time is '处理时间';
comment on column product_refund_apply.handle_note is '处理说明';
comment on column product_refund_apply.refunded is '是否已退款';



------------------------
-- product_order table
create table if not exists product_order (
                                             id bigint primary key,
                                             created_by bigint default 0 not null,
                                             created_at timestamp default CURRENT_TIMESTAMP not null,
                                             updated_by bigint default 0 not null,
                                             updated_at timestamp default CURRENT_TIMESTAMP not null,
                                             ver bigint default 0 not null,
                                             is_deleted smallint default 0 not null,
                                             user_id bigint not null,
                                             parent_id bigint default 0 not null,
                                             store_id bigint not null,
                                             refund_id bigint,
                                             order_no varchar not null,
                                             total_amount integer not null,
                                             real_amount integer not null,
                                             status smallint not null,
                                             pay_time timestamp,
                                             expire_time timestamp not null,
                                             refund_reason varchar,
                                             refund_time timestamp,
                                             remark varchar,
                                             merge smallint
);

-- product_order comment
comment on column product_order.id is 'ID';
comment on column product_order.created_by is '创建时间';
comment on column product_order.created_at is '创建人';
comment on column product_order.updated_by is '更新人';
comment on column product_order.updated_at is '更新时间';
comment on column product_order.ver is '版本号';
comment on column product_order.is_deleted is '是否删除 0:否 1:是';
comment on column product_order.user_id is '用户ID';
comment on column product_order.parent_id is '父ID';
comment on column product_order.store_id is '商铺ID';
comment on column product_order.refund_id is '退款ID';
comment on column product_order.order_no is '订单编号';
comment on column product_order.total_amount is '总金额';
comment on column product_order.real_amount is '实付金额';
comment on column product_order.status is '订单状态';
comment on column product_order.pay_time is '支付时间';
comment on column product_order.expire_time is '过期时间';
comment on column product_order.refund_reason is '退款原因';
comment on column product_order.refund_time is '退款时间';
comment on column product_order.remark is '备注';



------------------------
-- modify shopping_cart columns type
alter table if exists shopping_cart alter column updated_at type timestamp;
alter table if exists shopping_cart alter column created_at type timestamp;
alter table if exists shopping_cart alter column add_time type timestamp;


------------------------
-- product_review table
create table if not exists product_review (
                                              id bigint primary key,
                                              created_by bigint default 0 not null,
                                              created_at timestamp default CURRENT_TIMESTAMP not null,
                                              updated_by bigint default 0 not null,
                                              updated_at timestamp default CURRENT_TIMESTAMP not null,
                                              ver bigint default 0 not null,
                                              is_deleted smallint default 0 not null,
                                              user_id bigint not null,
                                              product_id bigint not null,
                                              order_id bigint not null,
                                              rating integer not null,
                                              content varchar,
                                              is_anonymous smallint not null,
                                              reply_content varchar,
                                              reply_time timestamp,
                                              show smallint default 1 not null
);

-- product_review comment
comment on column product_review.id is 'ID';
comment on column product_review.created_by is '创建时间';
comment on column product_review.created_at is '创建人';
comment on column product_review.updated_by is '更新人';
comment on column product_review.updated_at is '更新时间';
comment on column product_review.ver is '版本号';
comment on column product_review.is_deleted is '是否删除 0:否 1:是';
comment on column product_review.user_id is '用户ID';
comment on column product_review.product_id is '商品ID';
comment on column product_review.order_id is '订单ID';
comment on column product_review.rating is '评分(1-5)';
comment on column product_review.content is '评价内容';
comment on column product_review.is_anonymous is '是否匿名评价';
comment on column product_review.reply_content is '商家回复内容';
comment on column product_review.reply_time is '商家回复时间';
comment on column product_review.show is '是否显示';



------------------------
-- add article columns
alter table if exists article add column if not exists like_count integer default 0 not null;
alter table if exists article add column if not exists author varchar;
-- modify article columns comment
comment on column article.pages is '小程序内部跳转链接';


------------------------
-- modify config_allinpay columns comment
comment on column config_allinpay.cert_data is '';
comment on column config_allinpay.key_data is '';


------------------------
-- article_like_r table
create table if not exists article_like_r (
                                              id bigint primary key,
                                              created_by bigint default 0 not null,
                                              created_at timestamp default CURRENT_TIMESTAMP not null,
                                              updated_by bigint default 0 not null,
                                              updated_at timestamp default CURRENT_TIMESTAMP not null,
                                              ver bigint default 0 not null,
                                              user_id bigint,
                                              article_id bigint
);

-- article_like_r comment
comment on column article_like_r.id is 'ID';
comment on column article_like_r.created_by is '创建时间';
comment on column article_like_r.created_at is '创建人';
comment on column article_like_r.updated_by is '更新人';
comment on column article_like_r.updated_at is '更新时间';
comment on column article_like_r.ver is '版本号';



------------------------
-- modify tricycle_batch columns comment
comment on column tricycle_batch.type is '批次类型';


------------------------
-- store_employee_r table
create table if not exists store_employee_r (
                                                id bigint primary key,
                                                created_by bigint default 0 not null,
                                                created_at timestamp default CURRENT_TIMESTAMP not null,
                                                updated_by bigint default 0 not null,
                                                updated_at timestamp default CURRENT_TIMESTAMP not null,
                                                ver bigint default 0 not null,
                                                store_id bigint,
                                                user_id bigint,
                                                name varchar,
                                                mobile varchar,
                                                status smallint
);

-- store_employee_r comment
comment on column store_employee_r.id is 'ID';
comment on column store_employee_r.created_by is '创建时间';
comment on column store_employee_r.created_at is '创建人';
comment on column store_employee_r.updated_by is '更新人';
comment on column store_employee_r.updated_at is '更新时间';
comment on column store_employee_r.ver is '版本号';



------------------------
-- client_user_points table
create table if not exists client_user_points (
                                                  id bigint primary key,
                                                  created_by bigint default 0 not null,
                                                  created_at timestamp default CURRENT_TIMESTAMP not null,
                                                  updated_by bigint default 0 not null,
                                                  updated_at timestamp default CURRENT_TIMESTAMP not null,
                                                  ver bigint default 0 not null,
                                                  is_deleted smallint default 0 not null,
                                                  user_id bigint not null,
                                                  total_points integer not null,
                                                  available_points integer not null,
                                                  used_points integer not null,
                                                  expired_points integer not null
);

-- client_user_points comment
comment on column client_user_points.id is 'ID';
comment on column client_user_points.created_by is '创建时间';
comment on column client_user_points.created_at is '创建人';
comment on column client_user_points.updated_by is '更新人';
comment on column client_user_points.updated_at is '更新时间';
comment on column client_user_points.ver is '版本号';
comment on column client_user_points.is_deleted is '是否删除 0:否 1:是';
comment on column client_user_points.user_id is '用户ID';
comment on column client_user_points.total_points is '总积分';
comment on column client_user_points.available_points is '可用积分';
comment on column client_user_points.used_points is '已使用积分';
comment on column client_user_points.expired_points is '已过期积分';



------------------------
-- modify bi_trading_count columns type
alter table if exists bi_trading_count alter column date type timestamp;
alter table if exists bi_trading_count alter column updated_at type timestamp;
alter table if exists bi_trading_count alter column created_at type timestamp;


------------------------
-- product_order_item table
create table if not exists product_order_item (
                                                  id bigint primary key,
                                                  created_by bigint default 0 not null,
                                                  created_at timestamp default CURRENT_TIMESTAMP not null,
                                                  updated_by bigint default 0 not null,
                                                  updated_at timestamp default CURRENT_TIMESTAMP not null,
                                                  ver bigint default 0 not null,
                                                  is_deleted smallint default 0 not null,
                                                  order_id bigint not null,
                                                  product_id bigint not null,
                                                  quantity integer not null,
                                                  unit_price integer not null,
                                                  total_amount integer not null,
                                                  real_amount integer,
                                                  expire_time timestamp,
                                                  code varchar,
                                                  use_time timestamp,
                                                  snapshot varchar,
                                                  review_time timestamp,
                                                  check_admin_user_id bigint
);

-- product_order_item comment
comment on column product_order_item.id is 'ID';
comment on column product_order_item.created_by is '创建时间';
comment on column product_order_item.created_at is '创建人';
comment on column product_order_item.updated_by is '更新人';
comment on column product_order_item.updated_at is '更新时间';
comment on column product_order_item.ver is '版本号';
comment on column product_order_item.is_deleted is '是否删除 0:否 1:是';
comment on column product_order_item.order_id is '订单ID';
comment on column product_order_item.product_id is '商品ID';
comment on column product_order_item.quantity is '购买数量';
comment on column product_order_item.unit_price is '单价';
comment on column product_order_item.total_amount is '总金额';
comment on column product_order_item.expire_time is '过期时间';
comment on column product_order_item.code is '商品券号';
comment on column product_order_item.use_time is '使用时间';
comment on column product_order_item.snapshot is '快照';
comment on column product_order_item.review_time is '评价时间';
comment on column product_order_item.check_admin_user_id is '核销人ID';



------------------------
-- store_tag_r table
create table if not exists store_tag_r (
                                           id bigint primary key,
                                           created_by bigint default 0 not null,
                                           created_at timestamp default CURRENT_TIMESTAMP not null,
                                           updated_by bigint default 0 not null,
                                           updated_at timestamp default CURRENT_TIMESTAMP not null,
                                           ver bigint default 0 not null,
                                           store_id bigint not null,
                                           tag varchar not null
);

-- store_tag_r comment
comment on column store_tag_r.id is 'ID';
comment on column store_tag_r.created_by is '创建时间';
comment on column store_tag_r.created_at is '创建人';
comment on column store_tag_r.updated_by is '更新人';
comment on column store_tag_r.updated_at is '更新时间';
comment on column store_tag_r.ver is '版本号';
comment on column store_tag_r.store_id is '商铺ID';
comment on column store_tag_r.tag is '标签名称';



------------------------
-- modify bpm_node_instance columns comment
comment on column bpm_node_instance.operated_reason is '';
comment on column bpm_node_instance.remark is '';


------------------------
-- product table
create table if not exists product (
                                       id bigint primary key,
                                       created_by bigint default 0 not null,
                                       created_at timestamp default CURRENT_TIMESTAMP not null,
                                       updated_by bigint default 0 not null,
                                       updated_at timestamp default CURRENT_TIMESTAMP not null,
                                       ver bigint default 0 not null,
                                       is_deleted smallint default 0 not null,
                                       store_id bigint not null,
                                       title varchar not null,
                                       sub_title varchar,
                                       type smallint not null,
                                       description varchar,
                                       original_price integer not null,
                                       valid_days integer,
                                       limit_time varchar,
                                       price integer not null,
                                       valid_from timestamp not null,
                                       valid_to timestamp not null,
                                       max_purchase_per_user integer not null,
                                       stock integer not null,
                                       sold_count integer,
                                       use_rules varchar,
                                       refund_policy varchar,
                                       certification smallint,
                                       rate integer,
                                       total_reviews integer,
                                       status smallint not null,
                                       recommend smallint,
                                       direct_refund smallint
);

-- product comment
comment on column product.id is 'ID';
comment on column product.created_by is '创建时间';
comment on column product.created_at is '创建人';
comment on column product.updated_by is '更新人';
comment on column product.updated_at is '更新时间';
comment on column product.ver is '版本号';
comment on column product.is_deleted is '是否删除 0:否 1:是';
comment on column product.store_id is '商铺ID';
comment on column product.title is '标题';
comment on column product.sub_title is '副标题';
comment on column product.type is '商品类型';
comment on column product.description is '描述';
comment on column product.original_price is '原价';
comment on column product.valid_days is '有效期天数';
comment on column product.limit_time is '限制时间';
comment on column product.price is '商品价';
comment on column product.valid_from is '有效期开始时间';
comment on column product.valid_to is '有效期结束时间';
comment on column product.max_purchase_per_user is '每人最大购买数量';
comment on column product.stock is '库存数量';
comment on column product.sold_count is '已售';
comment on column product.use_rules is '使用规则';
comment on column product.refund_policy is '退款政策';
comment on column product.certification is '官方认证';
comment on column product.rate is '平均评分';
comment on column product.total_reviews is '总评价数';
comment on column product.status is '状态';
comment on column product.recommend is '是否推荐';
comment on column product.direct_refund is '是否支持直接退款';



------------------------
-- modify coupon columns comment
comment on column coupon.user_type is '用户类型';


------------------------
-- store table
create table if not exists store (
                                     id bigint primary key,
                                     created_by bigint default 0 not null,
                                     created_at timestamp default CURRENT_TIMESTAMP not null,
                                     updated_by bigint default 0 not null,
                                     updated_at timestamp default CURRENT_TIMESTAMP not null,
                                     ver bigint default 0 not null,
                                     is_deleted smallint default 0 not null,
                                     cell_no varchar not null,
                                     shop_id bigint not null,
                                     name varchar not null,
                                     type smallint,
                                     status smallint not null,
                                     address varchar,
                                     admin_user_id bigint,
                                     contact varchar,
                                     mobile varchar,
                                     rate integer,
                                     review_count integer,
                                     open_at varchar,
                                     min_price integer,
                                     close_at varchar,
                                     sold_count integer,
                                     recommend smallint
);

-- store comment
comment on column store.id is 'ID';
comment on column store.created_by is '创建时间';
comment on column store.created_at is '创建人';
comment on column store.updated_by is '更新人';
comment on column store.updated_at is '更新时间';
comment on column store.ver is '版本号';
comment on column store.is_deleted is '是否删除 0:否 1:是';
comment on column store.cell_no is '商铺编号';
comment on column store.shop_id is '商铺Id';
comment on column store.name is '店铺名称';
comment on column store.type is '商品类型';
comment on column store.status is '店铺状态';
comment on column store.address is '店铺地址';
comment on column store.admin_user_id is '管理员用户ID';
comment on column store.contact is '联系人';
comment on column store.mobile is '联系电话';
comment on column store.rate is '评分';
comment on column store.review_count is '评价数';
comment on column store.open_at is '开店时间';
comment on column store.min_price is '最低价格';
comment on column store.close_at is '闭店时间';
comment on column store.sold_count is '已售';
comment on column store.recommend is '是否推荐';



------------------------
-- modify lc_order columns comment
comment on column lc_order.match_mode is '匹配模式: 全字匹配、近似匹配、人工匹配、人工纠正,无信息时填''';
comment on column lc_order.open_gate_mode is '放行类型: 自动抬杆/手动放行,无信息时填''';


------------------------
-- client_user_points_record table
create table if not exists client_user_points_record (
                                                         id bigint primary key,
                                                         created_by bigint default 0 not null,
                                                         created_at timestamp default CURRENT_TIMESTAMP not null,
                                                         updated_by bigint default 0 not null,
                                                         updated_at timestamp default CURRENT_TIMESTAMP not null,
                                                         ver bigint default 0 not null,
                                                         is_deleted smallint default 0 not null,
                                                         user_id bigint not null,
                                                         points integer not null,
                                                         type smallint not null,
                                                         description varchar,
                                                         reason varchar,
                                                         order_id bigint,
                                                         expire_time timestamp,
                                                         remark varchar
);

-- client_user_points_record comment
comment on column client_user_points_record.id is 'ID';
comment on column client_user_points_record.created_by is '创建时间';
comment on column client_user_points_record.created_at is '创建人';
comment on column client_user_points_record.updated_by is '更新人';
comment on column client_user_points_record.updated_at is '更新时间';
comment on column client_user_points_record.ver is '版本号';
comment on column client_user_points_record.is_deleted is '是否删除 0:否 1:是';
comment on column client_user_points_record.user_id is '用户ID';
comment on column client_user_points_record.points is '积分变动';
comment on column client_user_points_record.type is '变动类型';
comment on column client_user_points_record.description is '变动描述';
comment on column client_user_points_record.reason is '变动原因';
comment on column client_user_points_record.order_id is '关联订单ID';
comment on column client_user_points_record.expire_time is '过期时间';
comment on column client_user_points_record.remark is '备注';



