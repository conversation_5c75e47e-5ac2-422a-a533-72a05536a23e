------------------------
-- add vehicle columns
alter table if exists vehicle add column if not exists coupon_id bigint;

------------------------
-- modify vehicle_monthly_card_order columns type
alter table if exists vehicle_monthly_card_order alter column status type smallint;

------------------------
-- add coupon columns
alter table if exists coupon add column if not exists user_type smallint default 0 not null;

------------------------
-- add admin_user columns
alter table if exists admin_user add column if not exists bound_broker_id bigint;
------------------------
-- modify config_allinpay columns comment
comment on column config_allinpay.cert_data is '';
comment on column config_allinpay.key_data is '';


------------------------
-- add tricycle_batch columns
alter table if exists tricycle_batch add column if not exists type smallint default 1 not null;


------------------------
-- modify bpm_node_instance columns comment
comment on column bpm_node_instance.operated_reason is '';
comment on column bpm_node_instance.remark is '';


------------------------
-- modify lc_order columns comment
comment on column lc_order.match_mode is '匹配模式: 全字匹配、近似匹配、人工匹配、人工纠正,无信息时填''';
comment on column lc_order.open_gate_mode is '放行类型: 自动抬杆/手动放行,无信息时填''';


------------------------
-- modify tricycle_batch columns comment
comment on column tricycle_batch.type is '批次类型';


------------------------
-- add tricycle columns
alter table if exists tricycle add column if not exists effective_at timestamp;


------------------------
-- modify tricycle columns null
alter table if exists tricycle alter column number drop default;
alter table if exists tricycle alter column number drop not null;
-- modify tricycle columns comment
comment on column tricycle.effective_at is '生效时间';


------------------------
-- add admin_role columns
alter table if exists admin_role add column if not exists home varchar;


------------------------
-- modify feedback columns null
alter table if exists feedback alter column user_id drop default;
alter table if exists feedback alter column user_id drop not null;


