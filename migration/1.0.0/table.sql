------------------------
-- broker_info table
create table if not exists broker_info (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    name varchar not null,
    avatar varchar,
    job_position varchar,
    job_title varchar,
    mobile varchar not null,
    past_volume_count integer default 0,
    status smallint default 1 not null,
    win_percent numeric default 0
);

-- broker_info comment
comment on column broker_info.id is 'ID';
comment on column broker_info.created_by is '创建时间';
comment on column broker_info.created_at is '创建人';
comment on column broker_info.updated_by is '更新人';
comment on column broker_info.updated_at is '更新时间';
comment on column broker_info.ver is '版本号';
comment on column broker_info.is_deleted is '是否删除 0:否 1:是';
comment on column broker_info.name is '姓名';
comment on column broker_info.avatar is '头像';
comment on column broker_info.job_position is '职位';
comment on column broker_info.job_title is '职称';
comment on column broker_info.mobile is '手机号';
comment on column broker_info.past_volume_count is '历史成交量';
comment on column broker_info.status is '状态';
comment on column broker_info.win_percent is '成功率';



------------------------
-- shop_region_r table
create table if not exists shop_region_r (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    shop_id bigint not null,
    region varchar not null
);

-- shop_region_r comment
comment on column shop_region_r.id is 'ID';
comment on column shop_region_r.created_by is '创建时间';
comment on column shop_region_r.created_at is '创建人';
comment on column shop_region_r.updated_by is '更新人';
comment on column shop_region_r.updated_at is '更新时间';
comment on column shop_region_r.ver is '版本号';
comment on column shop_region_r.shop_id is '商铺ID';
comment on column shop_region_r.region is '所属区域';



------------------------
-- vehicle_monthly_card table
create table if not exists vehicle_monthly_card (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    name varchar,
    description varchar,
    duration integer,
    price integer
);

-- vehicle_monthly_card comment
comment on column vehicle_monthly_card.id is 'ID';
comment on column vehicle_monthly_card.created_by is '创建时间';
comment on column vehicle_monthly_card.created_at is '创建人';
comment on column vehicle_monthly_card.updated_by is '更新人';
comment on column vehicle_monthly_card.updated_at is '更新时间';
comment on column vehicle_monthly_card.ver is '版本号';
comment on column vehicle_monthly_card.is_deleted is '是否删除 0:否 1:是';
comment on column vehicle_monthly_card.name is '月卡名称';
comment on column vehicle_monthly_card.description is '月卡说明';
comment on column vehicle_monthly_card.duration is '有效时长(天)';
comment on column vehicle_monthly_card.price is '价格';



------------------------
-- shop_sync_conflict table
create table if not exists shop_sync_conflict (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    cell_no varchar,
    conflict_field varchar,
    remote_value varchar,
    local_value varchar,
    right_value varchar,
    handled smallint default 0 not null,
    handled_at timestamp
);

-- shop_sync_conflict comment
comment on column shop_sync_conflict.id is 'ID';
comment on column shop_sync_conflict.created_by is '创建时间';
comment on column shop_sync_conflict.created_at is '创建人';
comment on column shop_sync_conflict.updated_by is '更新人';
comment on column shop_sync_conflict.updated_at is '更新时间';
comment on column shop_sync_conflict.ver is '版本号';
comment on column shop_sync_conflict.is_deleted is '是否删除 0:否 1:是';
comment on column shop_sync_conflict.cell_no is '编号';
comment on column shop_sync_conflict.conflict_field is '冲突字段';
comment on column shop_sync_conflict.remote_value is '远程值';
comment on column shop_sync_conflict.local_value is '本地值';
comment on column shop_sync_conflict.right_value is '解决冲突之后的值';
comment on column shop_sync_conflict.handled is '是否已处理';
comment on column shop_sync_conflict.handled_at is '处理时间';



------------------------
-- coupon_tpl table
create table if not exists coupon_tpl (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    title varchar not null,
    receive_title varchar not null,
    type smallint not null,
    scene smallint,
    end_type smallint not null,
    absolute_end_at timestamp,
    relative_end_at integer,
    par_value integer not null,
    publish_count integer default 0 not null,
    received_count integer default 0 not null,
    used_count integer default 0 not null,
    status smallint default 1 not null
);

-- coupon_tpl comment
comment on column coupon_tpl.id is 'ID';
comment on column coupon_tpl.created_by is '创建时间';
comment on column coupon_tpl.created_at is '创建人';
comment on column coupon_tpl.updated_by is '更新人';
comment on column coupon_tpl.updated_at is '更新时间';
comment on column coupon_tpl.ver is '版本号';
comment on column coupon_tpl.is_deleted is '是否删除 0:否 1:是';
comment on column coupon_tpl.title is '优惠券标题';
comment on column coupon_tpl.receive_title is '领取标题';
comment on column coupon_tpl.type is '优惠券类型';
comment on column coupon_tpl.scene is '使用场景';
comment on column coupon_tpl.end_type is '过期类型';
comment on column coupon_tpl.absolute_end_at is '绝对过期时间';
comment on column coupon_tpl.relative_end_at is '相对过期时间(分钟)';
comment on column coupon_tpl.par_value is '面值';
comment on column coupon_tpl.publish_count is '发布数量';
comment on column coupon_tpl.received_count is '已领取数量';
comment on column coupon_tpl.used_count is '已使用数量';
comment on column coupon_tpl.status is '状态';



------------------------
-- config_aliyun_oss table
create table if not exists config_aliyun_oss (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    endpoint varchar,
    access_key varchar,
    secret varchar,
    bucket_name varchar,
    domain varchar
);

-- config_aliyun_oss comment
comment on column config_aliyun_oss.id is 'ID';
comment on column config_aliyun_oss.created_by is '创建时间';
comment on column config_aliyun_oss.created_at is '创建人';
comment on column config_aliyun_oss.updated_by is '更新人';
comment on column config_aliyun_oss.updated_at is '更新时间';
comment on column config_aliyun_oss.ver is '版本号';



------------------------
-- setting table
create table if not exists setting (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    key varchar not null,
    parent_key varchar,
    value text
);

-- setting comment
comment on column setting.id is 'ID';
comment on column setting.created_by is '创建时间';
comment on column setting.created_at is '创建人';
comment on column setting.updated_by is '更新人';
comment on column setting.updated_at is '更新时间';
comment on column setting.ver is '版本号';
comment on column setting.is_deleted is '是否删除 0:否 1:是';
comment on column setting.key is '配置键名';
comment on column setting.parent_key is '父级配置键名';
comment on column setting.value is '配置值';



------------------------
-- article_category_r table
create table if not exists article_category_r (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    article_id bigint not null,
    category varchar not null
);

-- article_category_r comment
comment on column article_category_r.id is 'ID';
comment on column article_category_r.created_by is '创建时间';
comment on column article_category_r.created_at is '创建人';
comment on column article_category_r.updated_by is '更新人';
comment on column article_category_r.updated_at is '更新时间';
comment on column article_category_r.ver is '版本号';
comment on column article_category_r.article_id is '文章ID';
comment on column article_category_r.category is '分类名称';



------------------------
-- feedback table
create table if not exists feedback (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    content text not null,
    user_id bigint not null,
    type smallint not null,
    mobile varchar,
    channel smallint not null,
    handled_at timestamp,
    remark text
);

-- feedback comment
comment on column feedback.id is 'ID';
comment on column feedback.created_by is '创建时间';
comment on column feedback.created_at is '创建人';
comment on column feedback.updated_by is '更新人';
comment on column feedback.updated_at is '更新时间';
comment on column feedback.ver is '版本号';
comment on column feedback.is_deleted is '是否删除 0:否 1:是';
comment on column feedback.content is '反馈内容';
comment on column feedback.user_id is '用户ID';
comment on column feedback.type is '反馈类型';
comment on column feedback.mobile is '联系电话';
comment on column feedback.channel is '反馈渠道';
comment on column feedback.handled_at is '处理时间';
comment on column feedback.remark is '备注';



------------------------
-- dict_tag_r table
create table if not exists dict_tag_r (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    dict_id bigint not null,
    tag varchar not null
);

-- dict_tag_r comment
comment on column dict_tag_r.id is 'ID';
comment on column dict_tag_r.created_by is '创建时间';
comment on column dict_tag_r.created_at is '创建人';
comment on column dict_tag_r.updated_by is '更新人';
comment on column dict_tag_r.updated_at is '更新时间';
comment on column dict_tag_r.ver is '版本号';
comment on column dict_tag_r.dict_id is '字典ID';
comment on column dict_tag_r.tag is '标签';



------------------------
-- config_park_api table
create table if not exists config_park_api (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    api_prefix varchar,
    api_key varchar
);

-- config_park_api comment
comment on column config_park_api.id is 'ID';
comment on column config_park_api.created_by is '创建时间';
comment on column config_park_api.created_at is '创建人';
comment on column config_park_api.updated_by is '更新人';
comment on column config_park_api.updated_at is '更新时间';
comment on column config_park_api.ver is '版本号';



------------------------
-- config_aliyun_openapi table
create table if not exists config_aliyun_openapi (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    endpoint varchar,
    access_key varchar,
    secret varchar
);

-- config_aliyun_openapi comment
comment on column config_aliyun_openapi.id is 'ID';
comment on column config_aliyun_openapi.created_by is '创建时间';
comment on column config_aliyun_openapi.created_at is '创建人';
comment on column config_aliyun_openapi.updated_by is '更新人';
comment on column config_aliyun_openapi.updated_at is '更新时间';
comment on column config_aliyun_openapi.ver is '版本号';



------------------------
-- file_info table
create table if not exists file_info (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    type smallint,
    name varchar,
    object text,
    url text not null,
    size bigint,
    field varchar,
    description text,
    outer_id bigint,
    seq integer default 0,
    width bigint,
    height bigint,
    duration bigint
);

-- file_info comment
comment on column file_info.id is 'ID';
comment on column file_info.created_by is '创建时间';
comment on column file_info.created_at is '创建人';
comment on column file_info.updated_by is '更新人';
comment on column file_info.updated_at is '更新时间';
comment on column file_info.ver is '版本号';
comment on column file_info.is_deleted is '是否删除 0:否 1:是';
comment on column file_info.type is '文件类型';
comment on column file_info.name is '文件名称';
comment on column file_info.object is '文件对象';
comment on column file_info.url is '文件URL';
comment on column file_info.size is '文件大小(字节)';
comment on column file_info.field is '关联字段名称';
comment on column file_info.description is '文件描述';
comment on column file_info.outer_id is '外部ID';
comment on column file_info.seq is '序号';
comment on column file_info.width is '图片宽度(像素)';
comment on column file_info.height is '图片高度(像素)';
comment on column file_info.duration is '视频时长(秒)';



------------------------
-- lc_order_modify table
create table if not exists lc_order_modify (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    lc_order_id bigint,
    old_bc_order_id varchar,
    new_bc_order_id varchar,
    old_plate varchar,
    new_plate varchar,
    modify_time timestamp
);

-- lc_order_modify comment
comment on column lc_order_modify.id is 'ID';
comment on column lc_order_modify.created_by is '创建时间';
comment on column lc_order_modify.created_at is '创建人';
comment on column lc_order_modify.updated_by is '更新人';
comment on column lc_order_modify.updated_at is '更新时间';
comment on column lc_order_modify.ver is '版本号';
comment on column lc_order_modify.lc_order_id is '停车订单号';
comment on column lc_order_modify.old_bc_order_id is '旧蓝卡订单号';
comment on column lc_order_modify.new_bc_order_id is '新蓝卡订单号';
comment on column lc_order_modify.old_plate is '旧车牌号';
comment on column lc_order_modify.new_plate is '新车牌号';
comment on column lc_order_modify.modify_time is '修改时间';



------------------------
-- client_user table
create table if not exists client_user (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    username varchar,
    nickname varchar,
    avatar varchar,
    password varchar,
    last_login_at timestamp,
    last_login_ip varchar,
    open_id varchar,
    union_id varchar,
    mobile varchar,
    email varchar,
    realname varchar,
    gender smallint default 0 not null,
    bound_broker_id bigint,
    fake smallint default 0 not null
);

-- client_user comment
comment on column client_user.id is 'ID';
comment on column client_user.created_by is '创建时间';
comment on column client_user.created_at is '创建人';
comment on column client_user.updated_by is '更新人';
comment on column client_user.updated_at is '更新时间';
comment on column client_user.ver is '版本号';
comment on column client_user.is_deleted is '是否删除 0:否 1:是';
comment on column client_user.username is '用户名';
comment on column client_user.nickname is '昵称';
comment on column client_user.avatar is '头像';
comment on column client_user.password is '密码';
comment on column client_user.last_login_at is '最后登录时间';
comment on column client_user.last_login_ip is '最后登录IP';
comment on column client_user.open_id is '微信openid';
comment on column client_user.union_id is '微信unionid';
comment on column client_user.mobile is '手机号';
comment on column client_user.email is '邮箱';
comment on column client_user.realname is '真实姓名';
comment on column client_user.gender is '性别';
comment on column client_user.bound_broker_id is '绑定的经纪人ID';
comment on column client_user.fake is '是否虚拟用户';



------------------------
-- dict table
create table if not exists dict (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    key varchar not null,
    name varchar not null,
    description varchar,
    status smallint default 1 not null
);

-- dict comment
comment on column dict.id is 'ID';
comment on column dict.created_by is '创建时间';
comment on column dict.created_at is '创建人';
comment on column dict.updated_by is '更新人';
comment on column dict.updated_at is '更新时间';
comment on column dict.ver is '版本号';
comment on column dict.is_deleted is '是否删除 0:否 1:是';
comment on column dict.key is '字典键';
comment on column dict.name is '字典名称';
comment on column dict.description is '字典描述';
comment on column dict.status is '状态';



------------------------
-- bpm_action table
create table if not exists bpm_action (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    node_id bigint not null,
    name varchar not null,
    code varchar,
    params text,
    type smallint not null,
    to_node_id bigint,
    seq integer default 99 not null
);

-- bpm_action comment
comment on column bpm_action.id is 'ID';
comment on column bpm_action.created_by is '创建时间';
comment on column bpm_action.created_at is '创建人';
comment on column bpm_action.updated_by is '更新人';
comment on column bpm_action.updated_at is '更新时间';
comment on column bpm_action.ver is '版本号';
comment on column bpm_action.is_deleted is '是否删除 0:否 1:是';
comment on column bpm_action.node_id is '节点ID';
comment on column bpm_action.name is '动作名称';
comment on column bpm_action.code is '动作编码';
comment on column bpm_action.params is '动作参数';
comment on column bpm_action.type is '动作类型';
comment on column bpm_action.to_node_id is '目标节点ID';
comment on column bpm_action.seq is '动作排序';



------------------------
-- lc_order_charge table
create table if not exists lc_order_charge (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    lc_order_id bigint,
    pay_no varchar,
    pay_charge varchar,
    pay_kind varchar,
    pay_channel varchar,
    transaction_id varchar,
    get_time timestamp,
    memo varchar
);

-- lc_order_charge comment
comment on column lc_order_charge.id is 'ID';
comment on column lc_order_charge.created_by is '创建时间';
comment on column lc_order_charge.created_at is '创建人';
comment on column lc_order_charge.updated_by is '更新人';
comment on column lc_order_charge.updated_at is '更新时间';
comment on column lc_order_charge.ver is '版本号';
comment on column lc_order_charge.lc_order_id is '订单号';
comment on column lc_order_charge.pay_no is '支付订单号';
comment on column lc_order_charge.pay_charge is '支付金额';
comment on column lc_order_charge.pay_kind is '支付类型';
comment on column lc_order_charge.pay_channel is '支付渠道';
comment on column lc_order_charge.transaction_id is '线上交易流水号，每笔交易生成唯一流水号（支付结果下发）';
comment on column lc_order_charge.get_time is '结算时间';
comment on column lc_order_charge.memo is '备注';



------------------------
-- lc_passage_way table
create table if not exists lc_passage_way (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    lc_park_id bigint,
    passage_way_id varchar,
    passage_way_name varchar,
    device_id varchar,
    match_class_id varchar,
    open_barrior_type_id varchar,
    source_area_id varchar,
    target_area_id varchar,
    park_number varchar
);

-- lc_passage_way comment
comment on column lc_passage_way.id is 'ID';
comment on column lc_passage_way.created_by is '创建时间';
comment on column lc_passage_way.created_at is '创建人';
comment on column lc_passage_way.updated_by is '更新人';
comment on column lc_passage_way.updated_at is '更新时间';
comment on column lc_passage_way.ver is '版本号';
comment on column lc_passage_way.is_deleted is '是否删除 0:否 1:是';
comment on column lc_passage_way.lc_park_id is '场库';
comment on column lc_passage_way.passage_way_id is '通道Id';
comment on column lc_passage_way.passage_way_name is '通道名称';
comment on column lc_passage_way.device_id is '设备号';
comment on column lc_passage_way.match_class_id is '匹配级别id';
comment on column lc_passage_way.open_barrior_type_id is '开闸类型';
comment on column lc_passage_way.source_area_id is '源区域Id';
comment on column lc_passage_way.target_area_id is '目标区域Id';
comment on column lc_passage_way.park_number is '停车场编号';



------------------------
-- shop_subscribe table
create table if not exists shop_subscribe (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    title varchar not null,
    filter varchar not null,
    admin_user_id bigint not null
);

-- shop_subscribe comment
comment on column shop_subscribe.id is 'ID';
comment on column shop_subscribe.created_by is '创建时间';
comment on column shop_subscribe.created_at is '创建人';
comment on column shop_subscribe.updated_by is '更新人';
comment on column shop_subscribe.updated_at is '更新时间';
comment on column shop_subscribe.ver is '版本号';
comment on column shop_subscribe.is_deleted is '是否删除 0:否 1:是';
comment on column shop_subscribe.title is '订阅标题';
comment on column shop_subscribe.filter is '订阅过滤条件';
comment on column shop_subscribe.admin_user_id is '订阅人ID(商家)';



------------------------
-- bpm_instance table
create table if not exists bpm_instance (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    process_id bigint not null,
    user_id bigint not null,
    submit_user_id bigint not null,
    status smallint not null,
    batch_id varchar,
    current_node_id bigint not null,
    data text,
    outer_id bigint not null,
    outer_type varchar not null,
    end_at timestamp
);

-- bpm_instance comment
comment on column bpm_instance.id is 'ID';
comment on column bpm_instance.created_by is '创建时间';
comment on column bpm_instance.created_at is '创建人';
comment on column bpm_instance.updated_by is '更新人';
comment on column bpm_instance.updated_at is '更新时间';
comment on column bpm_instance.ver is '版本号';
comment on column bpm_instance.is_deleted is '是否删除 0:否 1:是';
comment on column bpm_instance.process_id is '流程ID';
comment on column bpm_instance.user_id is '执行对象用户ID';
comment on column bpm_instance.submit_user_id is '提交人用户ID';
comment on column bpm_instance.status is '实例状态';
comment on column bpm_instance.batch_id is '批次ID';
comment on column bpm_instance.current_node_id is '当前节点ID';
comment on column bpm_instance.data is '业务数据';
comment on column bpm_instance.outer_id is '外部ID';
comment on column bpm_instance.outer_type is '业务类型';
comment on column bpm_instance.end_at is '完成时间';



------------------------
-- msg_category table
create table if not exists msg_category (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    code varchar not null,
    parent_id bigint default 0,
    title varchar not null,
    icon varchar,
    color varchar,
    description text,
    type smallint not null
);

-- msg_category comment
comment on column msg_category.id is 'ID';
comment on column msg_category.created_by is '创建时间';
comment on column msg_category.created_at is '创建人';
comment on column msg_category.updated_by is '更新人';
comment on column msg_category.updated_at is '更新时间';
comment on column msg_category.ver is '版本号';
comment on column msg_category.is_deleted is '是否删除 0:否 1:是';
comment on column msg_category.code is '分类编码';
comment on column msg_category.parent_id is '父级分类ID';
comment on column msg_category.title is '分类标题';
comment on column msg_category.icon is '分类图标';
comment on column msg_category.color is '分类颜色';
comment on column msg_category.description is '分类描述';
comment on column msg_category.type is '消息类型';



------------------------
-- bill table
create table if not exists bill (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    entity_id bigint not null,
    entity_model varchar,
    type smallint not null,
    user_id bigint not null,
    user_type smallint not null,
    user_model varchar,
    original_money_cent integer,
    coupon_discount_cent integer,
    point_discount_cent integer,
    order_money_cent integer,
    paid_money_cent integer,
    refunded_money_cent integer default 0,
    paid_at timestamp,
    pay_status smallint not null,
    pay_trxid varchar,
    pay_fee_cent integer,
    remark varchar,
    closed_at timestamp,
    closed_reason smallint,
    other_data varchar
);

-- bill comment
comment on column bill.id is 'ID';
comment on column bill.created_by is '创建时间';
comment on column bill.created_at is '创建人';
comment on column bill.updated_by is '更新人';
comment on column bill.updated_at is '更新时间';
comment on column bill.ver is '版本号';
comment on column bill.is_deleted is '是否删除 0:否 1:是';
comment on column bill.entity_id is '商品Id';
comment on column bill.entity_model is '商品模型';
comment on column bill.type is '商品类型';
comment on column bill.user_id is '用户Id';
comment on column bill.user_type is '用户类型';
comment on column bill.user_model is '用户模型';
comment on column bill.original_money_cent is '原价金额';
comment on column bill.coupon_discount_cent is '优惠券抵扣金额';
comment on column bill.point_discount_cent is '积分抵扣金额';
comment on column bill.order_money_cent is '订单金额';
comment on column bill.paid_money_cent is '实付金额';
comment on column bill.refunded_money_cent is '已退款金额';
comment on column bill.paid_at is '付款时间';
comment on column bill.pay_status is '支付状态';
comment on column bill.pay_trxid is '通联支付流水号';
comment on column bill.pay_fee_cent is '支付手续费';
comment on column bill.remark is '备注';
comment on column bill.closed_at is '关闭时间';
comment on column bill.closed_reason is '关闭原因';
comment on column bill.other_data is '其他数据';



------------------------
-- artwork table
create table if not exists artwork (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    title varchar not null,
    artist varchar,
    content text,
    size varchar,
    view_count integer default 0,
    status smallint not null,
    price integer
);

-- artwork comment
comment on column artwork.id is 'ID';
comment on column artwork.created_by is '创建时间';
comment on column artwork.created_at is '创建人';
comment on column artwork.updated_by is '更新人';
comment on column artwork.updated_at is '更新时间';
comment on column artwork.ver is '版本号';
comment on column artwork.is_deleted is '是否删除 0:否 1:是';
comment on column artwork.title is '标题';
comment on column artwork.artist is '艺术家';
comment on column artwork.content is '作品内容';
comment on column artwork.size is '作品尺寸';
comment on column artwork.view_count is '浏览次数';
comment on column artwork.status is '状态';
comment on column artwork.price is '价格';



------------------------
-- lc_area table
create table if not exists lc_area (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    lc_park_id bigint,
    park_number varchar,
    area_id varchar,
    area_name varchar,
    space_count integer,
    last_space_count integer,
    book_space_count integer,
    book_in_park_count integer
);

-- lc_area comment
comment on column lc_area.id is 'ID';
comment on column lc_area.created_by is '创建时间';
comment on column lc_area.created_at is '创建人';
comment on column lc_area.updated_by is '更新人';
comment on column lc_area.updated_at is '更新时间';
comment on column lc_area.ver is '版本号';
comment on column lc_area.is_deleted is '是否删除 0:否 1:是';
comment on column lc_area.lc_park_id is '场库';
comment on column lc_area.park_number is '场库编号';
comment on column lc_area.area_id is '区域Id';
comment on column lc_area.area_name is '区域名称';
comment on column lc_area.space_count is '区域车位数';
comment on column lc_area.last_space_count is '区域空位数';
comment on column lc_area.book_space_count is '场库可预约数';
comment on column lc_area.book_in_park_count is '场库在场预约数';



------------------------
-- article table
create table if not exists article (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    title varchar not null,
    intro varchar,
    cover varchar,
    cover_v varchar,
    web_view_url varchar,
    content text,
    view_count integer default 0 not null,
    favourite_count integer default 0 not null,
    is_recommend smallint default 0 not null,
    status smallint default 0 not null
);

-- article comment
comment on column article.id is 'ID';
comment on column article.created_by is '创建时间';
comment on column article.created_at is '创建人';
comment on column article.updated_by is '更新人';
comment on column article.updated_at is '更新时间';
comment on column article.ver is '版本号';
comment on column article.is_deleted is '是否删除 0:否 1:是';
comment on column article.title is '标题';
comment on column article.intro is '简介';
comment on column article.cover is '横向图片';
comment on column article.cover_v is '纵向图片';
comment on column article.web_view_url is '跳转H5链接';
comment on column article.content is '内容';
comment on column article.view_count is '浏览数';
comment on column article.favourite_count is '收藏数';
comment on column article.is_recommend is '0:否 1:是';
comment on column article.status is '0:否 1:是';



------------------------
-- client_user_credential table
create table if not exists client_user_credential (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    user_id bigint not null,
    realname varchar,
    id_card_num varchar,
    id_card_url1 varchar,
    id_card_url2 varchar
);

-- client_user_credential comment
comment on column client_user_credential.id is 'ID';
comment on column client_user_credential.created_by is '创建时间';
comment on column client_user_credential.created_at is '创建人';
comment on column client_user_credential.updated_by is '更新人';
comment on column client_user_credential.updated_at is '更新时间';
comment on column client_user_credential.ver is '版本号';
comment on column client_user_credential.is_deleted is '是否删除 0:否 1:是';
comment on column client_user_credential.user_id is '用户ID';
comment on column client_user_credential.realname is '真实姓名';
comment on column client_user_credential.id_card_num is '身份证号';
comment on column client_user_credential.id_card_url1 is '身份证正面照片';
comment on column client_user_credential.id_card_url2 is '身份证反面照片';



------------------------
-- config_allinpay table
create table if not exists config_allinpay (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    env varchar,
    notify_url varchar,
    appid varchar,
    cusid varchar,
    cert_data text,
    key_data text
);

-- config_allinpay comment
comment on column config_allinpay.id is 'ID';
comment on column config_allinpay.created_by is '创建时间';
comment on column config_allinpay.created_at is '创建人';
comment on column config_allinpay.updated_by is '更新人';
comment on column config_allinpay.updated_at is '更新时间';
comment on column config_allinpay.ver is '版本号';



------------------------
-- dict_option table
create table if not exists dict_option (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    dict_id bigint not null,
    parent_id bigint,
    key varchar not null,
    label varchar not null,
    value varchar not null,
    status smallint default 1 not null
);

-- dict_option comment
comment on column dict_option.id is 'ID';
comment on column dict_option.created_by is '创建时间';
comment on column dict_option.created_at is '创建人';
comment on column dict_option.updated_by is '更新人';
comment on column dict_option.updated_at is '更新时间';
comment on column dict_option.ver is '版本号';
comment on column dict_option.dict_id is '字典ID';
comment on column dict_option.parent_id is '父级ID';
comment on column dict_option.key is '选项键';
comment on column dict_option.label is '选项标签';
comment on column dict_option.value is '选项值';
comment on column dict_option.status is '状态';



------------------------
-- banner_info table
create table if not exists banner_info (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    title varchar not null,
    url varchar not null,
    place smallint,
    type smallint,
    link_type smallint,
    link_url varchar,
    status smallint default 1 not null,
    seq integer default 99 not null
);

-- banner_info comment
comment on column banner_info.id is 'ID';
comment on column banner_info.created_by is '创建时间';
comment on column banner_info.created_at is '创建人';
comment on column banner_info.updated_by is '更新人';
comment on column banner_info.updated_at is '更新时间';
comment on column banner_info.ver is '版本号';
comment on column banner_info.is_deleted is '是否删除 0:否 1:是';
comment on column banner_info.title is '标题';
comment on column banner_info.url is '图片URL';
comment on column banner_info.place is '位置';
comment on column banner_info.type is '类型';
comment on column banner_info.link_type is '链接类型';
comment on column banner_info.link_url is '跳转链接';
comment on column banner_info.status is '状态';
comment on column banner_info.seq is '排序';



------------------------
-- bpm_node table
create table if not exists bpm_node (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    process_id bigint not null,
    step_id bigint,
    name varchar not null,
    type smallint not null,
    code varchar,
    params text,
    role smallint,
    role_value varchar
);

-- bpm_node comment
comment on column bpm_node.id is 'ID';
comment on column bpm_node.created_by is '创建时间';
comment on column bpm_node.created_at is '创建人';
comment on column bpm_node.updated_by is '更新人';
comment on column bpm_node.updated_at is '更新时间';
comment on column bpm_node.ver is '版本号';
comment on column bpm_node.is_deleted is '是否删除 0:否 1:是';
comment on column bpm_node.process_id is '流程ID';
comment on column bpm_node.name is '节点名称';
comment on column bpm_node.type is '节点类型';
comment on column bpm_node.code is '节点编码';
comment on column bpm_node.params is '节点参数';



------------------------
-- msg_detail table
create table if not exists msg_detail (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    category_id bigint default 0 not null,
    title varchar not null,
    content text,
    type smallint not null,
    channel smallint not null,
    status smallint not null,
    read smallint default 0,
    send_user_id bigint not null,
    receive_user_id bigint not null,
    process_user_id bigint,
    expired_at timestamp,
    outer_type varchar,
    outer_id bigint,
    extra text
);

-- msg_detail comment
comment on column msg_detail.id is 'ID';
comment on column msg_detail.created_by is '创建时间';
comment on column msg_detail.created_at is '创建人';
comment on column msg_detail.updated_by is '更新人';
comment on column msg_detail.updated_at is '更新时间';
comment on column msg_detail.ver is '版本号';
comment on column msg_detail.is_deleted is '是否删除 0:否 1:是';
comment on column msg_detail.category_id is '消息类别ID';
comment on column msg_detail.title is '消息标题';
comment on column msg_detail.content is '消息内容';
comment on column msg_detail.type is '消息类型';
comment on column msg_detail.channel is '消息渠道';
comment on column msg_detail.status is '消息处理状态';
comment on column msg_detail.read is '是否已读';
comment on column msg_detail.send_user_id is '发送者用户ID';
comment on column msg_detail.receive_user_id is '接收者用户ID';
comment on column msg_detail.process_user_id is '处理者用户ID';
comment on column msg_detail.expired_at is '过期时间';
comment on column msg_detail.outer_type is '外部类型';
comment on column msg_detail.outer_id is '外部关联ID';
comment on column msg_detail.extra is '额外信息';



------------------------
-- tricycle_illegal_rule table
create table if not exists tricycle_illegal_rule (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    fine integer default 1 not null,
    title varchar not null,
    need_tow smallint default 0 not null,
    punish_delay_minute integer default 0 not null,
    status smallint default 0 not null
);

-- tricycle_illegal_rule comment
comment on column tricycle_illegal_rule.id is 'ID';
comment on column tricycle_illegal_rule.created_by is '创建时间';
comment on column tricycle_illegal_rule.created_at is '创建人';
comment on column tricycle_illegal_rule.updated_by is '更新人';
comment on column tricycle_illegal_rule.updated_at is '更新时间';
comment on column tricycle_illegal_rule.ver is '版本号';
comment on column tricycle_illegal_rule.is_deleted is '是否删除 0:否 1:是';
comment on column tricycle_illegal_rule.fine is '罚款金额(分)';
comment on column tricycle_illegal_rule.title is '规则标题';
comment on column tricycle_illegal_rule.need_tow is '是否需要拖车';
comment on column tricycle_illegal_rule.punish_delay_minute is '处罚延迟时间(分钟)';
comment on column tricycle_illegal_rule.status is '规则状态(0:禁用,1:启用)';



------------------------
-- admin_permission table
create table if not exists admin_permission (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    name varchar not null,
    code varchar not null,
    description varchar,
    type smallint
);

-- admin_permission comment
comment on column admin_permission.id is 'ID';
comment on column admin_permission.created_by is '创建时间';
comment on column admin_permission.created_at is '创建人';
comment on column admin_permission.updated_by is '更新人';
comment on column admin_permission.updated_at is '更新时间';
comment on column admin_permission.ver is '版本号';
comment on column admin_permission.is_deleted is '是否删除 0:否 1:是';
comment on column admin_permission.name is '权限名称';
comment on column admin_permission.code is '权限编码';
comment on column admin_permission.description is '权限描述';
comment on column admin_permission.type is '权限类型';



------------------------
-- admin_user table
create table if not exists admin_user (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    username varchar,
    nickname varchar,
    avatar varchar,
    password varchar,
    is_cert smallint,
    last_login_at timestamp,
    last_login_ip varchar,
    open_id varchar,
    union_id varchar,
    mobile varchar,
    email varchar,
    realname varchar,
    gender smallint default 0 not null,
    is_enabled smallint default 1 not null,
    fake smallint default 0 not null
);

-- admin_user comment
comment on column admin_user.id is 'ID';
comment on column admin_user.created_by is '创建时间';
comment on column admin_user.created_at is '创建人';
comment on column admin_user.updated_by is '更新人';
comment on column admin_user.updated_at is '更新时间';
comment on column admin_user.ver is '版本号';
comment on column admin_user.is_deleted is '是否删除 0:否 1:是';
comment on column admin_user.username is '用户名';
comment on column admin_user.nickname is '昵称';
comment on column admin_user.avatar is '头像';
comment on column admin_user.password is '密码';
comment on column admin_user.is_cert is '是否认证';
comment on column admin_user.last_login_at is '最后登录时间';
comment on column admin_user.last_login_ip is '最后登录IP';
comment on column admin_user.open_id is '微信OpenID';
comment on column admin_user.union_id is '微信UnionID';
comment on column admin_user.mobile is '手机号';
comment on column admin_user.email is '邮箱';
comment on column admin_user.realname is '真实姓名';
comment on column admin_user.gender is '性别';
comment on column admin_user.is_enabled is '是否启用';
comment on column admin_user.fake is '是否虚拟用户';



------------------------
-- shop_house_type_r table
create table if not exists shop_house_type_r (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    shop_id bigint not null,
    house_type varchar not null
);

-- shop_house_type_r comment
comment on column shop_house_type_r.id is 'ID';
comment on column shop_house_type_r.created_by is '创建时间';
comment on column shop_house_type_r.created_at is '创建人';
comment on column shop_house_type_r.updated_by is '更新人';
comment on column shop_house_type_r.updated_at is '更新时间';
comment on column shop_house_type_r.ver is '版本号';
comment on column shop_house_type_r.shop_id is '商铺ID';
comment on column shop_house_type_r.house_type is '户型';



------------------------
-- bpm_action_event table
create table if not exists bpm_action_event (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    event_code varchar not null,
    event_name varchar not null,
    params text,
    type smallint not null,
    seq integer not null,
    action_id bigint not null
);

-- bpm_action_event comment
comment on column bpm_action_event.id is 'ID';
comment on column bpm_action_event.created_by is '创建时间';
comment on column bpm_action_event.created_at is '创建人';
comment on column bpm_action_event.updated_by is '更新人';
comment on column bpm_action_event.updated_at is '更新时间';
comment on column bpm_action_event.ver is '版本号';
comment on column bpm_action_event.event_code is '事件编码';
comment on column bpm_action_event.event_name is '事件名称';
comment on column bpm_action_event.params is '事件参数';
comment on column bpm_action_event.type is '事件类型';
comment on column bpm_action_event.seq is '事件排序';
comment on column bpm_action_event.action_id is '动作ID';



------------------------
-- tricycle_batch table
create table if not exists tricycle_batch (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    name varchar,
    description varchar,
    remark varchar,
    count integer,
    completed_count integer
);

-- tricycle_batch comment
comment on column tricycle_batch.id is 'ID';
comment on column tricycle_batch.created_by is '创建时间';
comment on column tricycle_batch.created_at is '创建人';
comment on column tricycle_batch.updated_by is '更新人';
comment on column tricycle_batch.updated_at is '更新时间';
comment on column tricycle_batch.ver is '版本号';
comment on column tricycle_batch.is_deleted is '是否删除 0:否 1:是';
comment on column tricycle_batch.name is '批次名称';
comment on column tricycle_batch.description is '批次描述';
comment on column tricycle_batch.remark is '备注';
comment on column tricycle_batch.count is '创建数量';
comment on column tricycle_batch.completed_count is '完成数量';



------------------------
-- shop table
create table if not exists shop (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    cell_no varchar,
    decoration varchar,
    floor integer,
    orientation smallint,
    payment varchar,
    title varchar,
    cell_id bigint,
    cell_name varchar,
    cell_property smallint,
    cell_area numeric,
    building_area numeric,
    cell_status smallint,
    contract_start_date timestamp,
    contract_end_date timestamp,
    contract_no varchar,
    cell_remark varchar,
    attachment_names varchar,
    attachment_paths varchar,
    cost_name varchar,
    unit_price numeric,
    billing_cycle smallint,
    charge_amount numeric,
    terant_id varchar,
    tenant_name varchar,
    tenant_phone varchar,
    contact_person varchar,
    contact_phone varchar,
    tenant_type smallint,
    tenant_id bigint,
    tenant_id_card varchar,
    tenant_address varchar,
    business_license varchar,
    tenant_remark varchar,
    on_shelf smallint,
    on_shelf_at timestamp,
    view_count integer default 0 not null,
    favourite_count integer default 0 not null,
    broker_called_count integer default 0 not null,
    recommend integer default 0 not null,
    overall_rank integer,
    overall_rank_score integer,
    edited_at timestamp,
    share_image_url varchar,
    share_image_version varchar
);

-- shop comment
comment on column shop.id is 'ID';
comment on column shop.created_by is '创建时间';
comment on column shop.created_at is '创建人';
comment on column shop.updated_by is '更新人';
comment on column shop.updated_at is '更新时间';
comment on column shop.ver is '版本号';
comment on column shop.is_deleted is '是否删除 0:否 1:是';
comment on column shop.cell_no is '商铺号';
comment on column shop.decoration is '装修';
comment on column shop.floor is '楼层';
comment on column shop.orientation is '朝向 1-东；2-南；3-西；4-北；5-东南；6-西南；7-西北；8-东北';
comment on column shop.payment is '付款方式';
comment on column shop.title is '标题';
comment on column shop.cell_id is '单元ID';
comment on column shop.cell_name is '单元名称';
comment on column shop.cell_property is '房产性质 1-空置; 2-已租; 3-业主自营; 4-自用; 5-不租; 6-安置办公; 7-其它1; 8-其它2; 9-其它3';
comment on column shop.cell_area is '套内面积';
comment on column shop.building_area is '建筑面积';
comment on column shop.cell_status is '单元状态';
comment on column shop.contract_start_date is '合同开始日期';
comment on column shop.contract_end_date is '合同结束日期';
comment on column shop.contract_no is '合同编号';
comment on column shop.cell_remark is '单元备注';
comment on column shop.attachment_names is '附件名称';
comment on column shop.attachment_paths is '附件路径';
comment on column shop.cost_name is '费用名称';
comment on column shop.unit_price is '单价';
comment on column shop.billing_cycle is '付款方式 1-押三付三；2-押三付六；3-1年付；4-3年付；5-面议';
comment on column shop.charge_amount is '费用金额';
comment on column shop.terant_id is '租户ID';
comment on column shop.tenant_name is '租户名称';
comment on column shop.tenant_phone is '租户电话';
comment on column shop.contact_person is '联系人';
comment on column shop.contact_phone is '联系人电话';
comment on column shop.tenant_type is '租户类型 1-个人；2-公司';
comment on column shop.tenant_id is '租户ID';
comment on column shop.tenant_id_card is '租户身份证号';
comment on column shop.tenant_address is '租户地址';
comment on column shop.business_license is '营业执照号';
comment on column shop.tenant_remark is '租户备注';
comment on column shop.on_shelf is '是否上架';
comment on column shop.on_shelf_at is '上架时间';
comment on column shop.view_count is '浏览量';
comment on column shop.favourite_count is '收藏量';
comment on column shop.broker_called_count is '通话量，经纪人被拨打次数';
comment on column shop.recommend is '推荐值';
comment on column shop.overall_rank is '排名';
comment on column shop.overall_rank_score is '排名分数';
comment on column shop.edited_at is '编辑时间';
comment on column shop.share_image_url is '分享图片';
comment on column shop.share_image_version is '分享图片版本';



------------------------
-- coupon_blank_code_r table
create table if not exists coupon_blank_code_r (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    coupon_blank_id bigint not null,
    code varchar not null
);

-- coupon_blank_code_r comment
comment on column coupon_blank_code_r.id is 'ID';
comment on column coupon_blank_code_r.created_by is '创建时间';
comment on column coupon_blank_code_r.created_at is '创建人';
comment on column coupon_blank_code_r.updated_by is '更新人';
comment on column coupon_blank_code_r.updated_at is '更新时间';
comment on column coupon_blank_code_r.ver is '版本号';
comment on column coupon_blank_code_r.coupon_blank_id is '优惠券空白ID';
comment on column coupon_blank_code_r.code is '优惠券编码';



------------------------
-- lc_park table
create table if not exists lc_park (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    park_number varchar,
    park_name varchar,
    space_count integer,
    free_space_count integer,
    book_space_count integer,
    book_in_park_count integer
);

-- lc_park comment
comment on column lc_park.id is 'ID';
comment on column lc_park.created_by is '创建时间';
comment on column lc_park.created_at is '创建人';
comment on column lc_park.updated_by is '更新人';
comment on column lc_park.updated_at is '更新时间';
comment on column lc_park.ver is '版本号';
comment on column lc_park.is_deleted is '是否删除 0:否 1:是';
comment on column lc_park.park_number is '场库编号';
comment on column lc_park.park_name is '场库名称';
comment on column lc_park.space_count is '场库总车位数';
comment on column lc_park.free_space_count is '场库空车位数';
comment on column lc_park.book_space_count is '场库可预约数';
comment on column lc_park.book_in_park_count is '场库在场预约数';



------------------------
-- lc_order_profit table
create table if not exists lc_order_profit (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    lc_order_id bigint,
    profit_code varchar,
    profit_time varchar,
    shop_name varchar,
    profit_charge varchar,
    profit_charge_value varchar,
    get_time timestamp,
    memo varchar
);

-- lc_order_profit comment
comment on column lc_order_profit.id is 'ID';
comment on column lc_order_profit.created_by is '创建时间';
comment on column lc_order_profit.created_at is '创建人';
comment on column lc_order_profit.updated_by is '更新人';
comment on column lc_order_profit.updated_at is '更新时间';
comment on column lc_order_profit.ver is '版本号';
comment on column lc_order_profit.lc_order_id is '停车场';
comment on column lc_order_profit.profit_code is '优惠码';
comment on column lc_order_profit.profit_time is '优惠时间';
comment on column lc_order_profit.shop_name is '商户名称';
comment on column lc_order_profit.profit_charge is '优惠金额面值';
comment on column lc_order_profit.profit_charge_value is '生效金额';
comment on column lc_order_profit.get_time is '优惠下发时间';
comment on column lc_order_profit.memo is '备注';



------------------------
-- lc_plate table
create table if not exists lc_plate (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    client_user_id bigint,
    plate varchar,
    last_order_at timestamp
);

-- lc_plate comment
comment on column lc_plate.id is 'ID';
comment on column lc_plate.created_by is '创建时间';
comment on column lc_plate.created_at is '创建人';
comment on column lc_plate.updated_by is '更新人';
comment on column lc_plate.updated_at is '更新时间';
comment on column lc_plate.ver is '版本号';
comment on column lc_plate.is_deleted is '是否删除 0:否 1:是';
comment on column lc_plate.client_user_id is '用户ID';
comment on column lc_plate.plate is '车牌号';
comment on column lc_plate.last_order_at is '最后订单时间';



------------------------
-- vehicle table
create table if not exists vehicle (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    number varchar not null,
    user_id bigint not null,
    check_status smallint not null,
    monthly_card_id bigint,
    monthly_card_expire_at timestamp
);

-- vehicle comment
comment on column vehicle.id is 'ID';
comment on column vehicle.created_by is '创建时间';
comment on column vehicle.created_at is '创建人';
comment on column vehicle.updated_by is '更新人';
comment on column vehicle.updated_at is '更新时间';
comment on column vehicle.ver is '版本号';
comment on column vehicle.is_deleted is '是否删除 0:否 1:是';
comment on column vehicle.number is '机动车编号';
comment on column vehicle.user_id is '所属用户ID';
comment on column vehicle.check_status is '审核状态';
comment on column vehicle.monthly_card_id is '月卡ID';
comment on column vehicle.monthly_card_expire_at is '月卡到期时间';



------------------------
-- config_wx_miniapp table
create table if not exists config_wx_miniapp (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    env varchar,
    app_id varchar,
    app_secret varchar,
    mch_id varchar,
    mch_key varchar,
    notify_url varchar,
    cert_data bytea,
    key_data bytea
);

-- config_wx_miniapp comment
comment on column config_wx_miniapp.id is 'ID';
comment on column config_wx_miniapp.created_by is '创建时间';
comment on column config_wx_miniapp.created_at is '创建人';
comment on column config_wx_miniapp.updated_by is '更新人';
comment on column config_wx_miniapp.updated_at is '更新时间';
comment on column config_wx_miniapp.ver is '版本号';



------------------------
-- lc_device table
create table if not exists lc_device (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    device_id integer,
    name varchar,
    device_type_id varchar,
    lc_park_id bigint,
    park_number varchar,
    state varchar,
    ip varchar,
    last_time timestamp
);

-- lc_device comment
comment on column lc_device.id is 'ID';
comment on column lc_device.created_by is '创建时间';
comment on column lc_device.created_at is '创建人';
comment on column lc_device.updated_by is '更新人';
comment on column lc_device.updated_at is '更新时间';
comment on column lc_device.ver is '版本号';
comment on column lc_device.is_deleted is '是否删除 0:否 1:是';
comment on column lc_device.device_id is '设备编号';
comment on column lc_device.name is '通道名称';
comment on column lc_device.device_type_id is '设备类型';
comment on column lc_device.lc_park_id is '场库';
comment on column lc_device.park_number is '场库编号';
comment on column lc_device.state is '是否在线 1-在线；2-离线';
comment on column lc_device.ip is '设备IP';
comment on column lc_device.last_time is '上次在线时间';



------------------------
-- shop_business_type_r table
create table if not exists shop_business_type_r (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    shop_id bigint not null,
    business_type varchar not null
);

-- shop_business_type_r comment
comment on column shop_business_type_r.id is 'ID';
comment on column shop_business_type_r.created_by is '创建时间';
comment on column shop_business_type_r.created_at is '创建人';
comment on column shop_business_type_r.updated_by is '更新人';
comment on column shop_business_type_r.updated_at is '更新时间';
comment on column shop_business_type_r.ver is '版本号';
comment on column shop_business_type_r.shop_id is '商铺ID';
comment on column shop_business_type_r.business_type is '经营类型';



------------------------
-- shop_tag_r table
create table if not exists shop_tag_r (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    shop_id bigint not null,
    tag varchar not null
);

-- shop_tag_r comment
comment on column shop_tag_r.id is 'ID';
comment on column shop_tag_r.created_by is '创建时间';
comment on column shop_tag_r.created_at is '创建人';
comment on column shop_tag_r.updated_by is '更新人';
comment on column shop_tag_r.updated_at is '更新时间';
comment on column shop_tag_r.ver is '版本号';
comment on column shop_tag_r.shop_id is '商铺ID';
comment on column shop_tag_r.tag is '标签名称';



------------------------
-- vehicle_monthly_card_order table
create table if not exists vehicle_monthly_card_order (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    user_id bigint not null,
    vehicle_id bigint not null,
    monthly_card_id bigint not null,
    coupon_id bigint,
    original_price integer not null,
    actual_price integer not null,
    status smallint default 0 not null,
    effective_at timestamp,
    expire_at timestamp
);

-- vehicle_monthly_card_order comment
comment on column vehicle_monthly_card_order.id is 'ID';
comment on column vehicle_monthly_card_order.created_by is '创建时间';
comment on column vehicle_monthly_card_order.created_at is '创建人';
comment on column vehicle_monthly_card_order.updated_by is '更新人';
comment on column vehicle_monthly_card_order.updated_at is '更新时间';
comment on column vehicle_monthly_card_order.ver is '版本号';
comment on column vehicle_monthly_card_order.is_deleted is '是否删除 0:否 1:是';
comment on column vehicle_monthly_card_order.user_id is '用户ID';
comment on column vehicle_monthly_card_order.vehicle_id is '机动车ID';
comment on column vehicle_monthly_card_order.monthly_card_id is '月卡ID';
comment on column vehicle_monthly_card_order.coupon_id is '优惠码ID';
comment on column vehicle_monthly_card_order.original_price is '原价(分)';
comment on column vehicle_monthly_card_order.actual_price is '实付金额(分)';
comment on column vehicle_monthly_card_order.status is '订单状态(0:待支付,1:已支付,2:已取消)';
comment on column vehicle_monthly_card_order.effective_at is '生效时间';
comment on column vehicle_monthly_card_order.expire_at is '到期时间';



------------------------
-- bpm_node_event table
create table if not exists bpm_node_event (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    event_code varchar not null,
    event_name varchar not null,
    params text,
    type smallint not null,
    seq integer not null,
    node_id bigint not null,
    status smallint
);

-- bpm_node_event comment
comment on column bpm_node_event.id is 'ID';
comment on column bpm_node_event.created_by is '创建时间';
comment on column bpm_node_event.created_at is '创建人';
comment on column bpm_node_event.updated_by is '更新人';
comment on column bpm_node_event.updated_at is '更新时间';
comment on column bpm_node_event.ver is '版本号';
comment on column bpm_node_event.event_code is '事件编码';
comment on column bpm_node_event.event_name is '事件名称';
comment on column bpm_node_event.params is '事件参数';
comment on column bpm_node_event.type is '事件类型';
comment on column bpm_node_event.seq is '事件排序';
comment on column bpm_node_event.node_id is '节点ID';



------------------------
-- coupon_blank table
create table if not exists coupon_blank (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    count integer not null,
    coupon_tpl_id bigint not null,
    download_url varchar,
    belong_user_id bigint not null
);

-- coupon_blank comment
comment on column coupon_blank.id is 'ID';
comment on column coupon_blank.created_by is '创建时间';
comment on column coupon_blank.created_at is '创建人';
comment on column coupon_blank.updated_by is '更新人';
comment on column coupon_blank.updated_at is '更新时间';
comment on column coupon_blank.ver is '版本号';
comment on column coupon_blank.is_deleted is '是否删除 0:否 1:是';
comment on column coupon_blank.count is '生成数量';
comment on column coupon_blank.coupon_tpl_id is '优惠券模板ID';
comment on column coupon_blank.download_url is '下载链接';
comment on column coupon_blank.belong_user_id is '归属用户ID';



------------------------
-- admin_user_credential table
create table if not exists admin_user_credential (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    user_id bigint not null,
    realname varchar,
    id_card_num varchar,
    id_card_url1 varchar,
    id_card_url2 varchar
);

-- admin_user_credential comment
comment on column admin_user_credential.id is 'ID';
comment on column admin_user_credential.created_by is '创建时间';
comment on column admin_user_credential.created_at is '创建人';
comment on column admin_user_credential.updated_by is '更新人';
comment on column admin_user_credential.updated_at is '更新时间';
comment on column admin_user_credential.ver is '版本号';
comment on column admin_user_credential.is_deleted is '是否删除 0:否 1:是';
comment on column admin_user_credential.user_id is '用户ID';
comment on column admin_user_credential.realname is '真实姓名';
comment on column admin_user_credential.id_card_num is '身份证号';
comment on column admin_user_credential.id_card_url1 is '身份证正面照片';
comment on column admin_user_credential.id_card_url2 is '身份证反面照片';



------------------------
-- bpm_node_instance table
create table if not exists bpm_node_instance (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    instance_id bigint not null,
    status smallint not null,
    type smallint,
    role smallint,
    role_value varchar,
    node_id bigint not null,
    step_id bigint,
    prev_id bigint,
    next_id bigint,
    remark text,
    data text,
    operated_at timestamp,
    operated_user_id bigint,
    operated_action_id bigint,
    operated_reason text
);

-- bpm_node_instance comment
comment on column bpm_node_instance.id is 'ID';
comment on column bpm_node_instance.created_by is '创建时间';
comment on column bpm_node_instance.created_at is '创建人';
comment on column bpm_node_instance.updated_by is '更新人';
comment on column bpm_node_instance.updated_at is '更新时间';
comment on column bpm_node_instance.ver is '版本号';
comment on column bpm_node_instance.is_deleted is '是否删除 0:否 1:是';
comment on column bpm_node_instance.instance_id is '实例ID';
comment on column bpm_node_instance.status is '节点状态';
comment on column bpm_node_instance.node_id is '节点ID';
comment on column bpm_node_instance.data is '节点数据';



------------------------
-- bpm_process table
create table if not exists bpm_process (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    code varchar not null,
    name varchar not null
);

-- bpm_process comment
comment on column bpm_process.id is 'ID';
comment on column bpm_process.created_by is '创建时间';
comment on column bpm_process.created_at is '创建人';
comment on column bpm_process.updated_by is '更新人';
comment on column bpm_process.updated_at is '更新时间';
comment on column bpm_process.ver is '版本号';
comment on column bpm_process.is_deleted is '是否删除 0:否 1:是';
comment on column bpm_process.code is '流程编码';
comment on column bpm_process.name is '流程名称';



------------------------
-- admin_role table
create table if not exists admin_role (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    name varchar not null,
    code varchar not null,
    description varchar,
    type smallint
);

-- admin_role comment
comment on column admin_role.id is 'ID';
comment on column admin_role.created_by is '创建时间';
comment on column admin_role.created_at is '创建人';
comment on column admin_role.updated_by is '更新人';
comment on column admin_role.updated_at is '更新时间';
comment on column admin_role.ver is '版本号';
comment on column admin_role.is_deleted is '是否删除 0:否 1:是';
comment on column admin_role.name is '角色名称';
comment on column admin_role.code is '角色编码';
comment on column admin_role.description is '角色描述';
comment on column admin_role.type is '权限类型';



------------------------
-- coupon table
create table if not exists coupon (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    coupon_tpl_id bigint not null,
    code varchar not null,
    title varchar not null,
    user_id bigint,
    type smallint not null,
    scene smallint,
    par_value integer not null,
    end_at timestamp,
    used_at timestamp,
    source_user_id bigint,
    is_instant_gen smallint default 0 not null,
    order_id bigint,
    order_model varchar
);

-- coupon comment
comment on column coupon.id is 'ID';
comment on column coupon.created_by is '创建时间';
comment on column coupon.created_at is '创建人';
comment on column coupon.updated_by is '更新人';
comment on column coupon.updated_at is '更新时间';
comment on column coupon.ver is '版本号';
comment on column coupon.is_deleted is '是否删除 0:否 1:是';
comment on column coupon.coupon_tpl_id is '优惠券模板ID';
comment on column coupon.code is '优惠券编码';
comment on column coupon.title is '优惠券标题';
comment on column coupon.user_id is '用户ID';
comment on column coupon.type is '优惠券类型';
comment on column coupon.scene is '使用场景';
comment on column coupon.par_value is '面值';
comment on column coupon.end_at is '有效期结束时间';
comment on column coupon.used_at is '使用时间';
comment on column coupon.source_user_id is '来源用户ID';
comment on column coupon.is_instant_gen is '是否即时生成';
comment on column coupon.order_id is '订单ID';
comment on column coupon.order_model is '订单模型';



------------------------
-- admin_user_role_r table
create table if not exists admin_user_role_r (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    role varchar not null,
    user_id bigint not null
);

-- admin_user_role_r comment
comment on column admin_user_role_r.id is 'ID';
comment on column admin_user_role_r.created_by is '创建时间';
comment on column admin_user_role_r.created_at is '创建人';
comment on column admin_user_role_r.updated_by is '更新人';
comment on column admin_user_role_r.updated_at is '更新时间';
comment on column admin_user_role_r.ver is '版本号';
comment on column admin_user_role_r.role is '角色编码';
comment on column admin_user_role_r.user_id is '用户ID';



------------------------
-- favourite table
create table if not exists favourite (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    type smallint not null,
    title varchar not null,
    user_id bigint not null,
    user_model varchar not null,
    entity_id bigint not null,
    entity_model varchar not null
);

-- favourite comment
comment on column favourite.id is 'ID';
comment on column favourite.created_by is '创建时间';
comment on column favourite.created_at is '创建人';
comment on column favourite.updated_by is '更新人';
comment on column favourite.updated_at is '更新时间';
comment on column favourite.ver is '版本号';
comment on column favourite.is_deleted is '是否删除 0:否 1:是';
comment on column favourite.type is '收藏类型';
comment on column favourite.title is '收藏标题';
comment on column favourite.user_id is '用户ID';
comment on column favourite.user_model is '用户模型';
comment on column favourite.entity_id is '实体ID';
comment on column favourite.entity_model is '实体模型';



------------------------
-- tricycle_illegal table
create table if not exists tricycle_illegal (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    user_id bigint not null,
    tricycle_id bigint not null,
    tricycle_no varchar not null,
    status smallint not null,
    deposit_id bigint not null,
    rule_id bigint not null,
    rule_name varchar not null,
    fine integer not null,
    paid_at timestamp,
    end_at timestamp,
    punish_delay_minute integer,
    is_end smallint default 0 not null,
    need_tow smallint default 0 not null,
    punished_at timestamp,
    punished_user_id bigint,
    canceled_at timestamp,
    canceled_user_id bigint,
    passed_at timestamp,
    passed_user_id bigint
);

-- tricycle_illegal comment
comment on column tricycle_illegal.id is 'ID';
comment on column tricycle_illegal.created_by is '创建时间';
comment on column tricycle_illegal.created_at is '创建人';
comment on column tricycle_illegal.updated_by is '更新人';
comment on column tricycle_illegal.updated_at is '更新时间';
comment on column tricycle_illegal.ver is '版本号';
comment on column tricycle_illegal.is_deleted is '是否删除 0:否 1:是';
comment on column tricycle_illegal.user_id is '违规用户ID';
comment on column tricycle_illegal.tricycle_id is '三轮车ID';
comment on column tricycle_illegal.tricycle_no is '三轮车编号';
comment on column tricycle_illegal.status is '违规状态';
comment on column tricycle_illegal.deposit_id is '押金记录ID';
comment on column tricycle_illegal.rule_id is '违规规则ID';
comment on column tricycle_illegal.rule_name is '违规规则名称';
comment on column tricycle_illegal.fine is '罚款金额(分)';
comment on column tricycle_illegal.paid_at is '缴费时间';
comment on column tricycle_illegal.end_at is '违规结束时间';
comment on column tricycle_illegal.punish_delay_minute is '处罚延迟时间(分钟)';
comment on column tricycle_illegal.is_end is '是否已结束';
comment on column tricycle_illegal.need_tow is '是否需要拖车';
comment on column tricycle_illegal.punished_at is '处罚时间';
comment on column tricycle_illegal.punished_user_id is '处罚人ID';
comment on column tricycle_illegal.canceled_at is '取消时间';
comment on column tricycle_illegal.canceled_user_id is '取消人ID';
comment on column tricycle_illegal.passed_at is '通过时间';
comment on column tricycle_illegal.passed_user_id is '通过人ID';



------------------------
-- lc_park_count table
create table if not exists lc_park_count (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    lc_park_id bigint,
    in_park_count integer,
    space_count integer,
    free_space_count integer,
    minutes integer
);

-- lc_park_count comment
comment on column lc_park_count.id is 'ID';
comment on column lc_park_count.created_by is '创建时间';
comment on column lc_park_count.created_at is '创建人';
comment on column lc_park_count.updated_by is '更新人';
comment on column lc_park_count.updated_at is '更新时间';
comment on column lc_park_count.ver is '版本号';
comment on column lc_park_count.is_deleted is '是否删除 0:否 1:是';
comment on column lc_park_count.lc_park_id is '场库名称';
comment on column lc_park_count.in_park_count is '在场车辆数';
comment on column lc_park_count.space_count is '场库总车位数';
comment on column lc_park_count.free_space_count is '场库空车位数';
comment on column lc_park_count.minutes is '分钟时间戳';



------------------------
-- bill_refund table
create table if not exists bill_refund (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    bill_id bigint not null,
    money_cent integer not null,
    fee_cent integer,
    trxid varchar,
    operated_at timestamp,
    refunded_at timestamp,
    reason varchar
);

-- bill_refund comment
comment on column bill_refund.id is 'ID';
comment on column bill_refund.created_by is '创建时间';
comment on column bill_refund.created_at is '创建人';
comment on column bill_refund.updated_by is '更新人';
comment on column bill_refund.updated_at is '更新时间';
comment on column bill_refund.ver is '版本号';
comment on column bill_refund.bill_id is '账单ID';
comment on column bill_refund.money_cent is '退款金额';
comment on column bill_refund.fee_cent is '退款手续费';
comment on column bill_refund.trxid is '通联退款流水号';
comment on column bill_refund.operated_at is '退款操作时间';
comment on column bill_refund.refunded_at is '退款成功时间';
comment on column bill_refund.reason is '退款原因';



------------------------
-- tricycle_deposit table
create table if not exists tricycle_deposit (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    tricycle_id bigint not null,
    type smallint not null,
    balance integer not null,
    change integer not null,
    remark varchar,
    fake smallint default 0 not null
);

-- tricycle_deposit comment
comment on column tricycle_deposit.id is 'ID';
comment on column tricycle_deposit.created_by is '创建时间';
comment on column tricycle_deposit.created_at is '创建人';
comment on column tricycle_deposit.updated_by is '更新人';
comment on column tricycle_deposit.updated_at is '更新时间';
comment on column tricycle_deposit.ver is '版本号';
comment on column tricycle_deposit.is_deleted is '是否删除 0:否 1:是';
comment on column tricycle_deposit.tricycle_id is '三轮车ID';
comment on column tricycle_deposit.type is '押金变更类型';
comment on column tricycle_deposit.balance is '变更后余额(分)';
comment on column tricycle_deposit.change is '变更金额(分)';
comment on column tricycle_deposit.remark is '备注说明';
comment on column tricycle_deposit.fake is '是否虚拟记录';



------------------------
-- bpm_step table
create table if not exists bpm_step (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    process_id bigint not null,
    name varchar not null,
    code varchar,
    params text,
    seq integer not null
);

-- bpm_step comment
comment on column bpm_step.id is 'ID';
comment on column bpm_step.created_by is '创建时间';
comment on column bpm_step.created_at is '创建人';
comment on column bpm_step.updated_by is '更新人';
comment on column bpm_step.updated_at is '更新时间';
comment on column bpm_step.ver is '版本号';
comment on column bpm_step.is_deleted is '是否删除 0:否 1:是';
comment on column bpm_step.process_id is '流程ID';
comment on column bpm_step.name is '步骤名称';
comment on column bpm_step.code is '步骤编码';
comment on column bpm_step.params is '步骤参数';
comment on column bpm_step.seq is '序号';



------------------------
-- coupon_tpl_user_r table
create table if not exists coupon_tpl_user_r (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    coupon_tpl_id bigint not null,
    user_id bigint not null
);

-- coupon_tpl_user_r comment
comment on column coupon_tpl_user_r.id is 'ID';
comment on column coupon_tpl_user_r.created_by is '创建时间';
comment on column coupon_tpl_user_r.created_at is '创建人';
comment on column coupon_tpl_user_r.updated_by is '更新人';
comment on column coupon_tpl_user_r.updated_at is '更新时间';
comment on column coupon_tpl_user_r.ver is '版本号';
comment on column coupon_tpl_user_r.coupon_tpl_id is '优惠券模板ID';
comment on column coupon_tpl_user_r.user_id is '推送人ID';



------------------------
-- lc_order table
create table if not exists lc_order (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    bc_order_id varchar,
    plate varchar,
    ticket_code varchar,
    plate_color varchar,
    in_time timestamp,
    in_channel varchar,
    in_image varchar,
    visit_reason varchar,
    open_gate_mode varchar,
    match_mode varchar,
    confidence integer,
    car_type varchar,
    user_info_id_card varchar,
    user_info_user_name varchar,
    user_info_phone varchar,
    user_info_address varchar,
    lc_area_id bigint,
    barrior_open varchar,
    in_cost_time integer,
    space_count integer,
    image_list varchar,
    image_name varchar,
    lc_park_id bigint,
    park_number varchar,
    operator_id varchar,
    operator_name varchar,
    invoice_no varchar,
    out_time timestamp,
    out_image varchar,
    out_channel varchar,
    charge varchar,
    off_line_charge varchar,
    off_line_profit_charge_num varchar,
    off_line_profit_charge_value varchar,
    off_line_profit_time_num varchar,
    off_line_profit_time_value varchar,
    on_line_charge varchar,
    on_line_profit_charge_num varchar,
    on_line_profit_charge_value varchar,
    on_line_profit_time_num varchar,
    on_line_profit_time_value varchar,
    profit_charge_total varchar,
    out_cost_time varchar,
    paid smallint,
    client_user_id bigint,
    coupon_id bigint
);

-- lc_order comment
comment on column lc_order.id is 'ID';
comment on column lc_order.created_by is '创建时间';
comment on column lc_order.created_at is '创建人';
comment on column lc_order.updated_by is '更新人';
comment on column lc_order.updated_at is '更新时间';
comment on column lc_order.ver is '版本号';
comment on column lc_order.is_deleted is '是否删除 0:否 1:是';
comment on column lc_order.bc_order_id is '入场记录编号';
comment on column lc_order.plate is '车牌';
comment on column lc_order.ticket_code is '无牌车票号';
comment on column lc_order.plate_color is '车牌颜色';
comment on column lc_order.in_time is '入场时间';
comment on column lc_order.in_channel is '入场通道名称';
comment on column lc_order.in_image is '入场图片名';
comment on column lc_order.visit_reason is '访问事由';
comment on column lc_order.open_gate_mode is '放行类型: 自动抬杆/手动放行,无信息时填''';
comment on column lc_order.match_mode is '匹配模式: 全字匹配、近似匹配、人工匹配、人工纠正,无信息时填''';
comment on column lc_order.confidence is '车牌识别可信度';
comment on column lc_order.car_type is '车辆类型';
comment on column lc_order.user_info_id_card is '证件号码';
comment on column lc_order.user_info_user_name is '车主姓名';
comment on column lc_order.user_info_phone is '联系电话';
comment on column lc_order.user_info_address is '地址';
comment on column lc_order.lc_area_id is '车位信息';
comment on column lc_order.barrior_open is '是否开闸: “开闸”“未开闸”';
comment on column lc_order.in_cost_time is '入场开闸耗时: 压地感到抬杆时间';
comment on column lc_order.space_count is '空位数';
comment on column lc_order.image_list is '其他联动设备图片信息';
comment on column lc_order.image_name is '图片名';
comment on column lc_order.lc_park_id is '场库';
comment on column lc_order.park_number is '场库编号';
comment on column lc_order.operator_id is '操作员Id';
comment on column lc_order.operator_name is '操作员姓名';
comment on column lc_order.invoice_no is '发票号码';
comment on column lc_order.out_time is '出场时间';
comment on column lc_order.out_image is '出场图片名';
comment on column lc_order.out_channel is '出口通道名称';
comment on column lc_order.charge is '总停车费';
comment on column lc_order.off_line_charge is '线下总收费';
comment on column lc_order.off_line_profit_charge_num is '线下累计优惠金额总面值';
comment on column lc_order.off_line_profit_charge_value is '线下累计优惠金额总抵扣值';
comment on column lc_order.off_line_profit_time_num is '线下累计优惠时间';
comment on column lc_order.off_line_profit_time_value is '线下累计优惠时间总抵扣值';
comment on column lc_order.on_line_charge is '线上总收费';
comment on column lc_order.on_line_profit_charge_num is '线上累计优惠金额总面值';
comment on column lc_order.on_line_profit_charge_value is '线上累计优惠金额总抵扣值';
comment on column lc_order.on_line_profit_time_num is '线上累计优惠时间';
comment on column lc_order.on_line_profit_time_value is '线上累计优惠时间总抵扣值';
comment on column lc_order.profit_charge_total is '线上线下金额和时间优惠累计抵扣值';
comment on column lc_order.out_cost_time is '出场开闸耗时: 压地感到抬杆时间';
comment on column lc_order.paid is '已支付';
comment on column lc_order.client_user_id is '用户';
comment on column lc_order.coupon_id is '优惠券';



------------------------
-- msg_template table
create table if not exists msg_template (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    category_id bigint default 0 not null,
    code varchar not null,
    title varchar not null,
    content text,
    type smallint not null,
    channel smallint not null
);

-- msg_template comment
comment on column msg_template.id is 'ID';
comment on column msg_template.created_by is '创建时间';
comment on column msg_template.created_at is '创建人';
comment on column msg_template.updated_by is '更新人';
comment on column msg_template.updated_at is '更新时间';
comment on column msg_template.ver is '版本号';
comment on column msg_template.is_deleted is '是否删除 0:否 1:是';
comment on column msg_template.category_id is '消息分类ID';
comment on column msg_template.code is '模板编码';
comment on column msg_template.title is '模板标题';
comment on column msg_template.content is '模板内容';
comment on column msg_template.type is '消息类型';
comment on column msg_template.channel is '发送渠道';



------------------------
-- artwork_category_r table
create table if not exists artwork_category_r (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    artwork_id bigint not null,
    category varchar not null
);

-- artwork_category_r comment
comment on column artwork_category_r.id is 'ID';
comment on column artwork_category_r.created_by is '创建时间';
comment on column artwork_category_r.created_at is '创建人';
comment on column artwork_category_r.updated_by is '更新人';
comment on column artwork_category_r.updated_at is '更新时间';
comment on column artwork_category_r.ver is '版本号';
comment on column artwork_category_r.artwork_id is '作品ID';
comment on column artwork_category_r.category is '分类名称';



------------------------
-- admin_role_permission_r table
create table if not exists admin_role_permission_r (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    role varchar,
    permission varchar
);

-- admin_role_permission_r comment
comment on column admin_role_permission_r.id is 'ID';
comment on column admin_role_permission_r.created_by is '创建时间';
comment on column admin_role_permission_r.created_at is '创建人';
comment on column admin_role_permission_r.updated_by is '更新人';
comment on column admin_role_permission_r.updated_at is '更新时间';
comment on column admin_role_permission_r.ver is '版本号';



------------------------
-- tricycle table
create table if not exists tricycle (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    number varchar not null,
    user_id bigint,
    deposit integer default 0 not null,
    deposit_refunded_at timestamp,
    check_status smallint not null,
    batch_id bigint
);

-- tricycle comment
comment on column tricycle.id is 'ID';
comment on column tricycle.created_by is '创建时间';
comment on column tricycle.created_at is '创建人';
comment on column tricycle.updated_by is '更新人';
comment on column tricycle.updated_at is '更新时间';
comment on column tricycle.ver is '版本号';
comment on column tricycle.is_deleted is '是否删除 0:否 1:是';
comment on column tricycle.number is '三轮车编号';
comment on column tricycle.user_id is '所属用户ID';
comment on column tricycle.deposit is '押金金额(分)';
comment on column tricycle.deposit_refunded_at is '押金退还时间';
comment on column tricycle.check_status is '审核状态';
comment on column tricycle.batch_id is '批次ID';



------------------------
-- article_tag_r table
create table if not exists article_tag_r (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    article_id bigint not null,
    tag varchar not null
);

-- article_tag_r comment
comment on column article_tag_r.id is 'ID';
comment on column article_tag_r.created_by is '创建时间';
comment on column article_tag_r.created_at is '创建人';
comment on column article_tag_r.updated_by is '更新人';
comment on column article_tag_r.updated_at is '更新时间';
comment on column article_tag_r.ver is '版本号';
comment on column article_tag_r.article_id is '文章ID';
comment on column article_tag_r.tag is '标签名称';



