------------------------
-- modify article columns comment
comment on column article.like_count is '点赞数';
comment on column article.author is '作者';


------------------------
-- modify config_allinpay columns comment
comment on column config_allinpay.cert_data is '';
comment on column config_allinpay.key_data is '';


------------------------
-- modify bpm_node_instance columns comment
comment on column bpm_node_instance.operated_reason is '';
comment on column bpm_node_instance.remark is '';


------------------------
-- modify lc_order columns comment
comment on column lc_order.match_mode is '匹配模式: 全字匹配、近似匹配、人工匹配、人工纠正,无信息时填''';
comment on column lc_order.open_gate_mode is '放行类型: 自动抬杆/手动放行,无信息时填''';


------------------------
-- withdraw_account_record table
create table if not exists withdraw_account_record (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    user_id bigint,
    related_id bigint,
    type smallint,
    direction smallint,
    amount integer,
    before_balance integer,
    after_balance integer,
    remark varchar
);

-- withdraw_account_record comment
comment on column withdraw_account_record.id is 'ID';
comment on column withdraw_account_record.created_by is '创建时间';
comment on column withdraw_account_record.created_at is '创建人';
comment on column withdraw_account_record.updated_by is '更新人';
comment on column withdraw_account_record.updated_at is '更新时间';
comment on column withdraw_account_record.ver is '版本号';
comment on column withdraw_account_record.is_deleted is '是否删除 0:否 1:是';
comment on column withdraw_account_record.user_id is '用户ID';
comment on column withdraw_account_record.related_id is '关联记录ID';
comment on column withdraw_account_record.type is '记录类型';
comment on column withdraw_account_record.direction is '资金方向';
comment on column withdraw_account_record.amount is '变动金额（分）';
comment on column withdraw_account_record.before_balance is '变动前余额（分）';
comment on column withdraw_account_record.after_balance is '变动后余额（分）';
comment on column withdraw_account_record.remark is '备注';



------------------------
-- modify article columns comment
comment on column article.like_count is '点赞数';


------------------------
-- withdraw_account table
create table if not exists withdraw_account (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    user_id bigint,
    total_income integer,
    total_withdraw integer,
    total_fee integer,
    total_orders integer,
    available_amount integer,
    frozen_amount integer
);

-- withdraw_account comment
comment on column withdraw_account.id is 'ID';
comment on column withdraw_account.created_by is '创建时间';
comment on column withdraw_account.created_at is '创建人';
comment on column withdraw_account.updated_by is '更新人';
comment on column withdraw_account.updated_at is '更新时间';
comment on column withdraw_account.ver is '版本号';
comment on column withdraw_account.is_deleted is '是否删除 0:否 1:是';
comment on column withdraw_account.user_id is '用户ID';
comment on column withdraw_account.total_income is '总收益';
comment on column withdraw_account.total_withdraw is '总提现金额';
comment on column withdraw_account.total_fee is '总手续费';
comment on column withdraw_account.total_orders is '总订单数';
comment on column withdraw_account.available_amount is '可提现金额';
comment on column withdraw_account.frozen_amount is '冻结金额';



------------------------
-- withdraw_config table
create table if not exists withdraw_config (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    user_id bigint,
    name varchar,
    bank varchar,
    branch varchar,
    number varchar
);

-- withdraw_config comment
comment on column withdraw_config.id is 'ID';
comment on column withdraw_config.created_by is '创建时间';
comment on column withdraw_config.created_at is '创建人';
comment on column withdraw_config.updated_by is '更新人';
comment on column withdraw_config.updated_at is '更新时间';
comment on column withdraw_config.ver is '版本号';
comment on column withdraw_config.is_deleted is '是否删除 0:否 1:是';
comment on column withdraw_config.name is '账户姓名';
comment on column withdraw_config.bank is '开户行信息';
comment on column withdraw_config.branch is '支行信息';
comment on column withdraw_config.number is '账号';



------------------------
-- withdraw_record table
create table if not exists withdraw_record (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    user_id bigint,
    amount integer,
    name varchar,
    bank varchar,
    branch varchar,
    number varchar,
    status smallint,
    checked_at timestamp,
    remark varchar,
    check_admin_user_id bigint
);

-- withdraw_record comment
comment on column withdraw_record.id is 'ID';
comment on column withdraw_record.created_by is '创建时间';
comment on column withdraw_record.created_at is '创建人';
comment on column withdraw_record.updated_by is '更新人';
comment on column withdraw_record.updated_at is '更新时间';
comment on column withdraw_record.ver is '版本号';
comment on column withdraw_record.is_deleted is '是否删除 0:否 1:是';
comment on column withdraw_record.user_id is '用户ID';
comment on column withdraw_record.amount is '提现金额';
comment on column withdraw_record.name is '账户姓名';
comment on column withdraw_record.bank is '开户行信息';
comment on column withdraw_record.branch is '支行信息';
comment on column withdraw_record.number is '账号';
comment on column withdraw_record.status is '状态';
comment on column withdraw_record.checked_at is '审核时间';
comment on column withdraw_record.remark is '备注';
comment on column withdraw_record.check_admin_user_id is '审核人ID';



------------------------
-- add store columns
alter table if exists store add column if not exists fee_rate integer default 0;

------------------------
-- job_posting_tag_r table
create table if not exists job_posting_tag_r (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    job_posting_id bigint not null,
    tag varchar not null
);

-- job_posting_tag_r comment
comment on column job_posting_tag_r.id is 'ID';
comment on column job_posting_tag_r.created_by is '创建时间';
comment on column job_posting_tag_r.created_at is '创建人';
comment on column job_posting_tag_r.updated_by is '更新人';
comment on column job_posting_tag_r.updated_at is '更新时间';
comment on column job_posting_tag_r.ver is '版本号';
comment on column job_posting_tag_r.is_deleted is '是否删除 0:否 1:是';
comment on column job_posting_tag_r.job_posting_id is '用工发布ID';
comment on column job_posting_tag_r.tag is '标签';



------------------------
-- park_service_appointment table
create table if not exists park_service_appointment (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    service_id bigint not null,
    user_id bigint not null,
    phone varchar not null,
    status smallint not null,
    check_user_id bigint,
    remark varchar
);

-- park_service_appointment comment
comment on column park_service_appointment.id is 'ID';
comment on column park_service_appointment.created_by is '创建时间';
comment on column park_service_appointment.created_at is '创建人';
comment on column park_service_appointment.updated_by is '更新人';
comment on column park_service_appointment.updated_at is '更新时间';
comment on column park_service_appointment.ver is '版本号';
comment on column park_service_appointment.is_deleted is '是否删除 0:否 1:是';
comment on column park_service_appointment.service_id is '服务ID';
comment on column park_service_appointment.user_id is '用户ID';
comment on column park_service_appointment.phone is '手机号';
comment on column park_service_appointment.status is '状态';
comment on column park_service_appointment.remark is '备注';



------------------------
-- job_posting table
create table if not exists job_posting (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    user_id bigint not null,
    position varchar not null,
    salary integer not null,
    salary_type smallint not null,
    employer varchar not null,
    contact varchar not null,
    status smallint not null,
    check_user_id bigint,
    remark varchar,
    employment_type smallint not null,
    description varchar
);

-- job_posting comment
comment on column job_posting.id is 'ID';
comment on column job_posting.created_by is '创建时间';
comment on column job_posting.created_at is '创建人';
comment on column job_posting.updated_by is '更新人';
comment on column job_posting.updated_at is '更新时间';
comment on column job_posting.ver is '版本号';
comment on column job_posting.is_deleted is '是否删除 0:否 1:是';
comment on column job_posting.user_id is '用户ID';
comment on column job_posting.position is '岗位名称';
comment on column job_posting.salary is '待遇';
comment on column job_posting.salary_type is '待遇类型';
comment on column job_posting.employer is '招聘方';
comment on column job_posting.contact is '联系方式';
comment on column job_posting.status is '审核状态';
comment on column job_posting.remark is '审核备注';
comment on column job_posting.employment_type is '用工类型';
comment on column job_posting.description is '职位描述';



------------------------
-- park_service table
create table if not exists park_service (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    is_deleted smallint default 0 not null,
    name varchar not null,
    price_type smallint not null,
    price integer,
    description varchar
);

-- park_service comment
comment on column park_service.id is 'ID';
comment on column park_service.created_by is '创建时间';
comment on column park_service.created_at is '创建人';
comment on column park_service.updated_by is '更新人';
comment on column park_service.updated_at is '更新时间';
comment on column park_service.ver is '版本号';
comment on column park_service.is_deleted is '是否删除 0:否 1:是';
comment on column park_service.name is '服务名称';
comment on column park_service.price_type is '价格类型';
comment on column park_service.price is '价格';
comment on column park_service.description is '描述';



------------------------
-- modify store columns comment
comment on column store.fee_rate is '手续费率 ‰';


------------------------
-- add withdraw_account_record columns
alter table if exists withdraw_account_record add column if not exists fee integer;


------------------------
-- add withdraw_record columns
alter table if exists withdraw_record add column if not exists fee integer;


------------------------
-- modify withdraw_account_record columns comment
comment on column withdraw_account_record.fee is '变动手续费';


------------------------
-- add job_posting columns
alter table if exists job_posting add column if not exists show smallint;


------------------------
-- add park_service columns
alter table if exists park_service add column if not exists minutes integer;
alter table if exists park_service add column if not exists end_time varchar;
alter table if exists park_service add column if not exists start_time varchar;


------------------------
-- modify withdraw_record columns comment
comment on column withdraw_record.fee is '手续费';


------------------------
-- add park_service_appointment columns
alter table if exists park_service_appointment add column if not exists end_time timestamp;
alter table if exists park_service_appointment add column if not exists start_time timestamp;


------------------------
-- modify job_posting columns comment
comment on column job_posting.show is '是否显示';


------------------------
-- modify park_service columns comment
comment on column park_service.start_time is '开始时间';
comment on column park_service.minutes is '服务时长（分钟）';
comment on column park_service.end_time is '结束时间';


------------------------
-- add park_service columns
alter table if exists park_service add column if not exists days integer;


------------------------
-- park_service_times table
create table if not exists park_service_times (
    id bigint primary key,
    created_by bigint default 0 not null,
    created_at timestamp default CURRENT_TIMESTAMP not null,
    updated_by bigint default 0 not null,
    updated_at timestamp default CURRENT_TIMESTAMP not null,
    ver bigint default 0 not null,
    service_id bigint not null,
    start_time varchar,
    end_time varchar
);

-- park_service_times comment
comment on column park_service_times.id is 'ID';
comment on column park_service_times.created_by is '创建时间';
comment on column park_service_times.created_at is '创建人';
comment on column park_service_times.updated_by is '更新人';
comment on column park_service_times.updated_at is '更新时间';
comment on column park_service_times.ver is '版本号';
comment on column park_service_times.service_id is '服务ID';



------------------------
-- modify park_service columns comment
comment on column park_service.days is '可预约天数';



