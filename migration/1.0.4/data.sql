-- 创建一个空白的流程
-- code:自己起
-- name:自己起
INSERT INTO public.bpm_process (id, created_by, created_at, updated_by, updated_at, ver, is_deleted, code, name) VALUES (1312836347790233600, 0, '2024-12-01 09:43:27.097811', 0, '2024-12-01 09:43:27.097811', 0, 0, 'vehicleCheck', '机动车审核');
-- 步骤
-- process_id:上面插入的流程的id
-- seq:步骤的顺序
-- name:步骤的名称
-- code: 自己起（用不到）
-- params:步骤的参数 （用不到）
INSERT INTO public.bpm_step (id, created_by, created_at, updated_by, updated_at, ver, is_deleted, process_id, name, seq, code, params) VALUES (1312836347790233601, 0, '2024-12-01 09:44:37.845669', 0, '2024-12-01 09:44:37.845669', 0, 0, 1312836347790233600, '录入车辆', 1, null, null);
INSERT INTO public.bpm_step (id, created_by, created_at, updated_by, updated_at, ver, is_deleted, process_id, name, seq, code, params) VALUES (1312836347790233602, 0, '2024-12-01 09:44:37.845669', 0, '2024-12-01 09:44:37.845669', 0, 0, 1312836347790233600, '审核', 2, null, null);
INSERT INTO public.bpm_step (id, created_by, created_at, updated_by, updated_at, ver, is_deleted, process_id, name, seq, code, params) VALUES (1312836347790233603, 0, '2024-12-01 09:44:37.845669', 0, '2024-12-01 09:44:37.845669', 0, 0, 1312836347790233600, '交费', 3, null, null);
INSERT INTO public.bpm_step (id, created_by, created_at, updated_by, updated_at, ver, is_deleted, process_id, name, seq, code, params) VALUES (1312836347790233604, 0, '2024-12-01 09:44:37.845669', 0, '2024-12-01 09:44:37.845669', 0, 0, 1312836347790233600, '录入停车场', 4, null, null);
INSERT INTO public.bpm_step (id, created_by, created_at, updated_by, updated_at, ver, is_deleted, process_id, name, seq, code, params) VALUES (1312836347790233605, 0, '2024-12-01 09:44:37.845669', 0, '2024-12-01 09:44:37.845669', 0, 0, 1312836347790233600, '结束', 5, null, null);
-- 节点
-- process_id: 上面插入的流程的id
-- step_id:步骤的id
-- name:节点的名称
-- type:节点的类型 NodeTypeEnum(END,APPROVE,DELAY,CODE,) 0-结束 1-审批 2-延时 3-自定义??
-- role:节点的角色 NodeRoleEnum(SYSTEM,SELF,USER,ROLE,) 0-系统 1-自己 2-用户 3-角色·
-- role_value:节点的角色值
-- code:自己起名字
-- params:节点的参数
INSERT INTO public.bpm_node (id, created_by, created_at, updated_by, updated_at, ver, is_deleted, process_id, step_id, name, type, role, role_value, code, params) VALUES (1312836347790233606, 0, '2024-12-01 09:45:43.151050', 0, '2024-12-01 09:45:43.151050', 0, 0, 1312836347790233600, 1312836347790233601, '录入车辆', 1, 1, null, null, null);
INSERT INTO public.bpm_node (id, created_by, created_at, updated_by, updated_at, ver, is_deleted, process_id, step_id, name, type, role, role_value, code, params) VALUES (1312836347790233607, 0, '2024-12-01 09:45:43.151050', 0, '2024-12-01 09:45:43.151050', 0, 0, 1312836347790233600, 1312836347790233602, '审核', 1, 3, 'admin', null, null);
INSERT INTO public.bpm_node (id, created_by, created_at, updated_by, updated_at, ver, is_deleted, process_id, step_id, name, type, role, role_value, code, params) VALUES (1312836347790233608, 0, '2024-12-01 09:45:43.151050', 0, '2024-12-01 09:45:43.151050', 0, 0, 1312836347790233600, 1312836347790233603, '交费', 1, 1, null, null, null);
INSERT INTO public.bpm_node (id, created_by, created_at, updated_by, updated_at, ver, is_deleted, process_id, step_id, name, type, role, role_value, code, params) VALUES (1312836347790233609, 0, '2024-12-01 09:45:43.151050', 0, '2024-12-01 09:45:43.151050', 0, 0, 1312836347790233600, 1312836347790233604, '录入停车场', 1, 3, 'admin', null, null);
INSERT INTO public.bpm_node (id, created_by, created_at, updated_by, updated_at, ver, is_deleted, process_id, step_id, name, type, role, role_value, code, params) VALUES (1312836347790233610, 0, '2024-12-01 09:45:43.151050', 0, '2024-12-01 09:45:43.151050', 0, 0, 1312836347790233600, 1312836347790233605, '结束', 0, 0, null, null, null);
INSERT INTO public.bpm_node (id, created_by, created_at, updated_by, updated_at, ver, is_deleted, process_id, step_id, name, type, role, role_value, code, params) VALUES (1312836347790233611, 0, '2025-02-11 03:28:13.767375', 0, '2025-02-11 03:28:13.767375', 0, 0, 1312836347790233600, 1312836347790233605, '支付超时', 0, 0, null, null, null);
INSERT INTO public.bpm_node (id, process_id, step_id, name, type, role, role_value, code, params) VALUES (1312836347790233612, 1312836347790233600, 1312836347790233605, '拒绝', 0, 0, null, null, null);
-- 节点事件，生成某个节点后会触发的事件
-- event_code 自己起
-- event_name 自己起
-- params 事件的参数
-- type EventTypeEnum(NONE, PRE, POST, EXCEPTION, TIMEOUT, DELAY,) 0-无 1-前置 2-后置 3-异常 4-超时 5-延时
-- seq 事件的顺序 按从小到大执行??
-- node_id 节点的id
-- status NodeStatusEnum(WAITING, AUTO, APPROVED, FORWARDED, REJECTED, CANCELED, RECALLED, TIMEOUT,) 0-等待 1-自动 2-通过 3-转办 4-拒绝 5-取消 6-撤回 7-超时
INSERT INTO public.bpm_node_event (id, created_by, created_at, updated_by, updated_at, ver, event_code, event_name, params, type, seq, node_id, status) VALUES (1312837601945849862, 0, '2024-12-01 09:48:07.722918', 0, '2024-12-01 09:48:07.722918', 0, 'approvalDone', '待办完成', 'bpm', 2, 0, 1312836347790233610, 0);
INSERT INTO public.bpm_node_event (id, created_by, created_at, updated_by, updated_at, ver, event_code, event_name, params, type, seq, node_id, status) VALUES (1312837601945849860, 0, '2024-12-01 09:48:07.722918', 0, '2024-12-01 09:48:07.722918', 0, 'approvalDone', '待办完成', 'bpm', 2, 0, 1312836347790233609, 0);
INSERT INTO public.bpm_node_event (id, created_by, created_at, updated_by, updated_at, ver, event_code, event_name, params, type, seq, node_id, status) VALUES (1312837601945849856, 0, '2024-12-01 09:48:07.722918', 0, '2024-12-01 09:48:07.722918', 0, 'approvalDone', '待办完成', 'bpm', 2, 0, 1312836347790233607, 0);
INSERT INTO public.bpm_node_event (id, created_by, created_at, updated_by, updated_at, ver, event_code, event_name, params, type, seq, node_id, status) VALUES (1312837601941655552, 0, '2024-12-01 09:48:07.722918', 0, '2024-12-01 09:48:07.722918', 0, 'approvalDone', '待办完成', 'bpm', 2, 0, 1312836347790233606, 0);
INSERT INTO public.bpm_node_event (id, created_by, created_at, updated_by, updated_at, ver, event_code, event_name, params, type, seq, node_id, status) VALUES (1312837601945849857, 0, '2024-12-01 09:48:07.722918', 0, '2024-12-01 09:48:07.722918', 0, 'approvalMsg', '待办提醒', 'bpm', 1, 0, 1312836347790233607, 0);
INSERT INTO public.bpm_node_event (id, created_by, created_at, updated_by, updated_at, ver, event_code, event_name, params, type, seq, node_id, status) VALUES (1312837601941655553, 0, '2024-12-01 09:48:07.722918', 0, '2024-12-01 09:48:07.722918', 0, 'approvalMsg', '待办提醒', 'bpm', 1, 0, 1312836347790233606, 0);
INSERT INTO public.bpm_node_event (id, created_by, created_at, updated_by, updated_at, ver, event_code, event_name, params, type, seq, node_id, status) VALUES (1312837601945849861, 0, '2024-12-01 09:48:07.722918', 0, '2024-12-01 09:48:07.722918', 0, 'approvalMsg', '待办提醒', 'bpm', 1, 0, 1312836347790233609, 0);
INSERT INTO public.bpm_node_event (id, created_by, created_at, updated_by, updated_at, ver, event_code, event_name, params, type, seq, node_id, status) VALUES (1312837601945849863, 0, '2024-12-01 09:48:07.722918', 0, '2024-12-01 09:48:07.722918', 0, 'approvalMsg', '待办提醒', 'bpm', 1, 0, 1312836347790233610, 0);
INSERT INTO public.bpm_node_event (id, created_by, created_at, updated_by, updated_at, ver, event_code, event_name, params, type, seq, node_id, status) VALUES (1312837601945849859, 0, '2024-12-01 09:48:07.722918', 0, '2024-12-01 09:48:07.722918', 0, 'approvalMsg', '待办提醒', 'vehiclePay', 1, 0, 1312836347790233608, 0);
INSERT INTO public.bpm_node_event (id, created_by, created_at, updated_by, updated_at, ver, event_code, event_name, params, type, seq, node_id, status) VALUES (1312837601945849858, 0, '2024-12-01 09:48:07.722918', 0, '2024-12-01 09:48:07.722918', 0, 'approvalDone', '待办完成', 'vehiclePay', 2, 0, 1312836347790233608, 0);


-- 节点操作，节点可以执行的操作
-- type ActionTypeEnum(START, APPROVE, REJECT, RECALL, FORWARD, CANCEL, TIMEOUT) 0-开始 1-同意 2-打回 3-撤回 4-转交 5-取消 6-超时
-- code 同一个操作可以有多个执行，实际执行的哪个的判断条件。参考TricycleIllegalNotNeedTowHandle
-- name 自己起
-- to_node_id 操作完之后生成哪种节点
-- node_id 操作的当前节点
-- params 默认null
-- seq 操作的顺序
INSERT INTO public.bpm_action (id, created_by, created_at, updated_by, updated_at, ver, type, is_deleted, code, name, to_node_id, node_id, params, seq) VALUES (1312838005567918080, 0, '2024-12-01 09:51:36.042549', 0, '2024-12-01 09:51:36.042549', 0, 1, 0, null, '提交', 1312836347790233607, 1312836347790233606, null, 99);
INSERT INTO public.bpm_action (id, created_by, created_at, updated_by, updated_at, ver, type, is_deleted, code, name, to_node_id, node_id, params, seq) VALUES (1312838005567918081, 0, '2024-12-01 09:51:36.042549', 0, '2024-12-01 09:51:36.042549', 0, 5, 0, null, '取消', null, 1312836347790233606, null, 99);
INSERT INTO public.bpm_action (id, created_by, created_at, updated_by, updated_at, ver, type, is_deleted, code, name, to_node_id, node_id, params, seq) VALUES (1312838005567918082, 0, '2024-12-01 09:51:36.042549', 0, '2024-12-01 09:51:36.042549', 0, 1, 0, null, '通过', 1312836347790233608, 1312836347790233607, null, 99);
INSERT INTO public.bpm_action (id, created_by, created_at, updated_by, updated_at, ver, type, is_deleted, code, name, to_node_id, node_id, params, seq) VALUES (1312838005567918083, 0, '2024-12-01 09:51:36.042549', 0, '2024-12-01 09:51:36.042549', 0, 2, 0, null, '打回', 1312836347790233612, 1312836347790233607, null, 99);
INSERT INTO public.bpm_action (id, created_by, created_at, updated_by, updated_at, ver, type, is_deleted, code, name, to_node_id, node_id, params, seq) VALUES (1312838005567918084, 0, '2024-12-01 09:51:36.042549', 0, '2024-12-01 09:51:36.042549', 0, 1, 0, 'needPayMonthly', '缴费', 1312836347790233610, 1312836347790233608, null, 99);
INSERT INTO public.bpm_action (id, created_by, created_at, updated_by, updated_at, ver, type, is_deleted, code, name, to_node_id, node_id, params, seq) VALUES (1312838005567918085, 0, '2024-12-01 09:51:36.042549', 0, '2024-12-01 09:51:36.042549', 0, 5, 0, null, '取消', null, 1312836347790233608, null, 99);
INSERT INTO public.bpm_action (id, created_by, created_at, updated_by, updated_at, ver, type, is_deleted, code, name, to_node_id, node_id, params, seq) VALUES (1312838005567918086, 0, '2024-12-01 09:51:36.042549', 0, '2024-12-01 09:51:36.042549', 0, 1, 0, null, '录入完成', 1312836347790233610, 1312836347790233609, null, 99);
INSERT INTO public.bpm_action (id, created_by, created_at, updated_by, updated_at, ver, type, is_deleted, code, name, to_node_id, node_id, params, seq) VALUES (1312838005567918087, 0, '2025-02-11 03:26:50.224365', 0, '2025-02-11 03:26:50.224365', 0, 6, 0, null, '支付等待超时', 1312836347790233611, 1312836347790233608, null, 99);
-- 节点操作的事件，执行节点操作会触发的事件
-- action_id 操作的id
-- event_code 自己起
-- event_name 自己起
-- type EventTypeEnum(NONE, PRE, POST, EXCEPTION, TIMEOUT, DELAY,) 0-无 1-前置 2-后置 3-异常 4-超时 5-延时
-- seq 事件的顺序 按从小到大执行
-- params 默认null，自己起给handle传参，例如
INSERT INTO public.bpm_action_event (id, created_by, created_at, updated_by, updated_at, ver, action_id, event_code, event_name, type, seq, params) VALUES (1337069114853822464, 0, '2025-02-06 06:36:30.004248', 0, '2025-02-06 06:36:30.004248', 0, 1312838005567918082, 'vehicleStatusChange', '机动车状态更新', 1, 0, '6');
INSERT INTO public.bpm_action_event (id, created_by, created_at, updated_by, updated_at, ver, action_id, event_code, event_name, type, seq, params) VALUES (1337069114853822465, 0, '2025-02-06 06:36:30.004248', 0, '2025-02-06 06:36:30.004248', 0, 1312838005567918083, 'vehicleStatusChange', '机动车状态更新', 1, 0, '2');
INSERT INTO public.bpm_action_event (id, created_by, created_at, updated_by, updated_at, ver, action_id, event_code, event_name, type, seq, params) VALUES (1337069114853822466, 0, '2025-02-06 06:37:10.761932', 0, '2025-02-06 06:37:10.761932', 0, 1312838005567918084, 'vehicleStatusChange', '机动车状态更新', 1, 0, '1');
INSERT INTO public.bpm_action_event (id, created_by, created_at, updated_by, updated_at, ver, action_id, event_code, event_name, type, seq, params) VALUES (1337069114853822467, 0, '2025-02-07 03:07:45.167583', 0, '2025-02-07 03:07:45.167583', 0, 1312838005567918086, 'vehicleStatusChange', '机动车状态更新', 1, 0, '7,1');
INSERT INTO public.bpm_action_event (id, created_by, created_at, updated_by, updated_at, ver, action_id, event_code, event_name, type, seq, params) VALUES (1337069114853822468, 0, '2025-02-07 12:01:46.310374', 0, '2025-02-07 12:01:46.310374', 0, 1312838005567918085, 'vehicleStatusChange', '机动车状态更新', 1, 0, '3');
INSERT INTO public.bpm_action_event (id, created_by, created_at, updated_by, updated_at, ver, action_id, event_code, event_name, type, seq, params) VALUES (1337069114853822469, 0, '2025-02-07 12:01:46.310374', 0, '2025-02-07 12:01:46.310374', 0, 1312838005567918081, 'vehicleStatusChange', '机动车状态更新', 1, 0, '3');
INSERT INTO public.bpm_action_event (id, created_by, created_at, updated_by, updated_at, ver, action_id, event_code, event_name, type, seq, params) VALUES (1337069114853822568, 0, '2025-02-11 03:10:27.235787', 0, '2025-02-11 03:10:27.235787', 0, 1312838005567918087, 'vehicleTimeout', '机动车支付超时', 4, 0, null);
INSERT INTO public.bpm_action_event (id, created_by, created_at, updated_by, updated_at, ver, action_id, event_code, event_name, type, seq, params) VALUES (1337069114853822569, 0, '2025-02-11 03:43:05.307480', 0, '2025-02-11 03:43:05.307480', 0, 1312838005567918087, 'vehicleStatusChange', '机动车支付超时', 1, 1, '4');
