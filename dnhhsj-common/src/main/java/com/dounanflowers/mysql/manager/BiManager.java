package com.dounanflowers.mysql.manager;

import com.dounanflowers.common.entity.BiTradingCount;
import com.dounanflowers.common.enums.BiTradingCountTypeEnum;
import com.dounanflowers.common.repo.BiTradingCountRepo;
import com.mybatisflex.core.datasource.DataSourceKey;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Db;
import com.mybatisflex.core.row.Row;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Component
@RequiredArgsConstructor
public class BiManager {

    private final BiTradingCountRepo biTradingCountRepo;

    public List<Row> flowerPrice() {
        try {
            DataSourceKey.use("mysql");
            String startDate = LocalDate.now().minusDays(8).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            return Db.selectListByQuery("huaeb_quotation", QueryWrapper.create()
                    .select("trnDateString AS day", "classify", "AVG(avgPrice) AS avg")
                    .gt("trnDateString", startDate)
                    .groupBy("classify", "day")
                    .orderBy("day ASC", "avg DESC")
            );
        } finally {
            DataSourceKey.clear();
        }

    }

    public List<Row> truckCount() {
        //        SELECT TrnDateString, StopName, COUNT(TrnDateString) as count FROM `c_transbill` where TrnDateString = '20241207' GROUP BY StopName ORDER BY count desc;
        try {
            DataSourceKey.use("mysql");
            String today = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            return Db.selectListByQuery("c_transbill", QueryWrapper.create()
                    .select("TrnDateString", "StopName", "COUNT(TrnDateString) AS count")
                    .eq("TrnDateString", today)
                    .groupBy("StopName")
                    .orderBy("count DESC")
            );
        } finally {
            DataSourceKey.clear();
        }
    }

    public void saveBiTradingCount(BiTradingCount biTradingCount) {
        biTradingCountRepo.save(biTradingCount);
    }

    public BiTradingCount getBiTradingCountByDate(LocalDate date, BiTradingCountTypeEnum type) {
        return biTradingCountRepo.selectOneByQuery(QueryWrapper.create()
                .eq(BiTradingCount::getType, type)
                .eq(BiTradingCount::getDate, date)
                .orderBy(BiTradingCount::getCreatedAt).desc().limit(1)
        );
    }

    public BiTradingCount getLatestBiTradingCount(BiTradingCountTypeEnum type) {
        return biTradingCountRepo.selectOneByQuery(QueryWrapper.create()
                .select("SUM(order_count) AS order_count", "SUM(amount_count) AS amount_count")
                .eq(BiTradingCount::getType, type)
        );
    }

    public void deleteBiTradingCountByDate(LocalDate start, BiTradingCountTypeEnum type) {
        biTradingCountRepo.deleteByQuery(QueryWrapper.create()
                .eq(BiTradingCount::getType, type)
                .eq(BiTradingCount::getDate, start)
        );
    }
}
