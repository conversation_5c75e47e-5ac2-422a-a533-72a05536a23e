package com.dounanflowers.mysql.manager;

import com.dounanflowers.mysql.entity.HuaebQuotation;
import com.dounanflowers.mysql.repo.HuaebQuotationRepo;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class HuaebManager {

    private final HuaebQuotationRepo huaebQuotationRepo;

    public List<HuaebQuotation> fetchHuaebQuotationList() {
        return huaebQuotationRepo.selectListByQuery(QueryWrapper.create().limit(1));
    }

}
