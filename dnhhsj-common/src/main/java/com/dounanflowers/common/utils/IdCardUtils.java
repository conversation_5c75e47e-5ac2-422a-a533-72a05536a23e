package com.dounanflowers.common.utils;

public class IdCardUtils {

    public static boolean verifyIdCard(String idCard) {
        idCard = idCard.trim();
        if (!idCard.matches("(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)")) {
            return false;
        }
        idCard = idCard.trim();
        int[] idCardNums = new int[18];
        for (int i = 0; i < idCard.length(); i++) {
            char item = idCard.charAt(i);
            if (i == 17 && (item == 'x' || item == 'X')) {
                idCardNums[i] = 10;
            } else {
                idCardNums[i] = Character.getNumericValue(item);
            }
        } // Get the ID card number array

        // Weighted sum
        int[] weights = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1};
        int valideIndex = 0;
        for (int i = 0; i < 17; i++) {
            valideIndex += idCardNums[i] * weights[i];
        }
        valideIndex %= 11;

        int[] validValues = {1, 0, 10, 9, 8, 7, 6, 5, 4, 3, 2};
        return idCardNums[17] == validValues[valideIndex];
    }

}
