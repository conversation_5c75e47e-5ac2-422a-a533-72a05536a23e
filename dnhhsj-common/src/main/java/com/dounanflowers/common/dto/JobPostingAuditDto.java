package com.dounanflowers.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用工发布审核DTO")
public class JobPostingAuditDto extends EditIdDto {

    @Schema(description = "审核状态")
    private String status;

    @Schema(description = "备注（拒绝时必填）")
    private String remark;

}
