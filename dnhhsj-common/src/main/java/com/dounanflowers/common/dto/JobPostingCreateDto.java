package com.dounanflowers.common.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "用工发布创建DTO")
public class JobPostingCreateDto {

    @Schema(description = "岗位名称")
    private String position;

    @Schema(description = "待遇")
    private Integer salary;

    @Schema(description = "待遇类型")
    private String salaryType;

    @Schema(description = "标签")
    private List<String> tags;

    @Schema(description = "招聘方")
    private String employer;

    @Schema(description = "联系方式")
    private String contact;

    @Schema(description = "用工类型")
    private String employmentType;

    @Schema(description = "职位描述")
    private String description;
}
