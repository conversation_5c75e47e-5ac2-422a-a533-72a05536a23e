package com.dounanflowers.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ProductOrderForceRefundDto {
    @Schema(description = "订单id")
    Long orderId;

    @Schema(description = "退款金额")
    private Integer refundCent;

    @Schema(description = "退款原因")
    private String reason;

    @Schema(description = "退款备注")
    private String remark;
}
