package com.dounanflowers.common.dto;

import lombok.Data;

@Data
public class PayNotifyDto {
    /**
     * 用户账号。即微信openid
     */
    private String acct;
    /**
     * 借贷标识 00-借记卡 02-信用卡 99-其他（花呗/余额等）
     */
    private String accttype;
    /**
     * 收银宝APPID
     */
    private String appid;
    /**
     * 发卡行
     */
    private String bankcode;
    /**
     * 渠道号
     */
    private String chnlid;
    /**
     * 渠道流水号
     */
    private String chnltrxid;
    /**
     * 商户号
     */
    private String cmid;
    /**
     * 用户号
     */
    private String cusid;
    /**
     * 商户订单号
     */
    private String cusorderid;
    /**
     * 手续费
     */
    private String fee;

    private String feecycle;
    /**
     * 下单金额
     */
    private String initamt;
    /**
     * 商户订单号
     */
    private String outtrxid;
    /**
     * 交易完成时间
     */
    private String paytime;
    /**
     * 签名
     */
    private String sign;
    /**
     * 签名类型
     */
    private String signtype;
    /**
     * 终端授权码
     */
    private String termauthno;
    /**
     * 终端参考号
     */
    private String termrefnum;
    /**
     * 终端流水号
     */
    private String termtraceno;
    /**
     * 交易金额
     */
    private String trxamt;
    /**
     * 交易码
     */
    private String trxcode;
    /**
     * 交易日期
     */
    private String trxdate;
    /**
     * 通联流水号
     */
    private String trxid;
    /**
     * 保留字段
     */
    private String trxreserved;
    /**
     * 交易状态
     */
    private String trxstatus;
}
