package com.dounanflowers.common.dto;

import com.dounanflowers.common.enums.InvoiceHeaderTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 创建发票业务对象
 */
@Data
@Accessors(chain = true)
public class InvoiceApplyDto {

    /**
     * 抬头类型
     */
    private InvoiceHeaderTypeEnum headerType;

    /**
     * 抬头名称
     */
    private String headerName;

    /**
     * 税号
     */
    private String taxNumber;

    /**
     * 发票内容
     */
    private String content;

    /**
     * 备注
     */
    private String remark;

    /**
     * 关联的账单ID列表
     */
    private List<Long> billIds;

    /**
     * 用户ID
     */
    private Long userId;
}
