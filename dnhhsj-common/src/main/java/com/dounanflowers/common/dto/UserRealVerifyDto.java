package com.dounanflowers.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UserRealVerifyDto {

    @Schema(title = "真实姓名")
    private String realname;

    @Schema(title = "身份证号")
    private String idCardNum;

    @Schema(title = "身份证人像面")
    private String idCardUrl1;

    @Schema(title = "身份证国徽面")
    private String idCardUrl2;

    @Schema(hidden = true)
    private Boolean fake;

}

