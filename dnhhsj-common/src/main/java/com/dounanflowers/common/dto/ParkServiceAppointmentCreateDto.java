package com.dounanflowers.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "园区服务预约创建DTO")
public class ParkServiceAppointmentCreateDto {

    @Schema(description = "服务ID")
    private Long serviceId;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

}
