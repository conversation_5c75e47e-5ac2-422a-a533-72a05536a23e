package com.dounanflowers.common.dto;

import com.dounanflowers.common.enums.VehicleRefundStatusEnum;
import lombok.Data;

import java.util.List;

@Data
public class VehicleRefundCheckDto {
    /** 车辆id */
    private Long id;
    /** 退款金额：分 */
    private Integer refundCent;
    /** 审核状态 */
    private VehicleRefundStatusEnum refundStatus;
    /** 拒绝理由 */
    private String rejectReason;
    /** 备注 */
    private String remark;
    /** 图片 */
    private List<String> images;
}
