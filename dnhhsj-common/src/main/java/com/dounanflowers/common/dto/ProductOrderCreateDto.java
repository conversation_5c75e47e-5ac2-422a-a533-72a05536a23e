package com.dounanflowers.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "创建商品订单DTO")
public class ProductOrderCreateDto {
    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "购买数量")
    private Integer quantity;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "收货地址ID")
    private Long addressId;

    @Schema(description = "商品单价")
    private BigDecimal price;

}
