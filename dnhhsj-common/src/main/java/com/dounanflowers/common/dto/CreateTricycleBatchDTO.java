package com.dounanflowers.common.dto;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CreateTricycleBatchDTO {

    @NotBlank(message = "批次名称不能为空")
    private String name;

    private String description;

    @NotNull(message = "创建数量不能为空")
    @Min(value = 1, message = "创建数量必须大于0")
    private Integer count;

    private String remark;
}
