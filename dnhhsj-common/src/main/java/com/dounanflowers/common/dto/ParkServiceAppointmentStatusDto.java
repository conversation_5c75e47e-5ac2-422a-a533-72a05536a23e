package com.dounanflowers.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "园区服务预约状态更新DTO")
public class ParkServiceAppointmentStatusDto extends EditIdDto {

    @Schema(description = "状态")
    private String status;

    @Schema(description = "备注")
    private String remark;
}
