package com.dounanflowers.common.dto;

import com.dounanflowers.framework.bean.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商品评价分页请求DTO")
public class ProductReviewPageDto extends PageRequest {

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商铺ID")
    private Long storeId;

    @Schema(description = "评分", example = "5")
    private Integer rating;
}
