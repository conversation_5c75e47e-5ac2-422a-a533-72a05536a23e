package com.dounanflowers.common.dto;

import com.dounanflowers.framework.enums.IsEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用工发布置顶DTO")
public class JobPostingRecommendDto extends EditIdDto {

    @Schema(description = "推荐")
    private Integer recommend;

    @Schema(description = "是否官方岗位")
    private IsEnum isOfficial;

}
