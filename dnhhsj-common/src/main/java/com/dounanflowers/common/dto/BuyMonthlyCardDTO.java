package com.dounanflowers.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "购买月卡DTO")
public class BuyMonthlyCardDTO {

    @Schema(description = "月卡ID")
    private Long monthlyCardId;

    @Schema(description = "机动车ID")
    private Long vehicleId;

    @Schema(description = "优惠码")
    private String couponCode;

    @Schema(description = "优惠券ID")
    private Long couponId;
}
