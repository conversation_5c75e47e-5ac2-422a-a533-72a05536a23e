package com.dounanflowers.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "创建月卡DTO")
public class CreateMonthlyCardDTO {

    private Long id;

    @Schema(description = "月卡名称")
    private String name;

    @Schema(description = "月卡说明")
    private String description;

    @Schema(description = "有效时长(天)")
    private Integer duration;

    @Schema(description = "提醒天数")
    private Integer remindDays;

    @Schema(description = "价格(分)")
    private Integer price;
}
