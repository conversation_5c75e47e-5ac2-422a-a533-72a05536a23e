package com.dounanflowers.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class FeedbackCreateDto {

    @Schema(title = "类型")
    private String type;

    @Schema(title = "内容")
    private String content;

    @Schema(title = "图片")
    private List<String> images;

    @Schema(title = "用户输入手机号")
    private String mobile;

    @Schema(hidden = true)
    private Long userId;

}

