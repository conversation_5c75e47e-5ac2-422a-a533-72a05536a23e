package com.dounanflowers.common.dto;

import com.dounanflowers.common.enums.FavouriteTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class FavouriteToggleDto {

    @Schema(title = "类型", description = "1-商铺(Shop); 2-攻略(Article); 3-景点动态(Article)")
    private FavouriteTypeEnum type;

    @Schema(title = "实体Id")
    private Long entityId;

    @Schema(title = "标题", description = "保存标题用于搜索")
    private String title;

    @Schema(title = "强制操作", description = "字段不传时，自动切换收藏状态，否则根据此字段bool值添加收藏或取消收藏")
    private Boolean forceAction;

}

