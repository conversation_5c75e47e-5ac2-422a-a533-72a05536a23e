package com.dounanflowers.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ProductRefundApplyDto {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long orderId;

//    @Schema(description = "订单项ID")
//    private Long orderItemId;
//
//    @Schema(description = "退款金额")
//    private Integer refundAmount;

    @Schema(description = "退款原因")
    private String reason;

    @Schema(description = "退款描述")
    private String description;

    @Schema(description = "图片")
    private List<String> images;

}
