package com.dounanflowers.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "直接购买DTO")
public class DirectPurchaseDto {

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "购买数量")
    private Integer quantity;

    @Schema(description = "备注")
    private String remark;

    @Schema(hidden = true)
    private Boolean fake;
}
