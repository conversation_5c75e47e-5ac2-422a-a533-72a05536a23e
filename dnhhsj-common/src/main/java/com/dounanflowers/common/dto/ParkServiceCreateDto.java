package com.dounanflowers.common.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "园区服务创建DTO")
public class ParkServiceCreateDto {

    @Schema(description = "服务名称")
    private String name;

    @Schema(description = "价格类型")
    private String priceType;

    @Schema(description = "价格")
    private Integer price;

    @Schema(description = "图片列表")
    private List<String> images;

    @Schema(description = "描述")
    private String description;
}
