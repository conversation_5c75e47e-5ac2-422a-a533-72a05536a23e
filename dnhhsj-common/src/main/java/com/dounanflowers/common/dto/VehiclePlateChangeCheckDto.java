package com.dounanflowers.common.dto;

import com.dounanflowers.common.enums.VehiclePlateChangeStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class VehiclePlateChangeCheckDto {
    private Long vehiclePlateChangeId;

    private VehiclePlateChangeStatusEnum changeStatus;

    private String rejectReason;

    private String remark;

    @Schema(hidden = true)
    private boolean force;
}
