package com.dounanflowers.common.dto;

import com.dounanflowers.common.enums.InvoiceStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 更新发票业务对象
 */
@Data
public class InvoiceProcessDto {

    @Schema(title = "_id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(title = "开票状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private InvoiceStatusEnum status;

    @Schema(title = "拒绝理由", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String rejectReason;

    @Schema(title = "备注", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String remark;

    @Schema(title = "下载地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String downloadUrl;
}
