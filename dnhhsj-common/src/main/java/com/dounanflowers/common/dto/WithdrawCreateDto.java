package com.dounanflowers.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "创建提现记录DTO")
public class WithdrawCreateDto {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "提现金额（分）")
    private Integer amount;

    @Schema(description = "账户姓名")
    private String name;

    @Schema(description = "开户行信息")
    private String bank;

    @Schema(description = "支行信息")
    private String branch;

    @Schema(description = "账号")
    private String number;

    @Schema(description = "是否保存为默认提现配置")
    private Boolean saveAsConfig;

}
