package com.dounanflowers.common.dto;

import com.dounanflowers.common.enums.UserTypeEnum;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class CouponReceiveDto {

    private Long couponTplId;

    private List<Long> userIds = Lists.newArrayList();

    private Integer blankCount = 0;

    private Long sourceUserId;

    private boolean instantGen;

    private boolean test;

    private UserTypeEnum userType = UserTypeEnum.CLIENT;

}
