package com.dounanflowers.common.dto;

import lombok.Data;

@Data
public class HandleStatusBo {
    private Boolean success;

    public static HandleStatusBo success() {
        HandleStatusBo bo = new HandleStatusBo();
        bo.setSuccess(true);
        return bo;
    }

    public static HandleStatusBo fail() {
        HandleStatusBo bo = new HandleStatusBo();
        bo.setSuccess(false);
        return bo;
    }
}
