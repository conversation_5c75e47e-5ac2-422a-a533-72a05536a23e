package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.Artwork;
import com.dounanflowers.common.entity.ArtworkCategory;
import com.dounanflowers.common.repo.ArtworkCategoryRepo;
import com.dounanflowers.common.repo.ArtworkRepo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ArtworkManager {

    private final ArtworkRepo artworkRepo;

    private final ArtworkCategoryRepo artworkCategoryRepo;

    public Page<Artwork> pageList(PageRequest dto) {
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("categoryId".equals(filter.getField())) {
                    QueryWrapper subWrapper = filter.wrapper(QueryWrapper.create().select("artwork_id").from(ArtworkCategory.class), "category");
                    filter.setField("id");
                    filter.setType("sub");
                    dto.addSubQuery("id", w -> w.in("id", subWrapper));
                }
            }
        }
        return artworkRepo.selectPageWithRelations(dto);
    }

    public void save(Artwork artwork) {
        if (artwork.getId() == null) {
            artwork.setViewCount(0);
        }
        artworkRepo.save(artwork);
        if (artwork.getCategories() != null) {
            artworkCategoryRepo.deleteByQuery(QueryWrapper.create().eq("artwork_id", artwork.getId()));
            for (String category : artwork.getCategories()) {
                ArtworkCategory artworkCategory = new ArtworkCategory();
                artworkCategory.setArtworkId(artwork.getId());
                artworkCategory.setCategory(category);
                artworkCategoryRepo.save(artworkCategory);
            }
        }
    }

    public Artwork fetchById(Long id) {
        return artworkRepo.selectOneWithRelationsById(id);
    }

    public void deleteById(Long id) {
        artworkRepo.deleteById(id);
        artworkCategoryRepo.deleteByQuery(QueryWrapper.create().eq("artwork_id", id));
    }

    public Artwork fetchByTitle(String title) {
        return artworkRepo.selectOneWithRelationsByQuery(QueryWrapper.create().eq("title", title));
    }

}
