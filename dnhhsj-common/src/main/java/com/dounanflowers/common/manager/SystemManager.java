package com.dounanflowers.common.manager;


import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.BannerPlaceEnum;
import com.dounanflowers.common.enums.FeedbackTypeEnum;
import com.dounanflowers.common.enums.FileTypeEnum;
import com.dounanflowers.common.repo.*;
import com.dounanflowers.framework.annotation.Cached;
import com.dounanflowers.framework.annotation.CachedEvict;
import com.dounanflowers.framework.annotation.CachedEvicts;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.utils.JsonUtils;
import com.google.common.collect.Maps;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

@Component
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class SystemManager {

    private final SettingRepo settingRepo;

    private final DictRepo dictRepo;

    private final DictOptionRepo dictOptionRepo;

    private final DictTagRepo dictTagRepo;

    private final FileRepo fileRepo;

    private final BannerInfoRepo bannerInfoRepo;

    private final FeedbackRepo feedbackRepo;



    @Cached(value = "setting")
    public Map<String, Map<String, Object>> fetchSettingMap() {
        List<Setting> settings = settingRepo.selectAll();
        Map<String, Map<String, Object>> settingMap = Maps.newHashMap();
        for (Setting setting : settings) {
            Map<String, Object> oneMap = settingMap.computeIfAbsent(setting.getParentKey(), k -> Maps.newHashMap());
            oneMap.put(setting.getKey(), JsonUtils.parse(setting.getValue()));
        }
        return settingMap;
    }

    public Setting fetchSetting(String parentKey, String key) {
        return settingRepo.selectOneByQuery(QueryWrapper.create().eq(Setting::getParentKey, parentKey).eq(Setting::getKey, key));
    }

    @CachedEvict(value = "setting")
    public void saveSetting(Setting setting) {
        settingRepo.save(setting);
    }

    @Cached(value = "dict:tags")
    public List<String> fetchDictTags() {
        return dictTagRepo.selectListByQueryAs(QueryWrapper.create().select("distinct tag"), String.class);
    }

    public Page<Dict> fetchDictPageList(PageRequest dto) {
        for (PageFilter pageFilter : dto.getFilter()) {
            if ("tags".equals(pageFilter.getField())) {
                QueryWrapper subWrapper = pageFilter.wrapper(QueryWrapper.create().select("dict_id").from(DictTag.class), "tag");
                pageFilter.setField("id");
                pageFilter.setType("sub");
                dto.addSubQuery("id", w -> w.in("id", subWrapper));
            }
        }
        return dictRepo.selectPageWithRelations(dto);
    }

    @CachedEvicts({
            @CachedEvict(value = "dict"),
            @CachedEvict(value = "dict:tags")
    })
    public void deleteDictById(Long id) {
        dictRepo.deleteById(id);
        dictTagRepo.deleteByQuery(QueryWrapper.create().eq(DictTag::getDictId, id));
        dictOptionRepo.deleteByQuery(QueryWrapper.create().eq(DictOption::getDictId, id));
    }

    @Cached(value = "dict")
    public List<Dict> fetchAllDictMap() {
        return dictRepo.selectAllWithRelations();
    }

    @CachedEvicts({
            @CachedEvict(value = "dict"),
            @CachedEvict(value = "dict:tags"),
    })
    public void saveDict(Dict dict) {
        dictRepo.save(dict);
        dictTagRepo.deleteByQuery(QueryWrapper.create().eq(DictTag::getDictId, dict.getId()));
        for (String tag : dict.getTags()) {
            DictTag dictTag = new DictTag();
            dictTag.setDictId(dict.getId());
            dictTag.setTag(tag);
            dictTagRepo.save(dictTag);
        }
        dictOptionRepo.deleteByQuery(QueryWrapper.create().eq(DictOption::getDictId, dict.getId()));
        for (DictOption option : dict.getOptions()) {
            option.setDictId(dict.getId());
            dictOptionRepo.insert(option);
        }
    }

    public void save(FileInfo FileInfo) {
        fileRepo.save(FileInfo);
    }

    public List<FileInfo> getTypeFileByOuterId(FileTypeEnum type, Long outerId) {
        return fileRepo.selectListByQuery(QueryWrapper.create().eq(FileInfo::getOuterId, outerId).eq(FileInfo::getType, type).orderBy(FileInfo::getSeq).asc());
    }

    public FileInfo findByUrl(String url) {
        return fileRepo.selectOneByQuery(QueryWrapper.create().eq(FileInfo::getUrl, url));
    }

    public List<FileInfo> findByUrls(List<String> urls) {
        if (urls == null || urls.isEmpty()) {
            return List.of();
        }
        return fileRepo.selectListByQuery(QueryWrapper.create().in(FileInfo::getUrl, urls));
    }

    public void deleteById(Long id) {
        fileRepo.deleteById(id);
    }

    public FileInfo findById(Long id) {
        return fileRepo.selectOneById(id);
    }

    public List<FileInfo> list(Long outerId) {
        return fileRepo.selectListByQuery(QueryWrapper.create().eq(FileInfo::getOuterId, outerId).orderBy(FileInfo::getSeq).asc());
    }

    public Page<FileInfo> filePageList(PageRequest pageRequest) {
        return fileRepo.selectPageByQuery(pageRequest);
    }

    public List<BannerInfo> fetchBannerShowList(BannerPlaceEnum place) {
        return bannerInfoRepo.selectListByQuery(QueryWrapper.create().eq(BannerInfo::getPlace, place).eq(BannerInfo::getStatus, IsEnum.TRUE).orderBy(BannerInfo::getSeq).asc());
    }

    public void deleteBanner(Long id) {
        bannerInfoRepo.deleteById(id);
    }

    public void saveBanner(BannerInfo bannerInfo) {
        bannerInfoRepo.save(bannerInfo);
    }

    public Page<BannerInfo> fetchBannerPage(PageRequest pageRequest) {
        return bannerInfoRepo.selectPageByQuery(pageRequest);
    }

    public List<FileInfo> fetchFileList(List<String> images) {
        if (CollectionUtils.isEmpty(images)) {
            return List.of();
        }
        return fileRepo.selectListByQuery(QueryWrapper.create().in(FileInfo::getUrl, images));
    }

    public void saveFile(FileInfo file) {
        fileRepo.save(file);
    }

    public void saveFeedback(Feedback feedback) {
        feedbackRepo.save(feedback);
    }

    public Feedback fetchFeedbackById(Long id) {
        return feedbackRepo.selectOneWithRelationsById(id);
    }

    public Page<Feedback> fetchFeedbackPage(PageRequest pageRequest) {
        for (PageFilter pageFilter : pageRequest.getFilter()) {
            if ("type".equals(pageFilter.getField())) {
                pageFilter.setValue(BaseEnum.ordinalOf(FeedbackTypeEnum.class, pageFilter.getValue().toString()));
            }
        }
        return feedbackRepo.selectPageWithRelations(pageRequest);
    }

    public List<FileInfo> fetchFileListByOuterId(Long id) {
        return fileRepo.selectListByQuery(QueryWrapper.create().eq(FileInfo::getOuterId, id));
    }

    public List<FileInfo> fetchFileListByOuterId(Long id, String field) {
        return fileRepo.selectListByQuery(QueryWrapper.create().eq(FileInfo::getOuterId, id).eq(FileInfo::getField, field));
    }

    public List<FileInfo> fetchFileListByOuterIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return fileRepo.selectListByQuery(QueryWrapper.create().in(FileInfo::getOuterId, ids));
    }


}
