package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.CouponSceneEnum;
import com.dounanflowers.common.enums.CouponTplEndTypeEnum;
import com.dounanflowers.common.enums.CouponTypeEnum;
import com.dounanflowers.common.repo.*;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateChain;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.dounanflowers.common.entity.table.CouponTplTableDef.COUPON_TPL;
import static com.dounanflowers.common.entity.table.CouponTableDef.COUPON;
import static com.mybatisflex.core.query.QueryMethods.count;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class CouponManager {

    private final CouponRepo couponRepo;

    private final CouponBlankRepo couponBlankRepo;

    private final CouponBlankCodeRelRepo couponBlankCodeRelRepo;

    private final CouponTplRepo couponTplRepo;

    private final CouponTplUserRelRepo couponTplUserRelRepo;

    public CouponTpl fetchCouponTplById(Long id) {
        return couponTplRepo.selectOneWithRelationsById(id);
    }

    public CouponTpl fetchCouponTplByCode(String code) {
        return couponTplRepo.selectOneWithRelationsByQuery(QueryWrapper.create().eq(CouponTpl::getCode, code));
    }

    public Boolean deleteCouponTplById(Long id) {
        int i = couponTplRepo.deleteById(id);
        couponBlankRepo.deleteByQuery(QueryWrapper.create().eq(CouponBlank::getCouponTplId, id));
        return i > 0;
    }

    public Page<CouponTpl> pageCouponTplList(PageRequest pageRequest) {
        for (PageFilter pageFilter : pageRequest.getFilter()) {
            if ("endAtType".equals(pageFilter.getField())) {
                pageFilter.setField("endType");
                pageFilter.setValue(BaseEnum.ordinalOf(CouponTplEndTypeEnum.class, pageFilter.getValue().toString()));
            } else if ("type".equals(pageFilter.getField())) {
                pageFilter.setField("type");
                pageFilter.setValue(BaseEnum.ordinalOf(CouponTypeEnum.class, pageFilter.getValue().toString()));
            } else if ("useSceneType".equals(pageFilter.getField())) {
                pageFilter.setField("scene");
                pageFilter.setValue(BaseEnum.ordinalOf(CouponSceneEnum.class, pageFilter.getValue().toString()));
            } else if ("useable".equals(pageFilter.getField()) && "custom".equals(pageFilter.getType())) {
                boolean value = (boolean) pageFilter.getValue();
                if (value) {
                    pageRequest.addCustom("useable", w -> w.eq(CouponTpl::getStatus, IsEnum.TRUE)
                            .gt(CouponTpl::getAbsoluteEndAt, LocalDateTime.now())
                            .and(COUPON_TPL.PUBLISH_COUNT.gt(COUPON_TPL.RECEIVED_COUNT)));
                } else {
                    pageRequest.addCustom("useable", w -> w
                            .and(COUPON_TPL.STATUS.eq(IsEnum.FALSE)
                                    .or(COUPON_TPL.ABSOLUTE_END_AT.lt(LocalDateTime.now())
                                            .or(COUPON_TPL.PUBLISH_COUNT.le(COUPON_TPL.RECEIVED_COUNT)))));
                }
            }
        }
        return couponTplRepo.selectPageWithRelations(pageRequest);
    }

    public Page<CouponTpl> clientPublicCouponTplPage(PageRequest pageRequest, Long userId) {
//         var userCouponCount = QueryWrapper.create()
//         .select(COUPON.COUPON_TPL_ID.as("tpl_id"), count().as("count"))
//         .from(COUPON)
//         .where(COUPON.USER_ID.eq(userId))
//         .and(COUPON.IS_DELETED.eq(IsEnum.FALSE))
//         .groupBy(COUPON.COUPON_TPL_ID);

// return couponTplRepo.selectPageWithRelations(pageRequest, w -> w
//         .and(COUPON_TPL.CLIENT_PUBLIC.eq(IsEnum.TRUE))
//         .and(COUPON_TPL.STATUS.eq(IsEnum.TRUE))
//         .and(COUPON_TPL.ABSOLUTE_END_AT.gt(LocalDateTime.now()))
//         .and(COUPON_TPL.PUBLISH_COUNT.gt(COUPON_TPL.RECEIVED_COUNT))
//         .leftJoin("(" + userCouponCount.toSQL() + ") uc")
//         .on(COUPON_TPL.ID.eq(new QueryColumn("uc.tpl_id")))
//         .and(COUPON_TPL.CLIENT_RECEIVE_LIMIT.eq(0)
//                 .or(COUPON_TPL.CLIENT_RECEIVE_LIMIT.gt(new QueryColumn("COALESCE(uc.count, 0)")))
//         )
//         .select(COUPON_TPL.ALL_COLUMNS)
//         .select(new QueryColumn("COALESCE(uc.count, 0)", "currentUserReceiveCount"))
        return couponTplRepo.selectPageWithRelations(pageRequest, w -> w.and(COUPON_TPL.CLIENT_PUBLIC.eq(IsEnum.TRUE))
                .and(COUPON_TPL.STATUS.eq(IsEnum.TRUE))
                .and(COUPON_TPL.ABSOLUTE_END_AT.gt(LocalDateTime.now()))
                .and(COUPON_TPL.PUBLISH_COUNT.gt(COUPON_TPL.RECEIVED_COUNT))
                .and(COUPON_TPL.CLIENT_RECEIVE_LIMIT.eq(0)
                        .or(COUPON_TPL.CLIENT_RECEIVE_LIMIT.gt(
                                QueryWrapper.create()
                                        .select(count())
                                        .from(COUPON)
                                        .where(COUPON.USER_ID.eq(userId))
                                        .and(COUPON.COUPON_TPL_ID.eq(COUPON_TPL.ID))
                                        .and(COUPON.IS_DELETED.eq(IsEnum.FALSE))
                        ))
                )
        );
    }

    public void saveCouponTpl(CouponTpl tpl) {
        if (tpl.getId() == null) {
            tpl.setReceivedCount(0);
            String code;
            CouponTpl couponTpl;
            do {
                code = UUID.randomUUID().toString().replace("-", "").substring(0, 6).toUpperCase();
                couponTpl = fetchCouponTplByCode(code);
            } while (couponTpl != null);
            tpl.setCode(code);
        }
        couponTplRepo.save(tpl);
        if (tpl.getPusherIds() != null) {
            couponTplUserRelRepo.deleteByQuery(QueryWrapper.create().eq("coupon_tpl_id", tpl.getId()));
            for (Long pusherId : tpl.getPusherIds()) {
                CouponTplUserR rel = new CouponTplUserR();
                rel.setCouponTplId(tpl.getId());
                rel.setUserId(pusherId);
                couponTplUserRelRepo.save(rel);
            }
        }
    }

    public Page<CouponBlank> pageCouponBlankList(PageRequest pageRequest) {
        return couponBlankRepo.selectPageWithRelations(pageRequest);
    }

    public Boolean deleteCouponBlankById(Long id) {
        return couponBlankRepo.deleteById(id) > 0;
    }

    public void saveCouponBlank(CouponBlank blank) {
        couponBlankRepo.save(blank);
        if (blank.getCouponCodes() != null) {
            couponBlankCodeRelRepo.deleteByQuery(QueryWrapper.create().eq("coupon_blank_id", blank.getId()));
            for (String couponCode : blank.getCouponCodes()) {
                CouponBlankCodeR rel = new CouponBlankCodeR();
                rel.setCouponBlankId(blank.getId());
                rel.setCode(couponCode);
                couponBlankCodeRelRepo.save(rel);
            }
        }
    }

    public Page<Coupon> fetchCouponPage(PageRequest pageRequest) {
        if (pageRequest.getFilter() != null) {
            for (PageFilter pageFilter : pageRequest.getFilter()) {
                if ("useStatus".equals(pageFilter.getField()) && "custom".equals(pageFilter.getType())) {
                    pageRequest.addCustom("useStatus", w -> {
                        String useStatus = (String) pageFilter.getValue();
                        switch (useStatus) {
                            case "待使用":
                                w.isNull("used_at").gt("end_at", LocalDateTime.now());
                                break;
                            case "已使用":
                                w.isNotNull("used_at");
                                break;
                            case "已过期":
                                w.lt("end_at", LocalDateTime.now());
                                break;
                        }
                    });
                }
            }
        }
        return couponRepo.selectPageWithRelations(pageRequest);
    }

    public void saveCoupon(Coupon coupon) {
        couponRepo.save(coupon);
    }

    public CouponTpl fetchAvailableCouponTpl(Long id, Integer receiveCount) {
        CouponTpl tpl = fetchCouponTplById(id);
        if (tpl == null) {
            throw new BaseException("优惠券母版不存在");
        }
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime absoluteEndAt = tpl.getAbsoluteEndAt();

        // 检查优惠券母版是否已过期
        if (absoluteEndAt.isBefore(now)) {
            throw new BaseException("优惠券母版已过期");
        }

        // 重新计算已领取数量
        int receivedCouponCount = this.countReceivedCouponByTplId(id);
        if(tpl.getReceivedCount() != receivedCouponCount) {
            tpl.setReceivedCount(receivedCouponCount);
            log.info("更新优惠券母版已领取数量：{}", tpl.getReceivedCount());
            this.saveCouponTpl(tpl);
        }

        // 检查优惠券母版数量是否足够
        if (tpl.getPublishCount() - tpl.getReceivedCount() < receiveCount) {
            throw new BaseException("优惠券母版数量不足");
        }
        return tpl;
    }

    public void saveCouponBatch(List<Coupon> coupons) {
        couponRepo.insertBatch(coupons);
    }

    public Coupon fetchCouponByCode(String code) {
        return couponRepo.selectOneByQuery(QueryWrapper.create().eq(Coupon::getCode, code));
    }

    public CouponBlank fetchCouponBlankById(Long id) {
        return couponBlankRepo.selectOneWithRelationsById(id);
    }

    public List<Coupon> fetchCouponListByCodes(List<String> couponCodes) {
        return couponRepo.selectListWithRelationsByQuery(QueryWrapper.create().in("code", couponCodes));
    }

    public Coupon fetchLastBlankCoupon(Long tplId, Long currentUserId) {
        return couponRepo.selectOneByQuery(QueryWrapper.create()
                .eq(Coupon::getCouponTplId, tplId)
                .eq(Coupon::getSourceUserId, currentUserId)
                .eq(Coupon::getIsInstantGen, IsEnum.TRUE)
                .isNull(Coupon::getUserId)
                .orderBy(Coupon::getCreatedAt).desc().limit(1));
    }

    public Coupon fetchCouponById(Long couponId) {
        return couponRepo.selectOneWithRelationsById(couponId);
    }

    public List<CouponTpl> fetchCouponTplListByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return couponTplRepo.selectListByIds(ids);
    }

    public Integer countByUserId(Long id) {
        return (int) couponRepo.selectCountByQuery(
                QueryWrapper.create().isNull(Coupon::getUsedAt).eq(Coupon::getUserId, id).gt(Coupon::getEndAt, LocalDateTime.now()));
    }

    public Integer countUsedCouponByTplId(Long id) {
        return (int) couponRepo.selectCountByQuery(QueryWrapper.create().isNotNull(Coupon::getUsedAt).eq(Coupon::getCouponTplId, id));
    }

    public List<Coupon> fetchByIds(List<Long> couponIds) {
        if (CollectionUtils.isEmpty(couponIds)) {
            return List.of();
        }
        return couponRepo.selectListByIds(couponIds);
    }

    public Integer countUsableCouponTplByUserId(Long userId, Long tplId) {
        return (int) couponRepo.selectCountByQuery(
                QueryWrapper.create()
                        .eq(Coupon::getUserId, userId)
                        .eq(Coupon::getCouponTplId, tplId)
                        .isNull(Coupon::getUsedAt)
                        .gt(Coupon::getEndAt, LocalDateTime.now())
        );

    }

    public List<Coupon> fetchCouponListByIds(List<Long> couponIds) {
        if (CollectionUtils.isEmpty(couponIds)) {
            return new ArrayList<>();
        }
        return couponRepo.selectListByIds(couponIds);
    }

    public int countReceivedCouponByTplId(Long id) {
        return (int) couponRepo.selectCountByQuery(QueryWrapper.create().eq(Coupon::getCouponTplId, id));
    }

    public List<Coupon> fetchCouponsByOrderId(Long orderId, String orderModel) {
        return couponRepo.selectListByQuery(QueryWrapper.create().eq(Coupon::getOrderId, orderId).eq(Coupon::getOrderModel, orderModel));
    }

    public void updateCouponStackableByTplId(Long tplId, IsEnum isStackable) {
        UpdateChain.of(Coupon.class)
                .set(Coupon::getIsStackable, isStackable)
                .where(Coupon::getCouponTplId).eq(tplId)
                .where(Coupon::getUsedAt).isNull()
                .update();
    }
}
