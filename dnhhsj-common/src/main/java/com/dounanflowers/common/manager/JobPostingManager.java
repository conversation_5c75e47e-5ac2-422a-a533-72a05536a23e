package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.AdminMenuEventEnum;
import com.dounanflowers.common.enums.AppointmentStatusEnum;
import com.dounanflowers.common.enums.JobPostingStatusEnum;
import com.dounanflowers.common.repo.*;
import com.dounanflowers.common.service.AdminMenuService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.IsEnum;
import com.google.common.collect.Lists;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.DbChain;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

import static com.dounanflowers.common.entity.table.ParkServiceAppointmentTableDef.PARK_SERVICE_APPOINTMENT;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class JobPostingManager {
    private final AdminMenuService adminMenuService;
    private final JobPostingRepo jobPostingRepo;
    private final JobPostingTagRRepo jobPostingTagRRepo;
    private final ParkServiceRepo parkServiceRepo;
    private final ParkServiceAppointmentRepo parkServiceAppointmentRepo;
    private final ParkServiceTimesRepo parkServiceTimesRepo;

    public Page<JobPosting> pageJobPostings(PageRequest dto) {
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("tags".equals(filter.getField())) {
                    QueryWrapper subWrapper = filter.wrapper(QueryWrapper.create()
                            .select("job_posting_id")
                            .from("job_posting_tag_r"), "tag");
                    filter.setField("id");
                    filter.setType("sub");
                    dto.addSubQuery("id", w -> w.in("id", subWrapper));
                }
            }
        }
        return jobPostingRepo.selectPageWithRelations(dto);
    }

    public Page<ParkService> pageParkServices(PageRequest dto) {
        if(dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("todo".equals(filter.getField()) && "custom".equals(filter.getType())) {
                   List<Long> todoIds = this.getTodoParkServiceIds();
                   if(todoIds.isEmpty()) return Page.empty();
                   filter.setType("in");
                   filter.setField("id");
                   filter.setValue(todoIds);
                }
            }
        }
        return parkServiceRepo.selectPageWithRelations(dto);
    }

    public List<Long> getTodoParkServiceIds() {
        return DbChain.table("park_service_appointment")
                .select("service_id")
                .where("status in (?,?)", AppointmentStatusEnum.PENDING, AppointmentStatusEnum.ACCEPTED)
                .groupBy("service_id")
                .list().stream().map(r -> r.getLong("service_id")).toList();
    }

    /**
     * 获取所有用工信息，包括园区服务和用工发布
     */
    public List<Object> getAllJobPostings() {
        List<Object> result = new ArrayList<>();

        // 获取所有园区服务
        List<ParkService> parkServices = parkServiceRepo.selectAll();
        result.addAll(parkServices);

        // 获取所有用工发布
        List<JobPosting> jobPostings = jobPostingRepo.selectAll();
        result.addAll(jobPostings);

        return result;
    }

    public JobPosting createJobPosting(JobPosting jobPosting) {
        jobPosting.setStatus(JobPostingStatusEnum.PENDING);
        if (jobPosting.getShow() == null) {
            jobPosting.setShow(IsEnum.TRUE);
        }
        jobPostingRepo.save(jobPosting);
        adminMenuService.emitCount(AdminMenuEventEnum.JOB_AUDIT);
        // 删除旧的标签
        jobPostingTagRRepo.deleteByQuery(
                QueryWrapper.create()
                        .eq(JobPostingTagR::getJobPostingId, jobPosting.getId())
        );
        // 保存标签
        saveJobPostingTags(jobPosting);

        return jobPosting;
    }

    public ParkService createParkService(ParkService parkService) {
        parkServiceRepo.save(parkService);
        if (parkService.getTimes() != null) {
            for (ParkServiceTimes times : parkService.getTimes()) {
                times.setServiceId(parkService.getId());
                parkServiceTimesRepo.save(times);
            }
        }
        return parkService;
    }

    public JobPosting updateJobPosting(JobPosting jobPosting) {
        jobPostingRepo.save(jobPosting);
        adminMenuService.emitCount(AdminMenuEventEnum.JOB_AUDIT);

        if (jobPosting.getTags() != null) {
            // 删除旧的标签
            jobPostingTagRRepo.deleteByQuery(
                    QueryWrapper.create()
                            .eq(JobPostingTagR::getJobPostingId, jobPosting.getId())
            );

            // 保存新的标签
            saveJobPostingTags(jobPosting);
        }

        return jobPosting;
    }

    public ParkService updateParkService(ParkService parkService) {
        parkServiceRepo.save(parkService);
        if (parkService.getTimes() != null) {
            parkServiceTimesRepo.deleteByQuery(
                    QueryWrapper.create()
                            .where(ParkServiceTimes::getServiceId).eq(parkService.getId())
            );
            for (ParkServiceTimes times : parkService.getTimes()) {
                times.setServiceId(parkService.getId());
                parkServiceTimesRepo.save(times);
            }
        }
        return parkService;
    }

    public void updateJobPostingStatus(Long id, JobPostingStatusEnum status) {
        JobPosting jobPosting = new JobPosting();
        jobPosting.setId(id);
        jobPosting.setStatus(status);
        jobPostingRepo.save(jobPosting);
        adminMenuService.emitCount(AdminMenuEventEnum.JOB_AUDIT);
    }

    public void deleteJobPosting(Long id) {
        // 删除关联的标签
        jobPostingTagRRepo.deleteByQuery(
                QueryWrapper.create()
                        .where(JobPostingTagR::getJobPostingId).eq(id)
        );

        jobPostingRepo.deleteById(id);
    }

    public void deleteParkService(Long id) {
        parkServiceRepo.deleteById(id);
    }

    /**
     * 保存用工发布的标签
     */
    private void saveJobPostingTags(JobPosting jobPosting) {
        if (!CollectionUtils.isEmpty(jobPosting.getTags())) {
            for (String tag : jobPosting.getTags()) {
                JobPostingTagR tagR = new JobPostingTagR();
                tagR.setJobPostingId(jobPosting.getId());
                tagR.setTag(tag);
                jobPostingTagRRepo.save(tagR);
            }
        }
    }

    public void saveAppointment(ParkServiceAppointment appointment) {
        parkServiceAppointmentRepo.save(appointment);
        adminMenuService.emitCount(AdminMenuEventEnum.JOB_SERVICE_AUDIT);
    }

    public Page<ParkServiceAppointment> pageAppointments(PageRequest pageRequest) {
        return parkServiceAppointmentRepo.selectPageWithRelations(pageRequest);
    }

    public List<ParkService> fetchParkServiceByIds(List<Long> serviceIds) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return new ArrayList<>();
        }
        return parkServiceRepo.selectListWithRelationsByQuery(QueryWrapper.create().in(ParkService::getId, serviceIds));
    }

    public ParkService fetchParkServiceById(Long serviceId) {
        return parkServiceRepo.selectOneWithRelationsByQuery(QueryWrapper.create().eq(ParkService::getId, serviceId));
    }

    public JobPosting fetchJobPostingById(Long id) {
        return jobPostingRepo.selectOneById(id);
    }

    public ParkServiceAppointment fetchAppointmentById(Long id) {
        return parkServiceAppointmentRepo.selectOneById(id);
    }

    public List<ParkServiceAppointment> fetchTodoAppointmentsByServiceIds(List<Long> serviceIds) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return new ArrayList<>();
        }
        return parkServiceAppointmentRepo.selectListByQuery(QueryWrapper.create().in(ParkServiceAppointment::getStatus, Lists.newArrayList(AppointmentStatusEnum.PENDING, AppointmentStatusEnum.ACCEPTED)).in(ParkServiceAppointment::getServiceId, serviceIds));
    }

    public Long fetchTodoAppointmentCount() {
        return parkServiceAppointmentRepo.selectCountByQuery(QueryWrapper.create().in(ParkServiceAppointment::getStatus, Lists.newArrayList(AppointmentStatusEnum.PENDING, AppointmentStatusEnum.ACCEPTED)));
    }

    public Long fetchTodoJobPostingCount() {
        return jobPostingRepo.selectCountByQuery(QueryWrapper.create().eq(JobPosting::getStatus, JobPostingStatusEnum.PENDING));
    }
}
