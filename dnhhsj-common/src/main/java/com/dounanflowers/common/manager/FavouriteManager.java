package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.Favourite;
import com.dounanflowers.common.enums.FavouriteTypeEnum;
import com.dounanflowers.common.repo.FavouriteRepo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Db;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.dounanflowers.common.entity.table.FavouriteTableDef.FAVOURITE;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class FavouriteManager {

    private final FavouriteRepo favouriteRepo;


    public Page<Favourite> page(PageRequest dto) {
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("type".equals(filter.getField())) {
                    if (filter.getValue() instanceof List list) {
                        filter.setValue(list.stream().map(v -> Integer.parseInt(v.toString())).toList());
                    } else {
                        filter.setValue(Integer.parseInt(filter.getValue().toString()));
                    }
                }
            }
        }
        return favouriteRepo.selectPageWithRelations(dto);
    }

    public Favourite fetchFavourite(FavouriteTypeEnum type, Long entityId, Long userId, String userModel) {
        return favouriteRepo.selectOneByQuery(QueryWrapper.create()
                .eq(Favourite::getType, type)
                .eq(Favourite::getEntityId, entityId)
                .eq(Favourite::getUserId, userId)
                .eq(Favourite::getUserModel, userModel)
        );
    }

    public void deleteById(Long id) {
        favouriteRepo.deleteById(id);
    }

    public void updateFavouriteCount(String entityModel, Long entityId) {
        // TODO 根据被更新的实体，更新实体的收藏数
    }

    public void save(Favourite favourite) {
        favouriteRepo.save(favourite);
    }

    public Integer countByEntityId(FavouriteTypeEnum type, Long entityId) {
        return (int) favouriteRepo.selectCountByQuery(QueryWrapper.create().eq(Favourite::getType, type).eq(Favourite::getEntityId, entityId));
    }

    public List<Favourite> fetchFavourites(FavouriteTypeEnum favouriteTypeEnum, List<Long> list) {
        if (CollectionUtils.isEmpty(list)) return Lists.newArrayList();
        return favouriteRepo.selectListByQuery(QueryWrapper.create().eq(Favourite::getType, favouriteTypeEnum).in(Favourite::getEntityId, list));
    }

    public Map<Long, Integer> countByEntityIds(FavouriteTypeEnum favouriteTypeEnum, List<Long> entityIds) {
        if (CollectionUtils.isEmpty(entityIds)) return Maps.newHashMap();
        return Db.selectListByQuery(
            FAVOURITE.getTableName(),
            QueryWrapper.create()
                .select("entity_id", "count(*) as count")
                    .where(FAVOURITE.IS_DELETED.eq(0))
                .where(FAVOURITE.ENTITY_ID.in(entityIds))
                .where(FAVOURITE.TYPE.eq(favouriteTypeEnum))
                .groupBy("entity_id")
        ).stream().collect(Collectors.toMap(v -> Long.parseLong(v.get("entity_id").toString()), v -> Integer.parseInt(v.get("count").toString())));

    }
}

