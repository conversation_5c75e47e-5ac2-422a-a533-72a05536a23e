package com.dounanflowers.common.manager;

import com.dounanflowers.common.dto.BillRefundDto;
import com.dounanflowers.common.dto.PayNotifyDto;
import com.dounanflowers.common.entity.AdminUser;
import com.dounanflowers.common.entity.Bill;
import com.dounanflowers.common.entity.BillRefund;
import com.dounanflowers.common.entity.ClientUser;
import com.dounanflowers.common.enums.BillPayStatusEnum;
import com.dounanflowers.common.enums.BillTypeEnum;
import com.dounanflowers.common.repo.AdminUserRepo;
import com.dounanflowers.common.repo.BillRefundRepo;
import com.dounanflowers.common.repo.BillRepo;
import com.dounanflowers.common.repo.ClientUserRepo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.DateUtils;
import com.dounanflowers.framework.utils.IdUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.third.ThirdPartyHolder;
import com.dounanflowers.third.bo.RefundResultBo;
import com.dounanflowers.third.dto.RefundParamsDto;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class BillManager {
    private final BillRepo billRepo;
    private final BillRefundRepo billRefundRepo;
    private final AdminUserRepo adminUserRepo;

    private final ClientUserRepo clientUserRepo;
    private final UserCommonManager userCommonManager;

    public Bill saveBillByPayNotify(Map<String, String> dto) {
        Boolean check = ThirdPartyHolder.allinpayService().verify(dto);
        if (!check) {
            throw new BaseException("响应验签失败");
        }
        PayNotifyDto notify = JsonUtils.toObject(JsonUtils.toJson(dto), PayNotifyDto.class);
        if (notify.getTrxstatus().equals("0000")) {
            Long billId = Long.valueOf(notify.getCusorderid());
            Bill bill = billRepo.selectOneById(billId);
            if (bill == null) {
                throw new BaseException("订单不存在");
            }
            bill.setPayStatus(BillPayStatusEnum.PAID);
            bill.setPaidAt(LocalDateTime.parse(notify.getPaytime(), DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            bill.setPayTrxid(notify.getTrxid());
            bill.setPaidMoneyCent(Integer.valueOf(notify.getTrxamt()));
            bill.setPayFeeCent(Integer.valueOf(notify.getFee()));
            billRepo.save(bill);
            return bill;
        } else {
            throw new BaseException("保存失败");
        }
    }

    public void saveBill(Bill bill) {
        billRepo.save(bill);
    }

    public void billRefund(Bill bill, BillRefundDto dto) {
        if (bill.getPayStatus() != BillPayStatusEnum.PAID && bill.getPayStatus() != BillPayStatusEnum.PART_REFUNDED) {
            throw new BaseException("订单状态不是已支付或部分退款");
        }
        int canRefundMoneyCent = bill.getPaidMoneyCent() - bill.getRefundedMoneyCent();
        if (dto.getMoneyCent() != null) {
            if (dto.getMoneyCent() < 0) {
                throw new BaseException("退款金额不能小于0");
            }
            if (dto.getMoneyCent() > canRefundMoneyCent) {
                throw new BaseException("退款金额不能大于订单可退金额");
            }
        }

        boolean isPartRefund = dto.getMoneyCent() != null && dto.getMoneyCent() < canRefundMoneyCent;
        int refundMoneyCent = dto.getMoneyCent() == null ? canRefundMoneyCent : dto.getMoneyCent();

        if(refundMoneyCent == 0) {
            return;
        }

        BillRefund billRefund = new BillRefund();
        billRefund.setId(IdUtils.nextId());
        billRefund.setBillId(bill.getId());
        billRefund.setMoneyCent(refundMoneyCent);
        billRefund.setOperatedAt(LocalDateTime.now());
        billRefund.setReason(dto.getReason());

        RefundParamsDto refundParams = new RefundParamsDto();
        refundParams.setTrxamt(refundMoneyCent);
        refundParams.setReqsn(String.valueOf(billRefund.getId()));
        refundParams.setOldreqsn(String.valueOf(bill.getId()));
        refundParams.setOldtrxid(bill.getPayTrxid());

        RefundResultBo refundResult = ThirdPartyHolder.allinpayService().refund(refundParams);

        billRefund.setRefundedAt(LocalDateTime.parse(refundResult.getFintime(), DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        billRefund.setTrxid(refundResult.getChnltrxid());
        billRefund.setFeeCent(Integer.parseInt(refundResult.getFee()));

        billRefundRepo.insertSelectiveWithPk(billRefund);

        bill.setRemark(dto.getRemark());
        bill.setRefundedMoneyCent(bill.getRefundedMoneyCent() + refundMoneyCent);
        bill.setPayStatus(isPartRefund ? BillPayStatusEnum.PART_REFUNDED : BillPayStatusEnum.REFUNDED);
        saveBill(bill);

    }

    public Page<Bill> pageList(PageRequest dto) {
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("userObj".equals(filter.getField()) && "custom".equals(filter.getType()) && StringUtil.isNotBlank((String) filter.getValue())) {
                    List<Long> userIds = userCommonManager.fetchUserIdsByLike((String) filter.getValue());
                    if(userIds.isEmpty()) return Page.empty();
                    filter.setType("in");
                    filter.setField("userId");
                    filter.setValue(userIds);
                } else if ("paidAt".equals(filter.getField()) && "range".equals(filter.getType())) {
                    List<?> list = (List<?>) filter.getValue();
                    filter.setValue(list.stream().map(v -> DateUtils.parseLocalDateTime(Long.parseLong(v.toString()))).toList());
                } else if ("type".equals(filter.getField()) && !"custom".equals(filter.getType())) {
                    if (filter.getValue() instanceof List list) {
                        filter.setValue(list.stream().map(v -> Integer.parseInt(v.toString())).toList());
                    } else {
                        filter.setValue(Integer.parseInt(filter.getValue().toString()));
                    }
                } else if ("payStatus".equals(filter.getField()) && !"custom".equals(filter.getType())) {
                    if (filter.getValue() instanceof List list) {
                        filter.setValue(list.stream().map(v -> Integer.parseInt(v.toString())).toList());
                    } else {
                        filter.setValue(Integer.parseInt(filter.getValue().toString()));
                    }
                }
            }
        }
        return billRepo.selectPageWithRelations(dto);
    }

    public Bill fetchById(Long id) {
        return billRepo.selectOneById(id);
    }

    public List<Bill> fetchRelatList(BillTypeEnum type, String entityId, String entityModel) {
        return billRepo.selectListWithRelationsByQuery(
                QueryWrapper.create()
                        .eq(Bill::getType, type)
                        .eq(Bill::getEntityId, entityId)
                        .eq(Bill::getEntityModel, entityModel)
        );
    }

    public Bill fetchByEntityId(Long id) {
        return billRepo.selectOneByQuery(QueryWrapper.create().eq(Bill::getEntityId, id).orderBy(Bill::getCreatedAt, false));
    }

    public List<Bill> listByIds(List<Long> billIds) {
        if(billIds == null || billIds.isEmpty()) {
            return new ArrayList<>();
        }
        return billRepo.selectListByIds(billIds);
    }


}
