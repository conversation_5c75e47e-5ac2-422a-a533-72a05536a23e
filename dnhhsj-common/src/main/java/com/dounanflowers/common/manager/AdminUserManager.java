package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.PermissionTypeEnum;
import com.dounanflowers.common.repo.*;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.utils.DateUtils;
import com.dounanflowers.security.utils.SecurityHolder;
import com.google.common.collect.Lists;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static com.dounanflowers.common.entity.table.AdminPermissionTableDef.ADMIN_PERMISSION;
import static com.dounanflowers.common.entity.table.AdminRolePermissionRelTableDef.ADMIN_ROLE_PERMISSION_REL;
import static com.dounanflowers.common.entity.table.AdminRoleTableDef.ADMIN_ROLE;
import static com.dounanflowers.common.entity.table.AdminUserRoleRelTableDef.ADMIN_USER_ROLE_REL;
import static com.dounanflowers.common.entity.table.AdminUserTableDef.ADMIN_USER;

@Component
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class AdminUserManager {

    private final AdminUserRepo adminUserRepo;

    private final AdminUserRoleRelRepo adminUserRoleRelRepo;

    private final AdminUserCredentialRepo adminUserCredentialRepo;

    private final AdminPermissionRepo adminPermissionRepo;

    private final AdminRolePermissionRelRepo adminRolePermissionRelRepo;

    private final AdminRoleRepo adminRoleRepo;

    public AdminUser fetchByUsername(String username) {
        return adminUserRepo.selectOneWithRelationsByQuery(QueryWrapper.create().eq(AdminUser::getUsername, username));
    }

    public AdminUser fetchById(Long userId) {
        return adminUserRepo.selectOneById(userId);
    }

    public void save(AdminUser adminUser) {
        adminUserRepo.save(adminUser);
        if (CollectionUtils.isNotEmpty(adminUser.getRoles())) {
            adminUserRoleRelRepo.deleteByQuery(QueryWrapper.create().eq(AdminUserRoleRel::getUserId, adminUser.getId()));
            for (String role : adminUser.getRoles()) {
                AdminUserRoleRel adminUserRoleRel = new AdminUserRoleRel();
                adminUserRoleRel.setUserId(adminUser.getId());
                adminUserRoleRel.setRole(role);
                adminUserRoleRelRepo.insert(adminUserRoleRel);
            }
        }
    }

    public AdminUser fetchUserByOpenId(String openId) {
        return adminUserRepo.selectOneWithRelationsByQuery(QueryWrapper.create().eq(AdminUser::getOpenId, openId));
    }

    public List<AdminUser> fetchByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return adminUserRepo.selectListByIds(ids);
    }

    public AdminUser fetchByMobile(String phone) {
        return adminUserRepo.selectOneByQuery(QueryWrapper.create().eq(AdminUser::getMobile, phone));
    }


    public AdminUser fetchByMobileOrCreate(String phone, String ifNullName) {
        AdminUser adminUser = adminUserRepo.selectOneByQuery(QueryWrapper.create().eq(AdminUser::getMobile, phone));
        if (adminUser != null) {
            return adminUser;
        } else {
            AdminUser user = new AdminUser();
            user.setMobile(phone);
            user.setNickname(phone);
            user.setPassword(SecurityHolder.context().encryptPassword("no_password"));
            user.setUsername(ifNullName == null ? phone : ifNullName);
            adminUserRepo.save(user);
            AdminUserRoleRel adminUserRoleRel = new AdminUserRoleRel();
            adminUserRoleRel.setUserId(user.getId());
            adminUserRoleRel.setRole("0"); // 设为商家
            adminUserRoleRelRepo.insert(adminUserRoleRel);
            return user;
        }

    }

    public Page<AdminUser> pageList(PageRequest dto) {
        if (CollectionUtils.isEmpty(dto.getFilter())) {
            return adminUserRepo.selectPageWithRelations(dto);
        }
        for (PageFilter filter : dto.getFilter()) {
            if ("lastLoginAt".equals(filter.getField())) {
                List<LocalDateTime> newVar = Lists.newArrayList();
                for (Object o : (List) filter.getValue()) {
                    newVar.add(DateUtils.parseLocalDateTime(Long.parseLong(o.toString())));
                }
                filter.setValue(newVar);
            } else if ("roles".equals(filter.getField())) {
                QueryWrapper subWrapper = filter.wrapper(QueryWrapper.create().select("user_id").from(AdminUserRoleRel.class), "role");
                filter.setType("sub");
                filter.setField("id");
                dto.addSubQuery("id", w -> w.in("id", subWrapper));
            } else if ("search".equals(filter.getField()) && "custom".equals(filter.getType())) {
                dto.addCustom("search", w -> w.and(sw -> sw.like("username", filter.getValue())
                        .or(or -> or.like("nickname", filter.getValue()), true)
                        .or(or -> or.like("mobile", filter.getValue()), true)
                        .or(or -> or.like("realname", filter.getValue()), true), true));
            } else if ("custom".equals(filter.getType()) && "client".equals(filter.getField())) {
                PermissionTypeEnum type = PermissionTypeEnum.valueOf(filter.getValue().toString().toUpperCase());
                dto.addCustom(filter.getField(), w -> {
                    if (type == PermissionTypeEnum.SHOP) {
                        w.and(and -> and.in("id", fetchUserIdsByRoleType(type)).or(w1 -> w1.notIn("id", fetchUserIdsByRoleType(PermissionTypeEnum.ADMIN)), true), true);
                    } else {
                        w.in("id", fetchUserIdsByRoleType(type));
                    }
                });
            } else if ("wxOpenId".equals(filter.getField())) {
                filter.setField("openId");
            }
        }
        return adminUserRepo.selectPageWithRelations(dto);
    }

    private List<Long> fetchUserIdsByRoleType(PermissionTypeEnum type) {
        List<AdminRole> roles = adminRoleRepo.selectListByQuery(QueryWrapper.create().eq(AdminRole::getType, type));
        List<String> roleCodes = roles.stream().map(AdminRole::getCode).toList();
        return adminUserRoleRelRepo.selectListByQuery(QueryWrapper.create().in(AdminUserRoleRel::getRole, roleCodes))
                .stream().map(AdminUserRoleRel::getUserId).toList();
    }

    public void delete(Long id) {
        adminUserRepo.deleteById(id);
    }

    public void updateUserCredential(Long userId, String realname, String idCardNum, String idCardUrl1, String idCardUrl2) {
        AdminUserCredential adminUserCredential = adminUserCredentialRepo.selectOneByQuery(QueryWrapper.create().eq(AdminUserCredential::getUserId, userId));
        if (adminUserCredential == null) {
            adminUserCredential = new AdminUserCredential();
            adminUserCredential.setUserId(userId);
            adminUserCredential.setRealname(realname);
            adminUserCredential.setIdCardNum(idCardNum);
            adminUserCredential.setIdCardUrl1(idCardUrl1);
            adminUserCredential.setIdCardUrl2(idCardUrl2);
            adminUserCredentialRepo.insert(adminUserCredential);
        } else {
            adminUserCredential.setRealname(realname);
            adminUserCredential.setIdCardNum(idCardNum);
            adminUserCredential.setIdCardUrl1(idCardUrl1);
            adminUserCredential.setIdCardUrl2(idCardUrl2);
            adminUserCredentialRepo.update(adminUserCredential);
        }
    }

    public void updateUserRole(Long id, List<String> roles) {
        adminUserRoleRelRepo.deleteByQuery(QueryWrapper.create().eq(AdminUserRoleRel::getUserId, id));
        if (CollectionUtils.isEmpty(roles)) {
            return;
        }
        for (String role : roles) {
            AdminUserRoleRel adminUserRoleRel = new AdminUserRoleRel();
            adminUserRoleRel.setUserId(id);
            adminUserRoleRel.setRole(role);
            adminUserRoleRelRepo.insert(adminUserRoleRel);
        }
    }

    public List<String> fetchRoleCodeByUserId(Long userId) {
        List<AdminUserRoleRel> list = adminUserRoleRelRepo.selectListByQuery(QueryWrapper.create().eq(AdminUserRoleRel::getUserId, userId));
        List<String> roles = Lists.newArrayList();
        for (AdminUserRoleRel adminUserRoleRel : list) {
            roles.add(adminUserRoleRel.getRole());
        }
        return roles;
    }

    public List<Long> fetchUserIdsByRole(String roleValue) {
        List<AdminUserRoleRel> list = adminUserRoleRelRepo.selectListByQuery(QueryWrapper.create().eq(AdminUserRoleRel::getRole, roleValue));
        List<Long> userIds = Lists.newArrayList();
        for (AdminUserRoleRel adminUserRoleRel : list) {
            userIds.add(adminUserRoleRel.getUserId());
        }
        return userIds;
    }

    public List<AdminPermission> listPermissionsByRoles(List<String> roleCodes) {
        if (CollectionUtils.isEmpty(roleCodes)) {
            return Lists.newArrayList();
        }

        // 1. 获取角色对应的所有权限关系
        List<AdminRolePermissionRel> rolePermissionRels = adminRolePermissionRelRepo.selectListByQuery(
                QueryWrapper.create()
                        .where(ADMIN_ROLE_PERMISSION_REL.ROLE.in(roleCodes))
        );
        if (CollectionUtils.isEmpty(rolePermissionRels)) {
            return Lists.newArrayList();
        }

        // 2. 提取权限编码
        List<String> permissionCodes = rolePermissionRels.stream()
                .map(AdminRolePermissionRel::getPermission)
                .distinct()
                .toList();

        // 3. 查询权限信息
        return adminPermissionRepo.selectListByQuery(
                QueryWrapper.create()
                        .where(ADMIN_PERMISSION.CODE.in(permissionCodes))
        );
    }

    public List<AdminRole> fetchRolesByUserIdAndType(Long userId, PermissionTypeEnum type) {
        // 1. 获取用户的所有角色关系
        List<AdminUserRoleRel> userRoleRels = adminUserRoleRelRepo.selectListByQuery(
                QueryWrapper.create().eq(AdminUserRoleRel::getUserId, userId)
        );
        if (CollectionUtils.isEmpty(userRoleRels)) {
            return Lists.newArrayList();
        }

        // 2. 提取角色编码
        List<String> roleCodes = userRoleRels.stream()
                .map(AdminUserRoleRel::getRole)
                .toList();

        // 3. 根据角色编码和权限类型查询角色
        return adminRoleRepo.selectListByQuery(
                QueryWrapper.create()
                        .where(ADMIN_ROLE.CODE.in(roleCodes))
                        .and(ADMIN_ROLE.TYPE.eq(type))
        );
    }

    public void deleteUserRole(Long id) {
        adminUserRoleRelRepo.deleteByQuery(QueryWrapper.create().eq(AdminUserRoleRel::getUserId, id));
    }

    // Permission Management Methods
    public Page<AdminPermission> pagePermissions(PageRequest request) {
        return adminPermissionRepo.selectPageByQuery(request);
    }

    public AdminPermission getPermissionByCode(String code) {
        return adminPermissionRepo.selectOneByQuery(
                QueryWrapper.create().where(ADMIN_PERMISSION.CODE.eq(code))
        );
    }

    public void savePermission(AdminPermission permission) {
        adminPermissionRepo.save(permission);
    }

    public AdminPermission getPermissionById(Long id) {
        return adminPermissionRepo.selectOneById(id);
    }

    public void deletePermission(Long id) {
        adminPermissionRepo.deleteById(id);
    }

    public List<AdminPermission> searchPermissions(String keyword) {
        return adminPermissionRepo.selectListByQuery(
                QueryWrapper.create()
                        .where(ADMIN_PERMISSION.NAME.like("%" + keyword + "%")
                                .or(ADMIN_PERMISSION.CODE.like("%" + keyword + "%")))
        );
    }

    public boolean isPermissionInUse(String permission) {
        return adminRolePermissionRelRepo.selectCountByQuery(
                QueryWrapper.create()
                        .where(ADMIN_ROLE_PERMISSION_REL.PERMISSION.eq(permission))
        ) > 0;
    }

    public List<AdminPermission> listPermissionsByCodes(List<String> codes) {
        return adminPermissionRepo.selectListByQuery(
                QueryWrapper.create()
                        .where(ADMIN_PERMISSION.CODE.in(codes))
        );
    }

    // Role Management Methods
    public Page<AdminRole> pageRoles(PageRequest request) {
        return adminRoleRepo.selectPageByQuery(request);
    }

    public AdminRole getRoleByCode(String code) {
        return adminRoleRepo.selectOneByQuery(
                QueryWrapper.create().where(ADMIN_ROLE.CODE.eq(code))
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveRole(AdminRole role, List<String> permissionCodes) {
        adminRoleRepo.save(role);
        if (role.getId() != null) {
            role = adminRoleRepo.selectOneById(role.getId());
        }
        if (permissionCodes != null) {
            setRolePermissions(role.getCode(), permissionCodes);
        }
    }

    public AdminRole getRoleById(Long id) {
        return adminRoleRepo.selectOneById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteRole(Long id) {
        // 删除角色权限关系
        adminRolePermissionRelRepo.deleteByQuery(
                QueryWrapper.create().where(ADMIN_ROLE_PERMISSION_REL.ROLE.eq(id))
        );
        // 删除角色
        adminRoleRepo.deleteById(id);
    }

    public boolean isRoleInUse(Long roleId) {
        // 检查角色是否被用户使用
        return adminUserRoleRelRepo.selectCountByQuery(
                QueryWrapper.create()
                        .where(ADMIN_USER_ROLE_REL.ROLE.eq(roleId))
        ) > 0;
    }

    public List<String> listPermissionCodesByRoleId(String role) {
        return adminRolePermissionRelRepo.selectListByQuery(
                        QueryWrapper.create().where(ADMIN_ROLE_PERMISSION_REL.ROLE.eq(role))
                ).stream()
                .map(AdminRolePermissionRel::getPermission)
                .toList();
    }

    @Transactional(rollbackFor = Exception.class)
    public void setRolePermissions(String role, List<String> permissionCodes) {
        // 删除原有的权限关系
        adminRolePermissionRelRepo.deleteByQuery(
                QueryWrapper.create()
                        .where(ADMIN_ROLE_PERMISSION_REL.ROLE.eq(role))
        );
        // 创建新的权限关系
        List<AdminRolePermissionRel> relations = permissionCodes.stream()
                .map(permission -> {
                    AdminRolePermissionRel rel = new AdminRolePermissionRel();
                    rel.setRole(role);
                    rel.setPermission(permission);
                    return rel;
                })
                .toList();

        // 批量插入新的权限关系
        adminRolePermissionRelRepo.insertBatch(relations);
    }

    public List<AdminPermission> listAllPermissions() {
        return adminPermissionRepo.selectAll();
    }

    public List<AdminUser> fetchByMobiles(List<String> mobile) {
        if (mobile == null || mobile.isEmpty()) {
            return List.of();
        }
        return adminUserRepo.selectListByQuery(
                QueryWrapper.create()
                        .where(ADMIN_USER.MOBILE.in(mobile))
        );
    }

    public void deleteUsersUnderwriter(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        adminUserRoleRelRepo.deleteByQuery(QueryWrapper.create().in(AdminUserRoleRel::getUserId, userIds).eq(AdminUserRoleRel::getRole, "2"));
    }

    public void addUsersUnderwriter(List<Long> userIds) {
        for (Long userId : userIds) {
            AdminUserRoleRel adminUserRoleRel = new AdminUserRoleRel();
            adminUserRoleRel.setUserId(userId);
            adminUserRoleRel.setRole("2");
            adminUserRoleRelRepo.insert(adminUserRoleRel);
        }
    }

}
