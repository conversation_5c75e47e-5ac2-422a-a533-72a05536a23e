package com.dounanflowers.common.manager;

import com.dounanflowers.common.bo.CheckOrderPageBo;
import com.dounanflowers.common.dto.CheckOrderPageDto;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.AdminMenuEventEnum;
import com.dounanflowers.common.enums.OrderStatusEnum;
import com.dounanflowers.common.repo.*;
import com.dounanflowers.common.service.AdminMenuService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.bean.PageSort;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.DateUtils;
import com.google.common.collect.Lists;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ProductManager {

    private final ProductProductRepo productProductRepo;

    private final ProductOrderRepo productOrderRepo;

    private final ProductOrderItemRepo productOrderItemRepo;

    private final ProductReviewRepo productReviewRepo;
    private final ProductRefundApplyRepo productRefundApplyRepo;


    public Page<ProductReview> productReviewPageList(PageRequest dto) {
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("custom".equals(filter.getType()) && "storeId".equals(filter.getField())) {
                    QueryWrapper subWrapper = QueryWrapper.create().select("id").from(Product.class);
                    subWrapper.eq("store_id", filter.getValue());
                    dto.addCustom("storeId", w -> w.in("product_id", subWrapper));
                }
            }
        }
        return productReviewRepo.selectPageWithRelations(dto);
    }

    public Page<Product> productPageList(PageRequest dto) {
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("custom".equals(filter.getType()) && "store".equals(filter.getField())) {
                    QueryWrapper subWrapper = QueryWrapper.create().select("id").from(Store.class)
                            .like("name", filter.getValue()).or(ww -> ww.eq("id", Long.parseLong(filter.getValue().toString())), true);
                    dto.addCustom("store", w -> w.in("store_id", subWrapper));
                } else if ("validFrom".equals(filter.getField())) {
                    if (filter.getValue() instanceof List) {
                        List<LocalDateTime> newVar = Lists.newArrayList();
                        for (Object o : (List) filter.getValue()) {
                            newVar.add(DateUtils.parseLocalDate(o.toString(), "yyyy-MM-dd").atStartOfDay());
                        }
                        filter.setValue(newVar);
                    } else {
                        filter.setValue(DateUtils.parseLocalDate(filter.getValue().toString(), "yyyy-MM-dd").atStartOfDay());
                    }
                } else if ("validTo".equals(filter.getField())) {
                    if (filter.getValue() instanceof List) {
                        List<LocalDateTime> newVar = Lists.newArrayList();
                        for (Object o : (List) filter.getValue()) {
                            newVar.add(DateUtils.parseLocalDate(o.toString(), "yyyy-MM-dd").plusDays(1).atStartOfDay());
                        }
                        filter.setValue(newVar);
                    } else {
                        filter.setValue(DateUtils.parseLocalDate(filter.getValue().toString(), "yyyy-MM-dd").plusDays(1).atStartOfDay());
                    }
                }
            }
        }
        return productProductRepo.selectPageWithRelations(dto);
    }

    public List<Product> fetchProductByIdsWithRelation(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return List.of();
        }
        return productProductRepo.selectListWithRelationsByQuery(QueryWrapper.create().in(Product::getId, productIds));
    }

    public List<Product> fetchProductByIds(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return List.of();
        }
        return productProductRepo.selectListByQuery(QueryWrapper.create().in(Product::getId, productIds));
    }

    public Product fetchProductByIdWithRelation(Long id) {
        return productProductRepo.selectOneWithRelationsById(id);
    }

    public Product fetchProductById(Long id) {
        return productProductRepo.selectOneById(id);
    }

    public Long createOrder(ProductOrder order) {
        // 1. 设置订单号
        order.setOrderNo(generateOrderNo());

        if (order.getMerge() == null) {
            order.setMerge(IsEnum.FALSE);
        }

        if (order.getParentId() == null) {
            order.setParentId(0L);
        }

        // 2. 保存订单
        productOrderRepo.insert(order);

        // 3. 保存订单项
        if (CollectionUtils.isNotEmpty(order.getItems())) {
            order.getItems().forEach(item -> item.setOrderId(order.getId()));
            productOrderItemRepo.insertBatch(order.getItems());
        }

        return order.getId();
    }

    public ProductOrder getOrderWithItems(Long id) {
        ProductOrder order = productOrderRepo.selectOneById(id);
        if (order == null) {
            throw new BaseException("订单不存在");
        }

        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(ProductOrderItem::getOrderId).eq(id);
        List<ProductOrderItem> items = productOrderItemRepo.selectListByQuery(queryWrapper);
        order.setItems(items);

        return order;
    }

    public Page<ProductOrder> getOrderPage(PageRequest dto) {
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("custom".equals(filter.getType()) && "checkSearch".equals(filter.getField())) {
                    dto.addCustom("checkSearch", w ->
                            w.and(ww -> ww.like("orderNo", filter.getValue())
                                    .or(www -> www.in("userId", QueryWrapper.create().select("id").from(ClientUser.class).like("mobile", filter.getValue())), true), true));
                } else if ("custom".equals(filter.getType()) && "store".equals(filter.getField())) {
                    QueryWrapper subWrapper = QueryWrapper.create().select("id").from(Store.class)
                            .like("name", filter.getValue());

                    dto.addCustom("store", w -> w.in("store_id", subWrapper));
                } else if ("custom".equals(filter.getType()) && "user".equals(filter.getField())) {
                    QueryWrapper subWrapper = QueryWrapper.create().select("id").from(ClientUser.class)
                            .like("realname", filter.getValue())
                            .or(ww -> ww.like("mobile", filter.getValue()), true)
                            .or(ww -> ww.like("nickname", filter.getValue()), true);
                    dto.addCustom("user", w -> w.in("user_id", subWrapper));
                } else if ("payTime".equals(filter.getField())) {
                    if (filter.getValue() instanceof List) {
                        List<LocalDateTime> newVar = Lists.newArrayList();
                        for (Object o : (List) filter.getValue()) {
                            newVar.add(DateUtils.parseLocalDateTime(Long.parseLong(o.toString())));
                        }
                        filter.setValue(newVar);
                    } else {
                        filter.setValue(DateUtils.parseLocalDateTime(Long.parseLong(filter.getValue().toString())));
                    }
                }
            }
        }
        return productOrderRepo.selectPageWithRelations(dto);

//        return productOrderRepo.selectPageWithRelations(dto, w->w.orderByUnSafely("case when status = 1 then 1 when status = 0 then 2 when status = 4 then 3 when status = 3 then 4 when status = 6 then 5 when status = 5 then 6 end"));
    }

    /**
     * 获取用户已购买商品数量
     *
     * @param userId    用户ID
     * @param productId 商品ID
     * @return 已购买数量
     */
    public Integer getUserPurchasedQuantity(Long userId, Long productId) {
        List<ProductOrder> orders = productOrderRepo.selectListWithRelationsByQuery(QueryWrapper.create()
                .eq(ProductOrder::getUserId, userId)
                .in(ProductOrder::getStatus, Lists.newArrayList(OrderStatusEnum.COMPLETED, OrderStatusEnum.PAID, OrderStatusEnum.PENDING_PAYMENT)));

        return orders.stream()
                .flatMap(order -> order.getItems().stream())
                .filter(item -> item.getProductId().equals(productId))
                .mapToInt(ProductOrderItem::getQuantity)
                .sum();
    }

    /**
     * 更新订单
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrder(ProductOrder order) {
        productOrderRepo.update(order);

        if (CollectionUtils.isNotEmpty(order.getItems())) {
            for (ProductOrderItem item : order.getItems()) {
                productOrderItemRepo.save(item);
            }
        }
    }

    /**
     * 保存商品
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveProduct(Product product) {
        if (product.getId() == null) {
            productProductRepo.insert(product);
        } else {
            productProductRepo.update(product);
        }
    }

    /**
     * 删除商品
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteProduct(Long id) {
        productProductRepo.deleteById(id);
    }

    private String generateOrderNo() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    public List<Product> fetchProductByStoreId(Long id) {
        return productProductRepo.selectListWithRelationsByQuery(QueryWrapper.create().eq(Product::getStoreId, id));
    }

    public ProductOrder fetchOrderById(Long entityId) {
        return productOrderRepo.selectOneWithRelationsById(entityId);
    }

    public List<ProductOrder> fetchOrderByParentId(Long id) {
        return productOrderRepo.selectListWithRelationsByQuery(QueryWrapper.create().eq(ProductOrder::getParentId, id));
    }

    public List<ProductOrder> fetchOrderByIds(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return List.of();
        }
        return productOrderRepo.selectListWithRelationsByQuery(QueryWrapper.create().in(ProductOrder::getId, orderIds));
    }

    public List<ProductOrderItem> fetchOrderItemByIds(List<Long> orderItemIds) {
        if (CollectionUtils.isEmpty(orderItemIds)) {
            return List.of();
        }
        return productOrderItemRepo.selectListByQuery(QueryWrapper.create().in(ProductOrderItem::getId, orderItemIds));
    }

    public ProductOrderItem fetchOrderItemByCode(String code) {
        return productOrderItemRepo.selectOneByQuery(QueryWrapper.create().eq(ProductOrderItem::getCode, code));
    }

    public void saveProductOrderItem(ProductOrderItem productOrderItem) {
        productOrderItemRepo.save(productOrderItem);
    }

    public List<ProductOrder> orderStatsByUserId(Long userId) {
        return productOrderRepo.selectListByQuery(QueryWrapper.create().eq(ProductOrder::getUserId, userId).eq(ProductOrder::getMerge, IsEnum.FALSE));
    }

    public ProductOrderItem fetchOrderItemById(Long id) {
        return productOrderItemRepo.selectOneById(id);
    }

    public List<ProductOrder> fetchOrderByStatus(OrderStatusEnum orderStatusEnum) {
        return productOrderRepo.selectListByQuery(QueryWrapper.create().eq(ProductOrder::getStatus, orderStatusEnum));
    }

    public CheckOrderPageBo statsCheckOrderInfo(CheckOrderPageDto dto) {
        if (dto.getFilter() != null) {
            dto.getFilter().removeIf(pageFilter -> "custom".equals(pageFilter.getType()) && "checkSearch".equals(pageFilter.getField()));
        }
        QueryWrapper queryWrapper = dto.filterWrapper();
        List<ProductOrder> orders = productOrderRepo.selectListByQuery(queryWrapper);
        CheckOrderPageBo result = new CheckOrderPageBo();
        result.setTotalOrder(orders.size());
        long count = orders.stream().filter(v -> v.getStatus().equals(OrderStatusEnum.REFUNDED)).count();
        result.setRefundOrder((int) count);
        result.setTotalAmount(orders.stream().mapToInt(ProductOrder::getRealAmount).sum());
        result.setRefundAmount(orders.stream().filter(v -> v.getStatus().equals(OrderStatusEnum.REFUNDED)).mapToInt(ProductOrder::getRealAmount).sum());
        return result;
    }

    public List<ProductOrderItem> fetchItemByCheckAdminUserId(Long userId) {
        return productOrderItemRepo.selectListByQuery(QueryWrapper.create().eq(ProductOrderItem::getCheckAdminUserId, userId));
    }

    public void saveReview(ProductReview review) {
        productReviewRepo.save(review);
    }

    public List<ProductOrder> fetchOrderByStoreIds(List<Long> storeIds) {
        return productOrderRepo.selectListByQuery(QueryWrapper.create().in(ProductOrder::getStoreId, storeIds));
    }

    public Page<ProductOrderItem> fetchOrderItemsPage(CheckOrderPageDto dto, List<Long> storeIds) {
        if (CollectionUtils.isEmpty(storeIds)) {
            return Page.empty();
        }
        QueryWrapper wrapper = QueryWrapper.create().in(ProductOrder::getStoreId, storeIds);
        if (dto.getStartDate() != null) {
            wrapper.ge(ProductOrder::getCreatedAt, dto.getStartDate().atStartOfDay());
        }
        if (dto.getEndDate() != null) {
            wrapper.lt(ProductOrder::getCreatedAt, dto.getEndDate().plusDays(1).atStartOfDay());
        }
        if (dto.getSearchKey() != null) {
            wrapper.and(ww -> ww.like("order_no", dto.getSearchKey())
                    .or(www -> www.in("user_id", QueryWrapper.create().select("id").from(ClientUser.class).like("mobile", dto.getSearchKey())), true), true);
        }
        List<ProductOrder> orders = productOrderRepo.selectListByQuery(wrapper);
        if (CollectionUtils.isEmpty(orders)) {
            return Page.empty();
        }
        Map<Long, ProductOrder> orderMap = orders.stream().collect(Collectors.toMap(ProductOrder::getId, v -> v));
        List<Long> orderIds = orders.stream().map(ProductOrder::getId).collect(Collectors.toList());
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPageNum(dto.getPageNum());
        pageRequest.setPageSize(dto.getPageSize());
        pageRequest.addFilter(new PageFilter().setField("orderId").setType("in").setValue(orderIds));
        pageRequest.addSort(new PageSort().setOrder("asc").setField("use_time"));
        Page<ProductOrderItem> productOrderItemPage = productOrderItemRepo.selectPageByQuery(pageRequest, w -> w.leftJoin("product_order")
                .on(ProductOrderItem::getOrderId, ProductOrder::getId)
                .orderByUnSafely("case when product_order.status = 1 and use_time is null then 1 " +
                        "when product_order.status = 0 then 2 " +
                        "when product_order.status = 4 then 3 " +
                        "when product_order.status = 3 then 4 " +
                        "when product_order.status = 1 and use_time is not null then 5 " +
                        "when product_order.status = 6 then 6 " +
                        "when product_order.status = 5 then 7 end"));
        for (ProductOrderItem item : productOrderItemPage.getList()) {
            item.setOrder(orderMap.get(item.getOrderId()));
        }
        return productOrderItemPage;
    }

    public List<ProductReview> fetchReviewByProductIdsAndUserId(List<Long> list, Long userId) {
        return productReviewRepo.selectListByQuery(QueryWrapper.create().in(ProductReview::getProductId, list).eq(ProductReview::getUserId, userId));
    }

    public List<ProductRefundApply> fetchRefundApplyByOrderIds(List<Long> storeIds) {
        return productRefundApplyRepo.selectListByQuery(QueryWrapper.create().in(ProductRefundApply::getOrderId, storeIds));
    }
}
