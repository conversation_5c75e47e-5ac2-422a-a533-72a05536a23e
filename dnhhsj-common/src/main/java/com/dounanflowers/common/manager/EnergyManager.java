package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.EnergyCell;
import com.dounanflowers.common.repo.EnergyCellRepo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class EnergyManager {
    private final EnergyCellRepo energyCellRepo;


    public Page<EnergyCell> page(PageRequest dto) {
        return energyCellRepo.selectPageWithRelations(dto);
    }

    public void save(EnergyCell energyCell) {
        energyCellRepo.save(energyCell);
    }

    public EnergyCell fetchById(Long id) {
        return energyCellRepo.selectOneById(id);
    }

    public void deleteById(Long id) {
        energyCellRepo.deleteById(id);
    }

    /**
     * 检查是否已经绑定
     */
    public Boolean checkBoundCell(Long propertyCellId, Long adminUserId) {
        EnergyCell energyCell = energyCellRepo.selectOneByQuery(new QueryWrapper().eq(EnergyCell::getPropertyCellId, propertyCellId).eq(EnergyCell::getAdminUserId, adminUserId));
        return energyCell != null;
    }
}
