package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.TricycleCheckStatusEnum;
import com.dounanflowers.common.model.TricycleCount;
import com.dounanflowers.common.model.TricycleIllegalCount;
import com.dounanflowers.common.model.TricycleStatusCount;
import com.dounanflowers.common.repo.*;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Row;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.dounanflowers.common.entity.table.TricycleIllegalTableDef.TRICYCLE_ILLEGAL;
import static com.dounanflowers.common.entity.table.TricycleInfoRecordTableDef.TRICYCLE_INFO_RECORD;
import static com.dounanflowers.common.entity.table.TricycleTableDef.TRICYCLE;
import static com.mybatisflex.core.query.QueryMethods.count;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class TricycleManager {

    private final TricycleRepo tricycleRepo;

    private final TricycleDepositRepo tricycleDepositRepo;

    private final TricycleIllegalRepo tricycleIllegalRepo;

    private final TricycleIllegalRuleRepo tricycleIllegalRuleRepo;

    private final TricycleBatchRepo tricycleBatchRepo;

    private final TricycleInfoRecordRepo tricycleInfoRecordRepo;

    public Page<TricycleIllegalRule> pageIllegalRule(PageRequest pageRequest) {
        return tricycleIllegalRuleRepo.selectPageByQuery(pageRequest);
    }

    public Boolean deleteIllegalRule(Long id) {
        return tricycleIllegalRuleRepo.deleteById(id) > 0;
    }

    public void saveIllegalRule(TricycleIllegalRule tricycleIllegalRule) {
        tricycleIllegalRuleRepo.save(tricycleIllegalRule);
    }

    public Tricycle fetchTricycleById(Long tricycleId) {
        return tricycleRepo.selectOneWithRelationsById(tricycleId);
    }

    public TricycleIllegalRule fetchIllegalRuleById(Long ruleId) {
        return tricycleIllegalRuleRepo.selectOneById(ruleId);
    }

    public TricycleIllegal fetchUnEndIllegalByTricycleAndRule(Long tricycleId, Long ruleId) {
        return tricycleIllegalRepo.selectOneByQuery(QueryWrapper.create().eq(TricycleIllegal::getTricycleId, tricycleId)
                .eq(TricycleIllegal::getRuleId, ruleId).eq(TricycleIllegal::getIsEnd, IsEnum.FALSE));
    }

    public void saveIllegal(TricycleIllegal tricycleIllegal) {
        tricycleIllegalRepo.save(tricycleIllegal);
    }

    public List<TricycleIllegalRule> listAvailableIllegalRule() {
        return tricycleIllegalRuleRepo
                .selectListByQuery(QueryWrapper.create().eq(TricycleIllegalRule::getStatus, IsEnum.TRUE));
    }

    public Page<TricycleIllegal> pageIllegal(PageRequest pageRequest) {
        return tricycleIllegalRepo.selectPageWithRelations(pageRequest);
    }

    public TricycleIllegal fetchIllegalById(Long id) {
        return tricycleIllegalRepo.selectOneWithRelationsById(id);
    }

    public List<TricycleIllegal> fetchIllegalByTricycleId(Long id) {
        return tricycleIllegalRepo.selectListByQuery(QueryWrapper.create().eq(TricycleIllegal::getTricycleId, id));
    }

    public String getMaxTricycleNo() {
        return tricycleRepo.selectOneByQueryAs(QueryWrapper.create().select("max(number)").where("number ~ '^[0-9]+$'"),
                String.class);
    }

    public void save(Tricycle tricycle) {
        tricycleRepo.save(tricycle);
    }

    public void saveDeposit(TricycleDeposit deposit) {
        tricycleDepositRepo.save(deposit);
    }

    public Page<Tricycle> pageList(PageRequest dto) {
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("checkStatus".equals(filter.getField())) {
                    filter.setValue(BaseEnum.ordinalOf(TricycleCheckStatusEnum.class, filter.getValue().toString()));
                } else if ("merchantUserObj".equals(filter.getField()) && "custom".equals(filter.getType())) {
                    QueryWrapper subWrapper = QueryWrapper.create().select("id").from(AdminUser.class)
                            .and(and -> and.like("username", filter.getValue())
                                    .or(or -> or.like("nickname", filter.getValue()), true)
                                    .or(or -> or.like("mobile", filter.getValue()), true)
                                    .or(or -> or.like("realname", filter.getValue()), true), true);
                    dto.addCustom("merchantUserObj", w -> w.in("user_id", subWrapper));
                } else if ("numCode".equals(filter.getField())) {
                    filter.setField("number");
                } else if ("batchObj".equals(filter.getField())) {
                    QueryWrapper subWrapper = filter
                            .wrapper(QueryWrapper.create().select("id").from(TricycleBatch.class), "name");
                    filter.setType("sub");
                    filter.setField("batchId");
                    dto.addSubQuery("batchId", w -> w.in("batch_id", subWrapper));
                } else if ("search".equals(filter.getField()) && "custom".equals(filter.getType())) {
                    QueryWrapper subWrapper = QueryWrapper.create().select("id").from(AdminUser.class)
                            .and(and -> and.like("username", filter.getValue())
                                    .or(or -> or.like("nickname", filter.getValue()), true)
                                    .or(or -> or.like("mobile", filter.getValue()), true)
                                    .or(or -> or.like("realname", filter.getValue()), true)
                                    .or(or -> or.likeRight("number", filter.getValue()), true), true);
                    dto.addCustom("search", w -> w.in("user_id", subWrapper));
                }
            }
        }
        return tricycleRepo.selectPageWithRelations(dto);
    }

    public Page<TricycleDeposit> tricycleDepositPage(PageRequest dto) {
        return tricycleDepositRepo.selectPageByQuery(dto);
    }

    public List<Tricycle> fetchTricycleListByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return tricycleRepo.selectListByIds(ids);
    }

    public TricycleDeposit fetchDepositById(Long id) {
        return tricycleDepositRepo.selectOneById(id);
    }

    public List<TricycleCount> fetchTricycleByUserIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return List.of();
        }
        return tricycleRepo.selectListByQueryAs(QueryWrapper.create().in(Tricycle::getUserId, userIds)
                .select(TRICYCLE.USER_ID, count(TRICYCLE.USER_ID))
                .groupBy(Tricycle::getUserId), TricycleCount.class);
    }

    public List<TricycleIllegalCount> fetchUnEndTricycleIllegalByUserIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return List.of();
        }
        return tricycleIllegalRepo.selectListByQueryAs(QueryWrapper.create().in(TricycleIllegal::getUserId, userIds)
                .eq(TricycleIllegal::getIsEnd, IsEnum.FALSE)
                .select(TRICYCLE_ILLEGAL.USER_ID, count(TRICYCLE_ILLEGAL.USER_ID))
                .groupBy(TricycleIllegal::getUserId), TricycleIllegalCount.class);
    }

    public List<Tricycle> tricycleListByUser(Long userId) {
        return tricycleRepo.selectListWithRelationsByQuery(QueryWrapper.create().eq(Tricycle::getUserId, userId));
    }

    public List<TricycleIllegal> fetchUnEndIllegalByTricycleIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return tricycleIllegalRepo.selectListByQuery(QueryWrapper.create().in(TricycleIllegal::getTricycleId, ids)
                .eq(TricycleIllegal::getIsEnd, IsEnum.FALSE));
    }

    public List<TricycleStatusCount> countTricycleByStatus() {
        return tricycleRepo.selectListByQueryAs(QueryWrapper.create()
                .select(TRICYCLE.CHECK_STATUS, count(TRICYCLE.CHECK_STATUS))
                .where(TRICYCLE.USER_ID.isNotNull())
                .groupBy(Tricycle::getCheckStatus), TricycleStatusCount.class);
    }

    public List<Tricycle> tricycleDepositLowList(Long userId, int depositWarnAmount) {
        return tricycleRepo.selectListWithRelationsByQuery(QueryWrapper.create()
                .eq(Tricycle::getUserId, userId)
                .eq(Tricycle::getCheckStatus, TricycleCheckStatusEnum.APPROVED)
                .le(Tricycle::getDeposit, depositWarnAmount));
    }

    public void saveBatch(TricycleBatch batch) {
        tricycleBatchRepo.save(batch);
    }

    public void insertBatch(List<Tricycle> tricycles) {
        tricycleRepo.insertBatch(tricycles);
    }

    public TricycleBatch fetchBatchById(Long batchId) {
        return tricycleBatchRepo.selectOneById(batchId);
    }

    public void updateBatch(TricycleBatch batch) {
        tricycleBatchRepo.update(batch);
    }

    public Page<TricycleBatch> tricycleBatchPage(PageRequest dto) {
        return tricycleBatchRepo.selectPageByQuery(dto);
    }

    public List<Tricycle> fetchTricycleByBatchId(Long batchId) {
        return tricycleRepo.selectListByQuery(QueryWrapper.create().eq(Tricycle::getBatchId, batchId));
    }

    public List<TricycleBatch> fetchBatchByIds(List<Long> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return List.of();
        }
        return tricycleBatchRepo.selectListByIds(batchIds);
    }

    public List<Tricycle> fetchTricyclesByStatusAndIds(TricycleCheckStatusEnum status, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return tricycleRepo
                .selectListByQuery(QueryWrapper.create().in(Tricycle::getId, ids).eq(Tricycle::getCheckStatus, status));
    }

    public List<Tricycle> fetchTricycleByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return tricycleRepo.selectListByIds(ids);
    }

    public void deleteTricycle(Long id) {
        tricycleRepo.deleteById(id);
    }

    public List<Tricycle> fetchTricyclesByStatus(TricycleCheckStatusEnum status) {
        return tricycleRepo.selectListByQuery(QueryWrapper.create().eq(Tricycle::getCheckStatus, status));
    }

    public void updateTricycleWithNull(Tricycle tricycle) {
        tricycleRepo.update(tricycle, false);
    }

    public Page<TricycleInfoRecord> tricycleInfoRecordPage(PageRequest dto) {

        return tricycleInfoRecordRepo.selectPageByQuery(dto);
    }

    public void saveTricycleInfoRecord(TricycleInfoRecord record) {
        tricycleInfoRecordRepo.save(record);
    }

    public Map<Long, Integer> countTricycleInfoRecordByTricycleIds(List<Long> tricycleIds) {
        List<Row> count = tricycleInfoRecordRepo.selectListByQueryAs(QueryWrapper.create()
                .select(TRICYCLE_INFO_RECORD.TRICYCLE_ID, count(TRICYCLE_INFO_RECORD.TRICYCLE_ID).as("count"))
                .in(TricycleInfoRecord::getTricycleId, tricycleIds)
                .groupBy(TRICYCLE_INFO_RECORD.TRICYCLE_ID), Row.class);
        return count.stream().collect(Collectors.toMap(v -> (Long) v.get("tricycle_id"), v -> ((Long) v.get("count")).intValue()));

    }

}
