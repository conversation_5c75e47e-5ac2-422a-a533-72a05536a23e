package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.MsgCategory;
import com.dounanflowers.common.entity.MsgDetail;
import com.dounanflowers.common.entity.MsgTemplate;
import com.dounanflowers.common.enums.MsgProcessStatusEnum;
import com.dounanflowers.common.enums.MsgTypeEnum;
import com.dounanflowers.common.repo.MsgCategoryRepo;
import com.dounanflowers.common.repo.MsgDetailRepo;
import com.dounanflowers.common.repo.MsgTemplateRepo;
import com.dounanflowers.framework.annotation.Cached;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.BaseEnum;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class MsgManager {

    private final MsgCategoryRepo msgCategoryRepo;
    private final MsgDetailRepo msgDetailRepo;
    private final MsgTemplateRepo msgTemplateRepo;

    public void saveMsg(MsgDetail msgDetail) {
        msgDetailRepo.save(msgDetail);
    }

    public Page<MsgDetail> pageList(PageRequest dto) {
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("categoryCode".equals(filter.getField())) {
                    QueryWrapper subQuery = QueryWrapper.create().select("id").from(MsgCategory.class).eq(MsgCategory::getCode, filter.getValue());
                    filter.setType("sub");
                    dto.addSubQuery("categoryCode", w -> w.in(MsgDetail::getCategoryId, subQuery));
                } else if ("type".equals(filter.getField())) {
                    filter.setField("type");
                    filter.setValue(BaseEnum.ordinalOf(MsgTypeEnum.class, filter.getValue() + ""));
                } else if ("status".equals(filter.getField())) {
                    filter.setField("status");
                    filter.setValue(BaseEnum.ordinalOf(MsgProcessStatusEnum.class, filter.getValue() + ""));
                }
            }
        }
        return msgDetailRepo.selectPageByQuery(dto);
    }

    public List<MsgTemplate> fetchTemplateByCode(String templateCode) {
        return msgTemplateRepo.selectListByQuery(QueryWrapper.create().eq(MsgTemplate::getCode, templateCode));
    }

    public void insertMsgList(List<MsgDetail> msgDetails) {
        msgDetailRepo.insertBatch(msgDetails);
    }

    public List<MsgDetail> fetchTodoMsgByOuterTypeAndId(String outerType, Long id) {
        return msgDetailRepo.selectListByQuery(QueryWrapper.create().eq(MsgDetail::getType, MsgTypeEnum.TODO)
                .eq(MsgDetail::getOuterType, outerType)
                .eq(MsgDetail::getOuterId, id));
    }

    public MsgDetail fetchById(Long id) {
        return msgDetailRepo.selectOneById(id);
    }

    @Cached(value = "msg:category")
    public List<MsgCategory> listAllCategories() {
        return msgCategoryRepo.selectListByQuery(QueryWrapper.create());
    }

}
