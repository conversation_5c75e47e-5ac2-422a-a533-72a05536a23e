package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.ClientUser;
import com.dounanflowers.common.entity.ProductRefundApply;
import com.dounanflowers.common.entity.Store;
import com.dounanflowers.common.enums.RefundStatusEnum;
import com.dounanflowers.common.repo.ProductRefundApplyRepo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.DateUtils;
import com.google.common.collect.Lists;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
@RequiredArgsConstructor
public class ProductRefundManager {

    private final ProductRefundApplyRepo refundApplyRepo;

    public void createApply(ProductRefundApply refundApply) {
        // 检查是否已经存在退款申请
        QueryWrapper wrapper = QueryWrapper.create().eq(ProductRefundApply::getOrderId, refundApply.getOrderId());
        if (refundApply.getOrderItemId() != null) {
            wrapper.eq(ProductRefundApply::getOrderItemId, refundApply.getOrderItemId());
        }
        wrapper.eq(ProductRefundApply::getStatus, RefundStatusEnum.PENDING);
        ProductRefundApply exists = refundApplyRepo.selectOneByQuery(wrapper);
        if (exists != null) {
            throw new BaseException("该订单项已存在退款申请");
        }
        refundApplyRepo.insert(refundApply);
    }

    public ProductRefundApply fetchById(Long id) {
        return refundApplyRepo.selectOneWithRelationsById(id);
    }

    public Page<ProductRefundApply> pageList(PageRequest dto) {
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("custom".equals(filter.getType()) && "store".equals(filter.getField())) {
                    QueryWrapper subWrapper = QueryWrapper.create().select("id").from(Store.class)
                            .like("name", filter.getValue()).or(ww -> ww.eq("id", Long.parseLong(filter.getValue().toString())), true);
                    dto.addCustom("store", w -> w.in("store_id", subWrapper));
                } else if ("custom".equals(filter.getType()) && "user".equals(filter.getField())) {
                    QueryWrapper subWrapper = QueryWrapper.create().select("id").from(ClientUser.class)
                            .like("realname", filter.getValue())
                            .or(ww -> ww.like("mobile", filter.getValue()), true)
                            .or(ww -> ww.like("nickname", filter.getValue()), true);
                    dto.addCustom("user", w -> w.in("user_id", subWrapper));
                } else if ("handleTime".equals(filter.getField())) {
                    if (filter.getValue() instanceof List) {
                        List<LocalDateTime> newVar = Lists.newArrayList();
                        for (Object o : (List) filter.getValue()) {
                            newVar.add(DateUtils.parseLocalDateTime(Long.parseLong(o.toString())));
                        }
                        filter.setValue(newVar);
                    } else {
                        filter.setValue(DateUtils.parseLocalDateTime(Long.parseLong(filter.getValue().toString())));
                    }
                }
            }
        }
        return refundApplyRepo.selectPageWithRelations(dto);
    }

    public List<ProductRefundApply> fetchByIds(List<Long> ids) {
        return refundApplyRepo.selectListByIds(ids);
    }

    public void updateStatus(Long id, RefundStatusEnum status, String handleNote) {
        ProductRefundApply refundApply = fetchById(id);
        refundApply.setStatus(status);
        refundApply.setHandleNote(handleNote);
        refundApply.setHandleTime(LocalDateTime.now());
        refundApplyRepo.update(refundApply);
    }

    public void delete(Long id) {
        refundApplyRepo.deleteById(id);
    }

    public void saveRefunded(Long id, IsEnum isEnum) {
        ProductRefundApply refundApply = fetchById(id);
        refundApply.setRefunded(isEnum);
        refundApplyRepo.update(refundApply);
    }

}
