package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.ModalBanner;
import com.dounanflowers.common.enums.ClientTypeEnum;
import com.dounanflowers.common.repo.ModalBannerRepo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.utils.DateUtils;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

import static com.dounanflowers.common.entity.table.ModalBannerTableDef.MODAL_BANNER;

@Component
@RequiredArgsConstructor
public class ModalBannerManager {

    private final ModalBannerRepo modalBannerRepo;

    public Page<ModalBanner> pageList(PageRequest dto) {
        if (dto.getFilter() != null) {
            dto.getFilter().forEach(filter -> {
                if ("startAt".equals(filter.getField()) && "range".equals(filter.getType())) {
                    List<?> list = (List<?>) filter.getValue();
                    filter.setValue(list.stream().map(v -> DateUtils.parseLocalDateTime(Long.parseLong(v.toString()))).toList());
                }
            });
        }

        return modalBannerRepo.selectPageWithRelations(dto);
    }

    public ModalBanner fetchById(Long id) {
        return modalBannerRepo.selectOneById(id);
    }

    public void save(ModalBanner modalBanner) {
         modalBannerRepo.save(modalBanner);
    }

    public void deleteById(Long id) {
        modalBannerRepo.deleteById(id);
    }

    public List<ModalBanner> findActiveByClient(ClientTypeEnum client) {
        return modalBannerRepo.selectListByQuery(
                QueryWrapper.create()
                        .eq(ModalBanner::getClient, client)
                        .eq(ModalBanner::getStatus, IsEnum.TRUE)
                        .and(MODAL_BANNER.START_AT.isNull().or(MODAL_BANNER.START_AT.le(LocalDateTime.now())))
                        .and(MODAL_BANNER.END_AT.isNull().or(MODAL_BANNER.END_AT.ge(LocalDateTime.now())))


        );
    }
}
