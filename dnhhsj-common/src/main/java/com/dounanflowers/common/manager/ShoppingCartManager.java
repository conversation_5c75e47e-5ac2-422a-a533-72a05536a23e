package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.ShoppingCart;
import com.dounanflowers.common.repo.ShoppingCartRepo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.dounanflowers.common.entity.table.ShoppingCartTableDef.SHOPPING_CART;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ShoppingCartManager {

    private final ShoppingCartRepo shoppingCartRepo;

    public Page<ShoppingCart> pageList(PageRequest dto) {
        return shoppingCartRepo.selectPageWithRelations(dto);
    }

    public void save(ShoppingCart shoppingCart) {
        shoppingCartRepo.save(shoppingCart);
    }

    public ShoppingCart fetchById(Long id) {
        return shoppingCartRepo.selectOneWithRelationsById(id);
    }

    public void deleteById(Long id) {
        shoppingCartRepo.deleteById(id);
    }

    public List<ShoppingCart> fetchByUserId(Long userId) {
        return shoppingCartRepo.selectListWithRelationsByQuery(QueryWrapper.create()
                .where(SHOPPING_CART.USER_ID.eq(userId)));
    }

    public ShoppingCart fetchByUserIdAndProductId(Long userId, Long productId) {
        return shoppingCartRepo.selectOneWithRelationsByQuery(QueryWrapper.create()
                .where(SHOPPING_CART.USER_ID.eq(userId))
                .and(SHOPPING_CART.PRODUCT_ID.eq(productId)));
    }

    public void deleteByUserId(Long userId) {
        shoppingCartRepo.deleteByQuery(QueryWrapper.create()
                .where(SHOPPING_CART.USER_ID.eq(userId)));
    }

    public List<ShoppingCart> fetchByIds(List<Long> cartIds) {
        return shoppingCartRepo.selectListByIds(cartIds);
    }

    public void deleteByIds(List<Long> cartIds) {
        shoppingCartRepo.deleteBatchByIds(cartIds);
    }

    public Integer countByUserId(Long id) {
        return (int) shoppingCartRepo.selectCountByQuery(
                QueryWrapper.create().eq(ShoppingCart::getUserId, id));
    }

    public List<ShoppingCart> fetchByProductId(Long id) {
        return shoppingCartRepo.selectListByQuery(QueryWrapper.create().eq(ShoppingCart::getProductId, id));
    }

    public void deleteByProductId(Long id) {
        shoppingCartRepo.deleteByQuery(QueryWrapper.create().eq(ShoppingCart::getProductId, id));
    }
}
