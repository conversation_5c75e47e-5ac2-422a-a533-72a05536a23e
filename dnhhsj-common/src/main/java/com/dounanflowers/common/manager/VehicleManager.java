package com.dounanflowers.common.manager;

import com.dounanflowers.common.bo.VehicleBo;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.*;
import com.dounanflowers.common.repo.VehicleMonthlyCardOrderRepo;
import com.dounanflowers.common.repo.VehicleMonthlyCardRepo;
import com.dounanflowers.common.repo.VehiclePlateChangeRepo;
import com.dounanflowers.common.repo.VehicleRepo;
import com.dounanflowers.common.service.AdminMenuService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.bean.PageSort;
import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.DbChain;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Stream;

import static com.dounanflowers.common.entity.table.VehicleTableDef.VEHICLE;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class VehicleManager {

    private final VehicleRepo vehicleRepo;
    private final VehicleMonthlyCardRepo monthlyCardRepo;
    private final VehicleMonthlyCardOrderRepo monthlyCardOrderRepo;
    private final VehiclePlateChangeRepo vehiclePlateChangeRepo;
    private final AdminMenuService adminMenuService;

    public void save(Vehicle vehicle) {
        vehicleRepo.save(vehicle);
        adminMenuService.emitCount(AdminMenuEventEnum.VEHICLE_CHECK);
        adminMenuService.emitCount(AdminMenuEventEnum.VEHICLE_REFUND_CHECK);
    }

    public void hideOtherSamePlate(Vehicle vehicle) {
        if (vehicle == null || vehicle.getCheckStatus() != VehicleCheckStatusEnum.EFFECTIVE) {
            return;
        }

        Vehicle oldVehicle = vehicleRepo.selectOneByQuery(
                QueryWrapper.create()
                        .eq(Vehicle::getShow, IsEnum.TRUE)
                        .eq(Vehicle::getNumber, vehicle.getNumber())
                        .eq(Vehicle::getCheckStatus, VehicleCheckStatusEnum.EFFECTIVE)
                        .ne(Vehicle::getId, vehicle.getId()));
        System.out.println("oldVehicle");
        System.out.println(oldVehicle);
        if (oldVehicle == null || oldVehicle.getRootId() == null) {
            vehicle.setRootId(vehicle.getId());
            System.out.println("1");
        } else {
            vehicle.setRootId(oldVehicle.getRootId());
            System.out.println("2");
        }
        vehicleRepo.save(vehicle);
        System.out.println("vehicle");
        System.out.println(vehicle);

        Vehicle updateVehicle = new Vehicle();
        updateVehicle.setShow(IsEnum.FALSE);

        vehicleRepo.updateByQuery(
                updateVehicle,
                QueryWrapper.create()
                        .eq(Vehicle::getShow, IsEnum.TRUE)
                        .eq(Vehicle::getNumber, vehicle.getNumber())
                        .eq(Vehicle::getCheckStatus, VehicleCheckStatusEnum.EFFECTIVE)
                        .ne(Vehicle::getId, vehicle.getId()));
        adminMenuService.emitCount(AdminMenuEventEnum.VEHICLE_CHECK);
        adminMenuService.emitCount(AdminMenuEventEnum.VEHICLE_REFUND_CHECK);
    }

    public Page<Vehicle> pageList(PageRequest dto) {
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("checkStatus".equals(filter.getField())) {
                    filter.setValue(BaseEnum.ordinalOf(VehicleCheckStatusEnum.class, filter.getValue().toString()));
                } else if ("merchantUserObj".equals(filter.getField()) && "custom".equals(filter.getType())) {
                    QueryWrapper subWrapper = QueryWrapper.create().select("id").from(AdminUser.class)
                            .like("username", filter.getValue())
                            .or(or -> or.like("nickname", filter.getValue()), true)
                            .or(or -> or.like("mobile", filter.getValue()), true)
                            .or(or -> or.like("realname", filter.getValue()), true);
                    dto.addCustom("merchantUserObj", w -> w.in("user_id", subWrapper));
                } else if ("numberFirst".equals(filter.getField()) && "custom".equals(filter.getType())) {
                    dto.addSort(new PageSort().setConsumer(
                            w -> w.orderBy("number='" + filter.getValue() + "'", false).orderBy("id", false)));
                } else if ("todo".equals(filter.getField()) && "custom".equals(filter.getType())) {
                    List<Long> todoIds = this.getTodoVehicleIds();
                    if(todoIds.isEmpty()) return Page.empty();
                    filter.setType("in");
                    filter.setField("id");
                    filter.setValue(todoIds);
                }
            }
        }
        return vehicleRepo.selectPageWithRelations(dto);
    }

    public List<Long> getTodoVehicleIds() {
        List<Long> ids1 = vehicleRepo.selectListByQuery(
                QueryWrapper.create().select(VEHICLE.ID).where(VEHICLE.CHECK_STATUS.eq(VehicleCheckStatusEnum.UNCHECKED).or(VEHICLE.REFUND_STATUS.eq(VehicleRefundStatusEnum.WAIT_CHECK))
        )).stream().map(r -> r.getId()).toList();
        List<Long> ids2 = DbChain.table("vehicle_plate_change")
                .select("vehicle_id")
                .where("change_status = ?", VehiclePlateChangeStatusEnum.WAIT_CHANGE)
                .groupBy("vehicle_id")
                .list().stream().map(r -> r.getLong("vehicle_id")).toList();
        return Stream.of(ids1, ids2).flatMap(List::stream).distinct().toList();
    }

    public List<Vehicle> fetchVehicleListByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return vehicleRepo.selectListByIds(ids);
    }

    public List<Vehicle> vehicleListByUser(Long userId) {
        return vehicleRepo.selectListWithRelationsByQuery(QueryWrapper.create().eq(Vehicle::getUserId, userId));
    }

    public Vehicle fetchVehicleById(Long id) {
        return vehicleRepo.selectOneById(id);
    }

    // 月卡相关方法
    public void saveMonthlyCard(VehicleMonthlyCard monthlyCard) {
        monthlyCardRepo.save(monthlyCard);
    }

    public VehicleMonthlyCard getMonthlyCard(Long id) {
        return monthlyCardRepo.selectOneById(id);
    }

    public Page<VehicleMonthlyCard> pageMonthlyCards(PageRequest pageRequest) {
        return monthlyCardRepo.selectPageByQuery(pageRequest);
    }

    // 月卡订单相关方法
    public void saveMonthlyCardOrder(VehicleMonthlyCardOrder order) {
        monthlyCardOrderRepo.save(order);
    }

    public VehicleMonthlyCardOrder getMonthlyCardOrder(Long id) {
        return monthlyCardOrderRepo.selectOneById(id);
    }

    public VehicleMonthlyCardOrder getMonthlyCardOrderByVehicleId(Long vehicleId) {
        return monthlyCardOrderRepo.selectOneByQuery(QueryWrapper.create().eq(VehicleMonthlyCardOrder::getVehicleId, vehicleId).orderBy(VehicleMonthlyCardOrder::getCreatedAt, false));
    }

    public Page<VehicleMonthlyCardOrder> pageMonthlyCardOrders(PageRequest pageRequest) {
        return monthlyCardOrderRepo.selectPageByQuery(pageRequest);
    }

    public void deleteMonthlyCard(VehicleMonthlyCard monthlyCard) {
        monthlyCardRepo.delete(monthlyCard);
    }

    public VehicleMonthlyCard fetchMonthlyCardById(Long monthlyCardId) {
        return monthlyCardRepo.selectOneById(monthlyCardId);
    }

    public List<VehicleMonthlyCard> fetchMonthlyCardByIds(List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return List.of();
        }
        return monthlyCardRepo.selectListByIds(list);
    }

    public List<Vehicle> fetchVehiclesByStatus(VehicleCheckStatusEnum vehicleCheckStatusEnum) {
        return vehicleRepo.selectListWithRelationsByQuery(
                QueryWrapper.create().eq(Vehicle::getCheckStatus, vehicleCheckStatusEnum));
    }

    public List<VehicleMonthlyCard> fetchMonthlyCardsByIds(List<Long> monthCardIds) {
        if (CollectionUtils.isEmpty(monthCardIds)) {
            return List.of();
        }
        return monthlyCardRepo.selectListByIds(monthCardIds);
    }

    public Vehicle fetchCheckingOrPaying(String oldPlate) {
        return vehicleRepo.selectOneByQuery(
            QueryWrapper.create()
                    .eq(Vehicle::getNumber, oldPlate)
                    .eq(Vehicle::getShow, IsEnum.TRUE)
                    .in(Vehicle::getCheckStatus, VehicleCheckStatusEnum.UNCHECKED, VehicleCheckStatusEnum.PENDING_DEPOSIT)
        );
    }

    public Boolean plateChangeNeedPay(Long vehicleId) {
        VehiclePlateChange lastVehiclePlateChange = vehiclePlateChangeRepo.selectOneByQuery(
                QueryWrapper.create()
                        .eq(VehiclePlateChange::getVehicleId, vehicleId)
                        .eq(VehiclePlateChange::getChangeStatus, VehiclePlateChangeStatusEnum.DONE)
                        .gt(VehiclePlateChange::getCreatedAt, LocalDateTime.now().minusDays(30))
                        .orderBy(VehiclePlateChange::getId, false)
        );
        return lastVehiclePlateChange != null;
    }

    public void saveVehiclePlateChange(VehiclePlateChange vehiclePlateChange) {
        vehiclePlateChangeRepo.save(vehiclePlateChange);
        adminMenuService.emitCount(AdminMenuEventEnum.VEHICLE_PLATE_CHANGE_CHECK);
    }

    public VehiclePlateChange fetchVehiclePlateChangeById(Long vehiclePlateChangeId) {
        return vehiclePlateChangeRepo.selectOneById(vehiclePlateChangeId);
    }

    public Map<Long, VehiclePlateChange> fetchLastVehiclePlateChangeByVehicleIds(List<Long> vehicleIds) {
        List<VehiclePlateChange> list = vehiclePlateChangeRepo.selectListByQuery(
                QueryWrapper.create()
                        .where(VehiclePlateChange::getVehicleId).in(vehicleIds)
        );
        Map<Long, VehiclePlateChange> map = new HashMap<>();
        for (VehiclePlateChange vehiclePlateChange : list) {
            if(map.get(vehiclePlateChange.getVehicleId()) != null && map.get(vehiclePlateChange.getVehicleId()).getCreatedAt().isAfter(vehiclePlateChange.getCreatedAt())) {
                continue;
            }
            map.put(vehiclePlateChange.getVehicleId(), vehiclePlateChange);
        }
        return map;
    }

    public Vehicle getEffectiveVehicleByPlate(String plate) {
        return vehicleRepo.selectOneByQuery(
                QueryWrapper.create()
                        .eq(Vehicle::getNumber, plate)
                        .eq(Vehicle::getShow, IsEnum.TRUE)
                        .eq(Vehicle::getCheckStatus, VehicleCheckStatusEnum.EFFECTIVE)
        );
    }


    public Vehicle getCheckingVehicleByNumber(String plate) {
        return vehicleRepo.selectOneByQuery(
                QueryWrapper.create()
                        .eq(Vehicle::getNumber, plate)
                        .eq(Vehicle::getShow, IsEnum.TRUE)
                        .in(Vehicle::getCheckStatus, VehicleCheckStatusEnum.UNCHECKED, VehicleCheckStatusEnum.PENDING_DEPOSIT)
        );
    }

    public List<Vehicle> sameRootHiddenVehicles(Long rootId) {
        return vehicleRepo.selectListWithRelationsByQuery(
                QueryWrapper.create()
                        .eq(Vehicle::getRootId, rootId)
                        .eq(Vehicle::getShow, IsEnum.FALSE)
                        .orderBy(Vehicle::getId, false)
        );
    }
}
