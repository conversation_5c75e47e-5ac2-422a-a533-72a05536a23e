package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.WithdrawAccount;
import com.dounanflowers.common.entity.WithdrawAccountRecord;
import com.dounanflowers.common.entity.WithdrawConfig;
import com.dounanflowers.common.entity.WithdrawRecord;
import com.dounanflowers.common.enums.AccountDirectionEnum;
import com.dounanflowers.common.enums.AccountRecordTypeEnum;
import com.dounanflowers.common.enums.AdminMenuEventEnum;
import com.dounanflowers.common.enums.WithdrawStatusEnum;
import com.dounanflowers.common.repo.WithdrawAccountRecordRepo;
import com.dounanflowers.common.repo.WithdrawAccountRepo;
import com.dounanflowers.common.repo.WithdrawConfigRepo;
import com.dounanflowers.common.repo.WithdrawRecordRepo;
import com.dounanflowers.common.service.AdminMenuService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Component
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class WithdrawManager {

    private final WithdrawConfigRepo withdrawConfigRepo;
    private final WithdrawRecordRepo withdrawRecordRepo;
    private final WithdrawAccountRepo withdrawAccountRepo;
    private final WithdrawAccountRecordRepo withdrawAccountRecordRepo;
    private final AdminMenuService adminMenuService;

    public WithdrawConfig getConfig(Long userId) {
        return withdrawConfigRepo.selectOneByQuery(QueryWrapper.create().eq(WithdrawConfig::getUserId, userId));
    }

    public void saveConfig(WithdrawConfig config) {
        withdrawConfigRepo.save(config);
    }

    public WithdrawAccount getAccountByUserId(Long userId) {
        WithdrawAccount account = withdrawAccountRepo.selectOneByQuery(
                QueryWrapper.create().eq(WithdrawAccount::getUserId, userId));
        if (account == null) {
            account = new WithdrawAccount()
                    .setUserId(userId)
                    .setTotalIncome(0)
                    .setTotalWithdraw(0)
                    .setTotalFee(0)
                    .setTotalOrders(0)
                    .setAvailableAmount(0)
                    .setFrozenAmount(0);
            withdrawAccountRepo.insert(account);
        }
        return account;
    }

    public void createWithdrawRecord(WithdrawRecord record) {
        WithdrawAccount account = getAccountByUserId(record.getUserId());
        if (account.getAvailableAmount() < record.getAmount() || account.getAvailableAmount() == 0) {
            throw new BaseException("可提现余额不足");
        }
        // 更新账户金额
        int beforeBalance = account.getAvailableAmount();
        account.setAvailableAmount(account.getAvailableAmount() - record.getAmount() - record.getFee())
                .setFrozenAmount(account.getFrozenAmount() + record.getAmount() + record.getFee());
        withdrawAccountRepo.update(account);
        record.setStatus(WithdrawStatusEnum.REVIEWING);
        withdrawRecordRepo.insert(record);
        adminMenuService.emitCount(AdminMenuEventEnum.WITHDRAW_APPROVE);

        // 记录账目变动
        WithdrawAccountRecord accountRecord = new WithdrawAccountRecord()
                .setUserId(record.getUserId())
                .setRelatedId(record.getId())
                .setType(AccountRecordTypeEnum.WITHDRAW)
                .setDirection(AccountDirectionEnum.OUT)
                .setAmount(record.getAmount())
                .setFee(record.getFee())
                .setBeforeBalance(beforeBalance)
                .setAfterBalance(account.getAvailableAmount())
                .setRemark("余额提现(处理中)");
        withdrawAccountRecordRepo.save(accountRecord);
    }

    /**
     * 账户收入
     */
    public void updateAccountIncome(Long userId, Integer amount, Integer fee, Long relationId, AccountRecordTypeEnum type,
                                    String remark) {
        WithdrawAccount account = getAccountByUserId(userId);
        account.setTotalIncome(account.getTotalIncome() + amount)
                .setTotalOrders(account.getTotalOrders() + 1)
                .setAvailableAmount(account.getAvailableAmount() + amount);
        withdrawAccountRepo.update(account);

        // 记录账目变动
        WithdrawAccountRecord accountRecord = new WithdrawAccountRecord()
                .setUserId(userId)
                .setRelatedId(relationId)
                .setType(type)
                .setDirection(AccountDirectionEnum.IN)
                .setAmount(amount)
                .setFee(fee)
                .setBeforeBalance(account.getAvailableAmount() - amount)
                .setAfterBalance(account.getAvailableAmount())
                .setRemark(remark);
        withdrawAccountRecordRepo.save(accountRecord);
    }

    /**
     * 账户支出
     */
    public void updateAccountOutcome(Long userId, Integer amount, Integer fee, Long relationId, AccountRecordTypeEnum type,
                                     String remark) {
        WithdrawAccount account = getAccountByUserId(userId);
        account.setTotalWithdraw(account.getTotalIncome() - amount)
                .setTotalOrders(account.getTotalOrders() - 1)
                .setAvailableAmount(account.getAvailableAmount() - amount);
        withdrawAccountRepo.update(account);

        // 记录账目变动
        WithdrawAccountRecord accountRecord = new WithdrawAccountRecord()
                .setUserId(userId)
                .setRelatedId(relationId)
                .setType(type)
                .setDirection(AccountDirectionEnum.OUT)
                .setAmount(amount)
                .setFee(fee)
                .setBeforeBalance(account.getAvailableAmount() + amount)
                .setAfterBalance(account.getAvailableAmount())
                .setRemark(remark);
        withdrawAccountRecordRepo.save(accountRecord);
    }

    /**
     * 审批通过提现申请
     *
     * @param recordId    提现记录ID
     * @param adminUserId 审批人ID
     * @param remark      备注
     */
    public WithdrawRecord approveWithdraw(Long recordId, Long adminUserId, String remark) {
        WithdrawRecord record = withdrawRecordRepo.selectOneById(recordId);
        if (record == null) {
            throw new BaseException("提现记录不存在");
        }

        if (record.getStatus() != WithdrawStatusEnum.WAITING
                && record.getStatus() != WithdrawStatusEnum.REVIEWING) {
            throw new BaseException("提现记录状态不正确");
        }

        WithdrawAccount account = getAccountByUserId(record.getUserId());

        account.setFrozenAmount(account.getFrozenAmount() - record.getAmount() - record.getFee())
                .setTotalWithdraw(account.getTotalWithdraw() + record.getAmount() + record.getFee());
        withdrawAccountRepo.save(account);

        // 更新提现记录
        record.setStatus(WithdrawStatusEnum.SUCCESS)
                .setCheckAdminUserId(adminUserId)
                .setCheckedAt(LocalDateTime.now())
                .setRemark(remark);
        withdrawRecordRepo.save(record);
        adminMenuService.emitCount(AdminMenuEventEnum.WITHDRAW_APPROVE);
        // 记录账目变动
        WithdrawAccountRecord accountRecord = withdrawAccountRecordRepo.selectOneByQuery(QueryWrapper.create()
                .eq(WithdrawAccountRecord::getUserId, record.getUserId())
                .eq(WithdrawAccountRecord::getRelatedId, record.getId())
                .eq(WithdrawAccountRecord::getType, AccountRecordTypeEnum.WITHDRAW)
                .eq(WithdrawAccountRecord::getDirection, AccountDirectionEnum.OUT));
        accountRecord.setRemark("余额提现(处理完毕)");
        withdrawAccountRecordRepo.update(accountRecord);
        return record;
    }

    /**
     * 拒绝提现申请
     *
     * @param recordId    提现记录ID
     * @param adminUserId 审批人ID
     * @param remark      拒绝原因
     */
    public WithdrawRecord rejectWithdraw(Long recordId, Long adminUserId, String remark) {
        WithdrawRecord record = withdrawRecordRepo.selectOneById(recordId);
        if (record == null) {
            throw new BaseException("提现记录不存在");
        }

        if (record.getStatus() != WithdrawStatusEnum.WAITING
                && record.getStatus() != WithdrawStatusEnum.REVIEWING) {
            throw new BaseException("提现记录状态不正确");
        }

        WithdrawAccount account = getAccountByUserId(record.getUserId());

        // 解冻金额，返还到可用余额
        account.setFrozenAmount(account.getFrozenAmount() - record.getAmount() - record.getFee())
                .setAvailableAmount(account.getAvailableAmount() + record.getAmount() + record.getFee());
        withdrawAccountRepo.update(account);

        // 更新提现记录
        record.setStatus(WithdrawStatusEnum.FAIL)
                .setCheckAdminUserId(adminUserId)
                .setCheckedAt(LocalDateTime.now())
                .setRemark(remark);
        withdrawRecordRepo.update(record);
        adminMenuService.emitCount(AdminMenuEventEnum.WITHDRAW_APPROVE);

        // 记录账目变动
        WithdrawAccountRecord accountRecord = withdrawAccountRecordRepo.selectOneByQuery(QueryWrapper.create()
                .eq(WithdrawAccountRecord::getUserId, record.getUserId())
                .eq(WithdrawAccountRecord::getRelatedId, record.getId())
                .eq(WithdrawAccountRecord::getType, AccountRecordTypeEnum.WITHDRAW)
                .eq(WithdrawAccountRecord::getDirection, AccountDirectionEnum.OUT));
        accountRecord.setRemark("余额提现(拒绝提现) 备注:" + remark);
        accountRecord.setAfterBalance(accountRecord.getBeforeBalance());
        accountRecord.setAmount(0);
        accountRecord.setFee(0);
        withdrawAccountRecordRepo.update(accountRecord);
        return record;
    }

    public Page<WithdrawRecord> getRecordPage(PageRequest pageRequest) {
        return withdrawRecordRepo.selectPageWithRelations(pageRequest);
    }

    public Page<WithdrawAccount> getAccountPage(PageRequest pageRequest) {
        return withdrawAccountRepo.selectPageWithRelations(pageRequest);
    }

    public Page<WithdrawAccountRecord> getAccountRecordPage(PageRequest pageRequest) {
        return withdrawAccountRecordRepo.selectPageWithRelations(pageRequest);
    }

    public List<WithdrawRecord> fetchByIds(List<Long> relatedIds) {
        if (relatedIds.isEmpty()) {
            return List.of();
        }
        return withdrawRecordRepo.selectListByIds(relatedIds);
    }


}
