package com.dounanflowers.common.manager;

import com.dounanflowers.common.bo.UserObjBo;
import com.dounanflowers.common.entity.AdminUser;
import com.dounanflowers.common.entity.ClientUser;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.common.repo.AdminUserRepo;
import com.dounanflowers.common.repo.ClientUserRepo;
import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.utils.BeanUtils;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

@Component
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class UserCommonManager {
    private final AdminUserRepo adminUserRepo;

    private final ClientUserRepo clientUserRepo;
    
    public List<Long> fetchUserIdsByLike(String keyword) {
        List<Long> adminIds = adminUserRepo.selectListByQuery(QueryWrapper.create().select("id")
                .like("username", keyword)
                .or(or -> or.like("nickname", keyword), true)
                .or(or -> or.like("mobile", keyword), true)
                .or(or -> or.like("realname", keyword), true).limit(100)).stream().map(BaseEntity::getId).toList();
        List<Long> clientIds = clientUserRepo.selectListByQuery(QueryWrapper.create().select("id")
                .like("username", keyword)
                .or(or -> or.like("nickname", keyword), true)
                .or(or -> or.like("mobile", keyword), true)
                .or(or -> or.like("realname", keyword), true).limit(100)).stream().map(BaseEntity::getId).toList();
        return Stream.of(adminIds, clientIds).flatMap(List::stream).toList();
    }

    public Map<Long, UserObjBo> fetchUserMapByUserIds(List<Long> userIds) {
        Map<Long, UserObjBo> userMap = new HashMap<>();
        if (CollectionUtils.isEmpty(userIds)) {
            return userMap;
        }

        List<AdminUser> adminUsers = adminUserRepo.selectListByIds(userIds);
        List<ClientUser> clientUsers = clientUserRepo.selectListByIds(userIds);
        adminUsers.forEach(v -> {
            UserObjBo userObjBo = BeanUtils.copy(v, UserObjBo.class);
            userObjBo.setType(UserTypeEnum.ADMIN);
            userMap.put(v.getId(), userObjBo);
        });
        clientUsers.forEach(v -> {
            UserObjBo userObjBo = BeanUtils.copy(v, UserObjBo.class);
            userObjBo.setType(UserTypeEnum.CLIENT);
            userMap.put(v.getId(), userObjBo);
        });
        return userMap;
    }

    public UserObjBo fetchUserObjById(Long clientUserId) {
        AdminUser adminUser = adminUserRepo.selectOneById(clientUserId);
        if (adminUser != null) {
            UserObjBo userObjBo = BeanUtils.copy(adminUser, UserObjBo.class);
            userObjBo.setType(UserTypeEnum.ADMIN);
            return userObjBo;
        }
        ClientUser clientUser = clientUserRepo.selectOneById(clientUserId);
        if (clientUser != null) {
            UserObjBo userObjBo = BeanUtils.copy(clientUser, UserObjBo.class);
            userObjBo.setType(UserTypeEnum.CLIENT);
            return userObjBo;
        }
        return null;
    }
}
