package com.dounanflowers.common.manager;

import com.dounanflowers.common.bo.InvoiceCountBo;
import com.dounanflowers.common.bo.InvoiceHeaderInfoBo;
import com.dounanflowers.common.entity.AdminUser;
import com.dounanflowers.common.entity.Bill;
import com.dounanflowers.common.entity.ClientUser;
import com.dounanflowers.common.entity.Invoice;
import com.dounanflowers.common.enums.AdminMenuEventEnum;
import com.dounanflowers.common.enums.BillPayStatusEnum;
import com.dounanflowers.common.enums.InvoiceStatusEnum;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.common.repo.AdminUserRepo;
import com.dounanflowers.common.repo.BillRepo;
import com.dounanflowers.common.repo.ClientUserRepo;
import com.dounanflowers.common.repo.InvoiceRepo;
import com.dounanflowers.common.service.AdminMenuService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;

import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.DateUtils;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateChain;
import com.mybatisflex.core.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.stream.Stream;

import static com.dounanflowers.common.entity.table.BillTableDef.BILL;
import static com.dounanflowers.common.entity.table.InvoiceTableDef.INVOICE;

/**
 * 发票管理器
 */
@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class InvoiceManager {

    private final InvoiceRepo invoiceRepo;

    private final BillRepo billRepo;

    private final AdminUserRepo adminUserRepo;

    private final ClientUserRepo clientUserRepo;
    private final UserCommonManager userCommonManager;
    private final AdminMenuService adminMenuService;

    public Invoice getInvoiceCount(Long userId, UserTypeEnum userType) {
        return invoiceRepo.selectOneByQuery(QueryWrapper.create()
                .select("COUNT(1) AS id, SUM(bill_count) AS billCount")
                .from(Invoice.class)
                .eq(Invoice::getStatus, InvoiceStatusEnum.COMPLETED)
                .eq(Invoice::getUserId, userId)
                .eq(Invoice::getUserType, userType));
//        return BeanUtils.copy(invoice, InvoiceCountBo.class);

    }

    public Page<Bill> pageBillsForInvoice(PageRequest pageRequest, Long userId, UserTypeEnum userType) {
        // 查询已支付且未关联到发票的账单
        return billRepo.selectPageWithRelations(pageRequest, w ->w.and(BILL.PAY_STATUS.in(BillPayStatusEnum.PAID, BillPayStatusEnum.PART_REFUNDED).and(BILL.PAID_MONEY_CENT.isNotNull()))
                .and(BILL.INVOICE_ID.isNull().and(BILL.USER_ID.eq(userId)).and(BILL.USER_TYPE.eq(userType)))
        );
    }

    /**
     * 分页查询发票
     */
    public Page<Invoice> pageInvoices(PageRequest pageRequest) {
                if (pageRequest.getFilter() != null) {
            for (PageFilter filter : pageRequest.getFilter()) {
                if ("userObj".equals(filter.getField()) && "custom".equals(filter.getType()) && StringUtil.isNotBlank((String) filter.getValue())) {
                    List<Long> userIds = userCommonManager.fetchUserIdsByLike((String) filter.getValue());
                    if(userIds.isEmpty()) return Page.empty();
                    filter.setType("in");
                    filter.setField("userId");
                    filter.setValue(userIds);
                } else if ("processedAt".equals(filter.getField()) && "range".equals(filter.getType())) {
                    List<?> list = (List<?>) filter.getValue();
                    filter.setValue(list.stream().map(v -> DateUtils.parseLocalDateTime(Long.parseLong(v.toString()))).toList());
                }
            }
        }
        return invoiceRepo.selectPageWithRelations(pageRequest);
    }

    /**
     * 获取发票详情
     */
    public Invoice fetchById(Long id) {
        return invoiceRepo.selectOneById(id);
    }

    public void saveInvoice(Invoice invoice) {
        invoiceRepo.save(invoice);
        adminMenuService.emitCount(AdminMenuEventEnum.INVOICE_PROCESS);
    }

    public void relateBill(Long invoiceId, List<Long> billIds) {
        if (invoiceId == null || billIds == null || billIds.isEmpty()) {
            return;
        }

        UpdateChain.of(Bill.class)
                .set(Bill::getInvoiceId, invoiceId)
                .where(Bill::getId).in(billIds)
                .update();
    }

    public void removeRelateBill(Long invoiceId) {
        if (invoiceId == null) {
            return;
        }

        UpdateChain.of(Bill.class)
                .set(Bill::getInvoiceId, null)
                .where(Bill::getInvoiceId).eq(invoiceId)
                .update();

    }

    public List<Invoice> invoiceHeaderInfo(Long userId) {
        return invoiceRepo.selectListByQuery(QueryWrapper.create()
                .select(INVOICE.HEADER_NAME, INVOICE.TAX_NUMBER, INVOICE.HEADER_TYPE)
                .eq(Invoice::getUserId, userId)
                .groupBy(INVOICE.HEADER_NAME, INVOICE.TAX_NUMBER, INVOICE.HEADER_TYPE)
        );
    }

    public Invoice fetchByBillId(Long billId) {
        Bill bill = billRepo.selectOneById(billId);
        if(bill == null || bill.getInvoiceId() == null) return null;
        return  invoiceRepo.selectOneById(bill.getInvoiceId());
    }

    public Long fetchTodoInvoiceCount() {
        return invoiceRepo.selectCountByQuery(QueryWrapper.create().eq(Invoice::getStatus, InvoiceStatusEnum.PENDING));
    }
}
