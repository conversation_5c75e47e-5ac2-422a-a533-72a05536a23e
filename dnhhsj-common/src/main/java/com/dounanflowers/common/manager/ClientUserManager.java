package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.ClientUser;
import com.dounanflowers.common.entity.ClientUserCredential;
import com.dounanflowers.common.entity.ClientUserPoints;
import com.dounanflowers.common.entity.ClientUserPointsRecord;
import com.dounanflowers.common.enums.PointsRecordTypeEnum;
import com.dounanflowers.common.repo.ClientUserCredentialRepo;
import com.dounanflowers.common.repo.ClientUserPointsRecordRepo;
import com.dounanflowers.common.repo.ClientUserPointsRepo;
import com.dounanflowers.common.repo.ClientUserRepo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.utils.DateUtils;
import com.google.common.collect.Lists;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ClientUserManager {

    private final ClientUserRepo clientUserRepo;

    private final ClientUserCredentialRepo clientUserCredentialRepo;

    private final ClientUserPointsRepo clientUserPointsRepo;

    private final ClientUserPointsRecordRepo clientUserPointsRecordRepo;

    public ClientUser fetchUserByOpenId(String openId) {
        return clientUserRepo.selectOneByQuery(QueryWrapper.create().eq(ClientUser::getOpenId, openId));
    }

    public ClientUser fetchById(Long userId) {
        return clientUserRepo.selectOneById(userId);
    }

    public void save(ClientUser clientUser) {
        clientUserRepo.save(clientUser);
    }

    public ClientUser fetchByMobile(String mobile) {
        return clientUserRepo.selectOneByQuery(QueryWrapper.create().eq(ClientUser::getMobile, mobile));
    }

    public void updateUserCredential(Long userId, String realname, String idCardNum, String idCardUrl1, String idCardUrl2) {
        ClientUserCredential clientUserCredential = clientUserCredentialRepo.selectOneByQuery(QueryWrapper.create().eq(ClientUserCredential::getUserId, userId));
        if (clientUserCredential == null) {
            clientUserCredential = new ClientUserCredential();
            clientUserCredential.setUserId(userId);
            clientUserCredential.setRealname(realname);
            clientUserCredential.setIdCardNum(idCardNum);
            clientUserCredential.setIdCardUrl1(idCardUrl1);
            clientUserCredential.setIdCardUrl2(idCardUrl2);
            clientUserCredentialRepo.insert(clientUserCredential);
        } else {
            clientUserCredential.setRealname(realname);
            clientUserCredential.setIdCardNum(idCardNum);
            clientUserCredential.setIdCardUrl1(idCardUrl1);
            clientUserCredential.setIdCardUrl2(idCardUrl2);
            clientUserCredentialRepo.update(clientUserCredential);
        }
    }

    public List<ClientUser> fetchByIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return List.of();
        }
        return clientUserRepo.selectListByIds(userIds);
    }

    public Page<ClientUser> page(PageRequest dto) {
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("lastLoginAt".equals(filter.getField())) {
                    List<LocalDateTime> newVar = Lists.newArrayList();
                    for (Object o : (List) filter.getValue()) {
                        newVar.add(DateUtils.parseLocalDateTime(Long.parseLong(o.toString())));
                    }
                    filter.setValue(newVar);
                } else if ("search".equals(filter.getField()) && "custom".equals(filter.getType())) {
                    dto.addCustom("search", w -> w.like("username", filter.getValue())
                            .or(or -> or.like("nickname", filter.getValue()), true)
                            .or(or -> or.like("mobile", filter.getValue()), true)
                            .or(or -> or.like("realname", filter.getValue()), true));
                } else if ("wxOpenId".equals(filter.getField())) {
                    filter.setField("openId");
                }
            }
        }
        return clientUserRepo.selectPageByQuery(dto);
    }

    /**
     * 获取用户积分
     *
     * @param userId 用户ID
     * @return 用户积分
     */
    public Integer getUserPoints(Long userId) {
        ClientUserPoints userPoints = clientUserPointsRepo.selectOneByQuery(
                QueryWrapper.create().eq(ClientUserPoints::getUserId, userId)
        );
        return userPoints != null ? userPoints.getAvailablePoints() : 0;
    }

    /**
     * 增加用户积分
     *
     * @param userId      用户ID
     * @param points      积分数量
     * @param description 积分描述
     */
    @Transactional(rollbackFor = Exception.class)
    public void addPoints(Long userId, Integer points, String description) {
        // 更新或创建用户积分记录
        ClientUserPoints userPoints = clientUserPointsRepo.selectOneByQuery(
                QueryWrapper.create().eq(ClientUserPoints::getUserId, userId)
        );

        if (userPoints == null) {
            userPoints = new ClientUserPoints();
            userPoints.setUserId(userId);
            userPoints.setTotalPoints(points);
            userPoints.setAvailablePoints(points);
            userPoints.setUsedPoints(0);
            userPoints.setExpiredPoints(0);
            clientUserPointsRepo.insert(userPoints);
        } else {
            userPoints.setTotalPoints(userPoints.getTotalPoints() + points);
            userPoints.setAvailablePoints(userPoints.getAvailablePoints() + points);
            clientUserPointsRepo.update(userPoints);
        }

        // 创建积分记录
        ClientUserPointsRecord record = new ClientUserPointsRecord();
        record.setUserId(userId);
        record.setPoints(points);
        record.setType(PointsRecordTypeEnum.EARN);
        record.setDescription(description);
        clientUserPointsRecordRepo.insert(record);
    }

    /**
     * 消费用户积分
     *
     * @param userId      用户ID
     * @param points      积分数量
     * @param description 积分描述
     * @return 是否消费成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean consumePoints(Long userId, Integer points, String description) {
        ClientUserPoints userPoints = clientUserPointsRepo.selectOneByQuery(
                QueryWrapper.create().eq(ClientUserPoints::getUserId, userId)
        );

        if (userPoints == null || userPoints.getAvailablePoints() < points) {
            return false;
        }

        // 更新用户积分
        userPoints.setAvailablePoints(userPoints.getAvailablePoints() - points);
        userPoints.setUsedPoints(userPoints.getUsedPoints() + points);
        clientUserPointsRepo.update(userPoints);

        // 创建积分记录
        ClientUserPointsRecord record = new ClientUserPointsRecord();
        record.setUserId(userId);
        record.setPoints(-points); // 负数表示消费
        record.setType(PointsRecordTypeEnum.USE);
        record.setDescription(description);
        clientUserPointsRecordRepo.insert(record);

        return true;
    }

    /**
     * 退还用户积分
     *
     * @param userId      用户ID
     * @param points      积分数量
     * @param description 积分描述
     */
    @Transactional(rollbackFor = Exception.class)
    public void refundPoints(Long userId, Integer points, String description) {
        ClientUserPoints userPoints = clientUserPointsRepo.selectOneByQuery(
                QueryWrapper.create().eq(ClientUserPoints::getUserId, userId)
        );

        if (userPoints == null) {
            userPoints = new ClientUserPoints();
            userPoints.setUserId(userId);
            userPoints.setTotalPoints(points);
            userPoints.setAvailablePoints(points);
            userPoints.setUsedPoints(0);
            userPoints.setExpiredPoints(0);
            clientUserPointsRepo.insert(userPoints);
        } else {
            userPoints.setAvailablePoints(userPoints.getAvailablePoints() + points);
            userPoints.setUsedPoints(userPoints.getUsedPoints() - points);
            clientUserPointsRepo.update(userPoints);
        }

        // 创建积分记录
        ClientUserPointsRecord record = new ClientUserPointsRecord();
        record.setUserId(userId);
        record.setPoints(points);
        record.setType(PointsRecordTypeEnum.REFUND);
        record.setDescription(description);
        clientUserPointsRecordRepo.insert(record);
    }

    /**
     * 处理积分过期
     *
     * @param userId      用户ID
     * @param points      过期积分数量
     * @param description 积分描述
     */
    @Transactional(rollbackFor = Exception.class)
    public void expirePoints(Long userId, Integer points, String description) {
        ClientUserPoints userPoints = clientUserPointsRepo.selectOneByQuery(
                QueryWrapper.create().eq(ClientUserPoints::getUserId, userId)
        );

        if (userPoints != null && userPoints.getAvailablePoints() >= points) {
            userPoints.setAvailablePoints(userPoints.getAvailablePoints() - points);
            userPoints.setExpiredPoints(userPoints.getExpiredPoints() + points);
            clientUserPointsRepo.update(userPoints);

            // 创建积分记录
            ClientUserPointsRecord record = new ClientUserPointsRecord();
            record.setUserId(userId);
            record.setPoints(-points);
            record.setType(PointsRecordTypeEnum.EXPIRE);
            record.setDescription(description);
            clientUserPointsRecordRepo.insert(record);
        }
    }
}
