package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.Article;
import com.dounanflowers.common.entity.ArticleCategory;
import com.dounanflowers.common.entity.ArticleLike;
import com.dounanflowers.common.entity.ArticleTag;
import com.dounanflowers.common.repo.ArticleCategoryRepo;
import com.dounanflowers.common.repo.ArticleLikeRepo;
import com.dounanflowers.common.repo.ArticleRepo;
import com.dounanflowers.common.repo.ArticleTagRepo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ArticleManager {

    private final ArticleRepo articleRepo;

    private final ArticleTagRepo articleTagRepo;

    private final ArticleCategoryRepo articleCategoryRepo;

    private final ArticleLikeRepo articleLikeRepo;

    public Page<Article> pageList(PageRequest dto) {
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("categoryId".equals(filter.getField())) {
                    QueryWrapper subWrapper = filter.wrapper(QueryWrapper.create().select("article_id").from(ArticleCategory.class), "category");
                    filter.setField("id");
                    filter.setType("sub");
                    dto.addSubQuery("id", w -> w.in("id", subWrapper));
                } else if ("tags".equals(filter.getField())) {
                    QueryWrapper subWrapper = filter.wrapper(QueryWrapper.create().select("article_id").from(ArticleTag.class), "tag");
                    filter.setField("id");
                    filter.setType("sub");
                    dto.addSubQuery("id", w -> w.in("id", subWrapper));
                }
            }
        }
        return articleRepo.selectPageWithRelations(dto);
    }

    public void save(Article article) {
        if (article.getId() == null) {
            article.setViewCount(0);
            article.setFavouriteCount(0);
        }
        articleRepo.save(article);
        if (article.getTags() != null) {
            articleTagRepo.deleteByQuery(QueryWrapper.create().eq("article_id", article.getId()));
            for (String tag : article.getTags()) {
                ArticleTag articleTag = new ArticleTag();
                articleTag.setArticleId(article.getId());
                articleTag.setTag(tag);
                articleTagRepo.save(articleTag);
            }
        }
        if (article.getCategories() != null) {
            articleCategoryRepo.deleteByQuery(QueryWrapper.create().eq("article_id", article.getId()));
            for (String category : article.getCategories()) {
                ArticleCategory articleCategory = new ArticleCategory();
                articleCategory.setArticleId(article.getId());
                articleCategory.setCategory(category);
                articleCategoryRepo.save(articleCategory);
            }
        }
    }

    public Article fetchById(Long id) {
        return articleRepo.selectOneWithRelationsById(id);
    }

    public void deleteById(Long id) {
        articleRepo.deleteById(id);
        articleTagRepo.deleteByQuery(QueryWrapper.create().eq("article_id", id));
        articleCategoryRepo.deleteByQuery(QueryWrapper.create().eq("article_id", id));
    }

    public List<String> allTags() {
        return articleTagRepo.selectListByQueryAs(QueryWrapper.create().select("DISTINCT tag"), String.class);
    }

    public Article fetchByTitle(String title) {
        return articleRepo.selectOneWithRelationsByQuery(QueryWrapper.create().eq("title", title));
    }

    public void toggleLikeArticle(Long userId, Long articleId) {
        ArticleLike entity = articleLikeRepo.selectOneByQuery(QueryWrapper.create().eq("user_id", userId).eq("article_id", articleId));
        if (entity != null) {
            articleLikeRepo.delete(entity);
            Article article = fetchById(articleId);
            article.setLikeCount(article.getLikeCount() - 1);
            save(article);
        } else {
            entity = new ArticleLike();
            entity.setUserId(userId);
            entity.setArticleId(articleId);
            articleLikeRepo.insert(entity);
            Article article = fetchById(articleId);
            article.setLikeCount(article.getLikeCount() + 1);
            save(article);
        }
    }

    public boolean isLikeArticle(Long userId, Long articleId) {
        ArticleLike entity = articleLikeRepo.selectOneByQuery(QueryWrapper.create().eq("user_id", userId).eq("article_id", articleId));
        return entity != null;
    }

    public Map<Long, Boolean> isLikeArticles(Long userId, List<Long> articleIds) {
        if (articleIds == null || articleIds.isEmpty()) {
            return Map.of();
        }
        List<ArticleLike> entities = articleLikeRepo.selectListByQuery(QueryWrapper.create().eq("user_id", userId).in("article_id", articleIds));
        return entities.stream().collect(Collectors.toMap(ArticleLike::getArticleId, e -> true));
    }

}
