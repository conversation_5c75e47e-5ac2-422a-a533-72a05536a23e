package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.repo.*;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.DateUtils;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Db;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.dounanflowers.common.entity.table.ShopTableDef.SHOP;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ShopManager {
    private final ShopRepo shopRepo;
    private final ShopTagRRepo shopTagRRepo;
    private final ShopHouseTypeRRepo shopHouseTypeRRepo;
    private final ShopBusinessTypeRRepo shopBusinessTypeRRepo;
    private final ShopRegionRRepo shopRegionRRepo;
    private final SystemManager systemManager;

    public Shop getByCellNo(String cellNo) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(SHOP.CELL_NO.eq(cellNo));
        return shopRepo.selectOneByQuery(queryWrapper);
    }

    public Page<Shop> pageList(PageRequest dto) {
        if (dto.getFilter() != null) {
            dto.getFilter().forEach(filter -> {
                if ("businessType".equals(filter.getField())) {
                    dto.addSubQuery("id", w -> w.in("id", filter.wrapper(QueryWrapper.create().select("shop_id").from(ShopBusinessTypeR.class), "businessType")));
                    filter.setField("id");
                    filter.setType("sub");
                } else if ("region".equals(filter.getField())) {
                    dto.addSubQuery("id", w -> w.in("id", filter.wrapper(QueryWrapper.create().select("shop_id").from(ShopRegionR.class), "region")));
                    filter.setField("id");
                    filter.setType("sub");
                } else if ("houseType".equals(filter.getField())) {
                    dto.addSubQuery("id", w -> w.in("id", filter.wrapper(QueryWrapper.create().select("shop_id").from(ShopHouseTypeR.class), "houseType")));
                    filter.setField("id");
                    filter.setType("sub");
                } else if ("tag".equals(filter.getField())) {
                    dto.addSubQuery("id", w -> w.in("id", filter.wrapper(QueryWrapper.create().select("shop_id").from(ShopTagR.class), "tag")));
                    filter.setField("id");
                    filter.setType("sub");
                } else if ("onShelfAt".equals(filter.getField()) && "range".equals(filter.getType())) {
                    List<?> list = (List<?>) filter.getValue();
                    filter.setValue(list.stream().map(v -> DateUtils.parseLocalDateTime(Long.parseLong(v.toString()))).toList());
                } else if ("orientation".equals(filter.getField())) {
                    if (filter.getValue() instanceof List list) {
                        filter.setValue(list.stream().map(v -> Integer.parseInt(v.toString())).toList());
                    } else {
                        filter.setValue(Integer.parseInt(filter.getValue().toString()));
                    }
                } else if ("isHot".equals(filter.getField()) && "custom".equals(filter.getType())) {
                    if (Objects.equals(true, filter.getValue())) {
                        filter.setValue(0);
                        filter.setType("gt");
                        filter.setField("overallRank");
                    }
                } else if ("cellProperty".equals(filter.getField())) {
                    if (filter.getValue() instanceof List list) {
                        filter.setValue(list.stream().map(v -> Integer.parseInt(v.toString())).toList());
                    } else {
                        filter.setValue(Integer.parseInt(filter.getValue().toString()));
                    }
                } else if ("cellStatus".equals(filter.getField())) {
                    if (filter.getValue() instanceof List list) {
                        filter.setValue(list.stream().map(v -> Integer.parseInt(v.toString())).toList());
                    } else {
                        filter.setValue(Integer.parseInt(filter.getValue().toString()));
                    }
                } else if ("contractEndDate".equals(filter.getField()) && "range".equals(filter.getType())) {
                    List<?> list = (List<?>) filter.getValue();
                    filter.setValue(list.stream().map(v -> DateUtils.parseLocalDateTime(Long.parseLong(v.toString()))).toList());
                }
            });
        }
        return shopRepo.selectPageWithRelations(dto);
    }

    public void save(Shop shop) {
        if (shop.getId() == null) {
            shop.setViewCount(0);
        } else {
            Shop db = shopRepo.selectOneById(shop.getId());
            if (db == null) {
                throw new BaseException("商铺不存在");
            } else {
                shop.setVer(db.getVer());
            }
        }
        shopRepo.save(shop);
        if (shop.getTags() != null) {
            shopTagRRepo.deleteByQuery(QueryWrapper.create().eq("shop_id", shop.getId()));
            for (String tag : shop.getTags()) {
                ShopTagR shopTagR = new ShopTagR();
                shopTagR.setShopId(shop.getId());
                shopTagR.setTag(tag);
                shopTagRRepo.save(shopTagR);
            }
        }
        if (shop.getHouseType() != null) {
            shopHouseTypeRRepo.deleteByQuery(QueryWrapper.create().eq("shop_id", shop.getId()));
            for (String houseType : shop.getHouseType()) {
                ShopHouseTypeR shopHouseTypeR = new ShopHouseTypeR();
                shopHouseTypeR.setShopId(shop.getId());
                shopHouseTypeR.setHouseType(houseType);
                shopHouseTypeRRepo.save(shopHouseTypeR);
            }
        }
        if (shop.getBusinessType() != null) {
            shopBusinessTypeRRepo.deleteByQuery(QueryWrapper.create().eq("shop_id", shop.getId()));
            for (String businessType : shop.getBusinessType()) {
                ShopBusinessTypeR shopBusinessTypeR = new ShopBusinessTypeR();
                shopBusinessTypeR.setShopId(shop.getId());
                shopBusinessTypeR.setBusinessType(businessType);
                shopBusinessTypeRRepo.save(shopBusinessTypeR);
            }
        }
        if (shop.getRegion() != null) {
            shopRegionRRepo.deleteByQuery(QueryWrapper.create().eq("shop_id", shop.getId()));
            for (String region : shop.getRegion()) {
                ShopRegionR shopRegionR = new ShopRegionR();
                shopRegionR.setShopId(shop.getId());
                shopRegionR.setRegion(region);
                shopRegionRRepo.save(shopRegionR);
            }
        }
    }

    public Shop fetchById(Long id) {
        return shopRepo.selectOneWithRelationsById(id);
    }

    public Shop fetchByCellNo(String cellNo) {
        return shopRepo.selectOneWithRelationsByCondition(SHOP.CELL_NO.eq(cellNo));
    }

    public void deleteById(Long id) {
        shopRepo.deleteById(id);
        shopTagRRepo.deleteByQuery(QueryWrapper.create().eq("shop_id", id));
        shopHouseTypeRRepo.deleteByQuery(QueryWrapper.create().eq("shop_id", id));
        shopBusinessTypeRRepo.deleteByQuery(QueryWrapper.create().eq("shop_id", id));
        shopRegionRRepo.deleteByQuery(QueryWrapper.create().eq("shop_id", id));
    }

    public List<String> allTags() {
        return shopTagRRepo.selectListByQueryAs(QueryWrapper.create().select("DISTINCT tag"), String.class);
    }

    public Boolean rank() {
        Map<String, Map<String, Object>> stringMapMap = systemManager.fetchSettingMap();
        Map<String, Object> shopSetting = stringMapMap.get("Shop");
        Integer shopAutoHotFavouriteCount = (Integer) shopSetting.get("shop_autoHot_favouriteCount");
        Integer shopAutoHotViewCount = (Integer) shopSetting.get("shop_autoHot_viewCount");
        Integer shopAutoHotBrokerCalledCount = (Integer) shopSetting.get("shop_autoHot_brokerCalledCount");

        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(SHOP.ALL_COLUMNS)
                .from(SHOP)
                .where(SHOP.RECOMMEND.gt(0))
                .or(SHOP.FAVOURITE_COUNT.ge(shopAutoHotFavouriteCount)
                        .and(SHOP.VIEW_COUNT.ge(shopAutoHotViewCount))
                        .and(SHOP.BROKER_CALLED_COUNT.ge(shopAutoHotBrokerCalledCount)));
        List<Shop> shops = shopRepo.selectListByQuery(queryWrapper);
        shops.forEach(shop -> {
            shop.setOverallRankScore(rankScore(shop));
        });
        List<Shop> sortedShops = shops.stream().sorted(Comparator.comparingInt(Shop::getOverallRankScore)).toList();
        for (int i = 0; i < sortedShops.size(); i++) {
            Shop shop = sortedShops.get(i);
            shop.setOverallRank(i + 1);
        }
        Db.updateEntitiesBatch(sortedShops);
        return true;
    }

    /**
     * 计算权重
     */
    private Integer rankScore(Shop shop) {
        Map<String, Map<String, Object>> stringMapMap = systemManager.fetchSettingMap();
        Map<String, Object> shopSetting = stringMapMap.get("Shop");
        Integer shopOverallRankViewCount = (Integer) shopSetting.get("shop_overallRank_viewCount");
        Integer shopOverallRankFavouriteCount = (Integer) shopSetting.get("shop_overallRank_favouriteCount");
        Integer shopOverallRankBrokerCalledCount = (Integer) shopSetting.get("shop_overallRank_brokerCalledCount");
        Integer shopOverallRankRecommend = (Integer) shopSetting.get("shop_overallRank_recommend");

        return shop.getViewCount() * shopOverallRankViewCount +
                shop.getFavouriteCount() * shopOverallRankFavouriteCount +
                shop.getBrokerCalledCount() * shopOverallRankBrokerCalledCount +
                shop.getRecommend() * shopOverallRankRecommend;
    }

    public List<Shop> fetchByIds(List<Long> ids) {
        return shopRepo.selectListWithRelationsByQuery(QueryWrapper.create().in(Shop::getId, ids));
    }

    public void updateViewCount(Long id) {
        shopRepo.updateViewCount(id);
    }

}
