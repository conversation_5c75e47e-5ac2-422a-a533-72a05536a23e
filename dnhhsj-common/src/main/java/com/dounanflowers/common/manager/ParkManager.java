package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.repo.*;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ParkManager {
    private final LcAreaRepo lcAreaRepo;
    private final LcDeviceRepo lcDeviceRepo;
    private final LcOrderChargeRepo lcOrderChargeRepo;
    private final LcOrderModifyRepo lcOrderModifyRepo;
    private final LcOrderProfitRepo lcOrderProfitRepo;
    private final LcOrderRepo lcOrderRepo;
    private final LcParkRepo lcParkRepo;
    private final LcPassageWayRepo lcPassageWayRepo;

    @Value("${park.apiPrefix:''}")
    private String parkApiPrefix;

    @Value("${park.apiKey:''}")
    private String parkApiKey;


    public LcPark fetchLcParkByParkNumber(String parkNumber) {
        return lcParkRepo.selectOneByQuery(QueryWrapper.create().eq(LcPark::getParkNumber, parkNumber));
    }

    public void saveLcPark(LcPark lcPark) {
        lcParkRepo.save(lcPark);
    }

    public List<LcArea> fetchLcAreasByParkNumber(String parkNumber) {
        return lcAreaRepo.selectListByQuery(QueryWrapper.create().eq(LcArea::getParkNumber, parkNumber));
    }

    public LcArea fetchLcAreasByAreaId(String areaId) {
        return lcAreaRepo.selectOneByQuery(QueryWrapper.create().eq(LcArea::getAreaId, areaId));
    }

    public void saveLcArea(LcArea lcArea) {
        lcAreaRepo.save(lcArea);
    }

    public List<LcDevice> fetchLcDevicesByParkNumber(String parkNumber) {
        return lcDeviceRepo.selectListByQuery(QueryWrapper.create().eq(LcArea::getParkNumber, parkNumber));
    }

    public void saveLcDevice(LcDevice lcDevice) {
        lcDeviceRepo.save(lcDevice);
    }

    public LcOrder fetchLcOrderByBcOrderId(String bcOrderId) {
        return lcOrderRepo.selectOneByQuery(QueryWrapper.create().eq(LcOrder::getBcOrderId, bcOrderId));
    }

    public void saveLcOrder(LcOrder lcOrder) {
        lcOrderRepo.save(lcOrder);
    }

    public void deleteLcOrderProfitByLcOrderId(Long lcOrderId) {
        lcOrderProfitRepo.deleteByQuery(QueryWrapper.create().eq(LcOrderProfit::getLcOrderId, lcOrderId));
    }


    public void saveLcOrderModify(LcOrderModify lcOrderModify) {
        lcOrderModifyRepo.save(lcOrderModify);
    }

    public void deleteLcOrderChargeByLcOrderId(Long lcOrderId) {
        lcOrderChargeRepo.deleteByQuery(QueryWrapper.create().eq(LcOrderCharge::getLcOrderId, lcOrderId));
    }

    public void saveLcOrderCharge(LcOrderCharge lcOrderCharge) {
        lcOrderChargeRepo.save(lcOrderCharge);
    }

    //    saveLcOrderProfit
    public void saveLcOrderProfit(LcOrderProfit lcOrderProfit) {
        lcOrderProfitRepo.save(lcOrderProfit);
    }

    public LcPassageWay fetchLcPassageWayByPassagewayId(String passageWayId) {
        return lcPassageWayRepo.selectOneByQuery(QueryWrapper.create().eq(LcPassageWay::getPassageWayId, passageWayId));
    }

    public void saveLcPassageWay(LcPassageWay lcPassageWay) {
        lcPassageWayRepo.save(lcPassageWay);
    }
}
