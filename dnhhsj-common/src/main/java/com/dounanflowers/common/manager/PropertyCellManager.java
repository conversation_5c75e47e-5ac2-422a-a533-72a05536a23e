package com.dounanflowers.common.manager;

import com.dounanflowers.common.bo.EnergyCellBo;
import com.dounanflowers.common.entity.PropertyCell;
import com.dounanflowers.common.repo.PropertyCellRepo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.third.ThirdPartyHolder;
import com.dounanflowers.third.bo.OcrBusinessLicenseBo;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class PropertyCellManager {
    private final PropertyCellRepo propertyCellRepo;

    public Page<PropertyCell> page(PageRequest dto) {
        return propertyCellRepo.selectPageWithRelations(dto);
    }

    public void save(PropertyCell propertyCell) {
        propertyCellRepo.save(propertyCell);
    }

    public PropertyCell fetchById(Long id) {
        return propertyCellRepo.selectOneById(id);
    }

    public void deleteById(Long id) {
        propertyCellRepo.deleteById(id);
    }

    public PropertyCell createIfNotExist(EnergyCellBo dto) {
        PropertyCell propertyCell = propertyCellRepo.selectOneByQuery(QueryWrapper.create().eq(PropertyCell::getCellId, dto.getCellId()).eq(PropertyCell::getCompanyCode, dto.getCompanyCode()));
        if (propertyCell == null) {
            propertyCell = new PropertyCell();
            propertyCell.setCompanyName(dto.getCompanyName());
            propertyCell.setCompanyCode(dto.getCompanyCode());
            propertyCell.setCellName(dto.getCellName());
            propertyCell.setCellNo(dto.getCellNo());
            propertyCell.setCellId(dto.getCellId());
            propertyCell.setMasterName(dto.getMasterName());
            propertyCell.setMasterMobile(dto.getMasterMobile());
            propertyCell.setMasterIdCardNo(dto.getMasterIdCardNo());
            propertyCell.setLayerName(dto.getLayerName());
            propertyCell.setLayerNo(dto.getLayerNo());
            propertyCellRepo.save(propertyCell);
            hideSameCellNoItems(propertyCell);
        }
        return propertyCell;
    }

    public void updateBusinessLicense(Long propertyCellId, String businessLicense) {
        PropertyCell propertyCell = fetchById(propertyCellId);
        propertyCell.setBusinessLicense(businessLicense);
        propertyCellRepo.save(propertyCell);
    }

    public PropertyCell findByCompanyCodeAndCellId(String companyCode, String cellId) {
        return propertyCellRepo.selectOneByQuery(QueryWrapper.create().eq(PropertyCell::getCompanyCode, companyCode).eq(PropertyCell::getCellId, cellId).eq(PropertyCell::getHide, IsEnum.FALSE));
    }

    /**
     * 隐藏相同cellNo的item
     * @param propertyCell
     */
    public void hideSameCellNoItems(PropertyCell propertyCell) {
        PropertyCell updatePropertyCell = new PropertyCell();
        updatePropertyCell.setHide(IsEnum.TRUE);
        propertyCellRepo.updateByQuery(
                updatePropertyCell,
                QueryWrapper.create()
                        .eq(PropertyCell::getHide, IsEnum.FALSE)
                        .eq(PropertyCell::getCompanyCode, propertyCell.getCompanyCode())
                        .eq(PropertyCell::getCellNo, propertyCell.getCellNo())
                        .ne(PropertyCell::getId, propertyCell.getId()));
    }

    public List<PropertyCell> fetchAllShowPropertyCell() {
        return propertyCellRepo.selectListByQuery(QueryWrapper.create().eq(PropertyCell::getHide, IsEnum.FALSE));
    }

    public List<PropertyCell> sameCellNoItems(Long id) {
        PropertyCell propertyCell = propertyCellRepo.selectOneById(id);
        if (propertyCell == null) {
            return List.of();
        }
        return propertyCellRepo.selectListByQuery(
                QueryWrapper.create().eq(PropertyCell::getHide, IsEnum.TRUE)
                        .eq(PropertyCell::getCellNo, propertyCell.getCellNo())
                        .eq(PropertyCell::getCompanyCode, propertyCell.getCompanyCode())
        );
    }

    public void saveOcrBusinessLicense(Long propertyCellId) {
        try {
            PropertyCell propertyCell = fetchById(propertyCellId);
            if (propertyCell == null || propertyCell.getBusinessLicense() == null) {
                return;
            }
            OcrBusinessLicenseBo ocrBusinessLicenseBo = ThirdPartyHolder.aliyunSmsService().ocrBusinessLicense(propertyCell.getBusinessLicense());
            propertyCell.setOcrBusinessLicenseResult(JsonUtils.toJson(ocrBusinessLicenseBo));
            propertyCellRepo.save(propertyCell);
        } catch (Exception e) {
            log.error("保存营业执照失败"+propertyCellId, e);
        }
    }
}
