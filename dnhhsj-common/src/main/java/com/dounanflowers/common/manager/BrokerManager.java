package com.dounanflowers.common.manager;


import com.dounanflowers.common.entity.BrokerInfo;
import com.dounanflowers.common.repo.BrokerInfoRepo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class BrokerManager {

    private final BrokerInfoRepo brokerInfoRepo;

    public void save(BrokerInfo brokerInfo) {
        brokerInfoRepo.save(brokerInfo);
    }

    public void deleteById(Long id) {
        brokerInfoRepo.deleteById(id);
    }

    public BrokerInfo findById(Long id) {
        return brokerInfoRepo.selectOneById(id);
    }

    public Page<BrokerInfo> pageList(PageRequest pageRequest) {
        return brokerInfoRepo.selectPageByQuery(pageRequest);
    }

    public BrokerInfo getRandBroker() {
        // 随机获取一位经纪人
        return brokerInfoRepo.selectOneByQuery(QueryWrapper.create().orderByUnSafely("random()").limit(1));
    }
}
