package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.ShopSubscribe;
import com.dounanflowers.common.repo.ShopSubscribeRepo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ShopSubscribeManager {
    private final ShopSubscribeRepo shopSubscribeRepo;

    public Page<ShopSubscribe> pageList(PageRequest dto) {
        return shopSubscribeRepo.selectPageWithRelations(dto);
    }

    public void deleteById(Long id) {
        shopSubscribeRepo.deleteById(id);
    }

    public void save(ShopSubscribe dto) {
        shopSubscribeRepo.save(dto);
    }
}
