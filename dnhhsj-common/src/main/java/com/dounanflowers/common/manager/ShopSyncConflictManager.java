package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.Shop;
import com.dounanflowers.common.entity.ShopSyncConflict;
import com.dounanflowers.common.repo.ShopSyncConflictRepo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.JsonUtils;
import com.mybatisflex.core.update.UpdateChain;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ShopSyncConflictManager {
    private final ShopSyncConflictRepo shopSyncConflictRepo;

    public void save() {

    }

    public Boolean handle(Long id, String rightValue) {
        ShopSyncConflict shopSyncConflict = shopSyncConflictRepo.selectOneById(id);
        if (shopSyncConflict == null) {
            throw new BaseException("找不到此冲突数据");
        }
        shopSyncConflict.setRightValue(rightValue);
        shopSyncConflict.setHandled(IsEnum.TRUE);
        shopSyncConflict.setHandledAt(LocalDateTime.now());
        shopSyncConflictRepo.update(shopSyncConflict);

        // TODO 待验证
        Object parsedRightValue = JsonUtils.parse(shopSyncConflict.getRightValue());

        UpdateChain.of(Shop.class)
                .set(shopSyncConflict.getConflictField(), parsedRightValue)
                .where(Shop::getCellNo).eq(shopSyncConflict.getCellNo())
                .update();

        return true;
    }

    public Page<ShopSyncConflict> pageList(PageRequest dto) {
        return shopSyncConflictRepo.selectPageWithRelations(dto);
    }
}
