package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.ClientActionCount;
import com.dounanflowers.common.repo.ClientActionCountRepo;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ClientActionCountManager {

    private final ClientActionCountRepo clientActionCountRepo;

    public void incCount(String key) {
      LocalDateTime hour = LocalDateTime.now().withMinute(0).withSecond(0).withNano(0);
      LocalDateTime day =  LocalDateTime.now().toLocalDate().atStartOfDay();
      ClientActionCount clientActionCount = clientActionCountRepo.selectOneByQuery(QueryWrapper.create().eq("key", key).eq("time", hour));
      if (clientActionCount == null) {
        clientActionCount = new ClientActionCount();
        clientActionCount.setKey(key);
        clientActionCount.setTime(hour);
        clientActionCount.setCount(1);
        clientActionCount.setTimeUnit("h");
      } else {
        clientActionCount.setCount(clientActionCount.getCount() + 1);
      }
      clientActionCountRepo.save(clientActionCount);

      ClientActionCount clientActionCount2 = clientActionCountRepo.selectOneByQuery(QueryWrapper.create().eq("key", key).eq("time", day));
      if (clientActionCount2 == null) {
        clientActionCount2 = new ClientActionCount();
        clientActionCount2.setKey(key);
        clientActionCount2.setTime(day);
        clientActionCount2.setCount(1);
        clientActionCount2.setTimeUnit("d");
      } else {
        clientActionCount2.setCount(clientActionCount2.getCount() + 1);
      }
      clientActionCountRepo.save(clientActionCount2);
    }


    public List<ClientActionCount> fetchPlaySourceList(LocalDateTime startDate, LocalDateTime endDate,String timeUnit) {
      if (startDate == null || endDate == null) {
        return new ArrayList<>();
      }
      return clientActionCountRepo.selectListByQuery(QueryWrapper.create()
              .likeLeft(ClientActionCount::getKey, "游乐")
              .eq(ClientActionCount::getTimeUnit, timeUnit)
              .between("time", startDate, endDate)
      );
    }
}
