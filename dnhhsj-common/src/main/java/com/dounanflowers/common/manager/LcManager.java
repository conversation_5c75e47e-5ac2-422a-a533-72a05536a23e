package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.repo.LcOrderRepo;
import com.dounanflowers.common.repo.LcParkCountRepo;
import com.dounanflowers.common.repo.LcParkRepo;
import com.dounanflowers.common.repo.LcPlateRepo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.utils.DateUtils;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class LcManager {

    private final LcOrderRepo lcOrderRepo;
    private final LcParkRepo lcParkRepo;
    private final LcPlateRepo lcPlateRepo;
    private final LcParkCountRepo lcParkCountRepo;
    private final UserCommonManager userCommonManager;

    public Page<LcPark> lcParkPageList(PageRequest dto) {
        return lcParkRepo.selectPageWithRelations(dto);
    }

    public Page<LcOrder> lcOrderPageList(PageRequest dto) {
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("userObj".equals(filter.getField()) && "custom".equals(filter.getType()) && StringUtil.isNotBlank((String) filter.getValue())) {
                    List<Long> userIds = userCommonManager.fetchUserIdsByLike((String) filter.getValue());
                    if(userIds.isEmpty()) return Page.empty();
                    filter.setType("in");
                    filter.setField("clientUserId");
                    filter.setValue(userIds);
                } else if ("inTime".equals(filter.getField()) && "range".equals(filter.getType())) {
                    List<?> list = (List<?>) filter.getValue();
                    filter.setValue(list.stream().map(v -> DateUtils.parseLocalDateTime(Long.parseLong(v.toString()))).toList());
                } else if ("outTime".equals(filter.getField()) && "range".equals(filter.getType())) {
                    List<?> list = (List<?>) filter.getValue();
                    filter.setValue(list.stream().map(v -> DateUtils.parseLocalDateTime(Long.parseLong(v.toString()))).toList());
                } else if ("bcOrderId".equals(filter.getField())) {
                    filter.setValue(filter.getValue().toString());
                }
            }
        }
        return lcOrderRepo.selectPageWithRelations(dto);
    }

    public LcOrder lcOrderGetById(Long id) {
        return lcOrderRepo.selectOneById(id);
    }

    public List<LcPlate> lcPlateListByClientUserId(Long clientUserId) {
        return lcPlateRepo.selectListByQuery(QueryWrapper.create().eq(LcPlate::getClientUserId, clientUserId));
    }

    public LcOrder fetchInParkLcOrderByPlate(String plate) {
        return lcOrderRepo.selectOneByQuery(QueryWrapper.create()
                .eq(LcOrder::getPlate, plate)
                .eq(LcOrder::getOutTime, null)
                .orderBy(LcOrder::getInTime, false)
        );
    }

    public LcPlate fetchLcPlate(String plate, Long userId) {
        return lcPlateRepo.selectOneByQuery(QueryWrapper.create()
                .eq(LcPlate::getClientUserId, userId)
                .eq(LcPlate::getPlate, plate));
    }

    public void saveLcPlate(LcPlate lcPlate) {
        lcPlateRepo.insert(lcPlate);
    }

    public void deleteLcPlate(LcPlate lcPlate) {
        lcPlateRepo.delete(lcPlate);
    }

    public LcPark fetchFistLcPark() {
        return lcParkRepo.selectOneByQuery(QueryWrapper.create());
    }

    public List<LcParkCount> fetchLcParkCountList(LocalDateTime start, LocalDateTime end) {
        return lcParkCountRepo.selectListByQuery(QueryWrapper.create().ge(LcParkCount::getCreatedAt, start).le(LcParkCount::getCreatedAt, end));
    }

    public LcOrder fetchLcOrderById(Long lcOrderId) {
        return lcOrderRepo.selectOneById(lcOrderId);
    }

    public Page<LcOrder> lcOrderNoPlate(PageRequest dto) {
        if (dto.getFilter() != null) {
            dto.getFilter().forEach(filter -> {
                if ("inTime".equals(filter.getField()) && "range".equals(filter.getType())) {
                    List<?> list = (List<?>) filter.getValue();
                    filter.setValue(list.stream().map(v -> DateUtils.parseLocalDateTime(Long.parseLong(v.toString()))).toList());
                }
            });
        }
        return lcOrderRepo.selectPageByQuery(dto, w -> w.ne(LcOrder::getTicketCode, "")
                .isNull(LcOrder::getOutTime)
                .isNotNull(LcOrder::getInImage));
    }
}