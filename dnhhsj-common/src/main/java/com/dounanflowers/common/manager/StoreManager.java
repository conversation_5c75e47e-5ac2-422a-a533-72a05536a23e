package com.dounanflowers.common.manager;

import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.repo.ProductProductRepo;
import com.dounanflowers.common.repo.StoreEmployeeRRepo;
import com.dounanflowers.common.repo.StoreRepo;
import com.dounanflowers.common.repo.StoreTagRRepo;
import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.google.common.collect.Lists;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.dounanflowers.common.entity.table.StoreTableDef.STORE;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class StoreManager {

    private final StoreRepo storeRepo;

    private final StoreTagRRepo storeTagRRepo;

    private final StoreEmployeeRRepo storeEmployeeRRepo;

    private final AdminUserManager adminUserManager;

    private final ProductProductRepo productRepo;

    public Page<Store> pageList(PageRequest dto) {

        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("custom".equals(filter.getType()) && "search".equals(filter.getField())) {
                    QueryWrapper subWrapper = QueryWrapper.create().select("store_id").from(Product.class);
                    subWrapper.like("title", filter.getValue());
                    QueryWrapper subWrapper2 = QueryWrapper.create().select("id").from(Store.class);
                    subWrapper2.like("name", filter.getValue());
                    dto.addCustom("search", w ->
                            w.and(ww -> ww.in("id", subWrapper)
                                    .or(ww1 -> ww1.in("id", subWrapper2), true), true));
                } else if ("tags".equals(filter.getField())) {
                    QueryWrapper subWrapper = filter.wrapper(QueryWrapper.create().select("store_id").from(StoreTagR.class), "tag");
                    filter.setType("sub");
                    filter.setField("id");
                    dto.addSubQuery("id", w -> w.in("id", subWrapper));
                }
            }
        }
        return storeRepo.selectPageWithRelations(dto);
    }

    public void save(Store store) {
        storeRepo.save(store);
        if (store.getTags() != null) {
            storeTagRRepo.deleteByQuery(QueryWrapper.create().eq(StoreTagR::getStoreId, store.getId()));
            for (String tag : store.getTags()) {
                StoreTagR entity = new StoreTagR();
                entity.setStoreId(store.getId());
                entity.setTag(tag);
                storeTagRRepo.save(entity);
            }
        }
        if (store.getEmployees() != null) {
            List<StoreEmployeeR> storeEmployeeRS = storeEmployeeRRepo.selectListByQuery(QueryWrapper.create().eq(StoreEmployeeR::getStoreId, store.getId()));
            List<Long> userIds = storeEmployeeRS.stream().map(StoreEmployeeR::getUserId).toList();
            // 删除核销员员工
            storeEmployeeRRepo.deleteByQuery(QueryWrapper.create().eq(StoreEmployeeR::getStoreId, store.getId()));
            // 删除核销员角色
            deleteUsersUnderwriter(userIds);

            // 添加核销员员工
            List<AdminUser> adminUsers = adminUserManager.fetchByMobiles(store.getEmployees().stream().map(StoreEmployeeR::getMobile).filter(StringUtils::isNotBlank).toList());
            // 添加核销员角色
            addUsersUnderwriter(adminUsers.stream().map(BaseEntity::getId).toList());
            Map<String, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getMobile, v -> v));
            for (StoreEmployeeR employee : store.getEmployees()) {
                employee.setStoreId(store.getId());
                if (employee.getUserId() == null && employee.getMobile() != null) {
                    AdminUser adminUser = adminUserMap.get(employee.getMobile());
                    if (adminUser != null) {
                        employee.setUserId(adminUser.getId());
                    } else {
                        employee.setUserId(0L);
                    }
                }
                storeEmployeeRRepo.save(employee);
            }
        }
    }

    public void deleteUsersUnderwriter(List<Long> userIds) {
        userIds = Lists.newArrayList(userIds);
        List<Store> stores = storeRepo.selectListByQuery(QueryWrapper.create().in(Store::getAdminUserId, userIds));
        List<StoreEmployeeR> storeEmployeeRS = storeEmployeeRRepo.selectListByQuery(QueryWrapper.create().in(StoreEmployeeR::getUserId, userIds));
        Set<Long> exists = stores.stream().map(Store::getAdminUserId).collect(Collectors.toSet());
        exists.addAll(storeEmployeeRS.stream().map(StoreEmployeeR::getUserId).collect(Collectors.toSet()));
        userIds.removeIf(exists::contains);
        adminUserManager.deleteUsersUnderwriter(userIds);
    }

    public void addUsersUnderwriter(List<Long> userIds) {
        userIds = Lists.newArrayList(userIds);
        List<Store> stores = storeRepo.selectListByQuery(QueryWrapper.create().in(Store::getAdminUserId, userIds)); // 判断是否是其他店铺管理员
        List<StoreEmployeeR> storeEmployeeRS = storeEmployeeRRepo.selectListByQuery(QueryWrapper.create().in(StoreEmployeeR::getUserId, userIds)); // 判断是否已经是核销员
        Set<Long> exists = stores.stream().map(Store::getAdminUserId).collect(Collectors.toSet());
        exists.addAll(storeEmployeeRS.stream().map(StoreEmployeeR::getUserId).collect(Collectors.toSet()));
        userIds.removeIf(exists::contains); // 过滤掉已经是其他店铺管理员或核销员的用户
        adminUserManager.addUsersUnderwriter(userIds);
    }

    public Store fetchByIdWithRelation(Long id) {
        return storeRepo.selectOneWithRelationsById(id);
    }

    public Store fetchById(Long id) {
        Store store = storeRepo.selectOneById(id);
        store.setEmployees(null);
        return store;
    }

    public void deleteById(Long id) {
        storeRepo.deleteById(id);
        List<Long> userIds = storeEmployeeRRepo.selectListByQuery(QueryWrapper.create().eq(StoreEmployeeR::getStoreId, id)).stream().map(StoreEmployeeR::getUserId).toList();
        if (!userIds.isEmpty()) {
            storeEmployeeRRepo.deleteByQuery(QueryWrapper.create().eq(StoreEmployeeR::getStoreId, id));
            deleteUsersUnderwriter(userIds);
        }
    }

    public Store fetchByShopId(Long shopId) {
        return storeRepo.selectOneWithRelationsByQuery(QueryWrapper.create()
                .where(STORE.SHOP_ID.eq(shopId)));
    }

    public Store fetchByCellNo(String cellNo) {
        return storeRepo.selectOneWithRelationsByQuery(QueryWrapper.create()
                .where(STORE.CELL_NO.eq(cellNo)));
    }

    public List<Store> fetchByIds(List<Long> storeIds) {
        return storeRepo.selectListWithRelationsByQuery(QueryWrapper.create().in(Store::getId, storeIds));
    }

    public List<Long> fetchStoreIdsByUserId(Long userId) {
        List<StoreEmployeeR> storeEmployeeRS = storeEmployeeRRepo.selectListByQuery(QueryWrapper.create().eq(StoreEmployeeR::getUserId, userId));
        List<Long> storeIds = storeEmployeeRS.stream().map(StoreEmployeeR::getStoreId).collect(Collectors.toList());
        List<Store> stores = storeRepo.selectListByQuery(QueryWrapper.create().eq(Store::getAdminUserId, userId));
        storeIds.addAll(stores.stream().map(Store::getId).toList());
        return storeIds;
    }

    public List<Store> fetchStoreByUserId(Long userId) {
        List<StoreEmployeeR> storeEmployeeRS = storeEmployeeRRepo.selectListByQuery(QueryWrapper.create().eq(StoreEmployeeR::getUserId, userId));
        List<Long> storeIds = storeEmployeeRS.stream().map(StoreEmployeeR::getStoreId).collect(Collectors.toList());
        return storeRepo.selectListByQuery(QueryWrapper.create().eq(Store::getAdminUserId, userId).or(w -> w.in(Store::getId, storeIds), CollectionUtils.isNotEmpty(storeIds)));
    }

    public List<Store> fetchAll() {
        return storeRepo.selectAll();
    }

    public List<StoreEmployeeR> fetchAllEmployees() {
        return storeEmployeeRRepo.selectAll();
    }

    /**
     * 计算店铺销售数量
     * 根据店铺下所有商品的销售数量总和更新店铺的销售数量
     *
     * @param storeId 店铺ID
     * @return 更新后的店铺销售数量
     */
    public Integer calcSoldCount(Long storeId) {
        // 获取店铺信息
        Store store = storeRepo.selectOneById(storeId);
        if (store == null) {
            return 0;
        }

        // 查询该店铺下所有商品
        List<Product> products = productRepo.selectListByQuery(QueryWrapper.create().eq(Product::getStoreId, storeId));

        // 计算所有商品的销售总量
        Integer totalSoldCount = products.stream()
                .filter(product -> product.getSoldCount() != null)
                .mapToInt(Product::getSoldCount)
                .sum();

        // 更新店铺销售数量
        store.setSoldCount(totalSoldCount);
        storeRepo.update(store);

        return totalSoldCount;
    }
}
