package com.dounanflowers.common.event;
import com.dounanflowers.common.bo.WsMessageBo;
import com.dounanflowers.common.enums.AdminMenuEventEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
public class AdminMenuEventPublisher {
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    public void emit(final WsMessageBo message) {
        AdminMenuEvent event = new AdminMenuEvent(this, message);
        applicationEventPublisher.publishEvent(event);
    }
}
