package com.dounanflowers.common.service;


import com.dounanflowers.common.bo.WsMenuCountBo;
import com.dounanflowers.common.bo.WsMessageBo;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.*;
import com.dounanflowers.common.event.AdminMenuEventPublisher;
import com.dounanflowers.common.repo.*;
import com.dounanflowers.framework.enums.IsEnum;
import com.google.common.collect.Lists;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AdminMenuService {

    private final AdminMenuEventPublisher adminMenuEventPublisher;
    private final JobPostingRepo jobPostingRepo;
    private final ParkServiceAppointmentRepo parkServiceAppointmentRepo;
    private final InvoiceRepo invoiceRepo;
    private final WithdrawRecordRepo withdrawRecordRepo;
    private final ProductRefundApplyRepo productRefundApplyRepo;
    private final VehicleRepo vehicleRepo;
    private final VehiclePlateChangeRepo vehiclePlateChangeRepo;
    private final FeedbackRepo feedbackRepo;
    private final PropertyCellRepo propertyCellRepo;

    @Async
    public void emitCount(AdminMenuEventEnum type) {
        try {
            Thread.sleep(100);
            switch (type) {
                case JOB_AUDIT:
                    adminMenuEventPublisher.emit(this.jobAuditCount());
                    break;
                case JOB_SERVICE_AUDIT:
                    adminMenuEventPublisher.emit(this.jobServiceAuditCount());
                    break;
                case INVOICE_PROCESS:
                    adminMenuEventPublisher.emit(this.invoiceProcessCount());
                    break;
                case VEHICLE_CHECK:
                    adminMenuEventPublisher.emit(this.vehicleCheckCount());
                    break;
                case VEHICLE_PLATE_CHANGE_CHECK:
                    adminMenuEventPublisher.emit(this.vehiclePlateChangeCheckCount());
                    break;
                case VEHICLE_REFUND_CHECK:
                    adminMenuEventPublisher.emit(this.vehicleRefundCheckCount());
                    break;
                case PRODUCT_REFUND_CHECK:
                    adminMenuEventPublisher.emit(this.productRefundCheckCount());
                    break;
                case WITHDRAW_APPROVE:
                    adminMenuEventPublisher.emit(this.withdrawApproveCount());
                    break;
                case FEEDBACK_HANDLE:
                    adminMenuEventPublisher.emit(this.feedbackHandleCount());
                    break;
                case PROPERTY_CELL_SYNC_HANDLE:
                    adminMenuEventPublisher.emit(this.propertyCellSyncHandleCount());
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            // 处理异常（可选）
            Thread.currentThread().interrupt();
            System.err.println("异步任务被中断");
        }
    }

    private WsMessageBo<WsMenuCountBo> feedbackHandleCount() {
        Long count = feedbackRepo.selectCountByQuery(QueryWrapper.create().isNull(Feedback::getHandledAt));
        return this.wsMessage(AdminMenuEventEnum.FEEDBACK_HANDLE, count);
    }

    private WsMessageBo<WsMenuCountBo> withdrawApproveCount() {
        Long count = withdrawRecordRepo.selectCountByQuery(QueryWrapper.create().eq(WithdrawRecord::getStatus, WithdrawStatusEnum.REVIEWING));
        return this.wsMessage(AdminMenuEventEnum.WITHDRAW_APPROVE, count);
    }

    private WsMessageBo<WsMenuCountBo> productRefundCheckCount() {
        Long count = productRefundApplyRepo.selectCountByQuery(QueryWrapper.create().eq(ProductRefundApply::getStatus, RefundStatusEnum.PENDING));
        return this.wsMessage(AdminMenuEventEnum.PRODUCT_REFUND_CHECK, count);
    }

    private WsMessageBo<WsMenuCountBo> vehicleRefundCheckCount() {
        Long count = vehicleRepo.selectCountByQuery(QueryWrapper.create().eq(Vehicle::getRefundStatus, VehicleRefundStatusEnum.WAIT_CHECK));
        return this.wsMessage(AdminMenuEventEnum.VEHICLE_REFUND_CHECK, count);
    }

    private WsMessageBo<WsMenuCountBo> vehiclePlateChangeCheckCount() {
        Long count = vehiclePlateChangeRepo.selectCountByQuery(QueryWrapper.create().eq(VehiclePlateChange::getChangeStatus, VehiclePlateChangeStatusEnum.WAIT_CHANGE));
        return this.wsMessage(AdminMenuEventEnum.VEHICLE_PLATE_CHANGE_CHECK, count);
    }

    private WsMessageBo<WsMenuCountBo> vehicleCheckCount() {
        Long count = vehicleRepo.selectCountByQuery(QueryWrapper.create().eq(Vehicle::getCheckStatus, VehicleCheckStatusEnum.UNCHECKED));
        return this.wsMessage(AdminMenuEventEnum.VEHICLE_CHECK, count);
    }

    public WsMessageBo<WsMenuCountBo> jobAuditCount() {
        Long count = jobPostingRepo.selectCountByQuery(QueryWrapper.create().eq(JobPosting::getStatus, JobPostingStatusEnum.PENDING));
        return this.wsMessage(AdminMenuEventEnum.JOB_AUDIT, count);
    }

    public WsMessageBo<WsMenuCountBo> jobServiceAuditCount() {
        Long count = parkServiceAppointmentRepo.selectCountByQuery(QueryWrapper.create().in(ParkServiceAppointment::getStatus, Lists.newArrayList(AppointmentStatusEnum.PENDING, AppointmentStatusEnum.ACCEPTED)));
        return this.wsMessage(AdminMenuEventEnum.JOB_SERVICE_AUDIT, count);
    }

    public WsMessageBo<WsMenuCountBo> invoiceProcessCount() {
        Long count = invoiceRepo.selectCountByQuery(QueryWrapper.create().eq(Invoice::getStatus, InvoiceStatusEnum.PENDING));
        return this.wsMessage(AdminMenuEventEnum.INVOICE_PROCESS, count);
    }

    public WsMessageBo<WsMenuCountBo> propertyCellSyncHandleCount() {
        Long count = propertyCellRepo.selectCountByQuery(QueryWrapper.create().eq(PropertyCell::getWaitUpdate, IsEnum.TRUE).eq(PropertyCell::getHide, IsEnum.FALSE));
        return this.wsMessage(AdminMenuEventEnum.PROPERTY_CELL_SYNC_HANDLE, count);
    }

    public WsMessageBo<WsMenuCountBo> wsMessage(AdminMenuEventEnum type, Long count) {
        WsMenuCountBo countDto = new WsMenuCountBo();
        countDto.setCount(count.intValue());
        countDto.setApi(type.getApi());
        countDto.setType(type.toString());
        return WsMessageBo.of(countDto);
    }
}
