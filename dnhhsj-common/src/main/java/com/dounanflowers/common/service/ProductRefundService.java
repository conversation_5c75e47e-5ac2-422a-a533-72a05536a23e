package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.BillRefundDto;
import com.dounanflowers.common.dto.ProductOrderForceRefundDto;
import com.dounanflowers.common.dto.ProductRefundApplyDto;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.*;
import com.dounanflowers.common.manager.*;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.utils.SecurityHolder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ProductRefundService {

    private final ProductRefundManager refundManager;
    private final ProductManager productManager;
    private final StoreManager storeManager;
    private final BillManager billManager;
    private final ClientUserManager clientUserManager;
    private final WithdrawManager withdrawManager;
    private final AdminMenuService adminMenuService;

//    @Transactional(rollbackFor = Exception.class)
//    public void applyRefund(ProductRefundApplyDto dto) {
//        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
//
//        // 1. 检查订单和订单项
//        ProductOrder order = productManager.getOrderWithItems(dto.getOrderId());
//        if (order == null) {
//            throw new BaseException("订单不存在");
//        }
//        if (!order.getUserId().equals(userId)) {
//            throw new BaseException("无权操作此订单");
//        }
//        if (order.getStatus() != OrderStatusEnum.PAID) {
//            throw new BaseException("订单状态不正确");
//        }
//        // 3. 创建退款申请
//        ProductRefundApply refundApply = new ProductRefundApply();
//        if (dto.getRefundAmount() == null && dto.getOrderItemId() == null) {
//            dto.setRefundAmount(order.getRealAmount());
//        }
//
//        boolean directRefund = false;
//
//        if (dto.getOrderItemId() != null) {
//            ProductOrderItem orderItem = order.getItems().stream()
//                    .filter(item -> item.getId().equals(dto.getOrderItemId()))
//                    .findFirst()
//                    .orElseThrow(() -> new BaseException("订单项不存在"));
//            if (dto.getRefundAmount() == null) {
//                dto.setRefundAmount(orderItem.getRealAmount());
//            }
//            // 2. 检查退款金额
//            if (dto.getRefundAmount() > orderItem.getRealAmount()) {
//                throw new BaseException("退款金额不能大于实付金额");
//            }
//            refundApply.setProductId(orderItem.getProductId());
//
//            Product product = JsonUtils.toObject(orderItem.getSnapshot(), Product.class);
//            if (product != null && product.getDirectRefund() != null && product.getDirectRefund().isTrue()) {
//                directRefund = true;
//            }
//        } else {
//            directRefund = true;
//            for (ProductOrderItem item : order.getItems()) {
//                Product product = JsonUtils.toObject(item.getSnapshot(), Product.class);
//                if (product == null || product.getDirectRefund().isFalse()) {
//                    directRefund = false;
//                    break;
//                }
//            }
//        }
//
//        if (dto.getRefundAmount() > order.getRealAmount()) {
//            throw new BaseException("退款金额不能大于实付金额");
//        }
//
//        refundApply.setOrderId(dto.getOrderId())
//                .setOrderItemId(dto.getOrderItemId())
//                .setUserId(userId)
//                .setStoreId(order.getStoreId())
//                .setRefundAmount(dto.getRefundAmount())
//                .setReason(dto.getReason())
//                .setDescription(dto.getDescription())
//                .setStatus(RefundStatusEnum.PENDING)
//                .setImages(dto.getImages());
//
//        refundManager.createApply(refundApply);
//
//        order.setStatus(OrderStatusEnum.REFUNDING);
//        productManager.updateOrder(order);
//        adminMenuService.emitCount(AdminMenuEventEnum.PRODUCT_REFUND_CHECK);
//
//        if (directRefund) {
//            handleRefund(refundApply);
//        }
//    }
    @Transactional(rollbackFor = Exception.class)
    public void applyRefund(ProductRefundApplyDto dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();

        // 检查订单和订单项
        ProductOrder order = productManager.getOrderWithItems(dto.getOrderId());
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        if (!order.getUserId().equals(userId)) {
            throw new BaseException("无权操作此订单");
        }
        if (order.getStatus() != OrderStatusEnum.PAID) {
            throw new BaseException("订单状态不正确");
        }


        boolean directRefund = true; // 是否自动退款
        int refundAmount = 0; // 计算退款金额
        for(ProductOrderItem item : order.getItems()) {
            if(item.getUseTime() == null) {
                refundAmount += item.getRealAmount();
                Product product = JsonUtils.toObject(item.getSnapshot(), Product.class);
                if (item.getSnapshot() == null || product == null || product.getDirectRefund() == null || product.getDirectRefund().isFalse()) {
                    directRefund = false;
                }
            }
        }

        if (refundAmount > order.getRealAmount()) {
            throw new BaseException("退款金额不能大于实付金额");
        }

        // 创建退款申请
        ProductRefundApply refundApply = new ProductRefundApply();

        refundApply.setOrderId(dto.getOrderId())
                .setUserId(userId)
                .setStoreId(order.getStoreId())
                .setRefundAmount(refundAmount)
                .setReason(dto.getReason())
                .setDescription(dto.getDescription())
                .setStatus(RefundStatusEnum.PENDING)
                .setImages(dto.getImages());

        refundManager.createApply(refundApply);

        order.setStatus(OrderStatusEnum.REFUNDING);
        productManager.updateOrder(order);
        adminMenuService.emitCount(AdminMenuEventEnum.PRODUCT_REFUND_CHECK);

        if (directRefund) {
            refundManager.updateStatus(refundApply.getId(), RefundStatusEnum.APPROVED, "自动退款");
            handleRefund(refundApply);
        }
    }

    private List<Long> getIdsFromString(String str) {
        if(str == null || str.isEmpty()) return new ArrayList<>();
        return Arrays.stream(str.split(",")).map(Long::parseLong).toList();
    }

    public Page<ProductRefundApplyBo> pageList(PageRequest dto) {
        Page<ProductRefundApply> page = refundManager.pageList(dto);

        // 获取关联数据
        List<Long> productIds = page.getList().stream().map(ProductRefundApply::getProductId).toList();
        List<Long> storeIds = page.getList().stream().map(ProductRefundApply::getStoreId).toList();
        List<Long> orderIds = page.getList().stream().map(ProductRefundApply::getOrderId).toList();
//        List<Long> orderItemIds = new ArrayList<>();
//        Map<Long, List<Long>> applyOrderItemIdsMap = new HashMap<>();
//        for (ProductRefundApply refund : page.getList()) {
//            List<Long> ids = this.getIdsFromString(refund.getOrderItemIds());
//            orderItemIds.addAll(ids);
//            applyOrderItemIdsMap.put(refund.getId(), ids);
//        }

        List<Product> products = productManager.fetchProductByIdsWithRelation(productIds);
        List<Store> stores = storeManager.fetchByIds(storeIds);
        List<ProductOrder> orders = productManager.fetchOrderByIds(orderIds);
//        List<ProductOrderItem> orderItems = productManager.fetchOrderItemByIds(orderItemIds);

        Map<Long, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, v -> v));
        Map<Long, Store> storeMap = stores.stream().collect(Collectors.toMap(Store::getId, v -> v));
        Map<Long, ProductOrder> orderMap = orders.stream().collect(Collectors.toMap(ProductOrder::getId, v -> v));
//        Map<Long, ProductOrderItem> orderItemMap = orderItems.stream()
//                .collect(Collectors.toMap(ProductOrderItem::getId, v -> v));

        return page.convert(refund -> {
            ProductRefundApplyBo bo = BeanUtils.copy(refund, ProductRefundApplyBo.class);
            bo.setImages(refund.getImages());

            Product product = productMap.get(refund.getProductId());
            if (product != null) {
                bo.setProductObj(BeanUtils.copy(product, ProductBo.class));
            }

            Store store = storeMap.get(refund.getStoreId());
            if (store != null) {
                bo.setStoreObj(BeanUtils.copy(store, StoreBo.class));
            }

            ProductOrder order = orderMap.get(refund.getOrderId());
            if (order != null) {
                bo.setOrderObj(BeanUtils.copy(order, ProductOrderBo.class));
            }

//            ProductOrderItem orderItem = orderItemMap.get(refund.getOrderItemId());
//            if (orderItem != null) {
//                bo.setOrderItemObj(BeanUtils.copy(orderItem, ProductOrderItemBo.class));
//            }

            return bo;
        });
    }

    public ProductRefundApplyBo getDetail(Long id) {
        ProductRefundApply refund = refundManager.fetchById(id);
        if (refund == null) {
            throw new BaseException("退款申请不存在");
        }

        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        if (!refund.getUserId().equals(userId)) {
            throw new BaseException("无权查看此退款申请");
        }

        ProductRefundApplyBo bo = BeanUtils.copy(refund, ProductRefundApplyBo.class);
        bo.setImages(refund.getImages());

        // 填充关联数据
        Product product = productManager.fetchProductByIdWithRelation(refund.getProductId());
        if (product != null) {
            bo.setProductObj(BeanUtils.copy(product, ProductBo.class));
        }

        Store store = storeManager.fetchByIdWithRelation(refund.getStoreId());
        if (store != null) {
            bo.setStoreObj(BeanUtils.copy(store, StoreBo.class));
        }

        ProductOrder order = productManager.getOrderWithItems(refund.getOrderId());
        if (order != null) {
            bo.setOrderObj(BeanUtils.copy(order, ProductOrderBo.class));
        }

        return bo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelRefund(Long id) {
        ProductRefundApply refund = refundManager.fetchById(id);
        if (refund == null) {
            throw new BaseException("退款申请不存在");
        }

        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        if (!refund.getUserId().equals(userId)) {
            throw new BaseException("无权取消此退款申请");
        }

        if (refund.getStatus() != RefundStatusEnum.PENDING) {
            throw new BaseException("当前状态不可取消");
        }

        refundManager.updateStatus(id, RefundStatusEnum.CANCELLED, "用户取消退款申请");
        adminMenuService.emitCount(AdminMenuEventEnum.PRODUCT_REFUND_CHECK);
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleRefund(ProductRefundApply apply) {
        ProductOrder order = productManager.getOrderWithItems(apply.getOrderId());
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        if (order.getStatus() != OrderStatusEnum.REFUNDING) {
            throw new BaseException("当前订单状态不可处理");
        }
        ProductOrder originOrder = order;

        if (order.getParentId() != 0) {
            order = productManager.getOrderWithItems(order.getParentId());
            if (order == null) {
                throw new BaseException("订单不存在");
            }
        }

        Bill bill = billManager.fetchByEntityId(order.getId());
        if (bill == null) {
            throw new BaseException("订单不存在");
        }
        if (bill.getPayStatus() != BillPayStatusEnum.PAID && bill.getPayStatus() != BillPayStatusEnum.PART_REFUNDED) {
            throw new BaseException("当前账单状态不可处理");
        }
        
        // 处理商品的已售数量
        for (ProductOrderItem item : originOrder.getItems()) {
            if (item.getUseTime() == null) {  // 只处理未使用的商品
                Product product = productManager.fetchProductById(item.getProductId());
                if (product != null) {
                    product.setSoldCount(product.getSoldCount() - item.getQuantity());
                    product.setStock(product.getStock() + item.getQuantity());
                    if (product.getStock() > 0 && product.getStatus() == ProductStatusEnum.SOLD_OUT) {
                        product.setStatus(ProductStatusEnum.PUBLISHED);
                    }
                    productManager.saveProduct(product);
                }
            }
        }

        storeManager.calcSoldCount(originOrder.getStoreId());

        BillRefundDto refundDto = new BillRefundDto();
        refundDto.setReason(apply.getDescription());
        refundDto.setRemark(apply.getHandleNote());
        refundDto.setMoneyCent(apply.getRefundAmount());
        billManager.billRefund(bill, refundDto);

        originOrder.setStatus(OrderStatusEnum.REFUNDED);
        refundManager.saveRefunded(apply.getId(), IsEnum.TRUE);
        productManager.updateOrder(originOrder);
        adminMenuService.emitCount(AdminMenuEventEnum.PRODUCT_REFUND_CHECK);
    }

    @Transactional(rollbackFor = Exception.class)
    public void forceRefund(ProductOrderForceRefundDto dto) {
        ProductOrder order = productManager.getOrderWithItems(dto.getOrderId());
        if (order == null) {
            throw new BaseException("订单不存在");
        }
//        if (order.getStatus() != OrderStatusEnum.REFUNDING) {
//            throw new BaseException("当前订单状态不可处理");
//        }
        ProductOrder originOrder = order;

        if (order.getParentId() != 0) {
            order = productManager.getOrderWithItems(order.getParentId());
            if (order == null) {
                throw new BaseException("订单不存在");
            }
        }

        Bill bill = billManager.fetchByEntityId(order.getId());
        if (bill == null) {
            throw new BaseException("订单不存在");
        }
        if (bill.getPayStatus() != BillPayStatusEnum.PAID && bill.getPayStatus() != BillPayStatusEnum.PART_REFUNDED) {
            throw new BaseException("当前账单状态不可处理");
        }

        // 处理商品的已售数量
        for (ProductOrderItem item : originOrder.getItems()) {
            if (item.getUseTime() == null) {  // 只处理未使用的商品
                Product product = productManager.fetchProductById(item.getProductId());
                if (product != null) {
                    product.setSoldCount(product.getSoldCount() - item.getQuantity());
                    product.setStock(product.getStock() + item.getQuantity());
                    if (product.getStock() > 0 && product.getStatus() == ProductStatusEnum.SOLD_OUT) {
                        product.setStatus(ProductStatusEnum.PUBLISHED);
                    }
                    productManager.saveProduct(product);
                }
            }
        }

        storeManager.calcSoldCount(originOrder.getStoreId());

        BillRefundDto refundDto = new BillRefundDto();
        refundDto.setReason(dto.getReason() == null ? "后台强制退款" : dto.getReason());
        refundDto.setRemark(dto.getRemark() == null ? "后台强制退款" : dto.getRemark());
        refundDto.setMoneyCent(dto.getRefundCent());
        billManager.billRefund(bill, refundDto);

        originOrder.setStatus(OrderStatusEnum.REFUNDED);

        productManager.updateOrder(originOrder);
        adminMenuService.emitCount(AdminMenuEventEnum.PRODUCT_REFUND_CHECK);
    }

}
