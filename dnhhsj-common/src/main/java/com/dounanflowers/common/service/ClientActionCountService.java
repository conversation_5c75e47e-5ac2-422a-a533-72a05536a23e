package com.dounanflowers.common.service;

import com.dounanflowers.bpm.dto.PlaySourceCountDto;
import com.dounanflowers.common.bo.ClientActionCountBo;
import com.dounanflowers.common.entity.ClientActionCount;
import com.dounanflowers.common.manager.ClientActionCountManager;
import com.dounanflowers.framework.utils.BeanUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ClientActionCountService {
    private final ClientActionCountManager clientActionCountManager;

    public void incCount(String key) {
        clientActionCountManager.incCount(key);
    }

    public List<ClientActionCountBo> playSource(PlaySourceCountDto dto) {
        List<ClientActionCount> clientActionCounts = clientActionCountManager.fetchPlaySourceList(
                dto.getStartTime(),
                dto.getEndTime(),
                dto.getTimeUnit()
        );
        return clientActionCounts.stream().map(v -> BeanUtils.copy(v, ClientActionCountBo.class)).toList();
    }
}
