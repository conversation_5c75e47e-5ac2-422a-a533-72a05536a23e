package com.dounanflowers.common.service;

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.binarywang.wx.miniapp.constant.WxMaConstants;
import com.dounanflowers.common.bo.MsgCategoryBo;
import com.dounanflowers.common.bo.MsgDetailBo;
import com.dounanflowers.common.dto.MsgSendByTemplateDto;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.MsgProcessStatusEnum;
import com.dounanflowers.common.enums.MsgTypeEnum;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.common.manager.ClientUserManager;
import com.dounanflowers.common.manager.MsgManager;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.third.ThirdPartyHolder;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MsgService {
    private final MsgManager msgManager;
    private final TemplateEngine templateEngine;
    private final AdminUserManager adminUserManager;
    private final ClientUserManager clientUserManager;

    @Value("${isProd:true}")
    private boolean prod;

    @Transactional(rollbackFor = Exception.class)
    public void sendMessageByTemplate(MsgSendByTemplateDto dto) {
        List<MsgTemplate> msgTemplates = msgManager.fetchTemplateByCode(dto.getTemplateCode());
        if (msgTemplates == null || msgTemplates.isEmpty()) {
            return;
        }
        if (CollectionUtils.isEmpty(dto.getReceiveUserIds())) {
            return;
        }
        List<MsgDetail> msgDetails = Lists.newArrayList();
        Context context = new Context();
        context.setVariables(JsonUtils.toMap(JsonUtils.toJson(dto.getContext())));
        for (MsgTemplate msgTemplate : msgTemplates) {
            String title = templateEngine.process(msgTemplate.getTitle(), context);
            String content = templateEngine.process(msgTemplate.getContent(), context);
            for (Long receiveUserId : dto.getReceiveUserIds()) {
                MsgDetail msg = new MsgDetail();
                msg.setTitle(title);
                msg.setContent(content);
                msg.setCategoryId(msgTemplate.getCategoryId());
                msg.setSendUserId(dto.getSendUserId());
                msg.setReceiveUserId(receiveUserId);
                msg.setOuterType(dto.getOuterType());
                msg.setOuterId(dto.getOuterId());
                msg.setRead(IsEnum.FALSE);
                msg.setType(msgTemplate.getType());
                msg.setChannel(msgTemplate.getChannel());
                msg.setExtra(JsonUtils.toJson(dto.getContext()));
                if (msgTemplate.getType() == MsgTypeEnum.TODO) {
                    msg.setStatus(MsgProcessStatusEnum.UNPROCESSED);
                } else {
                    msg.setStatus(MsgProcessStatusEnum.DEFAULT);
                }
                msgDetails.add(msg);
            }
        }
        msgManager.insertMsgList(msgDetails);
        sendThirdMsg(msgDetails);
    }

    private void sendThirdMsg(List<MsgDetail> msgDetails) {
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(msgDetails.stream().map(MsgDetail::getReceiveUserId).distinct().toList());
        List<ClientUser> clientUsers = clientUserManager.fetchByIds(msgDetails.stream().map(MsgDetail::getReceiveUserId).distinct().toList());
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));
        Map<Long, ClientUser> clientUserMap = clientUsers.stream().collect(Collectors.toMap(ClientUser::getId, v -> v));
        for (MsgDetail msgDetail : msgDetails) {
            AdminUser adminUser = adminUserMap.get(msgDetail.getReceiveUserId());
            ClientUser clientUser = clientUserMap.get(msgDetail.getReceiveUserId());
            switch (msgDetail.getChannel()) {
                case WECHAT_MINI_PROGRAM -> {
                    if (adminUser != null) {
                        sendMiniMsg(adminUser.getOpenId(), msgDetail, "shop");
                    } else if (clientUser != null) {
                        sendMiniMsg(clientUser.getOpenId(), msgDetail, "client");
                    }
                }
            }
        }
    }

    private void sendMiniMsg(String openId, MsgDetail msgDetail, String env) {
        if (StringUtils.isBlank(openId)) {
            return;
        }
        WxMaSubscribeMessage subscribeMessage = new WxMaSubscribeMessage();
        subscribeMessage.setToUser(openId);
        subscribeMessage.setTemplateId(msgDetail.getTitle());
        Map<String, Object> map = JsonUtils.toMap(msgDetail.getContent());
        List<WxMaSubscribeMessage.MsgData> dataList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if ("pages".equals(entry.getKey())) {
                subscribeMessage.setPage(entry.getValue().toString());
            } else {
                WxMaSubscribeMessage.MsgData msgData = new WxMaSubscribeMessage.MsgData();
                msgData.setName(entry.getKey());
                if (entry.getKey().startsWith("thing")) {
                    String string = entry.getValue().toString();
                    String value = string.length() > 20 ? string.substring(0, 17) + "..." : string;
                    msgData.setValue(value);
                } else {
                    msgData.setValue(entry.getValue().toString());
                }
                dataList.add(msgData);
            }
        }
        subscribeMessage.setData(dataList);
        if (!prod) {
            subscribeMessage.setMiniprogramState(WxMaConstants.MiniProgramState.TRIAL);
        }
        try {
            ThirdPartyHolder.wxMaService(env).getMsgService().sendSubscribeMsg(subscribeMessage);
        } catch (WxErrorException e) {
            log.error("{}发送订阅消息失败", env, e);
        }
    }

    public void doneTodoMsgByOuterTypeAndId(String type, Long outerId, Long userId) {
        List<MsgDetail> msgDetails = msgManager.fetchTodoMsgByOuterTypeAndId(type, outerId);
        if (msgDetails == null || msgDetails.isEmpty()) {
            return;
        }
        for (MsgDetail msgDetail : msgDetails) {
            msgDetail.setStatus(MsgProcessStatusEnum.PROCESSED);
            msgDetail.setProcessUserId(userId);
            msgManager.saveMsg(msgDetail);
        }
    }

    public Page<MsgDetailBo> pageList(PageRequest pageRequest) {
        // 获取所有分类信息
        List<MsgCategory> categories = msgManager.listAllCategories();
        // 创建分类ID到分类的映射
        Map<Long, MsgCategory> categoryMap = categories.stream()
                .collect(Collectors.toMap(MsgCategory::getId, category -> category));

        // 转换消息列表，并设置分类信息
        return msgManager.pageList(pageRequest).convert(msgDetail -> {
            MsgDetailBo msgDetailBo = BeanUtils.copy(msgDetail, MsgDetailBo.class);
            // 如果消息有分类ID，则设置对应的分类信息
            if (msgDetail.getCategoryId() != null) {
                MsgCategory category = categoryMap.get(msgDetail.getCategoryId());
                if (category != null) {
                    msgDetailBo.setCategoryCode(category.getCode());
                    msgDetailBo.setCategoryName(category.getTitle());
                }
            }
            msgDetailBo.setExtra(JsonUtils.toMap(msgDetail.getExtra()));
            return msgDetailBo;
        });
    }

    public void read(Long id) {
        MsgDetail msgDetail = msgManager.fetchById(id);
        if (msgDetail != null) {
            msgDetail.setRead(IsEnum.TRUE);
            msgManager.saveMsg(msgDetail);
        }
    }

    public List<MsgCategoryBo> listAllCategories() {
        List<MsgCategory> categories = msgManager.listAllCategories();
        List<MsgCategoryBo> categoryBos = categories.stream()
                .map(category -> BeanUtils.copy(category, MsgCategoryBo.class))
                .toList();

        // 构建父子关系
        Map<Long, List<MsgCategoryBo>> parentChildrenMap = categoryBos.stream()
                .filter(bo -> bo.getParentId() != null && bo.getParentId() > 0)
                .collect(Collectors.groupingBy(MsgCategoryBo::getParentId));

        // 设置子节点
        categoryBos.forEach(bo -> bo.setChildren(parentChildrenMap.getOrDefault(bo.getId(), new ArrayList<>())));

        // 只返回顶层节点
        return categoryBos.stream()
                .filter(bo -> bo.getParentId() == null || bo.getParentId() == 0)
                .collect(Collectors.toList());
    }

    public void retry(Long id) {
        MsgDetail msgDetail = msgManager.fetchById(id);
        if (msgDetail != null) {
            sendThirdMsg(List.of(msgDetail));
        }
    }

}
