package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.FileBo;
import com.dounanflowers.common.bo.ShopBo;
import com.dounanflowers.common.entity.Shop;
import com.dounanflowers.common.enums.FileTypeEnum;
import com.dounanflowers.common.manager.ShopManager;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.HttpUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.util.List;
import java.util.Objects;


@Service
@Slf4j
@RequiredArgsConstructor
public class ShopService {
    private final ShopManager shopManager;
    private final FileService fileService;

    public ShopBo getById(Long id) {
        Shop shop = shopManager.fetchById(id);
        if (shop == null) {
            return null;
        }
        return getShopBo(shop);
    }

    @NotNull
    private ShopBo getShopBo(Shop shop) {
        ShopBo shopBo = BeanUtils.copy(shop, ShopBo.class);
        shopBo.setTags(shop.getTags());
        shopBo.setBusinessType(shop.getBusinessType());
        shopBo.setHouseType(shop.getHouseType());
        shopBo.setRegion(shop.getRegion());
        shopBo.setImages(shop.getImages());
        shopBo.setVideos(shop.getVideos());
        return shopBo;
    }

    @Transactional(rollbackFor = Exception.class)
    public ShopBo save(ShopBo bo) {
        Shop shop = BeanUtils.copy(bo, Shop.class);
        saveShopInner(bo, shop);
        bo.setId(shop.getId());
        return bo;
    }

    @Transactional(rollbackFor = Exception.class)
    public ShopBo saveWithCellNo(ShopBo bo) {
        // 根据cellNo查找已存在的shop
        Shop existingShop = shopManager.getByCellNo(bo.getCellNo());
        Shop shop = BeanUtils.copy(bo, Shop.class);
        if (existingShop != null) {
            shop.setId(existingShop.getId());
        } else {
            shop.setId(null);
        }
        saveShopInner(bo, shop);
        bo.setId(shop.getId());
        return bo;
    }

    private void saveShopInner(ShopBo bo, Shop shop) {
        shop.setBusinessType(bo.getBusinessType());
        shop.setRegion(bo.getRegion());
        shop.setTags(bo.getTags());
        shopManager.save(shop);
        if (bo.getHouseType() != null) {
            fileService.updateFileLink(shop.getId(), "houseType", bo.getHouseType(), FileTypeEnum.IMAGE);
        }
        if (bo.getImages() != null) {
            fileService.updateFileLink(shop.getId(), "images", bo.getImages(), FileTypeEnum.IMAGE);
        }
        if (bo.getVideos() != null) {
            fileService.updateFileLink(shop.getId(), "videos", bo.getVideos(), FileTypeEnum.VIDEO);
        }
    }

    public Boolean deleteById(Long id) {
        shopManager.deleteById(id);
        return true;
    }

    public Page<ShopBo> shopPageList(PageRequest pageRequest) {
        Page<Shop> shopPage = shopManager.pageList(pageRequest);
        return shopPage.convert(this::getShopBo);
    }

    public List<String> allTags() {
        return shopManager.allTags();
    }

    public Boolean rank() {
        return shopManager.rank();
    }

    public FileBo generateShopShareImage(Long id) {
        try {
            final double scale = 1.6;

            // 获取商店信息
            Shop shop = shopManager.fetchById(id);

            String cover = shop.getImages().getFirst();
            List<String> tags = shop.getTags();
            double unitPrice = shop.getUnitPrice();
            double cellArea = shop.getCellArea();
            boolean isRecommend = shop.getRecommend() != null && shop.getRecommend() > 0;
            boolean isHot = shop.getOverallRank() != null && shop.getOverallRank() > 0;

            // 生成state
            String newVersion = String.format("%d,%d,%s,%s,%.2f,%.2f",
                    isRecommend ? 1 : 0, isHot ? 1 : 0, cover,
                    String.join(",", tags), unitPrice, cellArea);
            if (!newVersion.equals(shop.getShareImageVersion())) {

                int canvasWidth = (int) (320 * scale);
                int canvasHeight = (int) (256 * scale);
                int imgHeight = (int) (150 * scale);

                // 创建图片
                BufferedImage image = new BufferedImage(
                        canvasWidth, canvasHeight, BufferedImage.TYPE_INT_ARGB);
                Graphics2D g2d = image.createGraphics();

                // 设置渲染质量
                g2d.setRenderingHint(
                        RenderingHints.KEY_ANTIALIASING,
                        RenderingHints.VALUE_ANTIALIAS_ON
                );

                // 绘制背景
                g2d.setColor(Color.WHITE);
                drawRoundRect(g2d, 0, 0, canvasWidth, canvasHeight, (int) (4 * scale));

                // 加载并绘制封面图
                BufferedImage coverImg = loadAndResizeImage(cover, canvasWidth, imgHeight);
                g2d.drawImage(coverImg, 0, 0, null);

                // 绘制标签
                int y = imgHeight + (int) (16 * scale);
                int x = 0;

                // 初始化字体
                Font font = Font.createFont(
                        Font.TRUETYPE_FONT,
                        Objects.requireNonNull(this.getClass().getClassLoader().getResourceAsStream("fonts/AlibabaPuHuiTi-3-55-Regular.ttf"))
                );

                int tagHeight = (int) (20 * scale);
                double tagGap = 4 * scale;

                // 绘制标签
                for (String tag : tags) {
                    int tagOffsetX = (int) (12 * scale);
                    // 绘制
                    x = (int) (tagGap + setTag(g2d, font, tag, x + tagOffsetX, y, tagHeight, (int) (2 * scale), "#f5f5f7", "#59595D") - tagOffsetX);
                }
                // 绘制热门
                if (isHot || isRecommend) {
                    String hotText = isHot ? "热门" : "推荐";
                    setTag(g2d, font, hotText, 0, 0, (int) (20 * scale), (int) (4 * scale), "#f94b30", "#ffffff");
                }
                // 绘制价格等信息
                y += (int) (44 * scale);
                x = (int) (12 * scale);
                Font priceFont = font.deriveFont(Font.BOLD, (int) (18 * scale));
                g2d.setFont(priceFont);
                g2d.setColor(new Color(249, 75, 48));

                String priceText = "¥" + unitPrice + "元/月";
                g2d.drawString(priceText, x, y);

                FontMetrics metrics = g2d.getFontMetrics(priceFont);
                int priceWidth = metrics.stringWidth(priceText);

                g2d.setFont(font.deriveFont(Font.BOLD, (int) (20 * scale)));
                g2d.drawString("起", x + priceWidth, y);

                g2d.setFont(font.deriveFont(Font.PLAIN, (int) (20 * scale)));
                g2d.drawString(cellArea + "㎡",
                        x + priceWidth + (int) (48 * scale), y);

                g2d.setFont(font.deriveFont(Font.BOLD, (int) (12 * scale)));
                g2d.setColor(new Color(153, 153, 153));

                g2d.drawString("租金", x, y + (int) (20 * scale));
                g2d.drawString("面积", x + priceWidth + (int) (50 * scale), y + (int) (20 * scale));

                // 保存图片
                File temp = File.createTempFile("shop_", shop.getId() + ".png");
                ImageIO.write(image, "PNG", temp);
                FileBo file = fileService.createFile(temp);
                shop.setShareImageUrl(file.getUrl());
                shop.setShareImageVersion(newVersion);
                shopManager.save(shop);
                return file;
            } else {
                FileBo fileBo = new FileBo();
                fileBo.setUrl(shop.getShareImageUrl());
                return fileBo;
            }
        } catch (Exception e) {
            log.error("生成商店分享图失败", e);
            throw new BaseException("生成商店分享图失败");
        }
    }

    private int setTag(Graphics2D g2d, Font font, String text, int x, int y, int height, int radius, String bg, String color) {
        Font font1 = font.deriveFont(Font.BOLD, height * 0.7f);
        int width = g2d.getFontMetrics(font1).stringWidth(text) + (int) (10 * 1.6);
        // 下一个安全位置
        int next_x = x + width;
        // 绘制标签背景
        g2d.setColor(Color.decode(bg));
        // 绘制圆角
        drawRoundRect(g2d, x, y, width, height, radius);
        // 绘制标签文本
        g2d.setColor(Color.decode(color));
        g2d.setFont(font1);
        FontMetrics metrics = g2d.getFontMetrics();
        int textX = x + (width - metrics.stringWidth(text)) / 2;
        int textY = y + ((height - metrics.getHeight()) / 2) + metrics.getAscent();
        g2d.drawString(text, textX, textY);
        return next_x;
    }

    private void drawRoundRect(Graphics2D g2d, int x, int y, int width, int height, int radius) {
        g2d.fillRoundRect(x, y, width, height, radius, radius);
    }

    private void drawTag(Graphics2D g2d, String text, int x, int y,
                         int width, int height, int radius,
                         Color bgColor, Color textColor) {
        // 绘制标签背景
        g2d.setColor(bgColor);
        drawRoundRect(g2d, x, y, width, height, radius);

        // 绘制文字
        g2d.setColor(textColor);
        FontMetrics metrics = g2d.getFontMetrics();
        int textX = x + (width - metrics.stringWidth(text)) / 2;
        int textY = y + ((height - metrics.getHeight()) / 2) + metrics.getAscent();
        g2d.drawString(text, textX, textY);
    }

    private BufferedImage loadAndResizeImage(String imageUrl, int width, int height) {
        try {
            HttpUtils.HttpResult send = HttpUtils.get(imageUrl + "?x-oss-process=image/resize,w_" + width + ",h_" + height + ",limit_0,m_fill").send();
            byte[] body = send.body();
            return ImageIO.read(new ByteArrayInputStream(body));
        } catch (Exception e) {
            throw new RuntimeException("Failed to load image: " + e.getMessage());
        }
    }

    public void updateViewCount(Long id) {
        shopManager.updateViewCount(id);
    }
}
