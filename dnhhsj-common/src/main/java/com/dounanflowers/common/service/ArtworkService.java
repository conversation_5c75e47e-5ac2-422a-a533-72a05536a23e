package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.ArtworkBo;
import com.dounanflowers.common.bo.FileBo;
import com.dounanflowers.common.entity.Artwork;
import com.dounanflowers.common.entity.FileInfo;
import com.dounanflowers.common.manager.ArtworkManager;
import com.dounanflowers.common.manager.SystemManager;
import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.utils.BeanUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ArtworkService {

    private final FileService fileService;

    private final ArtworkManager artworkManager;

    private final SystemManager systemManager;

    public Page<ArtworkBo> artworkPageList(PageRequest pageRequest) {
        Page<Artwork> artworkPage = artworkManager.pageList(pageRequest);
        List<Long> ids = artworkPage.getList().stream().map(BaseEntity::getId).toList();
        List<FileInfo> fileInfos = systemManager.fetchFileListByOuterIds(ids);
        Map<Long, List<FileInfo>> fileInfoMap = fileInfos.stream().collect(Collectors.groupingBy(FileInfo::getOuterId));
        return artworkPage.convert(v -> {
            ArtworkBo artworkBo = BeanUtils.copy(v, ArtworkBo.class);
            artworkBo.setCategoryId(v.getCategories());
            if (fileInfoMap.containsKey(v.getId())) {
                artworkBo.setImages(BeanUtils.copyList(fileInfoMap.get(v.getId()), FileBo.class));
            }
            return artworkBo;
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public ArtworkBo save(ArtworkBo bo) {
        Artwork artwork = BeanUtils.copy(bo, Artwork.class);
        artwork.setCategories(bo.getCategoryId());
        artworkManager.save(artwork);
        if (CollectionUtils.isNotEmpty(bo.getImages())) {
            fileService.updateFileBoLink(artwork.getId(), bo.getImages());
        }
        bo.setId(artwork.getId());
        return bo;
    }

    public ArtworkBo getById(Long id) {
        Artwork artwork = artworkManager.fetchById(id);
        if (artwork == null) {
            return null;
        }
        ArtworkBo artworkBo = BeanUtils.copy(artwork, ArtworkBo.class);
        artworkBo.setCategoryId(artwork.getCategories());
        List<FileInfo> fileInfos = systemManager.fetchFileListByOuterId(artwork.getId());
        artworkBo.setImages(BeanUtils.copyList(fileInfos, FileBo.class));
        return artworkBo;
    }

    public Boolean deleteById(Long id) {
        artworkManager.deleteById(id);
        return true;
    }

}
