package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.ModalBannerBo;
import com.dounanflowers.common.entity.ModalBanner;
import com.dounanflowers.common.enums.ClientTypeEnum;
import com.dounanflowers.common.manager.ModalBannerManager;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.utils.BeanUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ModalBannerService {

    private final ModalBannerManager modalBannerManager;

    public Page<ModalBannerBo> pageList(PageRequest pageRequest) {
        Page<ModalBanner> page = modalBannerManager.pageList(pageRequest);
       return page.convert(this::toModalBannerBo);
    }

    public ModalBannerBo save(ModalBannerBo bo) {
        ModalBanner modalBanner = BeanUtils.copy(bo, ModalBanner.class);
        modalBannerManager.save(modalBanner);
        return toModalBannerBo(modalBanner);
    }

    public Boolean deleteModalBanner(Long id) {
        modalBannerManager.deleteById(id);
        return true;
    }

    public List<ModalBannerBo> getModalBannerShowList(ClientTypeEnum client) {
        return modalBannerManager.findActiveByClient(client)
                .stream()
                .map(this::toModalBannerBo)
                .toList();
    }

    private ModalBannerBo toModalBannerBo(ModalBanner modalBanner) {
        if (modalBanner == null) {
            return null;
        }
        ModalBannerBo bo = new ModalBannerBo();
        BeanUtils.copyProperties(modalBanner, bo);
        return bo;
    }
}
