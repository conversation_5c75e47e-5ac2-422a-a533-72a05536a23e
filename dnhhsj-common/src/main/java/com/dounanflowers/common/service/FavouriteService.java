package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.FavouriteBo;
import com.dounanflowers.common.dto.FavouriteCheckDto;
import com.dounanflowers.common.dto.FavouriteToggleDto;
import com.dounanflowers.common.entity.Favourite;
import com.dounanflowers.common.entity.Shop;
import com.dounanflowers.common.enums.FavouriteTypeEnum;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.common.manager.FavouriteManager;
import com.dounanflowers.common.manager.ShopManager;
import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.utils.BeanUtils;
import com.google.common.collect.Maps;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Db;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class FavouriteService {

    private final FavouriteManager favouriteManager;

    private final ShopManager shopManager;

    public Page<FavouriteBo> page(PageRequest pageRequest) {
        Page<Favourite> favouritePage = favouriteManager.page(pageRequest);
        Map<FavouriteTypeEnum, List<Long>> favouriteEntityIdMap = favouritePage.getList().stream()
                .collect(Collectors.groupingBy(
                        Favourite::getType,
                        Collectors.mapping(Favourite::getEntityId, Collectors.toList())
                ));

        Map<FavouriteTypeEnum, Map<Long, Object>> favouriteEntityNameMap =
                favouriteEntityIdMap.entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, e -> {
                            List<Long> ids = e.getValue();
                            FavouriteTypeEnum type = e.getKey();
                            if (type == FavouriteTypeEnum.SHOP) {
                                List<Shop> shops = shopManager.fetchByIds(ids);
                                return shops.stream().map(v -> {
                                    Shop shop = BeanUtils.copy(v, Shop.class);
                                    shop.setImages(v.getImages());
                                    shop.setBusinessType(v.getBusinessType());
                                    shop.setTags(v.getTags());
                                    shop.setRegion(v.getRegion());
                                    shop.setHouseType(v.getHouseType());
                                    shop.setVideos(v.getVideos());
                                    return shop;
                                }).collect(Collectors.toMap(Shop::getId, r -> r));
                            }
                            return Db.selectListByQuery(type.getTableName(), QueryWrapper.create().in("id", ids))
                                    .stream().map(v -> v.toObject(type.getDbClazz()))
                                    .collect(Collectors.toMap(BaseEntity::getId, r -> BeanUtils.copy(r, type.getBoClazz())));
                        }));
        return favouritePage.convert(v -> {
            FavouriteBo copy = BeanUtils.copy(v, FavouriteBo.class);
            copy.setEntityObj(favouriteEntityNameMap.getOrDefault(v.getType(), Maps.newHashMap()).get(v.getEntityId()));
            return copy;
        });
    }

    public boolean toggle(FavouriteToggleDto dto, Long userId, UserTypeEnum userType) {
        List<String> userModels = List.of("", "admin_user", "client_user");
        String userModel = userModels.get(userType.ordinal());
        List<String> entityModels = List.of("", "shop", "article", "article", "store", "job_posting");
        String entityModel = entityModels.get(dto.getType().ordinal());


        Favourite favourite = favouriteManager.fetchFavourite(dto.getType(), dto.getEntityId(), userId, userModel);
        boolean actionIsAdd = favourite == null;
        if (dto.getForceAction() != null) {
            actionIsAdd = dto.getForceAction();
        }
        int updateCount = 0;
        if (!actionIsAdd && favourite != null) { // 不要收藏但存在
            favouriteManager.deleteById(favourite.getId());
            updateCount = -1;
        } else if (actionIsAdd && favourite == null) { // 要收藏但不存在
            favourite = new Favourite();
            favourite.setType(dto.getType());
            favourite.setEntityId(dto.getEntityId());
            favourite.setEntityModel(entityModel);
            favourite.setUserId(userId);
            favourite.setUserModel(userModel);
            favourite.setTitle(dto.getTitle());
            favouriteManager.save(favourite);
            updateCount = 1;
        } else if (actionIsAdd) { // 要收藏也存在
            favourite.setTitle(dto.getTitle());
            favouriteManager.save(favourite);
            updateCount = 1;
        }
        if (updateCount != 0) {
            updateFavouriteCount(dto.getType(), dto.getEntityId());
        }
        return actionIsAdd;
    }

    public void updateFavouriteCount(FavouriteTypeEnum type, Long entityId) {
        Integer count = favouriteManager.countByEntityId(type, entityId);
        switch (type) {
            case SHOP:
                Shop shop = shopManager.fetchById(entityId).setFavouriteCount(count);
                shopManager.save(shop);
                break;
            default:
                break;
        }
    }


    public Boolean check(FavouriteCheckDto dto, Long userId, UserTypeEnum userType) {
        List<String> userModels = List.of("", "admin_user", "client_user");
        String userModel = userModels.get(userType.ordinal());
        Favourite favourite = favouriteManager.fetchFavourite(dto.getType(), dto.getEntityId(), userId, userModel);
        return favourite != null;
    }



}
