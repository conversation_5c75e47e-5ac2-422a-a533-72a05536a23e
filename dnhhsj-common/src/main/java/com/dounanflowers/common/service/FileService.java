package com.dounanflowers.common.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.dounanflowers.common.bo.FileBo;
import com.dounanflowers.common.bo.MediaBo;
import com.dounanflowers.common.entity.FileInfo;
import com.dounanflowers.common.enums.FileTypeEnum;
import com.dounanflowers.common.manager.SystemManager;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.DateUtils;
import com.dounanflowers.third.ThirdPartyHolder;
import com.dounanflowers.third.entity.AliyunOss;
import com.drew.imaging.ImageMetadataReader;
import com.drew.imaging.ImageProcessingException;
import com.drew.metadata.Metadata;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLConnection;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class FileService {

    private final SystemManager systemManager;

    public FileBo createFile(File file) {
        try (InputStream inputStream = new FileInputStream(file)) {
            String filename = file.getName();
            String suffix = filename.contains(".") ? filename.substring(filename.lastIndexOf(".")) : "";
            String saveName = String.format("uploads/%s/%s%s",
                    DateUtils.formatLocalDate(LocalDate.now(), "YYMMDD"),
                    UUID.randomUUID().toString().replaceAll("-", ""),
                    suffix);
            FileInfo fileInfo = new FileInfo();
            fileInfo.setName(filename);
            fileInfo.setObject(saveName);
            fileInfo.setSize(file.length());
            setFileType(file, fileInfo);
            fileInfo.setSeq(0);
            String url = uploadBytes(saveName, inputStream);
            String domain = ThirdPartyHolder.ossConfig().getDomain();
            fileInfo.setUrl(StringUtils.isNotBlank(domain) ? domain + saveName : url);
            systemManager.saveFile(fileInfo);
            return BeanUtils.copy(fileInfo, FileBo.class);
        } catch (Exception e) {
            log.error("上传失败", e);
            throw new BaseException("上传失败");
        }
    }

    public FileBo createFile(MultipartFile file) {
        String filename = file.getOriginalFilename() != null ? file.getOriginalFilename() : "";
        String suffix = filename.contains(".") ? filename.substring(filename.lastIndexOf(".")) : "";
        String saveName = String.format("uploads/%s/%s%s",
                DateUtils.formatLocalDate(LocalDate.now(), "YYMMDD"),
                UUID.randomUUID().toString().replaceAll("-", ""),
                suffix);
        FileInfo fileInfo = new FileInfo();
        fileInfo.setName(filename);
        fileInfo.setObject(saveName);
        fileInfo.setSize(file.getSize());
        fileInfo.setSeq(0);
        try {
            File temp = File.createTempFile(file.getOriginalFilename(), saveName.replace("/", "_"));
            file.transferTo(temp);
            setFileType(temp, fileInfo);
            InputStream inputStream;
            if (fileInfo.getType() == FileTypeEnum.IMAGE) {
                try {
                    if (fileInfo.getWidth() > 2048) {
                        int width = 2048;
                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                        Thumbnails.of(temp)
                                .width(width)
                                .outputFormat("jpg")
                                .toOutputStream(outputStream);
                        inputStream = new ByteArrayInputStream(outputStream.toByteArray());
                    } else if (fileInfo.getHeight() > 2048) {
                        int height = 2048;
                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                        Thumbnails.of(temp)
                                .height(height)
                                .outputFormat("jpg")
                                .toOutputStream(outputStream);
                        inputStream = new ByteArrayInputStream(outputStream.toByteArray());
                    } else {
                        inputStream = new FileInputStream(temp);
                    }
                } catch (IOException e) {
                    log.warn("图片压缩失败{}", saveName);
                    inputStream = new FileInputStream(temp);
                }
            } else {
                inputStream = new FileInputStream(temp);
            }
            String url = uploadBytes(saveName, inputStream);
            String domain = ThirdPartyHolder.ossConfig().getDomain();
            fileInfo.setUrl(StringUtils.isNotBlank(domain) ? domain + saveName : url);
            systemManager.save(fileInfo);
            return BeanUtils.copy(fileInfo, FileBo.class);
        } catch (IOException | ImageProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private static void setFileType(File file, FileInfo fileInfo) throws IOException, ImageProcessingException {
        URLConnection connection = file.toURI().toURL().openConnection();
        String mimeType = connection.getContentType();
        if (StringUtils.isNotBlank(mimeType)) {
            if (mimeType.startsWith("image")) {
                fileInfo.setType(FileTypeEnum.IMAGE);
                Metadata metadata = ImageMetadataReader.readMetadata(file);
                metadata.getDirectories().forEach(directory -> directory.getTags().forEach(tag -> {
                    if ("Image Height".equals(tag.getTagName())) {
                        String replace = tag.getDescription().replace(" pixels", "");
                        fileInfo.setHeight(Long.parseLong(replace));
                    } else if ("Image Width".equals(tag.getTagName())) {
                        String replace = tag.getDescription().replace(" pixels", "");
                        fileInfo.setWidth(Long.parseLong(replace));
                    }
                }));
            } else if (mimeType.startsWith("video")) {
                fileInfo.setType(FileTypeEnum.VIDEO);
                Metadata metadata = ImageMetadataReader.readMetadata(file);
                Map<String, String> meta = new HashMap<>();
                metadata.getDirectories().forEach(directory -> directory.getTags().forEach(tag -> {
                    if ("Duration".equals(tag.getTagName())) {
                        fileInfo.setDuration(Long.parseLong(tag.getDescription()));
                    } else if ("Width".equals(tag.getTagName())) {
                        String replace = tag.getDescription().replace(" pixels", "");
                        fileInfo.setWidth(Long.parseLong(replace));
                    } else if ("Height".equals(tag.getTagName())) {
                        String replace = tag.getDescription().replace(" pixels", "");
                        fileInfo.setHeight(Long.parseLong(replace));
                    }
                }));
            } else {
                fileInfo.setType(FileTypeEnum.UNKNOWN);
            }
        } else {
            fileInfo.setType(FileTypeEnum.UNKNOWN);
        }
    }

    public void updateFileLink(Long id, String field, String image) {
        updateFileLink(id, field, List.of(image));
    }

    public void updateFileLink(Long id, String image) {
        updateFileLink(id, "image", image);
    }

    public void updateFileBoLink(Long id, List<FileBo> images) {
        updateFileBoLink(id, "images", images);
    }

    public void updateFileBoLink(Long id, String field, List<FileBo> images) {
        List<FileInfo> existFileList = systemManager.fetchFileListByOuterId(id, field);
        List<String> urlList = images.stream().map(FileBo::getUrl).toList();
        Map<String, FileInfo> existFileMap = existFileList.stream().collect(Collectors.toMap(FileInfo::getUrl, v -> v));
        if (CollectionUtils.isNotEmpty(images)) {
            List<FileInfo> fileList = systemManager.fetchFileList(urlList);
            Map<String, FileInfo> fileMap = fileList.stream().collect(Collectors.toMap(FileInfo::getUrl, v -> v));
            int sequence = 0;
            for (FileBo fileBo : images) {
                existFileMap.remove(fileBo.getUrl());
                FileInfo fileInfo = fileMap.get(fileBo.getUrl());
                if (fileInfo == null) {
                    fileInfo = BeanUtils.copy(fileBo, FileInfo.class);
                    fileInfo.setType(FileTypeEnum.UNKNOWN);
                }
                fileInfo.setOuterId(id);
                fileInfo.setField(field);
                if (fileBo.getWidth() != null) {
                    fileInfo.setWidth(fileBo.getWidth().longValue());
                }
                if (fileBo.getHeight() != null) {
                    fileInfo.setHeight(fileBo.getHeight().longValue());
                }
                if (fileBo.getDuration() != null) {
                    fileInfo.setDuration(fileBo.getDuration().longValue());
                }
                fileInfo.setSeq(sequence++);
                systemManager.saveFile(fileInfo);
            }
        }
        existFileMap.values().forEach(fileInfo -> deleteById(fileInfo.getId()));
    }

    public void updateFileLink(Long id, String field, List<String> images) {
        updateFileLink(id, field, images, null);
    }

    public void updateFileLink(Long id, String field, List<String> images, FileTypeEnum type) {
        List<FileInfo> existFileList = systemManager.fetchFileListByOuterId(id, field);
        Map<String, FileInfo> existFileMap = existFileList.stream().collect(Collectors.toMap(FileInfo::getUrl, v -> v));
        if (CollectionUtils.isNotEmpty(images)) {
            int sequence = 0;
            List<FileInfo> fileInfos = systemManager.fetchFileList(images);
            Map<String, FileInfo> fileMap = fileInfos.stream().collect(Collectors.toMap(FileInfo::getUrl, v -> v));
            for (String image : images) {
                existFileMap.remove(image);
                FileInfo fileInfo = fileMap.get(image);
                if (fileInfo == null) {
                    fileInfo = new FileInfo();
                    fileInfo.setUrl(image);
                    fileInfo.setType(type == null ? FileTypeEnum.UNKNOWN : type);
                }
                fileInfo.setOuterId(id);
                fileInfo.setField(field);
                fileInfo.setSeq(sequence++);
                systemManager.saveFile(fileInfo);
            }
        }
        existFileMap.values().forEach(fileInfo -> deleteById(fileInfo.getId()));
    }

    public void updateFileLink(Long id, List<String> images) {
        updateFileLink(id, "images", images);
    }

    public void deleteById(Long id) {
        FileInfo file = systemManager.findById(id);
        if (file == null) {
            return;
        }
        systemManager.deleteById(file.getId());
        if (file.getObject() == null) {
            return;
        }
//        deleteOssObject(file.getObject());
    }

    private String uploadBytes(String name, InputStream content) {
        OSS ossClient = ThirdPartyHolder.ossClient();
        AliyunOss aliyunOss = ThirdPartyHolder.ossConfig();
        ObjectMetadata metadata = new ObjectMetadata();
        ossClient.putObject(aliyunOss.getBucketName(), name, content, metadata);
        OSSObject object = ossClient.getObject(aliyunOss.getBucketName(), name);
        return object.getResponse().getUri();
    }

    private void deleteOssObject(String name) {
        OSS ossClient = ThirdPartyHolder.ossClient();
        AliyunOss aliyunOss = ThirdPartyHolder.ossConfig();
        ossClient.deleteObject(aliyunOss.getBucketName(), name);
    }

    @SuppressWarnings("unchecked")
    public Page<MediaBo> mediaPageList(PageRequest pageRequest) {
        if (pageRequest.getFilter() != null) {
            for (PageFilter pageFilter : pageRequest.getFilter()) {
                if ("type".equals(pageFilter.getField())) {
                    if (pageFilter.getValue() instanceof List) {
                        List values = (List<String>) pageFilter.getValue();
                        values.replaceAll(o -> FileTypeEnum.valueOf(o.toString().toUpperCase()));
                    } else {
                        pageFilter.setValue(FileTypeEnum.valueOf(pageFilter.getValue().toString().toUpperCase()));
                    }
                }
            }
        }
        Page<FileInfo> page = systemManager.filePageList(pageRequest);
        return page.convert(fileInfo -> {
            MediaBo mediaBo = BeanUtils.copy(fileInfo, MediaBo.class);
            mediaBo.setType(fileInfo.getType().name().toLowerCase());
            if (fileInfo.getDuration() != null) {
                mediaBo.setDuration(fileInfo.getDuration() / 1000.0);
            } else {
                mediaBo.setDuration(0.0);
            }
            if (fileInfo.getWidth() == null) {
                mediaBo.setWidth(0);
            } else {
                mediaBo.setWidth(fileInfo.getWidth().intValue());
            }
            if (fileInfo.getHeight() == null) {
                mediaBo.setHeight(0);
            } else {
                mediaBo.setHeight(fileInfo.getHeight().intValue());
            }
            return mediaBo;
        });
    }

}
