package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.*;
import com.dounanflowers.common.entity.Bill;
import com.dounanflowers.common.entity.EnergyCell;
import com.dounanflowers.common.entity.PropertyCell;
import com.dounanflowers.common.enums.BillPayStatusEnum;
import com.dounanflowers.common.enums.BillTypeEnum;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.common.manager.BillManager;
import com.dounanflowers.common.manager.EnergyManager;
import com.dounanflowers.common.manager.PropertyCellManager;
import com.dounanflowers.common.manager.UserCommonManager;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.core.RedisCache;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.ThirdPartyHolder;
import com.dounanflowers.third.bo.*;
import com.dounanflowers.third.dto.PayParamsDto;
import com.mybatisflex.core.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class EnergyService {
    private final EnergyManager energyManager;
    private final UserCommonManager userCommonManager;
    private final BillManager billManager;
    private final PropertyCellManager propertyCellManager;

    @Autowired
    private final RedisCache redisCache;

    public Page<EnergyCellBo> energyCellPage(PageRequest dto) {
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("adminUserObj".equals(filter.getField()) && "custom".equals(filter.getType()) && StringUtil.isNotBlank((String) filter.getValue())) {
                    List<Long> userIds = userCommonManager.fetchUserIdsByLike((String) filter.getValue());
                    if(userIds.isEmpty()) return Page.empty();
                    filter.setType("in");
                    filter.setField("adminUserId");
                    filter.setValue(userIds);
                }
            }
        }

        Page<EnergyCell> page = energyManager.page(dto);
        List<Long> adminUserIds = page.getList().stream().map(EnergyCell::getAdminUserId).toList();
        Map<Long, UserObjBo> adminUserMap = userCommonManager.fetchUserMapByUserIds(adminUserIds);

        return page.convert(v -> {
            EnergyCellBo bo = BeanUtils.copy(v, EnergyCellBo.class);
            if (adminUserMap.get(v.getAdminUserId()) != null) {
                bo.setAdminUserObj(adminUserMap.get(v.getAdminUserId()));
            }
            return bo;
        });
    }

    public WxPayParamsBo energyCellPrepay(EnergyCellPrepayDto dto) {
        EnergyCell energyCell = energyManager.fetchById(dto.getEnergyCellId());
        AdminUserBo userInfo = SecurityHolder.<Long, AdminUserBo>session().getUserInfo();
        Bill bill = new Bill();
        bill.setType(BillTypeEnum.ENERGY_CELL_PREPAY);
        bill.setEntityId(energyCell.getId());
        bill.setUserId(energyCell.getAdminUserId());
        bill.setUserType(UserTypeEnum.ADMIN);
        bill.setUserModel("admin_user");
        bill.setOriginalMoneyCent(dto.getMoney() * 100);
        bill.setCouponDiscountCent(0);
        bill.setOrderMoneyCent(dto.getMoney() * 100);
        bill.setPayStatus(BillPayStatusEnum.UNPAID);
        bill.setOtherData(JsonUtils.toJson(energyCell));
        billManager.saveBill(bill);

        if (dto.getFake() == null || !dto.getFake()) {
            PayParamsDto params = new PayParamsDto();
            params.setTrxamt(bill.getOrderMoneyCent());
            params.setUnireqsn(String.valueOf(bill.getId()));
            params.setAcct(userInfo.getOpenId());
            params.setBody("预存水电费");
            params.setRemark(userInfo.getMobile());
            return ThirdPartyHolder.allinpayService("shop").pay(params);

        } else {
            bill.setPayStatus(BillPayStatusEnum.PAID);
            bill.setPaidAt(LocalDateTime.now());
            billManager.saveBill(bill);

            this.handleEnergyCellPaid(bill);
            WxPayParamsBo wxPayParamsBo = new WxPayParamsBo();
            wxPayParamsBo.setPaid(true);
            return wxPayParamsBo;
        }
    }

    /**
     * 处理水电费支付完成
     */
    public void handleEnergyCellPaid(Bill bill) {
        log.info("处理水电费支付完成, billId={}", bill.getId());
        EnergyCell energyCell = energyManager.fetchById(bill.getEntityId());
        ThirdPartyHolder.wuguanwangService().prepayEnergy(
            energyCell.getCompanyCode(),
            energyCell.getCellId(),
                bill.getOrderMoneyCent() / 100,
            String.valueOf(bill.getId())
        );
    }

    public EnergyCellBo energyCellGetById(Long id) {
        EnergyCell entity = energyManager.fetchById(id);
        return BeanUtils.copy(entity, EnergyCellBo.class);
    }

    public EnergyCellBo energyCellSave(EnergyCellBo bo) {
        EnergyCell entity = BeanUtils.copy(bo, EnergyCell.class);
        energyManager.save(entity);
        return energyCellGetById(entity.getId());
    }

    public void energyCellDelete(Long id) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUserInfo();
        if(!user.getRoles().contains("admin")) {
            EnergyCell energyCell = energyManager.fetchById(id);
            if (!Objects.equals(energyCell.getAdminUserId(), user.getId())) {
                throw new BaseException("无权删除");
            }
        }
        energyManager.deleteById(id);
    }



    public List<EnergyCellBo> energyCellFind(EnergyCellFindDto dto) {
        List<WgwCellBo> cells = ThirdPartyHolder.wuguanwangService().getCell(dto.getMobile(), dto.getIdCardNo());
        if (cells.isEmpty()) {
            return List.of();
        }
        return cells.stream().map(cell -> {
            EnergyCellBo energyCellBo = new EnergyCellBo();
            energyCellBo.setCompanyName(cell.getGsName());
            energyCellBo.setCompanyCode(cell.getGsCode());
            energyCellBo.setCellName(cell.getCellName());
            energyCellBo.setCellNo(cell.getCellNo());
            energyCellBo.setCellId(cell.getCellId());
            energyCellBo.setMasterName(cell.getName());
            energyCellBo.setMasterMobile(cell.getTel());
            energyCellBo.setMasterIdCardNo(cell.getZhhm());
            energyCellBo.setLayerName(cell.getLayerName());
            energyCellBo.setLayerNo(cell.getLayerNo());
            return energyCellBo;
        }).toList();

    }

    public List<EnergyCellBo> energyCellFindByKeyword(SearchKeywordDto dto) {
        List<WgwCellBo> cells = ThirdPartyHolder.wuguanwangService().searchCell(dto.getKeyword());
        if (cells.isEmpty()) {
            return List.of();
        }
        return cells.stream().map(cell -> {
            EnergyCellBo energyCellBo = new EnergyCellBo();
            energyCellBo.setCompanyName(cell.getGsName());
            energyCellBo.setCompanyCode(cell.getGsCode());
            energyCellBo.setCellName(cell.getCellName());
            energyCellBo.setCellNo(cell.getCellNo());
            energyCellBo.setCellId(cell.getCellId());
            energyCellBo.setMasterName(cell.getName());
            energyCellBo.setMasterMobile(cell.getTel());
            energyCellBo.setMasterIdCardNo(cell.getZhhm());
            energyCellBo.setLayerName(cell.getLayerName());
            energyCellBo.setLayerNo(cell.getLayerNo());
            return energyCellBo;
        }).toList();
    }

    /**
     * 查询店铺水电费余额
     * @param energyCellId 店铺编号
     * @return 水电费余额信息
     */
    public EnergyCellBalanceBo queryCellEnergyBalance(Long energyCellId) {
        EnergyCell energyCell = energyManager.fetchById(energyCellId);
        WgwEnergyLeftBo wgwEnergyLeftBo = ThirdPartyHolder.wuguanwangService().queryCellEnergyLeft(energyCell.getCompanyCode(), energyCell.getCellNo());
//        Double realLeft = Double.parseDouble(wgwEnergyLeftBo.getMoneyLeft()) - Double.parseDouble(wgwEnergyLeftBo.getPreCost());
//        wgwEnergyLeftBo.setMoneyLeft(String.valueOf(realLeft));
        return BeanUtils.copy(wgwEnergyLeftBo, EnergyCellBalanceBo.class);
    }

    /**
     * 查询店铺结算扣费记录
     * @param dto 查询参数，包含店铺编号和日期范围
     * @return 结算扣费记录列表
     */
    public List<EnergyRoomCheckLogBo> queryRoomCheckLog(EnergyRoomCheckLogDto dto) {
        EnergyCell energyCell = energyManager.fetchById(dto.getId());
        List<WgwEnergyCheckLogBo> logs = ThirdPartyHolder.wuguanwangService().queryRoomCheckLog(
                energyCell.getCompanyCode(),
                energyCell.getCellNo(),
                dto.getStartDate(),
                dto.getEndDate()
        );
        return logs.stream()
                .map(log -> BeanUtils.copy(log, EnergyRoomCheckLogBo.class))
                .collect(Collectors.toList());
    }

    /**
     * 查询店铺历史充值记录
     * @param dto 查询参数，包含店铺编号和日期范围
     * @return 充值记录列表
     */
    public List<EnergyRoomPayLogBo> queryRoomPayLog(EnergyRoomPayLogDto dto) {
        EnergyCell energyCell = energyManager.fetchById(dto.getId());
        List<WgwEnergyPayLogBo> logs = ThirdPartyHolder.wuguanwangService().queryRoomPayLog(
                energyCell.getCompanyCode(),
                energyCell.getCellNo(),
                dto.getStartDate(),
                dto.getEndDate()
        );
        return logs.stream()
                .map(log -> BeanUtils.copy(log, EnergyRoomPayLogBo.class))
                .collect(Collectors.toList());
    }

    public EnergyCellBo energyCellCreate(EnergyCellBo dto) {
        PropertyCell propertyCell = propertyCellManager.createIfNotExist(dto);
        dto.setPropertyCellId(propertyCell.getId());
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUserInfo();
        if(dto.getMasterMobile().equals(user.getMobile())) {
            dto.setIsVerify(IsEnum.TRUE);
        }
        return energyCellSave(dto);
    }

    public void energyCellSendVerifySms(Long id, Boolean isFake) {
        EnergyCell energyCell = energyManager.fetchById(id);
        // 生成验证码
        String code = String.format("%06d", new Random().nextInt(1000000));
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUserInfo();

        String mobile = energyCell.getMasterMobile();
        if(isFake) {
            mobile = user.getMobile();
        }

        // 发送短信
        ThirdPartyHolder.aliyunSmsService().sendVerifyCode(mobile, code);
        // 存储验证码
        redisCache.setCacheObject("energyCellVerify:" + id, code, 5 * 60, TimeUnit.SECONDS);
    }

    public void energyCellVerifyMobile(Long id, String code) {
        EnergyCell energyCell = energyManager.fetchById(id);
        if(energyCell == null) {
            throw new BaseException("店铺不存在");
        }
        Object smsCodeObj = redisCache.getCacheObject("energyCellVerify:" + id);
        if(!Objects.equals(smsCodeObj, code)) {
            throw new BaseException("验证码错误");
        }
        energyCell.setIsVerify(IsEnum.TRUE);
        energyManager.save(energyCell);
    }

    public void energyCellSaveBusinessLicense(Long id, String businessLicense) {
        EnergyCell energyCell = energyManager.fetchById(id);
        if(energyCell == null) {
            throw new BaseException("店铺不存在");
        }
        Long adminUserId = SecurityHolder.<Long, AdminUserBo>session().getUserInfo().getId();
        if (!Objects.equals(energyCell.getAdminUserId(), adminUserId)) {
            throw new BaseException("无权操作");
        }
        propertyCellManager.updateBusinessLicense(energyCell.getPropertyCellId(), businessLicense);
        propertyCellManager.saveOcrBusinessLicense(energyCell.getPropertyCellId());
    }


}
