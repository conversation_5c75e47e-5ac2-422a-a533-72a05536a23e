package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.ShopSyncConflictBo;
import com.dounanflowers.common.entity.ShopSyncConflict;
import com.dounanflowers.common.manager.ShopSyncConflictManager;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.utils.BeanUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class ShopSyncConflictService {
    private final ShopSyncConflictManager shopSyncConflictManager;

    public Boolean handle(Long id, String rightValue) {
        return shopSyncConflictManager.handle(id, rightValue);
    }

    public Page<ShopSyncConflictBo> pageList(PageRequest pageRequest) {
        Page<ShopSyncConflict> shopSyncConflictPage = shopSyncConflictManager.pageList(pageRequest);
        return shopSyncConflictPage.convert(shopSyncConflict -> BeanUtils.copy(shopSyncConflict, ShopSyncConflictBo.class));
    }


}
