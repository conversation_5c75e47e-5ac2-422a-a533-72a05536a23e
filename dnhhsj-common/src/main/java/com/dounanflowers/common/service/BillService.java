package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.BillBo;
import com.dounanflowers.common.bo.BillRefundBo;
import com.dounanflowers.common.bo.InvoiceBo;
import com.dounanflowers.common.bo.UserObjBo;
import com.dounanflowers.common.constant.MqConstant;
import com.dounanflowers.common.dto.BillRefundDto;
import com.dounanflowers.common.entity.AdminUser;
import com.dounanflowers.common.entity.Bill;
import com.dounanflowers.common.entity.ClientUser;
import com.dounanflowers.common.entity.Invoice;
import com.dounanflowers.common.enums.BillTypeEnum;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.common.manager.BillManager;
import com.dounanflowers.common.manager.ClientUserManager;
import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.google.common.collect.Maps;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Db;
import lombok.RequiredArgsConstructor;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class BillService {
    private final AmqpTemplate amqpTemplate;
    private final BillManager billManager;
    private final AdminUserManager adminUserManager;
    private final ClientUserManager clientUserManager;

    public void handleNotify(Map<String, String> dto) {
        Bill bill = billManager.saveBillByPayNotify(dto);
        amqpTemplate.convertAndSend(MqConstant.BILL_EXCHANGE, MqConstant.BILL_ROUTING_KEY, bill);
    }

    public Page<BillBo> pageList(PageRequest pageRequest) {
        Page<Bill> billPage = billManager.pageList(pageRequest);
        Map<BillTypeEnum, List<Long>> billEntityIdMap = billPage.getList().stream()
                .collect(Collectors.groupingBy(
                        Bill::getType,
                        Collectors.mapping(Bill::getEntityId, Collectors.toList())
                ));

        Map<BillTypeEnum, Map<Long, Object>> billEntityNameMap =
                billEntityIdMap.entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, e -> {
                            List<Long> ids = e.getValue();
                            BillTypeEnum type = e.getKey();
                            return Db.selectListByQuery(type.getTableName(), QueryWrapper.create().in("id", ids))
                                    .stream().map(v -> v.toObject(type.getDbClazz()))
                                    .collect(Collectors.toMap(BaseEntity::getId, r -> BeanUtils.copy(r, type.getBoClazz())));
                        }));
        List<Long> adminUserIds = billPage.getList().stream().filter(v -> v.getUserType().equals(UserTypeEnum.ADMIN)).map(Bill::getUserId).toList();
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(adminUserIds);
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));
        List<Long> clientUserIds = billPage.getList().stream().filter(v -> v.getUserType().equals(UserTypeEnum.CLIENT)).map(Bill::getUserId).toList();
        List<ClientUser> clientUsers = clientUserManager.fetchByIds(clientUserIds);
        Map<Long, ClientUser> clientUserMap = clientUsers.stream().collect(Collectors.toMap(ClientUser::getId, v -> v));
        return billPage.convert(bill -> {
            BillBo bo = BeanUtils.copy(bill, BillBo.class);
            bo.setRefundList(BeanUtils.copyList(bill.getRefundList(), BillRefundBo.class));
            bo.setInvoiceObj(bill.getInvoiceObj() == null ? null : BeanUtils.copy(bill.getInvoiceObj(), InvoiceBo.class));
            bo.setOtherData(bill.getOtherData() == null ? null : JsonUtils.toMap(bill.getOtherData()));
            bo.setImages(bill.getImages());
            bo.setEntityObj(billEntityNameMap.getOrDefault(bill.getType(), Maps.newHashMap()).get(bill.getEntityId()));
            if (bill.getUserType().equals(UserTypeEnum.ADMIN)) {
                AdminUser adminUser = adminUserMap.get(bill.getUserId());
                if (adminUser != null) {
                    bo.setUserObj(BeanUtils.copy(adminUser, UserObjBo.class));
                }
            }
            if (bill.getUserType().equals(UserTypeEnum.CLIENT)) {
                ClientUser clientUser = clientUserMap.get(bill.getUserId());
                if (clientUser != null) {
                    bo.setUserObj(BeanUtils.copy(clientUser, UserObjBo.class));
                }
            }
            return bo;
        });
    }

    public void refund(BillRefundDto dto) {
        Bill bill = billManager.fetchById(Long.valueOf(dto.getId()));
        billManager.billRefund(bill, dto);
    }


    public List<BillBo> relatList(BillTypeEnum type, String entityId, String entityModel) {
        List<Bill> billList = billManager.fetchRelatList(type, entityId, entityModel);
        return BeanUtils.copyList(billList, BillBo.class);
    }

    public BillBo getByEntityId(Long entityId) {
        Bill bill = billManager.fetchByEntityId(entityId);
        return BeanUtils.copy(bill, BillBo.class);
    }
}
