package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.DictMapGetDto;
import com.dounanflowers.common.dto.FeedbackCreateDto;
import com.dounanflowers.common.dto.MsgSendByTemplateDto;
import com.dounanflowers.common.dto.UpdateSettingDto;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.*;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.common.manager.ClientUserManager;
import com.dounanflowers.common.manager.SystemManager;
import com.dounanflowers.common.repo.AdminUserRepo;
import com.dounanflowers.common.repo.ClientUserRepo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.IdUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.utils.SecurityHolder;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class SystemService {

    private final SystemManager systemManager;
    private final ClientUserManager clientUserManager;
    private final AdminUserManager adminUserManager;
    private final MsgService msgService;
    private final AdminMenuService adminMenuService;


    public Map<String, Map<String, Object>> settingMap() {
        return systemManager.fetchSettingMap();
    }

    public boolean updateSetting(UpdateSettingDto dto) {
        Setting setting = systemManager.fetchSetting(dto.getParentKey(), dto.getKey());
        if (setting == null) {
            setting = new Setting();
            setting.setParentKey(dto.getParentKey());
            setting.setKey(dto.getKey());
        }
        setting.setValue(JsonUtils.toJson(dto.getValue()));
        systemManager.saveSetting(setting);
        return true;
    }

    public List<String> dictAllTags() {
        List<String> dictTags = systemManager.fetchDictTags();
        if (dictTags == null) {
            return List.of();
        }
        return dictTags;
    }

    public Page<DictBo> dictPageList(PageRequest pageRequest) {
        Page<Dict> dictPage = systemManager.fetchDictPageList(pageRequest);
        return dictPage.convert(v -> {
            DictBo copy = BeanUtils.copy(v, DictBo.class);
            copy.setTags(v.getTags());
            copy.setOptions(getDictOptionTree(v, v.getOptions()));
            return copy;
        });
    }

    public Boolean deleteDictById(Long id) {
        systemManager.deleteDictById(id);
        return true;
    }

    public List<DictOptionBo> getDictOptions(String key) {
        List<Dict> dicts = systemManager.fetchAllDictMap();
        Map<String, Dict> stringDictMap = dicts.stream().collect(Collectors.toMap(Dict::getKey, v -> v));
        Dict dict = stringDictMap.get(key);
        if (dict == null) {
            return List.of();
        }
        return getDictOptionTree(dict, dict.getOptions());
    }

    private List<DictOptionBo> getDictOptionTree(Dict dict, List<DictOption> dictOptions) {
        if (dict == null) {
            return List.of();
        }
        Map<Long, List<DictOptionBo>> parentMap = dictOptions.stream().collect(Collectors.groupingBy(DictOption::getParentId, Collectors.mapping(v -> BeanUtils.copy(v, DictOptionBo.class), Collectors.toList())));
        parentMap.forEach((k, v) -> v.forEach(option -> option.setChildren(parentMap.getOrDefault(option.getId(), List.of()))));
        return parentMap.get(0L);
    }

    public DictBo saveDict(DictBo dictBo) {
        Dict dict = BeanUtils.copy(dictBo, Dict.class);
        dict.setTags(dictBo.getTags());
        List<DictOption> options = Lists.newArrayList();
        addDictOptionChildren(0L, dictBo.getOptions(), options);
        dict.setOptions(options);
        systemManager.saveDict(dict);
        dictBo.setId(dict.getId());
        return dictBo;
    }

    private static void addDictOptionChildren(Long parentId, List<DictOptionBo> children, List<DictOption> options) {
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        for (DictOptionBo child : children) {
            if (child.getId() == null) {
                child.setId(IdUtils.nextId());
            }
            DictOption db = BeanUtils.copy(child, DictOption.class);
            db.setParentId(parentId);
            options.add(db);
            addDictOptionChildren(child.getId(), child.getChildren(), options);
        }
    }

    public Map<String, DictBo> getDictMap(DictMapGetDto dto) {
        List<Dict> dicts = systemManager.fetchAllDictMap();
        Set<Dict> result = Sets.newHashSet();
        if (dto.getIsOr()) {
            if (dto.getTags() != null) {
                result.addAll(dicts.stream().filter(v -> CollectionUtils.containsAny(dto.getTags(), v.getTags())).collect(Collectors.toSet()));
            }
            if (dto.getKey() != null) {
                result.addAll(dicts.stream().filter(v -> CollectionUtils.containsAny(dto.getKey(), v.getKey())).collect(Collectors.toSet()));
            }
            if (dto.getName() != null) {
                result.addAll(dicts.stream().filter(v -> CollectionUtils.containsAny(dto.getName(), v.getName())).collect(Collectors.toSet()));
            }
        } else {
            boolean tagEmpty = dto.getTags() == null || dto.getTags().isEmpty();
            boolean keyEmpty = dto.getKey() == null || dto.getKey().isEmpty();
            boolean nameEmpty = dto.getName() == null || dto.getName().isEmpty();
            dicts = dicts.stream()
                    .filter(v -> tagEmpty || CollectionUtils.containsAny(dto.getTags(), v.getTags()))
                    .filter(v -> keyEmpty || CollectionUtils.containsAny(dto.getKey(), v.getKey()))
                    .filter(v -> nameEmpty || CollectionUtils.containsAny(dto.getName(), v.getName()))
                    .collect(Collectors.toList());
        }
        return dicts.stream().collect(Collectors.toMap(Dict::getKey, v -> {
            DictBo copy = BeanUtils.copy(v, DictBo.class);
            copy.setOptions(getDictOptionTree(v, v.getOptions()));
            copy.setTags(v.getTags());
            return copy;
        }));
    }

    public List<DictOptionBo> getDictOptionsByName(String name) {
        List<Dict> dicts = systemManager.fetchAllDictMap();
        Map<String, Dict> stringDictMap = dicts.stream().collect(Collectors.toMap(Dict::getName, v -> v));
        Dict dict = stringDictMap.get(name);
        if (dict == null) {
            return List.of();
        }
        return getDictOptionTree(dict, dict.getOptions());
    }

    public List<BannerShowBo> getBannerShowList(String place) {
        List<BannerInfo> bannerInfos = systemManager.fetchBannerShowList(BaseEnum.ordinalOf(BannerPlaceEnum.class, place));
        return BeanUtils.copyList(bannerInfos, BannerShowBo.class);
    }

    public Boolean deleteBanner(Long id) {
        systemManager.deleteBanner(id);
        return true;
    }

    public BannerBo saveBanner(BannerBo dto) {
        BannerInfo bannerInfo = BeanUtils.copy(dto, BannerInfo.class);
        systemManager.saveBanner(bannerInfo);
        return BeanUtils.copy(bannerInfo, BannerBo.class);
    }

    public Page<BannerBo> getBannerPage(PageRequest pageRequest) {
        for (PageFilter pageFilter : pageRequest.getFilter()) {
            if ("place".equals(pageFilter.getField())) {
                pageFilter.setValue(BaseEnum.ordinalOf(BannerPlaceEnum.class, pageFilter.getValue().toString()));
            } else if ("type".equals(pageFilter.getField())) {
                pageFilter.setValue(BaseEnum.ordinalOf(BannerTypeEnum.class, pageFilter.getValue().toString()));
            } else if ("status".equals(pageFilter.getField())) {
                pageFilter.setValue(IsEnum.of((Boolean) pageFilter.getValue()));
            }
        }
        Page<BannerInfo> bannerPage = systemManager.fetchBannerPage(pageRequest);
        return bannerPage.convert(v -> BeanUtils.copy(v, BannerBo.class));
    }

    @Transactional(rollbackFor = Exception.class)
    public FeedbackBo feedbackCreate(FeedbackCreateDto dto, ChannelEnum channelEnum) {
        Feedback feedback = BeanUtils.copy(dto, Feedback.class);
        feedback.setChannel(channelEnum);
        systemManager.saveFeedback(feedback);
        List<FileInfo> files = systemManager.fetchFileList(dto.getImages());
        int i = 0;
        for (FileInfo file : files) {
            file.setOuterId(feedback.getId());
            file.setSeq(i++);
            systemManager.saveFile(file);
        }
        FeedbackBo copy = BeanUtils.copy(feedback, FeedbackBo.class);
        copy.setImages(files.stream().map(FileInfo::getUrl).toList());
        adminMenuService.emitCount(AdminMenuEventEnum.FEEDBACK_HANDLE);
        return copy;
    }

    public FeedbackBo feedbackHandle(Long id, String remark, Boolean handled) {
        AdminUserBo adminUser = SecurityHolder.<Long, AdminUserBo>session().getUserInfo();
        Feedback feedback = systemManager.fetchFeedbackById(id);
        feedback.setRemark(remark);
        if (handled) {
            feedback.setHandledAt(LocalDateTime.now());
            if (feedback.getUserId() != null) {
                MsgSendByTemplateDto template = new MsgSendByTemplateDto();
                template.setSendUserId(adminUser.getId());
                template.setContext(feedback);
                template.setOuterType("feedback");
                template.setOuterId(feedback.getId());
                template.setReceiveUserIds(List.of(feedback.getUserId()));
                if (feedback.getChannel() == ChannelEnum.CLIENT) {
                    template.setTemplateCode("feedbackClient");
                } else if (feedback.getChannel() == ChannelEnum.SHOP) {
                    template.setTemplateCode("feedbackShop");
                }
                msgService.sendMessageByTemplate(template);
            }
        }
        systemManager.saveFeedback(feedback);
        FeedbackBo copy = BeanUtils.copy(feedback, FeedbackBo.class);
        copy.setImages(feedback.getImages());
        return copy;
    }

    public Page<FeedbackBo> feedbackPageList(PageRequest pageRequest) {
        Page<Feedback> feedbackPage = systemManager.fetchFeedbackPage(pageRequest);
        List<Long> userIds = feedbackPage.getList().stream().map(Feedback::getUserId).toList();
        List<ClientUser> clientUsers = clientUserManager.fetchByIds(userIds);
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(userIds);
        Map<Long, ClientUser> clientUserMap = clientUsers.stream().collect(Collectors.toMap(ClientUser::getId, v -> v));
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));
        return feedbackPage.convert(v -> {
            FeedbackBo copy = BeanUtils.copy(v, FeedbackBo.class);
            copy.setImages(v.getImages());
            ClientUser clientUser = clientUserMap.get(v.getUserId());
            if (clientUser != null) {
                copy.setUserObj(BeanUtils.copy(clientUser, ClientUserBo.class));
            } else {
                AdminUser adminUser = adminUserMap.get(v.getUserId());
                if (adminUser != null) {
                    copy.setUserObj(BeanUtils.copy(adminUser, ClientUserBo.class));
                }
            }
            return copy;
        });
    }

    public FeedbackBo feedbackGet(Long id) {
        Feedback feedback = systemManager.fetchFeedbackById(id);
        FeedbackBo copy = BeanUtils.copy(feedback, FeedbackBo.class);
        copy.setImages(feedback.getImages());
        return copy;
    }


}
