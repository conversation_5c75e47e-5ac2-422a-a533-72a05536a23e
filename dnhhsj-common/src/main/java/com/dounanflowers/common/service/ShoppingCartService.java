package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.ProductBo;
import com.dounanflowers.common.bo.ShoppingCartBo;
import com.dounanflowers.common.bo.ShoppingCartStoreBo;
import com.dounanflowers.common.bo.StoreBo;
import com.dounanflowers.common.dto.ShoppingCartDto;
import com.dounanflowers.common.entity.Product;
import com.dounanflowers.common.entity.ShoppingCart;
import com.dounanflowers.common.entity.Store;
import com.dounanflowers.common.manager.ProductManager;
import com.dounanflowers.common.manager.ShoppingCartManager;
import com.dounanflowers.common.manager.StoreManager;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ShoppingCartService {

    private final ShoppingCartManager shoppingCartManager;

    private final ProductManager productManager;

    private final StoreManager storeManager;

    @Transactional
    public ShoppingCartBo create(ShoppingCartDto dto) {
        ShoppingCart existingCart = shoppingCartManager.fetchByUserIdAndProductId(dto.getUserId(), dto.getProductId());
        if (existingCart != null) {
            throw new BaseException("购物车已存在该商品");
        }
        Product product = productManager.fetchProductByIdWithRelation(dto.getProductId());
        ShoppingCart shoppingCart = new ShoppingCart();
        BeanUtils.copyProperties(dto, shoppingCart);
        shoppingCart.setStoreId(product.getStoreId());
        shoppingCart.setAddTime(LocalDateTime.now());
        shoppingCartManager.save(shoppingCart);
        return BeanUtils.copy(shoppingCart, ShoppingCartBo.class);
    }

    @Transactional
    public ShoppingCartBo update(Long id, ShoppingCartDto dto) {
        ShoppingCart shoppingCart = shoppingCartManager.fetchById(id);
        if (shoppingCart == null) {
            throw new BaseException("购物车项不存在");
        }

        BeanUtils.copyProperties(dto, shoppingCart);
        shoppingCart.setId(id);
        shoppingCartManager.save(shoppingCart);
        return BeanUtils.copy(shoppingCart, ShoppingCartBo.class);
    }

    @Transactional
    public void delete(Long id) {
        ShoppingCart shoppingCart = shoppingCartManager.fetchById(id);
        if (shoppingCart == null) {
            throw new BaseException("购物车项不存在");
        }
        shoppingCartManager.deleteById(id);
    }

    public ShoppingCartBo get(Long id) {
        ShoppingCart shoppingCart = shoppingCartManager.fetchById(id);
        if (shoppingCart == null) {
            throw new BaseException("购物车项不存在");
        }
        ShoppingCartBo copy = BeanUtils.copy(shoppingCart, ShoppingCartBo.class);

        Product product = productManager.fetchProductByIdWithRelation(shoppingCart.getProductId());
        if (product != null) {
            copy.setProductObj(BeanUtils.copy(product, ProductBo.class));
        }
        Store store = storeManager.fetchByIdWithRelation(shoppingCart.getStoreId());
        if (store != null) {
            copy.setStoreObj(BeanUtils.copy(store, StoreBo.class));
        }
        return copy;
    }

    public List<ShoppingCartStoreBo> getByUserId(Long userId) {
        List<ShoppingCart> shoppingCarts = shoppingCartManager.fetchByUserId(userId);
        List<Long> productIds = shoppingCarts.stream().map(ShoppingCart::getProductId).toList();
        List<Product> productProducts = productManager.fetchProductByIdsWithRelation(productIds);
        Map<Long, Product> productProductMap = productProducts.stream().collect(Collectors.toMap(Product::getId, v -> v));
        List<Long> storeIds = shoppingCarts.stream().map(ShoppingCart::getStoreId).toList();
        List<Store> stores = storeManager.fetchByIds(storeIds);
        Map<Long, Store> storeMap = stores.stream().collect(Collectors.toMap(Store::getId, v -> v));
        List<ShoppingCartBo> itemList = shoppingCarts.stream().map(v -> {
            ShoppingCartBo bo = BeanUtils.copy(v, ShoppingCartBo.class);
            Product product = productProductMap.get(v.getProductId());
            if (product != null) {
                ProductBo copy = BeanUtils.copy(product, ProductBo.class);
                copy.setImages(product.getImages());
                bo.setProductObj(copy);
            }
            Store store = storeMap.get(v.getStoreId());
            if (store != null) {
                StoreBo copy = BeanUtils.copy(store, StoreBo.class);
                copy.setImages(store.getImages());
                bo.setStoreObj(copy);
            }
            return bo;
        }).toList();
        Map<Long, List<ShoppingCartBo>> collect = itemList.stream().collect(Collectors.groupingBy(ShoppingCartBo::getStoreId));
        return collect.entrySet().stream().map(v -> {
            ShoppingCartStoreBo bo = new ShoppingCartStoreBo();
            bo.setStoreId(v.getKey());
            Store source = storeMap.get(v.getKey());
            if (source != null) {
                StoreBo copy = BeanUtils.copy(source, StoreBo.class);
                copy.setImages(storeMap.get(v.getKey()).getImages());
                bo.setStoreObj(copy);
            }
            bo.setItems(v.getValue());
            return bo;
        }).toList();
    }

    @Transactional
    public void clearUserCart(Long userId) {
        shoppingCartManager.deleteByUserId(userId);
    }

}
