package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.CouponReceiveDto;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.dto.QrcodeDto;
import com.dounanflowers.common.entity.Coupon;
import com.dounanflowers.common.entity.CouponTpl;
import com.dounanflowers.common.enums.ClientTypeEnum;
import com.dounanflowers.common.enums.CouponSceneEnum;
import com.dounanflowers.common.enums.CouponTypeEnum;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.common.manager.CouponManager;
import com.dounanflowers.common.manager.UserCommonManager;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.IdUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.utils.SecurityHolder;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class CouponService {

    private final CouponManager couponManager;
    private final UserCommonManager userCommonManager;

    public Page<CouponBo> couponPageList(PageRequest pageRequest) {
        Page<Coupon> couponPage = couponManager.fetchCouponPage(pageRequest);
        Map<Long, UserObjBo> userMap = userCommonManager.fetchUserMapByUserIds(couponPage.getList().stream().map(Coupon::getUserId).toList());
        return couponPage.convert(v -> {
            CouponBo one = BeanUtils.copy(v, CouponBo.class);
            if (userMap.get(v.getUserId()) != null) {
                one.setUserObj(userMap.get(v.getUserId()));
            }
            return one;
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public CouponBo couponReceive(QrcodeDto qrcodeDto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        String dataJson = new String(Base64.getUrlDecoder().decode(qrcodeDto.getQrCode()), StandardCharsets.UTF_8);
        CouponQrCodeInfoBo qrcodeBo = JsonUtils.toObject(dataJson, CouponQrCodeInfoBo.class);
        if ("blank".equals(qrcodeBo.getType())) {
            Coupon coupon = receiveCouponByCode(qrcodeBo.getCode(), userId, UserTypeEnum.CLIENT, false);
            if (coupon == null) {
                throw new BaseException("优惠券不存在");
            }
            return BeanUtils.copy(coupon, CouponBo.class);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public CouponBo couponReceiveTest(QrcodeDto qrcodeDto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        String dataJson = new String(Base64.getUrlDecoder().decode(qrcodeDto.getQrCode()), StandardCharsets.UTF_8);
        CouponQrCodeInfoBo qrcodeBo = JsonUtils.toObject(dataJson, CouponQrCodeInfoBo.class);
        if ("blank".equals(qrcodeBo.getType())) {
            Coupon coupon = receiveCouponByCode(qrcodeBo.getCode(), userId, UserTypeEnum.CLIENT, true);
            if (coupon == null) {
                throw new BaseException("优惠券不存在");
            }
            return BeanUtils.copy(coupon, CouponBo.class);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public Coupon receiveCouponToUser(Long couponTplId, Long userId, UserTypeEnum userType) {
        CouponReceiveDto dto = new CouponReceiveDto()
                .setCouponTplId(couponTplId)
                .setUserIds(Lists.newArrayList(userId))
                .setUserType(userType);
        List<String> codes = receiveCoupon(dto);
        if (CollectionUtils.isEmpty(codes)) {
            throw new BaseException("优惠券领取失败");
        }
        return couponManager.fetchCouponByCode(codes.getFirst());
    }

    @Transactional(rollbackFor = Exception.class)
    public List<String> receiveCoupon(CouponReceiveDto dto) {
        boolean give = CollectionUtils.isNotEmpty(dto.getUserIds());
        int length = give ? dto.getUserIds().size() : dto.getBlankCount();
        if (length == 0) {
            return Lists.newArrayList();
        }
        CouponTpl tpl = couponManager.fetchAvailableCouponTpl(dto.getCouponTplId(), length);
        LocalDateTime tplRealEndAt = getTplRealEndAt(tpl);
        List<Coupon> coupons = Lists.newArrayList();
        Iterator<Long> iterator = dto.getUserIds().iterator();
        for (int i = 0; i < length; i++) {
            Coupon coupon = new Coupon();
            Long id = IdUtils.nextId();
            coupon.setId(id);
            coupon.setType(tpl.getType());
            coupon.setCouponTplId(tpl.getId());
            coupon.setTitle(tpl.getReceiveTitle());
            coupon.setUserId(iterator.hasNext() ? iterator.next() : null);
            coupon.setEndAt(give ? tplRealEndAt : null);
            coupon.setScene(tpl.getScene());
            coupon.setCode(id.toString());
            coupon.setParValue(tpl.getParValue());
            coupon.setSourceUserId(dto.getSourceUserId());
            coupon.setIsInstantGen(IsEnum.of(dto.isInstantGen()));
            coupon.setUserType(dto.getUserType());
            coupon.setIsStackable(tpl.getIsStackable());
            coupons.add(coupon);

        }
        if (!dto.isTest()) {
            couponManager.saveCouponBatch(coupons);
            int receivedCouponCount = couponManager.countReceivedCouponByTplId(tpl.getId());
            tpl.setReceivedCount(receivedCouponCount);
            couponManager.saveCouponTpl(tpl);
        }
        return coupons.stream().map(Coupon::getCode).toList();
    }

    @Transactional(rollbackFor = Exception.class)
    public Coupon receiveCouponByCode(String code, Long userId, UserTypeEnum userType, boolean test) {
        Coupon coupon = couponManager.fetchCouponByCode(code);
        if (coupon == null) {
            return null;
        }
        if (coupon.getUserId() != null) {
            throw new BaseException("优惠券已被领取");
        }
        CouponTpl tpl = couponManager.fetchCouponTplById(coupon.getCouponTplId());
        LocalDateTime tplRealEndAt = getTplRealEndAt(tpl);
        coupon.setEndAt(tplRealEndAt);
        if (!test) {
            coupon.setUserId(userId);
            coupon.setUserType(userType);
            couponManager.saveCoupon(coupon);
        }
        return coupon;
    }

    private static LocalDateTime getTplRealEndAt(CouponTpl tpl) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime absoluteEndAt = tpl.getAbsoluteEndAt();
        LocalDateTime relativeEndAt = now.plusMinutes(tpl.getRelativeEndAt());
        return switch (tpl.getEndType()) {
            case RELATIVE_EXPIRE -> relativeEndAt;
            case ABSOLUTE_EXPIRE -> absoluteEndAt;
            case MIN_EXPIRE -> relativeEndAt.isBefore(absoluteEndAt) ? relativeEndAt : absoluteEndAt;
            case MAX_EXPIRE -> relativeEndAt.isAfter(absoluteEndAt) ? relativeEndAt : absoluteEndAt;
        };
    }

    public Integer countByUserId(Long id) {
        return couponManager.countByUserId(id);
    }

    /**
     * 计算使用优惠券后的实际价格
     *
     * @param originalPrice 原始价格
     * @param couponTplId   优惠券模板ID
     * @param scene         使用场景
     * @return 计算后的实际价格
     */
    public BigDecimal calculateActualPriceByTplId(BigDecimal originalPrice, Long couponTplId, CouponSceneEnum scene) {
        CouponTpl tpl = couponManager.fetchCouponTplById(couponTplId);
        if (tpl == null) {
            throw new BaseException("优惠券不存在");
        }
        if (!tpl.getScene().equals(scene)) {
            throw new BaseException("优惠券不可用于该场景");
        }
        if (CouponTypeEnum.DISCOUNT.equals(tpl.getType())) {
            return originalPrice.multiply(BigDecimal.valueOf(tpl.getParValue() / 10.0));
        } else if (CouponTypeEnum.MONEY.equals(tpl.getType())) {
            return originalPrice.subtract(BigDecimal.valueOf(tpl.getParValue()));
        }
        return originalPrice;
    }

    /**
     * 计算使用优惠券后的实际价格
     *
     * @param originalPrice 原始价格
     * @param couponId      优惠券ID
     * @param userId        用户ID
     * @param scene         使用场景
     * @return 计算后的实际价格
     */
    public BigDecimal calculateActualPrice(BigDecimal originalPrice, Long couponId, Long userId, CouponSceneEnum scene) {
        if (couponId == null) {
            return originalPrice;
        }

        Coupon coupon = couponManager.fetchCouponById(couponId);
        if (coupon == null) {
            throw new BaseException("优惠券不存在");
        }
        if (!scene.equals(coupon.getScene())) {
            throw new BaseException("优惠券不适用于当前场景");
        }
        if (coupon.getUserId() == null || !coupon.getUserId().equals(userId)) {
            throw new BaseException("优惠券不属于当前用户");
        }
        if (coupon.getUsedAt() != null) {
            throw new BaseException("优惠券已使用");
        }
        if (coupon.getEndAt() != null && coupon.getEndAt().isBefore(LocalDateTime.now())) {
            throw new BaseException("优惠券已过期");
        }
        if (!CouponTypeEnum.DISCOUNT.equals(coupon.getType()) && !CouponTypeEnum.MONEY.equals(coupon.getType())) {
            throw new BaseException("优惠券类型不正确，只能使用折扣券或金额券");
        }

        BigDecimal actualPrice = originalPrice;
        // 计算优惠后价格
        if (CouponTypeEnum.DISCOUNT.equals(coupon.getType())) {
            // 折扣券：按百分比计算
            BigDecimal discountPercentage = BigDecimal.valueOf(coupon.getParValue()).divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
            actualPrice = actualPrice.multiply(discountPercentage).setScale(0, RoundingMode.HALF_UP);
        } else {
            // 金额券：直接减去金额
            actualPrice = actualPrice.subtract(BigDecimal.valueOf(coupon.getParValue())).setScale(0, RoundingMode.HALF_UP);
            if (actualPrice.compareTo(BigDecimal.ZERO) < 0) {
                actualPrice = BigDecimal.ZERO;
            }
        }

        return actualPrice;
    }

    /**
     * 标记优惠券为已使用
     *
     * @param couponId 优惠券ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void markCouponAsUsed(Long couponId) {
        if (couponId == null) {
            return;
        }
        Coupon coupon = couponManager.fetchCouponById(couponId);
        if (coupon != null) {
            coupon.setUsedAt(LocalDateTime.now());
            couponManager.saveCoupon(coupon);
            CouponTpl tpl = couponManager.fetchCouponTplById(coupon.getCouponTplId());
            if (tpl != null) {
                tpl.setUsedCount(couponManager.countUsedCouponByTplId(tpl.getId()));
                couponManager.saveCouponTpl(tpl);
            }
        }
    }

    public Page<CouponTplBo> clientPublicCouponTplPage(PageRequest dto, Long userId) {
//        if(client == ClientTypeEnum.CLIENT) {
//            dto.getFilter().add(new PageFilter().setField("clientPublic").setType("eq").setValue(IsEnum.TRUE));
//        }
        Page<CouponTpl> page = couponManager.clientPublicCouponTplPage(dto, userId);
        return page.convert(v -> BeanUtils.copy(v, CouponTplBo.class));
    }

    @Transactional(rollbackFor = Exception.class)
    public CouponBo couponTplPublicReceive(Long couponTplId, Long userId, UserTypeEnum userType) {
        Integer count = couponManager.countUsableCouponTplByUserId(userId, couponTplId);
        if(count > 0) {
            throw new BaseException("此优惠券还有未使用的");
        }
        CouponTpl couponTpl = couponManager.fetchCouponTplById(couponTplId);
        if(couponTpl == null) {
            throw new BaseException("优惠券不存在");
        }
        if(couponTpl.getStatus() == IsEnum.FALSE) {
            throw new BaseException("优惠券已失效");
        }
        if(couponTpl.getAbsoluteEndAt().isBefore(LocalDateTime.now())) {
            throw new BaseException("优惠券已过期");
        }
        if(couponTpl.getPublishCount() - couponTpl.getReceivedCount() == 0) {
            throw new BaseException("优惠券已领完");
        }
        CouponReceiveDto cDto = new CouponReceiveDto()
                .setCouponTplId(couponTplId)
                .setUserIds(Lists.newArrayList(userId))
                .setUserType(userType);
        List<String> codes = receiveCoupon(cDto);
        if (CollectionUtils.isEmpty(codes)) {
            throw new BaseException("优惠券领取失败");
        }
        Coupon coupon = couponManager.fetchCouponByCode(codes.getFirst());
        return BeanUtils.copy(coupon, CouponBo.class);
    }
}
