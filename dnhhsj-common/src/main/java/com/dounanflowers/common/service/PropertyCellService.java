package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.PropertyCellBo;
import com.dounanflowers.common.entity.PropertyCell;
import com.dounanflowers.common.manager.PropertyCellManager;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.third.ThirdPartyHolder;
import com.dounanflowers.third.bo.WgwCellBo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class PropertyCellService {

    private final PropertyCellManager propertyCellManager;


    public void update(PropertyCellBo dto) {
        Long id = dto.getId();
        if (id == null) {
            throw new BaseException("id不能为空");
        }
        propertyCellManager.save(BeanUtils.copy(dto, PropertyCell.class));
        if(dto.getBusinessLicense() != null) {
            propertyCellManager.saveOcrBusinessLicense(dto.getId());
        }
    }

    public Page<PropertyCellBo> page(PageRequest dto) {
        dto.getFilter().add(new PageFilter().setField("hide").setType("eq").setValue(IsEnum.FALSE));
        Page<PropertyCell> page = propertyCellManager.page(dto);
        return page.convert(v -> BeanUtils.copy(v, PropertyCellBo.class));
    }

    public PropertyCell checkIsExist(PropertyCellBo dto) {
        return propertyCellManager.findByCompanyCodeAndCellId(dto.getCompanyCode(), dto.getCellId());
    }

    public void add(PropertyCellBo dto) {
        PropertyCell propertyCell = BeanUtils.copy(dto, PropertyCell.class); //propertyCell
        propertyCellManager.save(propertyCell);
        propertyCellManager.saveOcrBusinessLicense(propertyCell.getId());
        propertyCellManager.hideSameCellNoItems(propertyCell);
    }

    public PropertyCell wgwCell2PropertyCell(WgwCellBo wgwCell) {
        PropertyCell propertyCell = new PropertyCell();
        propertyCell.setCompanyName(wgwCell.getGsName());
        propertyCell.setCompanyCode(wgwCell.getGsCode());
        propertyCell.setCellName(wgwCell.getCellName());
        propertyCell.setCellNo(wgwCell.getCellNo());
        propertyCell.setCellId(wgwCell.getCellId());
        propertyCell.setMasterName(wgwCell.getName());
        propertyCell.setMasterMobile(wgwCell.getTel());
        propertyCell.setMasterIdCardNo(wgwCell.getZhhm());
        propertyCell.setLayerName(wgwCell.getLayerName());
        propertyCell.setLayerNo(wgwCell.getLayerNo());
        return propertyCell;
    }

    public void syncAllPropertyCell() {
        List<PropertyCell> propertyCells = propertyCellManager.fetchAllShowPropertyCell();
        syncPropertyCells(propertyCells);
    }

    public PropertyCell syncPropertyCell(Long id) {
        PropertyCell propertyCell = propertyCellManager.fetchById(id);
        List<PropertyCell> newPropertyCells = syncPropertyCells(List.of(propertyCell));
        return newPropertyCells.getFirst();
    }


    public List<PropertyCell> syncPropertyCells(List<PropertyCell> propertyCells) {
        try {
            List<String> cellKeys = propertyCells.stream().map(v -> v.getCompanyCode() + "::" + v.getCellNo()).toList();
            Map<String, PropertyCell> cellMap = propertyCells.stream()
                    .collect(Collectors.toMap(v -> v.getCompanyCode() + "::" + v.getCellNo(), v -> v, (a, b) -> a));
            List<WgwCellBo> wgwCells = ThirdPartyHolder.wuguanwangService().syncCell(new ArrayList<>(cellMap.keySet()));
            List<PropertyCell> newPropertyCells = wgwCells.stream().map(this::wgwCell2PropertyCell).toList();
            for (PropertyCell remotePropertyCell : newPropertyCells) {
                String cellKey = remotePropertyCell.getCompanyCode() + "::" + remotePropertyCell.getCellNo();
                PropertyCell propertyCell = cellMap.get(cellKey);
                if (propertyCell == null) {
                    continue;
                } else if(!propertyCell.getCellId().equals(remotePropertyCell.getCellId())) {
                    propertyCellManager.save(remotePropertyCell);
                    propertyCellManager.hideSameCellNoItems(remotePropertyCell);
                } else if(!propertyCell.getMasterName().equals(remotePropertyCell.getMasterName())
                        || !propertyCell.getMasterMobile().equals(remotePropertyCell.getMasterMobile())
                        || !propertyCell.getMasterIdCardNo().equals(remotePropertyCell.getMasterIdCardNo())
                ) {
//                    propertyCell.setWaitUpdate(IsEnum.TRUE);
                    propertyCell.setMasterName(remotePropertyCell.getMasterName());
                    propertyCell.setMasterMobile(remotePropertyCell.getMasterMobile());
                    propertyCell.setMasterIdCardNo(remotePropertyCell.getMasterIdCardNo());
                    propertyCell.setWaitUpdate(IsEnum.FALSE);
                    propertyCellManager.save(propertyCell);
                }
            }
//            adminMenuService.emitCount(AdminMenuEventEnum.PROPERTY_CELL_SYNC_HANDLE);
            return newPropertyCells;
        } catch (Exception e) {
            log.error("同步失败", e);
            throw new BaseException("同步失败");
        }
    }

    public List<PropertyCellBo> sameCellNoItems(Long id) {
        List<PropertyCell> sameCellNoItems = propertyCellManager.sameCellNoItems(id);
        return sameCellNoItems.stream().map(v -> BeanUtils.copy(v, PropertyCellBo.class)).toList();
    }
}
