package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.ClientUserBo;
import com.dounanflowers.common.dto.TwoFactorLoginDto;
import com.dounanflowers.common.dto.UserRealVerifyDto;
import com.dounanflowers.common.entity.ClientUser;
import com.dounanflowers.common.enums.GenderEnum;
import com.dounanflowers.common.manager.ClientUserManager;
import com.dounanflowers.common.utils.IdCardUtils;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.service.AliyunOpenapiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ClientUserService {

    private final ClientUserManager clientUserManager;

    private final AliyunOpenapiService aliyunOpenapiService;

    private final TwoFactorAuthService twoFactorAuthService;

    public void save(ClientUser user) {
        clientUserManager.save(user);
    }

    public void save(ClientUserBo user) {
        ClientUser clientUser = BeanUtils.copy(user, ClientUser.class);
        clientUserManager.save(clientUser);
    }

    public ClientUser fetchUserByOpenId(String openId) {
        return clientUserManager.fetchUserByOpenId(openId);
    }

    public ClientUser fetchUserById(Long id) {
        return clientUserManager.fetchById(id);
    }

    public ClientUser fetchByMobile(String mobile) {
        return clientUserManager.fetchByMobile(mobile);
    }

    public void realnameVerify(UserRealVerifyDto dto) {
        SessionInfo<Long, ClientUserBo> session = SecurityHolder.session();
        Long userId = session.getUserId();
        ClientUser user = clientUserManager.fetchById(userId);
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        if (dto.getFake() == null || !dto.getFake()) {
            aliyunOpenapiService.credentialVerify(dto.getRealname(), dto.getIdCardNum(), dto.getIdCardUrl1());
        } else {
            if (!IdCardUtils.verifyIdCard(dto.getIdCardNum())) {
                throw new BaseException("身份证格式不正确");
            }
            if (user.getFake().isFalse()) {
                throw new BaseException("当前用户无法模拟实名认证");
            }
        }
        user.setRealname(dto.getRealname());
        clientUserManager.updateUserCredential(userId, dto.getRealname(), dto.getIdCardNum(), dto.getIdCardUrl1(), dto.getIdCardUrl2());
        clientUserManager.save(user);
        session.getUserInfo().setRealname(user.getRealname());
        SecurityHolder.update(session);
    }

    public Page<ClientUserBo> page(PageRequest pageRequest) {
        Page<ClientUser> page = clientUserManager.page(pageRequest);
        return page.convert(v -> BeanUtils.copy(v, ClientUserBo.class));
    }

    public ClientUserBo updateSelf(String nickname, String avatar, String gender) {
        SessionInfo<Long, ClientUserBo> session = SecurityHolder.session();
        Long userId = session.getUserId();
        ClientUser user = clientUserManager.fetchById(userId);
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        if (StringUtils.isNotBlank(nickname)) {
            user.setAvatar(nickname);
        }
        if (StringUtils.isNotBlank(avatar)) {
            user.setAvatar(avatar);
        }
        if (StringUtils.isNotBlank(gender)) {
            user.setGender(BaseEnum.ordinalOf(GenderEnum.class, Integer.parseInt(gender)));
        }
        clientUserManager.save(user);
        session.getUserInfo().setNickname(user.getNickname());
        session.getUserInfo().setAvatar(user.getAvatar());
        session.getUserInfo().setGender(user.getGender().ordinal() + "");
        SecurityHolder.update(session);
        return session.getUserInfo();
    }

    public void unbindMobile(Long userId) {
        ClientUser user = clientUserManager.fetchById(userId);
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        user.setMobile("");
        clientUserManager.save(user);
        SecurityHolder.<Long,ClientUserBo>session().getUserInfo().setMobile(null);
        SecurityHolder.update(SecurityHolder.<Long,ClientUserBo>session());
    }

    public void unbindOpenid(Long userId) {
        ClientUser user = clientUserManager.fetchById(userId);
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        user.setOpenId("");
        clientUserManager.save(user);
        SecurityHolder.<Long,ClientUserBo>session().getUserInfo().setOpenId(null);
        SecurityHolder.update(SecurityHolder.<Long,ClientUserBo>session());
    }

    public void deleteUser(Long userId) {
        ClientUser user = clientUserManager.fetchById(userId);
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        user.setMobile(user.getMobile() + "_" + System.currentTimeMillis());
        user.setOpenId(user.getOpenId() + "_" + System.currentTimeMillis());
        clientUserManager.save(user);
    }

    /**
     * Login using 2FA code
     *
     * @param dto The 2FA login DTO containing mobile and code
     * @return The session info
     */
    public SessionInfo<Long, ClientUserBo> loginBy2Fa(TwoFactorLoginDto dto) {
        // Verify the 2FA code
        if (!twoFactorAuthService.verifyCode(dto.getCode())) {
            throw new BaseException("2FA验证码无效");
        }

        // Find the user by mobile
        ClientUser user = clientUserManager.fetchByMobile(dto.getMobile());
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        ClientUserBo bo = BeanUtils.copy(user, ClientUserBo.class);

        SessionInfo<Long, ClientUserBo> session = SecurityHolder.login(bo);
        log.info("用户登录成功:{}", JsonUtils.toJson(session));
        return session;
    }
}
