package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.AccountRecordTypeEnum;
import com.dounanflowers.common.enums.BillTypeEnum;
import com.dounanflowers.common.enums.OrderStatusEnum;
import com.dounanflowers.common.manager.ClientUserManager;
import com.dounanflowers.common.manager.ProductManager;
import com.dounanflowers.common.manager.StoreManager;
import com.dounanflowers.common.manager.WithdrawManager;
import com.dounanflowers.common.repo.ProductOrderItemRepo;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.IdUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CommonProductService {

    private final StoreManager storeManager;
    private final ProductManager productManager;
    private final ClientUserManager clientUserManager;
    private final WithdrawManager withdrawManager;

    public ProductOrderBo getOrderDetail(Long id) {
        ProductOrder order = productManager.getOrderWithItems(id);
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        ProductOrderBo orderBo = BeanUtils.copy(order, ProductOrderBo.class);

        List<ProductOrderItem> items = order.getItems();
        // 填充用户信息
        ClientUser user = clientUserManager.fetchById(order.getUserId());
        orderBo.setUserObj(BeanUtils.copy(user, ClientUserBo.class));

        Store store = storeManager.fetchByIdWithRelation(order.getStoreId());
        if (store != null) {
            orderBo.setStoreObj(BeanUtils.copy(store, StoreBo.class));
        }
        // 填充商品信息
        setOrderItems(orderBo, items);
        return orderBo;
    }

    public void setOrderItems(ProductOrderBo orderBo, List<ProductOrderItem> items) {
        orderBo.setItems(items.stream().map(item -> {
            ProductOrderItemBo copy = BeanUtils.copy(item, ProductOrderItemBo.class);
            Product product = JsonUtils.toObject(item.getSnapshot(), Product.class);
            ProductBo bo = BeanUtils.copy(product, ProductBo.class);
            bo.setImages(product.getImages());
            copy.setProductObj(bo);
            return copy;
        }).toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(Long id) {
        ProductOrder order = productManager.getOrderWithItems(id);
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        if (order.getStatus() != OrderStatusEnum.PENDING_PAYMENT) {
            throw new BaseException("订单状态不正确");
        }
        order.setStatus(OrderStatusEnum.CANCELLED);
        productManager.updateOrder(order);
        List<Long> productIds = order.getItems().stream().map(ProductOrderItem::getProductId).toList();
        List<Product> products = productManager.fetchProductByIds(productIds);
        Map<Long, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, v -> v));
        for (ProductOrderItem item : order.getItems()) {
            Product product = productMap.get(item.getProductId());
            if (product != null) {
                product.setStock(product.getStock() + item.getQuantity());
                product.setSoldCount(product.getSoldCount() - item.getQuantity());
                productManager.saveProduct(product);
            }
        }
        storeManager.calcSoldCount(order.getStoreId());
    }

    public void handleProductBill(Bill bill) {
        if (bill.getType() != BillTypeEnum.PRODUCT_ORDER) {
            return;
        }
        ProductOrder order = productManager.fetchOrderById(bill.getEntityId());
        if (order.getMerge().isTrue()) {
            List<ProductOrder> orders = productManager.fetchOrderByParentId(order.getId());
            if (orders.isEmpty()) {
                return;
            }
            for (ProductOrder sub : orders) {
                Store store = storeManager.fetchById(sub.getStoreId());
                sub.setStatus(OrderStatusEnum.PAID);
                sub.setPayTime(LocalDateTime.now());
                List<Product> products = productManager.fetchProductByIdsWithRelation(
                        sub.getItems().stream().map(ProductOrderItem::getProductId).distinct().toList());
                Map<Long, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, v -> v));
                for (ProductOrderItem item : sub.getItems()) {
                    item.setCode(IdUtils.nextId().toString());
                    item.setSnapshot(JsonUtils.toJson(productMap.get(item.getProductId())));
                }
                productManager.updateOrder(sub);
//                if (store.getAdminUserId() != null) {
//                    Product product = productMap.get(sub.getItems().get(0).getProductId());
//                    String title = product.getTitle();
//                    if (sub.getItems().size() > 1) {
//                        title = "等" + sub.getItems().size() + "件商品";
//                    }
//                    int fee = sub.getRealAmount() * store.getFeeRate() / 1000;
//                    int amount = sub.getRealAmount() - fee;
//                    withdrawManager.updateAccountIncome(store.getAdminUserId(),
//                            amount, fee, sub.getId(),
//                            AccountRecordTypeEnum.PRODUCT_ORDER_INCOME, title);
//                }
            }
        }
        order.setStatus(OrderStatusEnum.PAID);
        order.setPayTime(LocalDateTime.now());
        if (CollectionUtils.isNotEmpty(order.getItems())) {
            List<Product> products = productManager.fetchProductByIdsWithRelation(
                    order.getItems().stream().map(ProductOrderItem::getProductId).distinct().toList());
            Map<Long, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, v -> v));
            for (ProductOrderItem item : order.getItems()) {
                item.setCode(IdUtils.nextId().toString());
                item.setSnapshot(JsonUtils.toJson(productMap.get(item.getProductId())));
                Product product = productMap.get(item.getProductId());

                if (product.getValidDays() > 0) {
                    LocalDateTime itemExpireTime = LocalDateTime.now().plusDays(product.getValidDays());
                    if(itemExpireTime.isAfter(product.getValidTo())) {
                        itemExpireTime = product.getValidTo();
                    }
                    item.setExpireTime(itemExpireTime);
                }

            }
//            Store store = storeManager.fetchById(order.getStoreId());
//            if (store.getAdminUserId() != null) {
//                Product product = productMap.get(order.getItems().get(0).getProductId());
//                String title = product.getTitle();
//                if (order.getItems().size() > 1) {
//                    title = "等" + order.getItems().size() + "件商品";
//                }
//                int fee = order.getRealAmount() * store.getFeeRate() / 1000;
//                int amount = order.getRealAmount() - fee;
//                withdrawManager.updateAccountIncome(store.getAdminUserId(),
//                        amount, fee, order.getId(),
//                        AccountRecordTypeEnum.PRODUCT_ORDER_INCOME, title);
//            }
        }
        productManager.updateOrder(order);
        clientUserManager.addPoints(order.getUserId(), order.getRealAmount() / 100, "购物赠送积分");

    }

}
