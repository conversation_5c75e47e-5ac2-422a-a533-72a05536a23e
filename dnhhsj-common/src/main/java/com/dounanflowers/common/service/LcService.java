package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.BillPayStatusEnum;
import com.dounanflowers.common.enums.BillTypeEnum;
import com.dounanflowers.common.enums.CouponTypeEnum;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.common.manager.*;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.IdUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.third.ThirdPartyHolder;
import com.dounanflowers.third.bo.ParkWhiteBo;
import com.dounanflowers.third.bo.WxPayParamsBo;
import com.dounanflowers.third.dto.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.*;

@Service
@RequiredArgsConstructor
public class LcService {
    private final String defaultParkNumber = "p180930175706";
    private final LcManager lcManager;
    private final CouponManager couponManager;
    private final ParkManager parkManager;
    private final BillManager billManager;
    private final AdminUserManager adminUserManager;
    private final UserCommonManager userCommonManager;

    public Page<LcParkBo> lcParkPage(PageRequest pageRequest) {
        Page<LcPark> lcParkPage = lcManager.lcParkPageList(pageRequest);
        return lcParkPage.convert(lcPark -> BeanUtils.copy(lcPark, LcParkBo.class));
    }

    //    lcOrderPage
    public Page<LcOrderBo> lcOrderPage(PageRequest pageRequest) {
        Page<LcOrder> lcOrderPage = lcManager.lcOrderPageList(pageRequest);
        List<Long> clientUserIds = lcOrderPage.getList().stream().map(LcOrder::getClientUserId).filter(Objects::nonNull).toList();
        Map<Long, UserObjBo> userMap = userCommonManager.fetchUserMapByUserIds(clientUserIds);


        return lcOrderPage.convert(lcOrder -> {
            LcOrderBo bo = BeanUtils.copy(lcOrder, LcOrderBo.class);
            if (Objects.nonNull(lcOrder.getClientUserId()) && userMap.containsKey(lcOrder.getClientUserId())) {
                UserObjBo userObjBo = userMap.get(lcOrder.getClientUserId());
                bo.setUserObj(userObjBo);
            }
            return bo;
        });
    }

    public LcOrderBo lcOrderGetById(Long id) {
        LcOrder lcOrder = lcManager.lcOrderGetById(id);
        LcOrderBo bo = BeanUtils.copy(lcOrder, LcOrderBo.class);
        if(Objects.nonNull(lcOrder.getClientUserId())) {
            UserObjBo userObjBo = userCommonManager.fetchUserObjById(lcOrder.getClientUserId());
            bo.setUserObj(userObjBo);
        }
        return bo;
    }

    public List<String> lcPlateListByClientUserId(Long clientUserId) {
        List<LcPlate> lcPlates = lcManager.lcPlateListByClientUserId(clientUserId);
        return lcPlates.stream().map(LcPlate::getPlate).toList();
    }

    public LcOrderFeeBo lcOrderFee(String plate, Long couponId, Long lcOrderId) {
        int couponTimeMins = 0;
        Coupon coupon = null;
        if (couponId != null) {
            coupon = couponManager.fetchCouponById(couponId);
            if (coupon == null) {
                throw new BaseException("优惠券不存在");
            }
            if (coupon.getUsedAt() != null) {
                throw new BaseException("优惠券已使用");
            }
            if (coupon.getEndAt().isBefore(LocalDateTime.now())) {
                throw new BaseException("优惠券已过期");
            }
            if (coupon.getType() == CouponTypeEnum.HOURLY) {
                couponTimeMins = coupon.getParValue();
            }
        }
        LcOrder lcOrder = null;
        if(plate != null && !plate.isEmpty()) {
            lcOrder = lcManager.fetchInParkLcOrderByPlate(plate);
        } else if(lcOrderId != null) {
            lcOrder = lcManager.fetchLcOrderById(lcOrderId);
        }

        if (lcOrder == null) {
            throw new BaseException("未找到入场纪录");
        }

        ParkChargebfReqDto parkChargebfReqDto = new ParkChargebfReqDto();
        parkChargebfReqDto.setCouponTime(String.valueOf(couponTimeMins / 60));
        parkChargebfReqDto.setOrderId(lcOrder.getBcOrderId());
        parkChargebfReqDto.setPlate(lcOrder.getPlate());
        parkChargebfReqDto.setTicketCode(lcOrder.getTicketCode());
        parkChargebfReqDto.setParkNumber(lcOrder.getParkNumber());
        parkChargebfReqDto.setWxOpenId("");
        ParkChargebfResDto parkChargebfResDto = ThirdPartyHolder.parkApiService().fetchChargebf(parkChargebfReqDto);

        int moneyCent = (int) (Double.parseDouble(parkChargebfResDto.getCharge()) * 100);
        int payMoneyCent = (int) (Double.parseDouble(parkChargebfResDto.getPayCharge()) * 100);

        if (coupon != null) {
            if (coupon.getType() == CouponTypeEnum.MONEY) {
                payMoneyCent = moneyCent - coupon.getParValue();
            }
            if (coupon.getType() == CouponTypeEnum.DISCOUNT) {
                payMoneyCent = moneyCent * coupon.getParValue() / 100;
            }
        }

        int discountCent = moneyCent - payMoneyCent;
        payMoneyCent = Math.max(payMoneyCent, 0);

        LcOrderFeeBo lcOrderFeeBo = new LcOrderFeeBo();
        lcOrderFeeBo.setLcOrderId(lcOrder.getId());
        lcOrderFeeBo.setInTime(lcOrder.getInTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        lcOrderFeeBo.setMoneyCent(moneyCent);
        lcOrderFeeBo.setDiscountCent(discountCent);
        lcOrderFeeBo.setPayMoneyCent(payMoneyCent);

        return lcOrderFeeBo;
    }


    public WxPayParamsBo lcOrderPay(Long lcOrderId, Long couponId, ClientUserBo userInfo) {
        LcOrder lcOrder = lcManager.fetchLcOrderById(lcOrderId);
        if (lcOrder == null) {
            throw new BaseException("未找到入场纪录");
        }
        if (couponId != null) {
            Coupon coupon = couponManager.fetchCouponById(couponId);
            if (coupon == null) {
                throw new BaseException("优惠券不存在");
            }
            if (coupon.getUsedAt() != null) {
                throw new BaseException("优惠券已使用");
            }
            if (coupon.getEndAt().isBefore(LocalDateTime.now())) {
                throw new BaseException("优惠券已过期");
            }

            coupon.setOrderId(lcOrder.getId());
            coupon.setOrderModel("lc_order");
            coupon.setUsedAt(LocalDateTime.now());
            couponManager.saveCoupon(coupon);
            CouponTpl tpl = couponManager.fetchCouponTplById(coupon.getCouponTplId());
            if (tpl != null) {
                tpl.setUsedCount(couponManager.countUsedCouponByTplId(tpl.getId()));
                couponManager.saveCouponTpl(tpl);
            }

            ParkCouponSendReqDto.ParkCoupon parkCoupon = new ParkCouponSendReqDto.ParkCoupon();
            parkCoupon.setCouponCode(coupon.getCode());
            parkCoupon.setCouponModeId(String.valueOf(coupon.getType().getOrdinal()));
            parkCoupon.setEnd(coupon.getEndAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            parkCoupon.setId("1");
            parkCoupon.setOrderId(lcOrder.getBcOrderId());
            parkCoupon.setPlate(lcOrder.getPlate());

            parkCoupon.setPosition(String.valueOf(coupon.getParValue()));
            parkCoupon.setShopId(1001L);
            parkCoupon.setShopName(String.valueOf(coupon.getSourceUserId()));

            ParkCouponSendReqDto parkCouponSendReqDto = new ParkCouponSendReqDto();
            parkCouponSendReqDto.setParkNumber(lcOrder.getParkNumber());
            parkCouponSendReqDto.setDatas(List.of(parkCoupon));

            ThirdPartyHolder.parkApiService().fetchCouponSend(parkCouponSendReqDto);
        }

        ParkChargebfReqDto parkChargebfReqDto = new ParkChargebfReqDto();
        parkChargebfReqDto.setCouponTime("");
        parkChargebfReqDto.setOrderId(lcOrder.getBcOrderId());
        parkChargebfReqDto.setPlate(lcOrder.getPlate());
        parkChargebfReqDto.setParkNumber(lcOrder.getParkNumber());
        parkChargebfReqDto.setTicketCode(lcOrder.getTicketCode());
        parkChargebfReqDto.setWxOpenId("");
        ParkChargebfResDto parkChargebfResDto = ThirdPartyHolder.parkApiService().fetchChargebf(parkChargebfReqDto);

        int originalMoneyCent = (int) (Double.parseDouble(parkChargebfResDto.getCharge()) * 100);
        int couponDiscountCent = (int) (Double.parseDouble(parkChargebfResDto.getProfitChargeTotal()) * 100);
        int orderMoneyCent = (int) (Double.parseDouble(parkChargebfResDto.getPayCharge()) * 100);

        Bill bill = new Bill();
        bill.setUserId(userInfo.getId());
        bill.setUserType(UserTypeEnum.CLIENT);
        bill.setType(BillTypeEnum.PARK);
        bill.setEntityId(lcOrder.getId());
        bill.setEntityModel("lc_order");
        bill.setUserModel("client_user");
        bill.setOriginalMoneyCent(originalMoneyCent);
        bill.setCouponDiscountCent(couponDiscountCent);
        bill.setOrderMoneyCent(orderMoneyCent);
        bill.setPayStatus(BillPayStatusEnum.UNPAID);
        bill.setOtherData(JsonUtils.toJson(parkChargebfResDto));

        if (orderMoneyCent == 0) {
            bill.setPayStatus(BillPayStatusEnum.PAID);
            bill.setPaidAt(LocalDateTime.now());
            billManager.saveBill(bill);
            lcOrder.setClientUserId(userInfo.getId());
            lcOrder.setProfitChargeTotal(String.valueOf(bill.getCouponDiscountCent() * 0.01));
            parkManager.saveLcOrder(lcOrder);
            WxPayParamsBo wxPayParamsBo = new WxPayParamsBo();
            wxPayParamsBo.setPaid(true);
            return wxPayParamsBo;
        } else {
            billManager.saveBill(bill);
            PayParamsDto payDto = new PayParamsDto();
            payDto.setTrxamt(bill.getOrderMoneyCent());
            payDto.setUnireqsn(String.valueOf(bill.getId()));
            payDto.setAcct(userInfo.getOpenId());
            payDto.setBody("支付停车费");
            payDto.setRemark(userInfo.getMobile());
            return ThirdPartyHolder.allinpayService("client").pay(payDto);
        }
    }

    public LcPlateBo lcPlateAdd(String plate, Long userId) {
        LcPlate lcPlate = lcManager.fetchLcPlate(plate, userId);
        if (lcPlate != null) {
            throw new BaseException("车牌号已存在");
        }
        lcPlate = new LcPlate();
        lcPlate.setPlate(plate);
        lcPlate.setClientUserId(userId);
        lcManager.saveLcPlate(lcPlate);

        return BeanUtils.copy(lcPlate, LcPlateBo.class);
    }

    public void lcPlateDelete(String plate, Long userId) {
        LcPlate lcPlate = lcManager.fetchLcPlate(plate, userId);
        if (lcPlate == null) {
            throw new BaseException("车牌号不存在");
        }
        lcManager.deleteLcPlate(lcPlate);
    }

    public WxPayParamsBo testPay(String wxOpenId) {
        PayParamsDto payDto = new PayParamsDto();
        payDto.setTrxamt(1);
        payDto.setUnireqsn(String.valueOf(IdUtils.nextId()));
        //        payDto.setUnireqsn("a123456789");
        payDto.setAcct(wxOpenId);
        payDto.setBody("测试");
        payDto.setRemark("测试");
        return ThirdPartyHolder.allinpayService("client").pay(payDto);
    }

    /**
     * 处理支付完成
     *
     * @param bill 账单
     */
    @Transactional(rollbackFor = Exception.class)
    public void handlePaid(Bill bill) {
        LcOrder lcOrder = lcManager.lcOrderGetById(bill.getEntityId());
        lcOrder.setClientUserId(bill.getUserId());
        lcOrder.setCharge(String.valueOf(bill.getOriginalMoneyCent() * 0.01));
        lcOrder.setOnLineCharge(String.valueOf(bill.getOrderMoneyCent() * 0.01));
        lcOrder.setProfitChargeTotal(String.valueOf(bill.getCouponDiscountCent() * 0.01));
        parkManager.saveLcOrder(lcOrder);

        ParkPayResultReqDto dto = new ParkPayResultReqDto();
        String otherDataStr = bill.getOtherData();
        ParkChargebfResDto otherData = JsonUtils.toObject(otherDataStr, ParkChargebfResDto.class);

        dto.setGetTime(otherData.getGetTime());
        dto.setOrderId(lcOrder.getBcOrderId());
        dto.setOutTradeNo(otherData.getOutTradeNo());
        dto.setParkNumber(lcOrder.getParkNumber());
        dto.setPayChannel("自助缴费");
        dto.setPayCharge(String.valueOf(bill.getPaidMoneyCent() * 0.01));
        dto.setPayTime(bill.getPaidAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        dto.setPayType("微信小程序");
        dto.setPlate(lcOrder.getPlate());
        dto.setRealCharge(String.valueOf(bill.getPaidMoneyCent() * 0.01));
        dto.setTransactionID(String.valueOf(bill.getId()));

        ThirdPartyHolder.parkApiService().fetchPayResultSend(dto);
    }

    public Page<LcOrderBo> lcOrderNoPlate(PageRequest pageRequest) {
        Page<LcOrder> lcOrderPage = lcManager.lcOrderNoPlate(pageRequest);
        List<String> imageList = lcOrderPage.getList().stream().map(LcOrder::getInImage).toList();
        Map<String, String> inImageUrlMap = ThirdPartyHolder.parkApiService().getImageUrlMap(imageList);
        return lcOrderPage.convert(lcOrder -> {
            LcOrderBo lcOrderBo = BeanUtils.copy(lcOrder, LcOrderBo.class);
            String image = lcOrder.getInImage();
            lcOrderBo.setInImage(inImageUrlMap.get(image));
            return lcOrderBo;
        });
    }

    public void whitelistSave(
        String plate,
        Long userId,
        LocalDateTime startAt,
        LocalDateTime endAt
    ) {
        if (plate == null || plate.trim().isEmpty()) {
            throw new BaseException("车牌号不能为空");
        }
        plate = plate.trim();

        AdminUser user = adminUserManager.fetchById(userId);

        ParkSyncWhiteDto parkSyncWhiteDto = new ParkSyncWhiteDto();
        ParkSyncWhiteDto.Whitelist whitelist = new ParkSyncWhiteDto.Whitelist();
        whitelist.setPlate(plate);
        whitelist.setChargeType("不收费");
        String userName = user.getRealname();
        if (userName == null || userName.trim().isEmpty()) {
            userName = user.getNickname();
        }
        whitelist.setName(userName);
        whitelist.setCertificate(user.getId().toString());
        whitelist.setAddress(user.getOpenId());
        whitelist.setPhone(user.getMobile());
        whitelist.setPlateColor("蓝牌");
        whitelist.setStart(startAt.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        whitelist.setEnd(endAt.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        whitelist.setDept("商家");
        whitelist.setCarType("月租");
        whitelist.setMemo("");
        whitelist.setLevelId(1);
        whitelist.setAreasAndValidity(new ArrayList<>());

        parkSyncWhiteDto.setSize(1);
        parkSyncWhiteDto.setParkNumber(defaultParkNumber);
        parkSyncWhiteDto.setDatas(List.of(
            whitelist
        ));
        ThirdPartyHolder.parkApiService().fetchSynWhite(parkSyncWhiteDto);

    }

    public ParkWhiteBo whitelistQuery(
            String plate
    ) {
        if (plate == null || plate.trim().isEmpty()) {
            throw new BaseException("车牌号不能为空");
        }

        plate = plate.trim();
        ParkQueryWhiteDto.PlateDto plateDto = new ParkQueryWhiteDto.PlateDto();
        plateDto.setPlate(plate);

        ParkQueryWhiteDto parkQueryWhiteDto = new ParkQueryWhiteDto();
        parkQueryWhiteDto.setSize(1);
        parkQueryWhiteDto.setParkNumber(defaultParkNumber);
        parkQueryWhiteDto.setDatas(List.of(
                plateDto
        ));
        List<ParkWhiteBo> parkWhiteBos = ThirdPartyHolder.parkApiService().fetchSynWhiteQuery(parkQueryWhiteDto);
        if (parkWhiteBos == null || parkWhiteBos.isEmpty()) {
            return null;
        }
        ParkWhiteBo first = parkWhiteBos.getFirst();

        if(first == null || LocalDateTime.now().isAfter(LocalDateTime.parse(first.getEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))) {
            return null;
        } else {
            return first;
        }

    }

    /**
     * 为车牌添加白名单，如果已存在，则续期
     * @param plate 车牌
     * @param days 天数
     * @param userId 用户id
     * @return 开始时间和结束时间
     */
    public List<LocalDateTime> addWhitelist(String plate, Integer days, Long userId) {
        if (plate == null || plate.trim().isEmpty()) {
            throw new BaseException("车牌号不能为空");
        }
        ParkWhiteBo parkWhiteBo = whitelistQuery(plate);
        LocalDateTime start = LocalDateTime.now();
        LocalDateTime offsetStart = LocalDateTime.now();

        if(parkWhiteBo != null) {
            offsetStart = LocalDateTime.parse(parkWhiteBo.getEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            start = LocalDateTime.parse(parkWhiteBo.getStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        LocalDateTime end = offsetStart.plusDays(days);

        AdminUser user = adminUserManager.fetchById(userId);

        ParkSyncWhiteDto parkSyncWhiteDto = new ParkSyncWhiteDto();
        ParkSyncWhiteDto.Whitelist whitelist = new ParkSyncWhiteDto.Whitelist();
        whitelist.setPlate(plate);
        whitelist.setChargeType("不收费");
        String userName = user.getRealname();
        if (userName == null || userName.trim().isEmpty()) {
            userName = user.getNickname();
        }
        whitelist.setName(userName);
        whitelist.setCertificate(user.getId().toString());
        whitelist.setAddress(user.getOpenId());
        whitelist.setPhone(user.getMobile());
        whitelist.setPlateColor("蓝牌");
        whitelist.setStart(start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        whitelist.setEnd(end.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        whitelist.setDept("商家");
        whitelist.setCarType("月租");
        whitelist.setMemo("");
        whitelist.setLevelId(1);
        whitelist.setAreasAndValidity(new ArrayList<>());

        parkSyncWhiteDto.setSize(1);
        parkSyncWhiteDto.setParkNumber(defaultParkNumber);
        parkSyncWhiteDto.setDatas(List.of(
                whitelist
        ));
        ThirdPartyHolder.parkApiService().fetchSynWhite(parkSyncWhiteDto);

        return List.of(
                LocalDateTime.parse(whitelist.getStart() + " 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                LocalDateTime.parse(whitelist.getEnd() + " 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        );

    }




//    public void whitelistDelete(
//            String plate
//    ) {
//        if (plate == null || plate.trim().isEmpty()) {
//            throw new BaseException("车牌号不能为空");
//        }
//
//        plate = plate.trim();
//
//        ParkDeleteWhiteDto parkDeleteWhiteDto = new ParkDeleteWhiteDto();
//        parkDeleteWhiteDto.setSize(10);
//        parkDeleteWhiteDto.setParkNumber(defaultParkNumber);
//        parkDeleteWhiteDto.setPlate(plate);
//        ThirdPartyHolder.parkApiService().fetchDelWhite(parkDeleteWhiteDto);
//
//    }


    public LcOrderFeeBo lcOrderFee2(String plate, List<Long> couponIds, Long lcOrderId) {

        int couponTimeMins = 0;
        List<Coupon> coupons = couponManager.fetchCouponListByIds(couponIds);
        Optional<Coupon> firstDiscount = coupons.stream().filter(c -> c.getType() == CouponTypeEnum.DISCOUNT).findFirst();
        Coupon firstDiscountCoupon = firstDiscount.orElse(null);
        if(coupons.size() > 1 && firstDiscountCoupon != null) {
            throw new BaseException("折扣券不可叠加");
        }
        for (Coupon coupon : coupons) {
            if (coupon.getUsedAt() != null) {
                throw new BaseException("优惠券已使用");
            }
            if (coupon.getEndAt().isBefore(LocalDateTime.now())) {
                throw new BaseException("优惠券已过期");
            }
            if (coupon.getType() == CouponTypeEnum.HOURLY) {
                couponTimeMins += coupon.getParValue();
            }
        }

        LcOrder lcOrder = null;
        if(plate != null && !plate.isEmpty()) {
            lcOrder = lcManager.fetchInParkLcOrderByPlate(plate);
        } else if(lcOrderId != null) {
            lcOrder = lcManager.fetchLcOrderById(lcOrderId);
        }

        if (lcOrder == null) {
            throw new BaseException("未找到入场纪录");
        }

        ParkChargebfReqDto parkChargebfReqDto1 = new ParkChargebfReqDto();
        parkChargebfReqDto1.setCouponTime(String.valueOf(0));
        parkChargebfReqDto1.setOrderId(lcOrder.getBcOrderId());
        parkChargebfReqDto1.setPlate(lcOrder.getPlate());
        parkChargebfReqDto1.setTicketCode(lcOrder.getTicketCode());
        parkChargebfReqDto1.setParkNumber(lcOrder.getParkNumber());
        parkChargebfReqDto1.setWxOpenId("");
        ParkChargebfResDto parkChargebfResDto1 = ThirdPartyHolder.parkApiService().fetchChargebf(parkChargebfReqDto1);
        int moneyCent1 = (int) (Double.parseDouble(parkChargebfResDto1.getCharge()) * 100);
        int payMoneyCent1 = (int) (Double.parseDouble(parkChargebfResDto1.getPayCharge()) * 100);
        int paidTotal =  (int) (Double.parseDouble(parkChargebfResDto1.getPaidTotal()) * 100); // 490
        int baseDiscountCent = moneyCent1 - payMoneyCent1 - paidTotal;

        ParkChargebfReqDto parkChargebfReqDto = new ParkChargebfReqDto();
        parkChargebfReqDto.setCouponTime(String.valueOf(couponTimeMins / 60));
        parkChargebfReqDto.setOrderId(lcOrder.getBcOrderId());
        parkChargebfReqDto.setPlate(lcOrder.getPlate());
        parkChargebfReqDto.setTicketCode(lcOrder.getTicketCode());
        parkChargebfReqDto.setParkNumber(lcOrder.getParkNumber());
        parkChargebfReqDto.setWxOpenId("");
        ParkChargebfResDto parkChargebfResDto = ThirdPartyHolder.parkApiService().fetchChargebf(parkChargebfReqDto);

        int moneyCent = (int) (Double.parseDouble(parkChargebfResDto.getCharge()) * 100); // 500
        int payMoneyCent = (int) (Double.parseDouble(parkChargebfResDto.getPayCharge()) * 100); // 0


        double scale = 1.0;
        List<Coupon> usedCoupons = couponManager.fetchCouponsByOrderId(lcOrder.getId(), "lc_order");
        for (Coupon coupon : usedCoupons) {
            if (coupon.getType() == CouponTypeEnum.DISCOUNT) {
                scale = scale * coupon.getParValue() / 100;
            }
        }

        for (Coupon coupon : coupons) {
            if (coupon.getType() == CouponTypeEnum.MONEY) {
                payMoneyCent = payMoneyCent - (int) ((double)coupon.getParValue() * scale);
            }
            if (coupon.getType() == CouponTypeEnum.DISCOUNT) {
                payMoneyCent = payMoneyCent * coupon.getParValue() / 100;
            }
        }


        int discountCent = moneyCent - paidTotal - payMoneyCent;
        payMoneyCent = Math.max(payMoneyCent, 0);

        LcOrderFeeBo lcOrderFeeBo = new LcOrderFeeBo();
        lcOrderFeeBo.setLcOrderId(lcOrder.getId());
        lcOrderFeeBo.setInTime(lcOrder.getInTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        lcOrderFeeBo.setMoneyCent(moneyCent);
        lcOrderFeeBo.setDiscountCent(discountCent);
        lcOrderFeeBo.setPayMoneyCent(payMoneyCent);
        lcOrderFeeBo.setCurrentDiscountCent(discountCent - baseDiscountCent);
        lcOrderFeeBo.setBaseDiscountCent(baseDiscountCent);
        lcOrderFeeBo.setScale(scale);
        lcOrderFeeBo.setPaidTotal(paidTotal);

        return lcOrderFeeBo;
    }


    public WxPayParamsBo lcOrderPay2(Long lcOrderId, List<Long> couponIds, ClientUserBo userInfo) {
        LcOrder lcOrder = lcManager.fetchLcOrderById(lcOrderId);
        if (lcOrder == null) {
            throw new BaseException("未找到入场纪录");
        }

        if (!couponIds.isEmpty()) {
            List<Coupon> coupons = couponManager.fetchCouponListByIds(couponIds);
            Optional<Coupon> firstDiscount = coupons.stream().filter(c -> c.getType() == CouponTypeEnum.DISCOUNT).findFirst();
            Coupon firstDiscountCoupon = firstDiscount.orElse(null);
            if(coupons.size() > 1 && firstDiscountCoupon != null) {
                throw new BaseException("折扣券不可叠加");
            }
            for (Coupon coupon : coupons) {
                if (coupon.getUsedAt() != null) {
                    throw new BaseException("优惠券已使用");
                }
                if (coupon.getEndAt().isBefore(LocalDateTime.now())) {
                    throw new BaseException("优惠券已过期");
                }

                coupon.setOrderId(lcOrder.getId());
                coupon.setOrderModel("lc_order");
                coupon.setUsedAt(LocalDateTime.now());
                couponManager.saveCoupon(coupon);
                CouponTpl tpl = couponManager.fetchCouponTplById(coupon.getCouponTplId());

                if  (tpl != null) {
                    tpl.setUsedCount(couponManager.countUsedCouponByTplId(tpl.getId()));
                    couponManager.saveCouponTpl(tpl);
                }

                ParkCouponSendReqDto.ParkCoupon parkCoupon = new ParkCouponSendReqDto.ParkCoupon();
                parkCoupon.setCouponCode(coupon.getCode());
                parkCoupon.setCouponModeId(String.valueOf(coupon.getType().getOrdinal()));
                parkCoupon.setEnd(coupon.getEndAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                parkCoupon.setId("1");
                parkCoupon.setOrderId(lcOrder.getBcOrderId());
                parkCoupon.setPlate(lcOrder.getPlate());

                parkCoupon.setPosition(String.valueOf(coupon.getParValue()));
                parkCoupon.setShopId(1001L);
                parkCoupon.setShopName(String.valueOf(coupon.getSourceUserId()));

                ParkCouponSendReqDto parkCouponSendReqDto = new ParkCouponSendReqDto();
                parkCouponSendReqDto.setParkNumber(lcOrder.getParkNumber());
                parkCouponSendReqDto.setDatas(List.of(parkCoupon));

                ThirdPartyHolder.parkApiService().fetchCouponSend(parkCouponSendReqDto);
            }
        }

        ParkChargebfReqDto parkChargebfReqDto = new ParkChargebfReqDto();
        parkChargebfReqDto.setCouponTime("");
        parkChargebfReqDto.setOrderId(lcOrder.getBcOrderId());
        parkChargebfReqDto.setPlate(lcOrder.getPlate());
        parkChargebfReqDto.setParkNumber(lcOrder.getParkNumber());
        parkChargebfReqDto.setTicketCode(lcOrder.getTicketCode());
        parkChargebfReqDto.setWxOpenId("");
        ParkChargebfResDto parkChargebfResDto = ThirdPartyHolder.parkApiService().fetchChargebf(parkChargebfReqDto);

        int originalMoneyCent = (int) (Double.parseDouble(parkChargebfResDto.getCharge()) * 100);
        int couponDiscountCent = (int) (Double.parseDouble(parkChargebfResDto.getProfitChargeTotal()) * 100);
        int orderMoneyCent = (int) (Double.parseDouble(parkChargebfResDto.getPayCharge()) * 100);

        Bill bill = new Bill();
        bill.setUserId(userInfo.getId());
        bill.setUserType(UserTypeEnum.CLIENT);
        bill.setType(BillTypeEnum.PARK);
        bill.setEntityId(lcOrder.getId());
        bill.setEntityModel("lc_order");
        bill.setUserModel("client_user");
        bill.setOriginalMoneyCent(originalMoneyCent);
        bill.setCouponDiscountCent(couponDiscountCent);
        bill.setOrderMoneyCent(orderMoneyCent);
        bill.setPayStatus(BillPayStatusEnum.UNPAID);
        bill.setOtherData(JsonUtils.toJson(parkChargebfResDto));

        if (orderMoneyCent == 0) {
            bill.setPayStatus(BillPayStatusEnum.PAID);
            bill.setPaidAt(LocalDateTime.now());
            billManager.saveBill(bill);
            lcOrder.setClientUserId(userInfo.getId());
            lcOrder.setProfitChargeTotal(String.valueOf(bill.getCouponDiscountCent() * 0.01));
            parkManager.saveLcOrder(lcOrder);
            WxPayParamsBo wxPayParamsBo = new WxPayParamsBo();
            wxPayParamsBo.setPaid(true);
            return wxPayParamsBo;
        } else {
            billManager.saveBill(bill);
            PayParamsDto payDto = new PayParamsDto();
            payDto.setTrxamt(bill.getOrderMoneyCent());
            payDto.setUnireqsn(String.valueOf(bill.getId()));
            payDto.setAcct(userInfo.getOpenId());
            payDto.setBody("支付停车费");
            payDto.setRemark(userInfo.getMobile());
            return ThirdPartyHolder.allinpayService("client").pay(payDto);
        }
    }

}
