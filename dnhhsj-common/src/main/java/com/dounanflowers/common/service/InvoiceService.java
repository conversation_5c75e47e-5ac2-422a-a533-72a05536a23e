package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.InvoiceApplyDto;
import com.dounanflowers.common.dto.InvoiceProcessDto;
import com.dounanflowers.common.dto.MsgSendByTemplateDto;
import com.dounanflowers.common.entity.AdminUser;
import com.dounanflowers.common.entity.Bill;
import com.dounanflowers.common.entity.ClientUser;
import com.dounanflowers.common.entity.Invoice;
import com.dounanflowers.common.enums.BillPayStatusEnum;
import com.dounanflowers.common.enums.ChannelEnum;
import com.dounanflowers.common.enums.InvoiceStatusEnum;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.common.manager.*;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.google.common.collect.Maps;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 发票服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InvoiceService {

    private final AdminUserManager adminUserManager;
    private final ClientUserManager clientUserManager;
    private final InvoiceManager invoiceManager;
    private final BillManager billManager;
    private final MsgService msgService;
    private final UserCommonManager userCommonManager;

    /**
     * 分页查询可开票的账单
     *
     */
    public Page<BillBo> pageBillsForInvoice(PageRequest pageRequest, Long userId, UserTypeEnum userType) {
        Page<Bill> billPage = invoiceManager.pageBillsForInvoice(pageRequest, userId, userType);
        return billPage.convert(v -> BeanUtils.copy(v, BillBo.class));
    }

    public List<InvoiceHeaderInfoBo> invoiceHeaderInfoList(Long userId) {
        List<Invoice> invoices = invoiceManager.invoiceHeaderInfo(userId);
        return BeanUtils.copyList(invoices, InvoiceHeaderInfoBo.class);
    }

    /**
     * 分页查询发票
     */
    public Page<InvoiceBo> pageInvoices(PageRequest dto) {
        Page<Invoice> invoicePage = invoiceManager.pageInvoices(dto);
//        List<Long> adminUserIds = invoicePage.getList().stream().filter(v -> v.getUserType().equals(UserTypeEnum.ADMIN)).map(Invoice::getUserId).toList();
//        List<AdminUser> adminUsers = adminUserManager.fetchByIds(adminUserIds);
//        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));
//        List<Long> clientUserIds = invoicePage.getList().stream().filter(v -> v.getUserType().equals(UserTypeEnum.CLIENT)).map(Invoice::getUserId).toList();
//        List<ClientUser> clientUsers = clientUserManager.fetchByIds(clientUserIds);
//        Map<Long, ClientUser> clientUserMap = clientUsers.stream().collect(Collectors.toMap(ClientUser::getId, v -> v));
        Map<Long, UserObjBo> userMap = userCommonManager.fetchUserMapByUserIds(invoicePage.getList().stream().map(Invoice::getUserId).toList());
        List<Long> billIds = new ArrayList<>(List.of());
        Map<Long, List<Long>> billIdsMap = Maps.newHashMap();
        for (Invoice invoice : invoicePage.getList()) {
            if (invoice.getBillIds() != null) {
                List<Long> ids = JsonUtils.toList(invoice.getBillIds(), Long.class);
                billIdsMap.put(invoice.getId(), ids);
                billIds.addAll(ids);
            }
        }
        List<Bill> billList = billManager.listByIds(billIds);
        Map<Long, Bill> billMap = billList.stream().collect(Collectors.toMap(Bill::getId, v -> v));

        return invoicePage.convert(v -> {
            InvoiceBo invoice = BeanUtils.copy(v, InvoiceBo.class);
            if (userMap.get(v.getUserId()) != null) {
                invoice.setUserObj(userMap.get(v.getUserId()));
            }

            if (billIdsMap.get(v.getId()) != null) {
                List<BillBo> billObjList = billIdsMap.get(v.getId()).stream().map(v1 -> BeanUtils.copy(billMap.get(v1), BillBo.class)).toList();
                invoice.setBillObjList(billObjList);
            }
            return invoice;

        });
        
    }

    /**
     * 获取发票详情
     *
     * @param id 发票ID
     * @return 发票详情
     */
    public InvoiceBo fetchById(Long id) {
        Invoice invoice = invoiceManager.fetchById(id);
        if (invoice == null) {
            return null;
        }

        InvoiceBo invoiceBo = BeanUtils.copy(invoice, InvoiceBo.class);
        List<Long> billIds = JsonUtils.toList(invoice.getBillIds(), Long.class);
        List<Bill> bills = billManager.listByIds(billIds);
        invoiceBo.setBillObjList(BeanUtils.copyList(bills, BillBo.class));
        if(invoice.getUserType().equals(UserTypeEnum.ADMIN)){
            AdminUser user = adminUserManager.fetchById(invoice.getUserId());
            UserObjBo userBo = BeanUtils.copy(user, UserObjBo.class);
            userBo.setType(UserTypeEnum.ADMIN);
            invoiceBo.setUserObj(userBo);
        } else if (invoice.getUserType().equals(UserTypeEnum.CLIENT)) {
            ClientUser user = clientUserManager.fetchById(invoice.getUserId());
            UserObjBo userBo = BeanUtils.copy(user, UserObjBo.class);
            userBo.setType(UserTypeEnum.CLIENT);
            invoiceBo.setUserObj(userBo);
        }
        return invoiceBo;
    }

    /**
     * 提交开票申请
     */
    @Transactional(rollbackFor = Exception.class)
    public InvoiceBo applyInvoice(InvoiceApplyDto dto, Long userId, UserTypeEnum userType) {
                // 验证关联的账单
        if (dto.getBillIds() == null || dto.getBillIds().isEmpty()) {
            throw new BaseException("请选择需要开票的账单");
        }

        // 验证账单是否已支付
        List<Bill> bills = billManager.listByIds(dto.getBillIds());
       
        if (bills.size() != dto.getBillIds().size()) {
            throw new BaseException("部分账单不存在");
        }

        // 验证账单是否已关联发票
        for (Bill bill : bills) {
            if (!bill.getPayStatus().equals(BillPayStatusEnum.PAID)) {
                throw new BaseException("选中的账单有未支付的，不能开票");
            }

           
            if (bill.getInvoiceId() != null) {
                throw new BaseException("账单已关联发票，不能重复开票");
            }

            if (!bill.getUserId().equals(userId)) {
                throw new BaseException("不能为他人账单开票");
            }
        }

        // 计算发票总金额
        int totalAmount = bills.stream().mapToInt(v -> v.getPaidMoneyCent() - v.getRefundedMoneyCent()).sum();

        // 创建发票
        Invoice invoice = new Invoice()
                .setHeaderType(dto.getHeaderType())
                .setHeaderName(dto.getHeaderName())
                .setTaxNumber(dto.getTaxNumber())
                .setContent(dto.getContent())
                .setAmountCent(totalAmount)
                .setStatus(InvoiceStatusEnum.PENDING)
                .setRemark(dto.getRemark())
                .setUserId(userId)
                .setUserType(userType)
                .setBillCount(bills.size())
                .setBillIds(JsonUtils.toJson(dto.getBillIds()));

         invoiceManager.saveInvoice(invoice);
         
         // 关联账单
        List<Long> billIds = bills.stream().map(Bill::getId).toList();
        invoiceManager.relateBill(invoice.getId(),  billIds);
         
        return BeanUtils.copy(invoice, InvoiceBo.class);
    }

    /**
     * 处理发票申请（上传发票或拒绝）
     */
    @Transactional(rollbackFor = Exception.class)
    public void processInvoice(InvoiceProcessDto dto, Long adminUserId) {
        Invoice invoice = invoiceManager.fetchById(dto.getId());
        if (invoice == null) {
            throw new BaseException("发票申请不存在");
        }
        if (!invoice.getStatus().equals(InvoiceStatusEnum.PENDING)) {
            throw new BaseException("发票状态不是待开状态，无法处理");
        }
   
        // 更新发票状态
        invoice.setStatus(dto.getStatus());
        invoice.setRemark(dto.getRemark());
        invoice.setProcessedAt(LocalDateTime.now());
        invoice.setProcessorId(adminUserId);

        if (dto.getStatus().equals(InvoiceStatusEnum.REJECTED)) {
            // 拒绝开票
            if (StringUtils.isBlank(dto.getRejectReason())) {
                throw new BaseException("拒绝理由不能为空");
            }
            invoice.setRejectReason(dto.getRejectReason());
            invoiceManager.removeRelateBill(invoice.getId());
        } else if (dto.getStatus().equals(InvoiceStatusEnum.COMPLETED)) {
            // 已开票，设置下载地址
            if (StringUtils.isBlank(dto.getDownloadUrl())) {
                throw new BaseException("发票下载地址不能为空");
            }
            invoice.setDownloadUrl(dto.getDownloadUrl());
        }
        invoiceManager.saveInvoice(invoice);

        if (invoice.getUserId() != null) {
            MsgSendByTemplateDto template = new MsgSendByTemplateDto();
            template.setSendUserId(invoice.getProcessorId());
            Map<String, Object> context = JsonUtils.toMap(JsonUtils.toJson(invoice));
            context.put("statusName", invoice.getStatus() == InvoiceStatusEnum.COMPLETED ? "已开票" : "已拒绝");
            context.put("rejectReason", StringUtils.isBlank(invoice.getRejectReason()) ? "无" : invoice.getRejectReason());
            context.put("amount", invoice.getAmountCent() / 100.0);
            context.put("time", invoice.getProcessedAt());
            template.setContext(context);
            template.setOuterType("invoice");
            template.setOuterId(invoice.getId());
            template.setReceiveUserIds(List.of(invoice.getUserId()));
            if (invoice.getUserType() == UserTypeEnum.CLIENT) {
                template.setTemplateCode("invoiceClient");
            } else if (invoice.getUserType() == UserTypeEnum.ADMIN) {
                template.setTemplateCode("invoiceShop");
            }
            msgService.sendMessageByTemplate(template);
        }
    }

    public InvoiceCountBo count(Long userId, UserTypeEnum userTypeEnum) {
        Invoice invoice = invoiceManager.getInvoiceCount(userId, userTypeEnum);
        InvoiceCountBo invoiceCountBo = new InvoiceCountBo();
        invoiceCountBo.setBillCount(invoice.getBillCount());
        invoiceCountBo.setCount(invoice.getId());
        return invoiceCountBo;
    }

    public InvoiceBo fetchByBillId(Long billId) {
        Invoice invoice = invoiceManager.fetchByBillId(billId);
        return invoice == null ? null : BeanUtils.copy(invoice, InvoiceBo.class);
    }
}
