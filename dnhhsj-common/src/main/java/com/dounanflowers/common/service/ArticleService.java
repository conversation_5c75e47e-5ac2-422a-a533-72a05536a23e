package com.dounanflowers.common.service;

import com.dounanflowers.common.bo.ArticleBo;
import com.dounanflowers.common.bo.ClientUserBo;
import com.dounanflowers.common.entity.Article;
import com.dounanflowers.common.entity.Favourite;
import com.dounanflowers.common.enums.FavouriteTypeEnum;
import com.dounanflowers.common.enums.FileTypeEnum;
import com.dounanflowers.common.manager.ArticleManager;
import com.dounanflowers.common.manager.FavouriteManager;
import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.security.utils.SecurityHolder;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ArticleService {

    private final ArticleManager articleManager;
    private final FavouriteManager favouriteManager;
    private final FileService fileService;

    public Page<ArticleBo> articlePageList(PageRequest pageRequest) {
        Page<Article> page = articleManager.pageList(pageRequest);
        List<Long> ids = page.getList().stream().map(BaseEntity::getId).toList();
        Map<Long, Boolean> likeMap = new HashMap<>();
        if (SecurityHolder.session() != null) {
            likeMap.putAll(articleManager.isLikeArticles(SecurityHolder.<Long, ClientUserBo>session().getUserId(), ids));
        }
        Map<Long, Favourite> favouriteMap = Maps.newHashMap();
        if (SecurityHolder.session() != null) {
            List<Favourite> favourites = favouriteManager.fetchFavourites(FavouriteTypeEnum.GUIDE, page.getList().stream().map(Article::getId).toList());
            favouriteMap.putAll(favourites.stream().filter(v -> v.getUserId().equals(SecurityHolder.session().getUserId())).collect(Collectors.toMap(Favourite::getEntityId, v -> v)));
        }
        return page.convert(v -> {
            ArticleBo articleBo = BeanUtils.copy(v, ArticleBo.class);
            articleBo.setCategoryId(v.getCategories());
            articleBo.setTags(v.getTags());
            articleBo.setImages(v.getImages());
            articleBo.setLike(likeMap.getOrDefault(v.getId(), false));
            Favourite favourite = favouriteMap.get(v.getId());
            if (favourite != null) {
                articleBo.setFavourite(true);
            } else {
                articleBo.setFavourite(false);
            }
            return articleBo;
        });
    }

    public ArticleBo save(ArticleBo bo) {
        Article article = BeanUtils.copy(bo, Article.class);
        article.setCategories(bo.getCategoryId());
        article.setTags(bo.getTags());
        articleManager.save(article);
        bo.setId(article.getId());
        fileService.updateFileLink(article.getId(), "images", bo.getImages(), FileTypeEnum.IMAGE);
        return bo;
    }

    public ArticleBo getById(Long id) {
        Article article = articleManager.fetchById(id);
        if (article == null) {
            return null;
        }
        ArticleBo articleBo = BeanUtils.copy(article, ArticleBo.class);
        articleBo.setCategoryId(article.getCategories());
        articleBo.setTags(article.getTags());
        articleBo.setImages(article.getImages());
        if (SecurityHolder.session() != null) {
            boolean likeArticle = articleManager.isLikeArticle(SecurityHolder.<Long, ClientUserBo>session().getUserId(), id);
            articleBo.setLike(likeArticle);
        }
        return articleBo;
    }

    public Boolean deleteById(Long id) {
        articleManager.deleteById(id);
        return true;
    }


    public List<String> allTags() {
        return articleManager.allTags();
    }

    public ArticleBo getByTitle(String title) {
        Article article = articleManager.fetchByTitle(title);
        if (article == null) {
            return null;
        }
        ArticleBo articleBo = BeanUtils.copy(article, ArticleBo.class);
        articleBo.setCategoryId(article.getCategories());
        articleBo.setTags(article.getTags());
        articleBo.setImages(article.getImages());
        if (SecurityHolder.session() != null) {
            boolean likeArticle = articleManager.isLikeArticle(SecurityHolder.<Long, ClientUserBo>session().getUserId(), article.getId());
            articleBo.setLike(likeArticle);
        }
        return articleBo;
    }

    public void toggleArticleLike(Long id, Long userId) {
        articleManager.toggleLikeArticle(userId, id);
    }
}
