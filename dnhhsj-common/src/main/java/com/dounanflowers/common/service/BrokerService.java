package com.dounanflowers.common.service;


import com.dounanflowers.common.bo.BrokerBo;
import com.dounanflowers.common.dto.BrokerCalledDto;
import com.dounanflowers.common.entity.AdminUser;
import com.dounanflowers.common.entity.BrokerInfo;
import com.dounanflowers.common.entity.Shop;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.common.manager.BrokerManager;
import com.dounanflowers.common.manager.ShopManager;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.utils.BeanUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class BrokerService {

    private final BrokerManager brokerManager;

    private final AdminUserManager adminUserManager;

    private final ShopManager shopManager;

    public BrokerBo saveBroker(BrokerBo brokerBo) {
        BrokerInfo brokerInfo = BeanUtils.copy(brokerBo, BrokerInfo.class);
        brokerManager.save(brokerInfo);
        return BeanUtils.copy(brokerInfo, BrokerBo.class);
    }

    public Page<BrokerBo> pageBroker(PageRequest pageRequest) {
        Page<BrokerInfo> page = brokerManager.pageList(pageRequest);
        return page.convert(v -> BeanUtils.copy(v, BrokerBo.class));
    }

    public Boolean deleteBroker(Long id) {
        brokerManager.deleteById(id);
        return true;
    }

    public BrokerBo getBroker(Long id) {
        BrokerInfo brokerInfo = brokerManager.findById(id);
        return BeanUtils.copy(brokerInfo, BrokerBo.class);
    }

    public BrokerBo getBrokerByUserId(Long userId) {
        AdminUser adminUser = adminUserManager.fetchById(userId);
        Long boundBrokerId = adminUser.getBoundBrokerId();
        if (boundBrokerId != null) {
            return getBroker(boundBrokerId);
        }
        BrokerInfo broker = brokerManager.getRandBroker();
        if (broker == null) {
            return null;
        }
        return BeanUtils.copy(broker, BrokerBo.class);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean bindBroker(BrokerCalledDto dto) {
        AdminUser adminUser = adminUserManager.fetchById(dto.getUserId());
        adminUser.setBoundBrokerId(dto.getBrokerId());
        adminUserManager.save(adminUser);
        if (dto.getShopId() != null) {
            Shop shop = shopManager.fetchById(dto.getShopId());
            shop.setBrokerCalledCount(shop.getBrokerCalledCount() + 1);
            shopManager.save(shop);
        }
        return true;
    }

}
