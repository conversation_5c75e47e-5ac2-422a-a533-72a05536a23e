package com.dounanflowers.common.enums;

import com.dounanflowers.framework.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AdminMenuEventEnum implements BaseEnum<AdminMenuEventEnum> {
    JOB_AUDIT("/job/audit"), // 用工审核
    JOB_SERVICE_AUDIT("/job/service/appointment/status"), // 园区服务审核
    INVOICE_PROCESS("/invoice/process"), // 发票审核
    VEHICLE_CHECK("/vehicle/check"), // 机动车审核
    VEHICLE_PLATE_CHANGE_CHECK("/vehicle/plateChangeCheck"), // 机动车换牌审核
    VEHICLE_REFUND_CHECK("/vehicle/refundCheck"), // 机动车退款审核
    PRODUCT_REFUND_CHECK("/product/refund/handle"), // 商品订单退款审核
    WITHDRAW_APPROVE("/withdraw/approve"), // 店铺提现审核
    FEEDBACK_HANDLE("/feedback/handle"),
    PROPERTY_CELL_SYNC_HANDLE("/propertyCell/add"), // 物业单元同步冲突处理
    // 反馈处理
    ;

    private final String api;// 用工审核
}
