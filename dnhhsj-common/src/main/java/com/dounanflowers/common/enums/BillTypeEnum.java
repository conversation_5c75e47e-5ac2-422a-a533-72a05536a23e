package com.dounanflowers.common.enums;

import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BillTypeEnum implements BaseEnum<BillTypeEnum> {
    UNKNOWN("", BaseEntity.class, Object.class),
    PARK("lc_park", LcPark.class, LcParkBo.class), // 停车
    TRICYCLE_DEPOSIT("tricycle_deposit", TricycleDeposit.class, TricycleDepositBo.class), // 三轮车保证金
    TRICYCLE_ILLEGAL_FINE("tricycle_illegal", TricycleIllegal.class, TricycleIllegalBo.class), // 三轮车违规罚款
    TRICYCLE_MAKE("tricycle", Tricycle.class, TricycleBo.class), // 三轮车工本费
    VEHICLE_MONTHLY("vehicle_monthly_card_order", VehicleMonthlyCardOrder.class, VehicleMonthlyCardOrderBo.class), // 机动车月卡
    PRODUCT_ORDER("product_order", ProductOrder.class, ProductOrderBo.class), // 商品订单
    VEHICLE_PLATE_CHANGE("vehicle_plate_change", VehiclePlateChange.class, VehiclePlateChange.class), // 机动车更换车牌
    ENERGY_CELL_PREPAY("energy_cell", EnergyCell.class, EnergyCellBo.class), // 预存水电费
    ;
    private final String tableName;

    private final Class<? extends BaseEntity> dbClazz;

    private final Class<?> boClazz;
}
