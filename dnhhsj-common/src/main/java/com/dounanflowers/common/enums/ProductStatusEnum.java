package com.dounanflowers.common.enums;

import com.dounanflowers.framework.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@Schema(description = "商品状态")
@AllArgsConstructor
public enum ProductStatusEnum implements BaseEnum<ProductStatusEnum> {
    DRAFT("草稿"),
    PUBLISHED("已发布"),
    SOLD_OUT("已售罄"),
    OFFLINE("已下线"),
    ;

    private final String description;

}
