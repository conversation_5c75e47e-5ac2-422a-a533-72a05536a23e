package com.dounanflowers.common.enums;

import com.dounanflowers.framework.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@Schema(description = "商品订单状态")
@AllArgsConstructor
public enum OrderStatusEnum implements BaseEnum<OrderStatusEnum> {
    PENDING_PAYMENT("待支付"),
    PAID("已支付"),
    COMPLETED("已完成"),
    CANCELLED("已取消"),
    REFUNDING("退款中"),
    REFUNDED("已退款"),
    PENDING_REVIEW("待评价"),
    ;

    private final String description;

}
