package com.dounanflowers.common.enums;

import com.dounanflowers.framework.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@Schema(description = "商品类型")
@AllArgsConstructor
public enum ProductTypeEnum implements BaseEnum<ProductTypeEnum> {
    UNKNOWN("未知"),
    FOOD("美食"),
    TICKET("门票"),
    LEISURE("休闲"),
    ONE_DAY_TOUR("一日游"),
    FLOWER_DERIVATIVE("花卉衍生"),
    TRAVEL_PRODUCT("旅游产品"),
    ;

    private final String description;

}
