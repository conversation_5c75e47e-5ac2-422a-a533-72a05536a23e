package com.dounanflowers.common.enums;

import com.dounanflowers.common.bo.ArticleBo;
import com.dounanflowers.common.bo.JobPostingBo;
import com.dounanflowers.common.bo.ShopBo;
import com.dounanflowers.common.bo.StoreBo;
import com.dounanflowers.common.entity.Article;
import com.dounanflowers.common.entity.JobPosting;
import com.dounanflowers.common.entity.Shop;
import com.dounanflowers.common.entity.Store;
import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FavouriteTypeEnum implements BaseEnum<FavouriteTypeEnum> {
    UNKNOWN("", BaseEntity.class, Object.class),
    SHOP("shop", Shop.class, ShopBo.class), // 商铺
    GUIDE("article", Article.class, ArticleBo.class), // 攻略
    SCENIC_SPOT("article", Article.class, ArticleBo.class), // 景点
    STORE("store", Store.class, StoreBo.class), // 商铺
    JOB("job_posting", JobPosting.class, JobPostingBo.class), // 用工
    ;

    private final String tableName;

    private final Class<? extends BaseEntity> dbClazz;

    private final Class<?> boClazz;
}
