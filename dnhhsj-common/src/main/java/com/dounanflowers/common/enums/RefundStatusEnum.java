package com.dounanflowers.common.enums;

import com.dounanflowers.framework.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RefundStatusEnum implements BaseEnum<RefundStatusEnum> {
    PENDING(0, "待处理"),
    APPROVED(1, "已同意"),
    REJECTED(2, "已拒绝"),
    CANCELLED(3, "已取消");

    private final Integer value;
    private final String text;

}
