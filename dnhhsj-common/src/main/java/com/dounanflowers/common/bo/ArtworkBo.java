package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(title = "艺术品")
public class ArtworkBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "标题")
    private String title;

    @Schema(title = "艺术家")
    private String artist;

    @Schema(title = "图片")
    private List<FileBo> images;

    @Schema(title = "分类", description = "字典 artworkCategory")
    private List<String> categoryId;

    @Schema(title = "详细介绍")
    private String content;

    @Schema(title = "浏览量")
    private String viewCount;

    @Schema(title = "是否发布")
    private Boolean status;

    @Schema(title = "创建时间")
    private LocalDateTime createdAt;

    @Schema(title = "尺寸")
    private String size;

    @Schema(title = "价格")
    private Integer price;

}

