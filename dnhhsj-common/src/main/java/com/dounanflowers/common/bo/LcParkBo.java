package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "停车场区域")
public class LcParkBo {
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "场库编号")
    private String parkNumber;

    @Schema(title = "场库名称")
    private String parkName;

    @Schema(title = "场库总车位数")
    private Boolean spaceCount;

    @Schema(title = "场库空车位数")
    private Integer freeSpaceCount;

    @Schema(title = "场库可预约数")
    private Integer bookSpaceCount;

    @Schema(title = "场库在场预约数")
    private Integer bookInParkCount;
}

