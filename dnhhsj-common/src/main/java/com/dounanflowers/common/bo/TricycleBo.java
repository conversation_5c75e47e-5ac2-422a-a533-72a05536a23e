package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(title = "三轮车")
public class TricycleBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "车辆编号")
    @JsonProperty("numCode")
    private String number;

    @Schema(title = "图片")
    private List<String> images;

    @Schema(title = "商家ID")
    @JsonProperty("merchantUserId")
    private String userId;

    @JsonProperty("merchantUserObj")
    private AdminUserBo userObj;

    @Schema(title = "保证金余额", description = "单位分")
    @JsonProperty("depositBalanceCent")
    private Integer deposit;

    @Schema(title = "保证金退款时间")
    private String depositRefundedAt;

    @Schema(title = "审核状态")
    private String checkStatus;

    @Schema(title = "当前未结案的违规记录")
    private List<TricycleIllegalBo> runningIllegalList;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private Long batchId;

    private TricycleBatchBo batchObj;

    private LocalDateTime effectiveAt;

    @Schema(title = "填写记录数量")
    private Integer infoRecordCount;

}
