package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(title = "购物车")
public class ShoppingCartBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "用户ID")
    private Long userId;

    @Schema(title = "商品ID")
    private Long productId;

    @Schema(title = "商品对象")
    private ProductBo productObj;

    @Schema(title = "商铺ID")
    private Long storeId;

    @Schema(title = "商铺对象")
    private StoreBo storeObj;

    @Schema(title = "数量")
    private Integer quantity;

    @Schema(title = "加入时间")
    private LocalDateTime addTime;

}
