package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(title = "文章")
public class ArticleBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "标题")
    private String title;

    @Schema(title = "作者")
    private String author;

    @Schema(title = "封面")
    private String cover;

    @Schema(title = "封面")
    private String coverV;

    @Schema(title = "简介")
    private String intro;

    @Schema(title = "跳转链接", description = "此项不为空，则文章详情页将跳转到该链接")
    private String webViewUrl;

    @Schema(title = "内容")
    private String content;

    @Schema(title = "分类")
    private List<String> categoryId;

    @Schema(title = "标签")
    private List<String> tags;

    @Schema(title = "图片")
    private List<String> images;

    @Schema(title = "查看数")
    private Integer viewCount;

    @Schema(title = "收藏数")
    private Integer favouriteCount;

    @Schema(title = "创建人")
    private String createdById;

    private SimpleUserBo createdByObj;

    @Schema(title = "创建时间")
    private LocalDateTime createdAt;

    @Schema(title = "是否热点推荐")
    private Boolean isRecommend;

    private Boolean status;

    private String pages;

    @Schema(title = "点赞数")
    private Integer likeCount;

    private Boolean like;

    private Boolean favourite;

}

