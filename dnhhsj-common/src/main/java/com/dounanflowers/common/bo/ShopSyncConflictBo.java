package com.dounanflowers.common.bo;

import com.dounanflowers.framework.enums.IsEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(title = "商铺同步冲突")
public class ShopSyncConflictBo {
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "编号")
    private String cellNo;

    @Schema(title = "冲突字段")
    private String conflictField;

    @Schema(title = "远程值")
    private String remoteValue;

    @Schema(title = "本地值")
    private String localValue;

    @Schema(title = "解决冲突之后的值")
    private String rightValue;

    @Schema(title = "是否已处理")
    private IsEnum handled;

    @Schema(title = "处理时间")
    private LocalDateTime handledAt;
}
