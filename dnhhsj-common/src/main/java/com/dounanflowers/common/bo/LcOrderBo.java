package com.dounanflowers.common.bo;

import com.dounanflowers.common.entity.LcOrderModify;
import com.dounanflowers.framework.enums.IsEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.mybatisflex.annotation.RelationOneToMany;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(title = "停车场订单")
public class LcOrderBo {
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "入场记录编号")
    @JsonProperty("orderId")
    private String bcOrderId;

    @Schema(title = "车牌")
    private String plate;

    @Schema(title = "无牌车票号")
    private String ticketCode;

    @Schema(title = "车牌颜色")
    private String plateColor;

    @Schema(title = "入场时间")
    private LocalDateTime inTime;

    @Schema(title = "入场通道名称")
    private String inChannel;

    @Schema(title = "入场图片名")
    private String inImage;

    @Schema(title = "访问事由")
    private String visitReason;

    @Schema(title = "放行类型: 自动抬杆/手动放行,无信息时填''")
    private String openGateMode;

    @Schema(title = "匹配模式: 全字匹配、近似匹配、人工匹配、人工纠正,无信息时填''")
    private String matchMode;

    @Schema(title = "车牌识别可信度")
    private Integer confidence;


    @Schema(title = "车辆类型")
    private String carType;

    @Schema(title = "证件号码")
    private String userInfoIdCard;

    @Schema(title = "车主姓名")
    private String userInfoUserName;

    @Schema(title = "联系电话")
    private String userInfoPhone;

    @Schema(title = "地址")
    private String userInfoAddress;

    @Schema(title = "车位信息")
    private String placeInfo;

    @Schema(title = "是否开闸: “开闸”“未开闸”")
    private String barriorOpen;

    @Schema(title = "入场开闸耗时: 压地感到抬杆时间")
    private Integer inCostTime;

    @Schema(title = "空位数")
    private Integer spaceCount;


    @Schema(title = "其他联动设备图片信息")
    private String imageList;

    @Schema(title = "图片名")
    private String imageName;

    @RelationOneToMany(selfField = "id", targetField = "lcOrderId")
    private List<LcOrderModify> modifyList; // 修改记录

    @Schema(title = "场库")
    private Long lcParkId;

    @Schema(title = "场库编号")
    private String parkNumber;

    @Schema(title = "操作员Id")
    private String operatorId;

    @Schema(title = "操作员姓名")
    private String operatorName;

    @Schema(title = "发票号码")
    private String invoiceNo;

    @Schema(title = "出场时间")
    private LocalDateTime outTime;

    @Schema(title = "出场图片名")
    private String outImage;

    @Schema(title = "出口通道名称")
    private String outChannel;

    @Schema(title = "总停车费")
    private String charge;

    @Schema(title = "线下总收费")
    private String offLineCharge;

    @Schema(title = "线下累计优惠金额总面值")
    private String offLineProfitChargeNum;

    @Schema(title = "线下累计优惠金额总抵扣值")
    private String offLineProfitChargeValue;

    @Schema(title = "线下累计优惠时间")
    private String offLineProfitTimeNum;

    @Schema(title = "线下累计优惠时间总抵扣值")
    private String offLineProfitTimeValue;

    @Schema(title = "线上总收费")
    private String onLineCharge;

    @Schema(title = "线上累计优惠金额总面值")
    private String onLineProfitChargeNum;

    @Schema(title = "线上累计优惠金额总抵扣值")
    private String onLineProfitChargeValue;

    @Schema(title = "线上累计优惠时间")
    private String onLineProfitTimeNum;

    @Schema(title = "线上累计优惠时间总抵扣值")
    private String onLineProfitTimeValue;

    @Schema(title = "线上线下金额和时间优惠累计抵扣值")
    private String profitChargeTotal;

    @Schema(title = "出场开闸耗时: 压地感到抬杆时间")
    private String outCostTime;

    @Schema(title = "已支付")
    private IsEnum paid;

    @Schema(title = "用户")
    private Long clientUserId;

    @Schema(title = "优惠券")
    private Long couponId;

    @Schema(title = "用户")
    private UserObjBo userObj;

}

