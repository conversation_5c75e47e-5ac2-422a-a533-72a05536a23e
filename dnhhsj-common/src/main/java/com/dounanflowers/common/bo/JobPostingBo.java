package com.dounanflowers.common.bo;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.enums.IsEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "用工发布")
public class JobPostingBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户信息")
    private UserObjBo userObj;

    @Schema(description = "岗位名称")
    private String position;

    @Schema(description = "待遇")
    private Integer salary;

    @Schema(description = "待遇")
    private Integer salaryMax;

    @Schema(description = "待遇类型")
    private String salaryType;

    @Schema(description = "标签")
    private List<String> tags;

    @Schema(description = "招聘方")
    private String employer;

    @Schema(description = "联系方式")
    private String contact;

    @Schema(description = "审核状态")
    private String status;

    @Schema(description = "审核人ID")
    private Long checkUserId;

    @Schema(description = "审核人信息")
    private UserObjBo checkUserObj;

    @Schema(description = "审核备注")
    private String remark;

    @Schema(description = "用工类型")
    private String employmentType;

    @Schema(description = "职位描述")
    private String description;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "是否显示")
    private Boolean show;

//    private boolean favourite;

    @Schema(title = "推荐值")
    private Integer recommend;

    @Schema(title = "是否官方")
    private IsEnum isOfficial;

    @Schema(title = "是否置顶")
    private Integer favouriteCount;

}
