package com.dounanflowers.common.bo;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.enums.IsEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PropertyCellBo {
    private Long id;

    @Schema(title = "管理公司")
    private String companyName;

    @Schema(title = "管理公司编码")
    private String companyCode;

    @Schema(title = "店铺名称")
    private String cellName;

    @Schema(title = "店铺编号")
    private String cellNo;

    @Schema(title = "店铺ID")
    private String cellId;

    @Schema(title = "店主姓名")
    private String masterName;

    @Schema(title = "店主手机号")
    private String masterMobile;

    @Schema(title = "店主身份证号")
    private String masterIdCardNo;

    @Schema(title = "店主所在层")
    private String layerName;

    @Schema(title = "店铺层门牌号")
    private String layerNo;

    @Schema(title = "营业执照")
    private String businessLicense;

    @Schema(title = "是否待更新")
    private Integer waitUpdate;

    @Schema(title = "是否待更新")
    private Integer hide;

    @Schema(title = "Ocr结果")
    private String ocrBusinessLicenseResult;


}
