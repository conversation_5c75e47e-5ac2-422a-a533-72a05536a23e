package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(title = "停车场订单优惠记录")
public class LcOrderProfitBo {
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "停车场")
    private Long lcOrderId;

    @Schema(title = "优惠码")
    private String profitCode;

    @Schema(title = "优惠时间")
    private String profitTime;

    @Schema(title = "商户名称")
    private String shopName;

    @Schema(title = "优惠金额面值")
    private String profitCharge;

    @Schema(title = "生效金额")
    private String profitChargeValue;

    @Schema(title = "优惠下发时间")
    private LocalDateTime getTime;

    @Schema(title = "备注")
    private String memo;
}


