package com.dounanflowers.common.bo;

import com.dounanflowers.common.enums.RefundStatusEnum;
import com.dounanflowers.framework.enums.IsEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ProductRefundApplyBo {

    private Long id;
    private Long orderId;
    private Long orderItemId;
    private Long userId;
    private Long productId;
    private Long storeId;
    private Integer refundAmount;
    private String reason;
    private String description;
    private RefundStatusEnum status;
    private LocalDateTime handleTime;
    private String handleNote;
    private IsEnum refunded;
    private List<String> images;

    private ProductOrderBo orderObj;
    private ProductOrderItemBo orderItemObj;
    private ProductBo productObj;
    private StoreBo storeObj;
    private ClientUserBo userObj;
    private LocalDateTime createdAt;
}
