package com.dounanflowers.common.bo;

import com.dounanflowers.common.entity.VehiclePlateChange;
import com.dounanflowers.common.enums.VehicleRefundStatusEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(title = "机动车")
public class VehicleBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "车辆编号")
    @JsonProperty("numCode")
    private String number;

    @Schema(title = "图片")
    private List<String> images;

    @Schema(title = "商家ID")
    @JsonProperty("merchantUserId")
    private String userId;

    @JsonProperty("merchantUserObj")
    private AdminUserBo userObj;

    @Schema(title = "审核状态")
    private String checkStatus;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    private Long couponId;

    private Long monthlyCardId;

    private CouponBo couponObj;

    private CouponTplBo couponTplObj;

    private VehicleMonthlyCardBo monthlyCardObj;

    private LocalDateTime monthlyCardStartAt;

    private LocalDateTime monthlyCardExpireAt;

    private VehiclePlateChangeBo lastVehiclePlateChangeObj;

    @Schema(title = "退款状态")
    private String refundStatus;
    
    @Schema(title = "退款处理时间")
    private LocalDateTime refundProcessedAt;

    @Schema(title = "退款处理人")
    private Long refundProcessorId;

    @Schema(title = "退款金额")
    private Integer refundMoneyCent;

    @Schema(title = "退款拒绝理由")
    private String refundRejectReason;

    @Schema(title = "退款备注")
    private String refundRemark;

    @Schema(title = "退款图片")
    private List<String> refundImages;

    @Schema(title = "之前的蓝卡白名单")
    private String beforeLcWhite;

    @Schema(title = "审核拒绝理由")
    private String checkRejectReason;

    @Schema(title = "审核备注")
    private String checkRemark;



}

