package com.dounanflowers.common.bo;

import com.dounanflowers.common.entity.ProductOrder;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "商品订单项BO")
public class ProductOrderItemBo {

    @Schema(description = "订单项ID")
    private Long id;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "订单信息")
    private ProductOrderBo orderObj;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户信息")
    private ClientUserBo userObj;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品信息")
    private ProductBo productObj;

    @Schema(description = "购买数量")
    private Integer quantity;

    @Schema(description = "单价")
    private BigDecimal unitPrice;

    @Schema(description = "总金额")
    private Integer totalAmount;

    private Integer realAmount;

    private LocalDateTime expireTime;

    @Schema(description = "商品券号")
    private String code;

    @Schema(description = "使用时间")
    private LocalDateTime useTime;

    @Schema(description = "评价时间")
    private LocalDateTime reviewTime;

}
