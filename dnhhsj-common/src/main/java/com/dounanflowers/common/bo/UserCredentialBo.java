package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "用户实名认证信息")
public class UserCredentialBo {

    @JsonProperty("_id")
    private String id;

    @Schema(title = "姓名")
    private String realname;

    @Schema(title = "身份证号")
    private String idCardNum;

    @Schema(title = "人像面")
    private String idCardUrl1;

    @Schema(title = "国徽面")
    private String idCardUrl2;

    @Schema(title = "用户Id")
    private String userId;

}

