package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "停车场区域")
public class LcParkCountBo {
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "场库名称")
    private Long lcParkId;

    @Schema(title = "在场车辆数")
    private Integer inParkCount;

    @Schema(title = "场库总车位数")
    private Integer spaceCount;

    @Schema(title = "场库空车位数")
    private Integer freeSpaceCount;

    @Schema(title = "分钟时间戳")
    private Integer minutes;
}

