package com.dounanflowers.common.bo;

import com.dounanflowers.common.enums.UserTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UserObjBo {
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "昵称")
    private String nickname;

    @Schema(title = "真名")
    private String realname;

    @Schema(title = "手机号")
    private String mobile;

    @Schema(title = "头像")
    private String avatar;

    @Schema(title = "类型")
    private UserTypeEnum type;

}
