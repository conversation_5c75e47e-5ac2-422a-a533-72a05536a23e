package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "停车场区域")
public class LcAreaBo {
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "区域Id")
    private String areaId;

    @Schema(title = "区域名称")
    private String areaName;

    @Schema(title = "场库")
    private String lcParkId;

    @Schema(title = "场库编号")
    private String parkNumber;

    @Schema(title = "区域车位数")
    private Integer spaceCount;

    @Schema(title = "区域空位数")
    private Integer lastSpaceCount;

    @Schema(title = "场库可预约数")
    private Integer bookSpaceCount;

    @Schema(title = "场库在场预约数")
    private Integer bookInParkCount;
}

