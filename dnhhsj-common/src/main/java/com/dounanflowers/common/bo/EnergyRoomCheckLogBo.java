package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class EnergyRoomCheckLogBo {
    @Schema(title ="msg")
    private String msg;

    @Schema(title ="扣费ID")
    private String costId;

    @Schema(title ="店铺Code")
    private String roomCode;

    @Schema(title ="店铺名称")
    private String roomName;

    @Schema(title ="扣费方式")
    private Integer costMethod;

    @Schema(title ="扣费金额")
    private BigDecimal costMoney;

    @Schema(title ="店铺名")
    private String uName;

    @Schema(title ="用电")
    private BigDecimal electrcUse;

    @Schema(title ="电费加个")
    private BigDecimal electrcPrice;

    @Schema(title ="电费")
    private BigDecimal electrcCostMoney;

    @Schema(title ="用水")
    private BigDecimal waterUse;

    @Schema(title ="水价")
    private BigDecimal waterPrice;

    @Schema(title ="水费")
    private BigDecimal waterCostMoney;

    @Schema(title ="余额")
    private BigDecimal moneyLeft;

    @Schema(title ="创建时间")
    private String createTime;

    @Schema(title ="状态")
    private String status;

}
