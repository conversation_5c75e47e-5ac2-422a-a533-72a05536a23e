package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "商铺订阅")
public class ShopSubscribeBo {

    @JsonProperty("_id")
    private String id;

    @Schema(title = "订阅标题")
    private String title;

    @Schema(title = "订阅的过滤器")
    private Object filter;

    @Schema(title = "用户ID")
    private Long adminUserId;

    @Schema(title = "创建时间")
    private String createdAt;

}

