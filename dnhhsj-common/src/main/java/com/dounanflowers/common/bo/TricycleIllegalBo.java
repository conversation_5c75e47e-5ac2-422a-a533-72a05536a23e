package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(title = "三轮车违规记录")
public class TricycleIllegalBo {

    @JsonProperty("_id")
    private String id;

    @Schema(title = "违规照片", description = "违规时拍的照片")
    private List<String> images;

    @Schema(title = "三轮车ID")
    private String tricycleId;

    @Schema(title = "三轮车对象")
    private TricycleBo tricycleObj;

    @Schema(title = "三轮车编号")
    @JsonProperty("tricycleNumCode")
    private String tricycleNo;

    @Schema(title = "三轮车车主ID")
    @JsonProperty("tricycleMasterId")
    private Long userId;

    private AdminUserBo tricycleMasterObj;

    @Schema(title = "处理状态")
    private String handleStatus;

    @Schema(title = "创建者ID")
    @JsonProperty("createdById")
    private Long createdBy;

    private AdminUserBo createdByObj;

    @Schema(title = "创建事件")
    private LocalDateTime createdAt;

    @Schema(title = "规则ID")
    private String ruleId;

    @Schema(title = "规则名称")
    private String ruleName;

    @Schema(title = "罚款金额", description = "单位分")
    @JsonProperty("fineCent")
    private Integer fine;

    @Schema(title = "延时处罚的分钟数")
    private Integer punishDelayMinutes;

    @Schema(title = "可被取消的最后时间")
    private LocalDateTime canCancelEndAt;

    @Schema(title = "是否需要扣留")
    private Boolean needTow;

    @Schema(title = "取消时间")
    private LocalDateTime canceledAt;

    @Schema(title = "取消者ID")
    private Long canceledBy;

    private AdminUserBo canceledByObj;

    @Schema(title = "处罚时间", description = "执行拖走操作的人")
    private LocalDateTime punishedAt;

    @Schema(title = "处罚者ID")
    private Long punishedBy;

    private AdminUserBo punishedByObj;

    @Schema(title = "取回放行时间")
    private String passedAt;

    @Schema(title = "取回放行者ID")
    private Long passedBy;

    private AdminUserBo passedByObj;

    @Schema(title = "罚金支付时间")
    private LocalDateTime finePaidAt;

    @Schema(title = "罚金订单ID")
    private String fineOrderId;

    @Schema(title = "我的保证金订单")
    private BillBo fineOrderObj;

    @Schema(title = "是否结案", description = "结案=被取消或支付完罚金")
    private Boolean isEnd;

    @Schema(title = "结案时间")
    private LocalDateTime endAt;

}

