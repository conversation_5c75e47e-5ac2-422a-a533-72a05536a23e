package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(title = "商铺")
public class StoreBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "商铺编号")
    private String cellNo;

    @Schema(title = "商铺Id")
    private Long shopId;

    @Schema(description = "商品类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String type;

    @Schema(description = "商铺信息")
    private ShopBo shopObj;

    @Schema(title = "店铺名称")
    private String name;

    @Schema(title = "店铺状态")
    private String status;

    @Schema(title = "店铺地址")
    private String address;

    @Schema(description = "店铺坐标")
    private String location;

    @Schema(title = "管理员用户ID")
    private Long adminUserId;

    private AdminUserBo adminUserObj;

    @Schema(title = "联系人")
    private String contact;

    @Schema(title = "联系电话")
    private String mobile;

    @Schema(title = "最低价格")
    private Integer minPrice;

    @Schema(title = "评分")
    private Integer rate;

    @Schema(title = "评价数")
    private Integer reviewCount;

    @Schema(title = "开店时间")
    private String openAt;

    @Schema(title = "闭店时间")
    private String closeAt;

    @Schema(title = "图片")
    private List<String> images;

    private List<String> tags;

    @Schema(description = "是否推荐")
    private Boolean recommend;

    @Schema(description = "已售")
    private Integer soldCount;

    @Schema(description = "手续费率 ‰")
    private Integer feeRate;

    @Schema(description = "员工")
    private List<StoreEmployeeBo> employees;

}
