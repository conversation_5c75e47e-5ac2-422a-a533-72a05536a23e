package com.dounanflowers.common.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class StoreEmployeeBo {

    @Schema(title = "ID")
    private Long id;

    @Schema(title = "商铺ID")
    private Long storeId;

    @Schema(title = "用户ID")
    private Long userId;

    @Schema(title = "用户信息")
    private AdminUserBo userObj;

    @Schema(title = "联系人")
    private String name;

    @Schema(title = "联系电话")
    private String mobile;

    @Schema(title = "状态")
    private Boolean status;

}