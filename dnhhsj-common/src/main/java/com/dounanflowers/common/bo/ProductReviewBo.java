package com.dounanflowers.common.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "商品评价BO")
public class ProductReviewBo {
    @Schema(description = "评价ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户信息")
    private ClientUserBo userObj;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品信息")
    private ProductBo productObj;

    @Schema(description = "评价内容")
    private String content;

    @Schema(description = "评分(1-5)")
    private Integer rating;

    @Schema(description = "评价图片列表")
    private List<String> images;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "是否匿名评价")
    private Boolean isAnonymous;

    @Schema(description = "商家回复内容")
    private String replyContent;

}
