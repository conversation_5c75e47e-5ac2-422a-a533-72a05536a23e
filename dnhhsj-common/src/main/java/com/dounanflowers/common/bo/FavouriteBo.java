package com.dounanflowers.common.bo;

import com.dounanflowers.common.enums.FavouriteTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "收藏")
public class FavouriteBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "类型")
    private FavouriteTypeEnum type;

    @Schema(title = "标题")
    private String title;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "用户模型")
    private String userModel;

    @Schema(title = "实体id")
    private Long entityId;

    @Schema(title = "实体模型")
    private String entityModel;

    private Object entityObj;

    @Schema(title = "创建时间")
    private String createdAt;

    @Schema(title = "用户")
    private UserObjBo userObj;

}
