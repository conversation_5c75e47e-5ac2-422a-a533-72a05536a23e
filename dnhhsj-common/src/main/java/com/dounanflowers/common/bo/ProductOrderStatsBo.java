package com.dounanflowers.common.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ProductOrderStatsBo {
    @Schema(title = "总订单数")
    private int count;
    @Schema(title = "待支付")
    private int pendingPaymentCount;
    @Schema(title = "已支付")
    private int paidCount;
    @Schema(title = "已完成")
    private int completedCount;
    @Schema(title = "已取消")
    private int cancelledCount;
    @Schema(title = "退款中")
    private int refundingCount;
    @Schema(title = "已退款")
    private int refundedCount;
    @Schema(title = "待评价")
    private int pendingReviewCount;
}
