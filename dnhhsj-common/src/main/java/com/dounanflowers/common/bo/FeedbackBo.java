package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(title = "投诉建议")
public class FeedbackBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "类型")
    private String type;

    @Schema(title = "内容")
    private String content;

    @Schema(title = "图片")
    private List<String> images;

    @Schema(title = "用户输入手机号")
    private String mobile;

    @Schema(title = "用户id")
    private String userId;

    @Schema(title = "用户", description = "用户id或用户信息")
    private ClientUserBo userObj;

    @Schema(title = "备注")
    private String remark;

    @Schema(title = "处理时间")
    private String handledAt;

    @Schema(title = "创建时间")
    private String createdAt;

}

