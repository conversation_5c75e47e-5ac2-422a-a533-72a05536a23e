package com.dounanflowers.common.bo;

import com.dounanflowers.framework.bean.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(title = "返回数据")
public class TricycleIllegalPageBo extends Page<TricycleIllegalBo> {

    @Schema(title = "已警告数量")
    private Integer warnedTotal;

    @Schema(title = "待处理数量")
    private Integer waitHandleTotal;

}

