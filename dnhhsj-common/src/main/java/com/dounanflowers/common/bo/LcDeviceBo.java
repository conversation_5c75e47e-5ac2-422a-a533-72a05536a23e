package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "停车场设备")
public class LcDeviceBo {
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "设备编号")
    private String deviceId;

    @Schema(title = "通道名称")
    private String name;

    @Schema(title = "设备类型")
    private String deviceTypeId;

    @Schema(title = "场库")
    private String lcParkId;

    @Schema(title = "场库编号")
    private String parkNumber;

    @Schema(title = "是否在线 1-在线；2-离线")
    private String state;

    @Schema(title = "设备IP")
    private String ip;

    @Schema(title = "上次在线时间")
    private String lastTime;

}

