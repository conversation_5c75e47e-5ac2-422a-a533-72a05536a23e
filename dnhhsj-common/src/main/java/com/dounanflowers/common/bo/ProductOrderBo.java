package com.dounanflowers.common.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "商品订单BO")
public class ProductOrderBo {

    @Schema(description = "订单ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    private ClientUserBo userObj;

    @Schema(description = "订单编号")
    private String orderNo;

    private Long storeId;

    private StoreBo storeObj;

    @Schema(description = "总金额")
    private Integer totalAmount;

    private Integer realAmount;

    @Schema(description = "订单状态")
    private String status;

    @Schema(description = "支付时间")
    private LocalDateTime payTime;

    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    private LocalDateTime createdAt;

    @Schema(description = "退款原因")
    private String refundReason;

    @Schema(description = "退款时间")
    private LocalDateTime refundTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "订单项列表")
    private List<ProductOrderItemBo> items;

    @Schema(description = "退款ID")
    private Long refundId;

    @Schema(description = "父订单ID, 用来绑定账单")
    private Long parentId;

    @Schema(description = "退款信息")
    private ProductRefundApplyBo refundObj;

}
