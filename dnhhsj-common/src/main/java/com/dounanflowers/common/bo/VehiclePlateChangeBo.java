package com.dounanflowers.common.bo;

import com.dounanflowers.common.enums.VehiclePlateChangeStatusEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(title = "机动车车牌变更")
public class VehiclePlateChangeBo {
    @Schema(title = "_id")
    private Long id;
    
    @Schema(title = "用户ID", nullable = false)
    private Long userId;

    @Schema(title = "机动车ID", nullable = false)
    private Long vehicleId;

    @Schema(title = "变更前车牌", nullable = false)
    private String oldPlate;

    @Schema(title = "变更后车牌", nullable = false)
    private String newPlate;

    @Schema(title = "变更时间")
    private LocalDateTime modifiedAt;

    @Schema(title = "变更状态")
    private String changeStatus;

    @Schema(title = "账单号")
    private Long billId;

    @Schema(title = "拒绝理由")
    private String rejectReason;

    @Schema(title = "修改时间")
    private LocalDateTime changedAt;

    @Schema(title = "创建时间")
    private LocalDateTime createdAt;

}
