package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(title = "字典选项")
public class DictOptionBo implements Serializable {

    @JsonProperty("_id")
    private Long id;

    private String key;

    private String label;

    private String value;

    private Boolean status;

    private List<DictOptionBo> children;

}

