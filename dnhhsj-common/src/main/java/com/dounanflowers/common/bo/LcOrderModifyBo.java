package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(title = "停车场订单修改记录")
public class LcOrderModifyBo {
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "停车订单号")
    private Long lcOrderId;

    @Schema(title = "旧蓝卡订单号")
    private String oldBcOrderId;

    @Schema(title = "新蓝卡订单号")
    private String newBcOrderId;

    @Schema(title = "旧车牌号")
    private String oldPlate;

    @Schema(title = "新车牌号")
    private String newPlate;

    @Schema(title = "修改时间")
    private LocalDateTime modifyTime;
}


