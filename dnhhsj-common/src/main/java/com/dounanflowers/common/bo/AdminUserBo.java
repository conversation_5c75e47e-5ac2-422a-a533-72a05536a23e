package com.dounanflowers.common.bo;

import com.dounanflowers.common.entity.AdminUser;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.security.bean.IUsernameUser;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class AdminUserBo implements IUsernameUser<Long> {

    @Serial
    private static final long serialVersionUID = 1L;

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "昵称")
    private String nickname;

    @Schema(title = "真名")
    private String realname;

    @Schema(title = "手机号")
    private String mobile;

    @Schema(title = "头像")
    private String avatar;

    @Schema(title = "WxOpenId")
    @JsonProperty("wxOpenId")
    private String openId;

    @Schema(title = "最后登录时间")
    private String lastLoginAt;

    @Schema(title = "最后登录Ip")
    private String lastLoginIp;

    @Schema(title = "性别")
    private String gender;

    private String home;

    @Schema(title = "角色")
    private List<String> roles;

    private List<String> permissions;

    private String username;

    private String password;

    private String sessionKey;

    private boolean bind;

    private LocalDateTime createdAt;

    private Boolean isEnabled;

    private Integer tricycleCount;

    private Integer tricycleIllegalCount;

    @Override
    public AdminUserBo clearSensitive() {
        AdminUserBo copy = BeanUtils.copy(this, AdminUserBo.class);
        copy.setPassword(null);
        copy.setSessionKey(null);
        return copy;
    }

    public static AdminUserBo from(AdminUser user) {
        return BeanUtils.copy(user, AdminUserBo.class);
    }

}
