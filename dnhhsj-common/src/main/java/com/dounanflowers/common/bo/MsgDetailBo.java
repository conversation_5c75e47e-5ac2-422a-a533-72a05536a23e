package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class MsgDetailBo {

    @JsonProperty("_id")
    private Long id;

    private String title;

    private String content;

    private Long categoryId;

    private String categoryCode;

    private String categoryName;

    private String type;

    private String status;

    private Boolean read;

    private Long sendUserId;

    private Long receiveUserId;

    private Long processUserId;

    private LocalDateTime expiredAt;

    private String outerType;

    private Long outerId;

    private Object extra;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

}
