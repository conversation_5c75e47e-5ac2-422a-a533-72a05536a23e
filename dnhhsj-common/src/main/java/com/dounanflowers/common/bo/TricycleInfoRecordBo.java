package com.dounanflowers.common.bo;

import com.dounanflowers.framework.utils.JsonUtils;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class TricycleInfoRecordBo {

    private Long id;

    private Long tricycleId;

    private Long userId;

    private AdminUserBo userObj;

    private String realname;

    private String mobile;

    private List<String> images;

    private LocalDateTime createdAt;

    public void setImages(String json) {
        if (json != null) {
            this.images = JsonUtils.toList(json, String.class);
        }
    }


}
