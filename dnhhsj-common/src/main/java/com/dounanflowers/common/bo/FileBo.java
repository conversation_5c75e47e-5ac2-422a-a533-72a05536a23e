package com.dounanflowers.common.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class FileBo {

    @Schema(description = "文件ID")
    private Long id;

    @Schema(description = "文件名称")
    private String name;

    @Schema(description = "文件URL")
    private String url;

    @Schema(description = "顺序")
    private Integer seq;

    private Integer width;

    private Integer height;

    private Integer duration;

    @Schema(description = "文件类型")
    private String type;

}
