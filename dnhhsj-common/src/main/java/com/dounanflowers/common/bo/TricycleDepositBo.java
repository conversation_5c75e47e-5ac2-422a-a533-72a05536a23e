package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(title = "三轮车押金")
public class TricycleDepositBo {

    @JsonProperty("_id")
    private Long id;

    private String type;

    @JsonProperty("balanceCent")
    private Integer balance;

    @JsonProperty("changeCent")
    private Integer change;

    private String remark;

    private LocalDateTime createdAt;

}

