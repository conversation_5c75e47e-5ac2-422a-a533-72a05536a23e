package com.dounanflowers.common.bo;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(title = "停车场区域")
public class LcPlateBo {
    @JsonProperty("_id")
    private Long id;

    @ColumnDef(comment = "用户ID")
    private String clientUserId;

    @ColumnDef(comment = "车牌号")
    private String plate;

    @ColumnDef(comment = "最后订单时间")
    private LocalDateTime lastOrderAt;
}

