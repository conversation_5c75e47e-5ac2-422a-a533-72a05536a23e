package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "经纪人")
public class BrokerBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "姓名")
    private String name;

    @Schema(title = "头像")
    private String avatar;

    @Schema(title = "手机号")
    private String mobile;

    @Schema(title = "头衔")
    private String jobTitle;

    @Schema(title = "职位")
    private String jobPosition;

    @Schema(title = "历史成交量")
    private Integer pastVolumeCount;

    @Schema(title = "战胜比例", description = "每天跑个任务，排序之后更新，")
    private String winPercent;

    private Boolean status;

}

