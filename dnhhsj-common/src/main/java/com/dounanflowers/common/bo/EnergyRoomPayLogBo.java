package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;



@Data
public class EnergyRoomPayLogBo {
    @Schema(title ="msg")
    private String msg;

    @Schema(description = "充值ID")
    private String chargeId;
    
    @Schema(description = "店铺编号")
    private String roomCode;

    @Schema(description ="cellNo")
    private String roomName;

    @Schema(description ="店铺名")
    private String uName;
    
    @Schema(description = "充值方式")
    private Integer chargeMethod;
    
    @Schema(description = "充值金额")
    private BigDecimal chargeMoney;
    
    @Schema(description = "用户名")
    private String userName;
    
    @Schema(description = "电话")
    private String telPhone;
    
    @Schema(description = "备注")
    private String remark;
    
    @Schema(description = "创建时间")
    private String createTime;
    
    @Schema(description = "订单ID")
    private String orderId;
    
    @Schema(description = "状态")
    private String status;
}
