package com.dounanflowers.common.bo;

import com.dounanflowers.common.entity.PropertyCell;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.enums.IsEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class EnergyCellBo {

    private Long id;

    @Schema(title = "管理公司")
    private String companyName;

    @Schema(title = "管理公司编码")
    private String companyCode;

    @Schema(title = "店铺名称")
    private String cellName;

    @Schema(title = "店铺编号")
    private String cellNo;

    @Schema(title = "店铺ID")
    private String cellId;

    @Schema(title = "店主姓名")
    private String masterName;

    @Schema(title = "店主手机号")
    private String masterMobile;

    @Schema(title = "店主身份证号")
    private String masterIdCardNo;

    @Schema(title = "店主所在层")
    private String layerName;

    @Schema(title = "店铺层门牌号")
    private String layerNo;

    @Schema(title = "用户Id")
    private Long adminUserId;

    @Schema(title = "创建时间")
    private UserObjBo adminUserObj;

    @Schema(title = "是否订阅")
    private IsEnum subscribe;

    @Schema(title = "备注")
    private String remark;

    @Schema(title = "物业店铺")
    private Long propertyCellId;

    @Schema(title = "物业店铺名称")
    private PropertyCell propertyCellObj;

    @Schema(title = "是否验证")
    private IsEnum isVerify;

}
