package com.dounanflowers.common.bo;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(title = "优惠券模板")
public class CouponTplBo {

    @JsonProperty("_id")
    private Long id;

    private String code;

    @Schema(title = "使用场景", description = "Coupon_useSceneType")
    @JsonProperty("useSceneType")
    private String scene;

    @Schema(title = "类型", description = "Coupon_type, “0”小时券,”1”金额券 '2'折扣券")
    private String type;

    @Schema(title = "面值", description = "小时券,单位分钟，金额券,单位分 折扣券 0表示免费10表示1折85表示85折90表示9折")
    private Integer parValue;

    @Schema(title = "过期类型", description = "Coupon_endAtType")
    @JsonProperty("endAtType")
    private String endType;

    @Schema(title = "相对过期", description = "单位分钟")
    private Integer relativeEndAt;

    @Schema(title = "绝对过期")
    private LocalDateTime absoluteEndAt;

    @Schema(title = "总发布数量")
    private Integer publishCount;

    @Schema(title = "已领取数量")
    private Integer receivedCount;

    @Schema(title = "已使用数量")
    private Integer usedCount;

    @Schema(title = "启用状态")
    private Boolean status;

    @Schema(title = "标题")
    private String title;

    @Schema(title = "领取标题")
    private String receiveTitle;

    @Schema(title = "发券者")
    private List<Long> pusherIds;

    private List<AdminUserBo> pusherList;

    @Schema(title = "游客可见")
    private Boolean clientPublic;

    @Schema(title = "游客可领取个数, 0表示不限制")
    private Integer clientReceiveLimit;

    @Schema(title = "是否可叠加")
    private Boolean isStackable;

    @Schema(title = "绑定的月卡")
    private String boundVehicleMonthlyCardIds;
}

