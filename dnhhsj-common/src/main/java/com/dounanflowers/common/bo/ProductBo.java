package com.dounanflowers.common.bo;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "商品")
public class ProductBo {

    @Schema(description = "ID")
    @JsonProperty("_id")
    private Long id;

    @Schema(description = "商铺ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long storeId;

    private StoreBo storeObj;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    private String title;

    @Schema(description = "副标题")
    private String subTitle;

    @Schema(description = "商品类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String type;

    @Schema(description = "商品描述")
    private String description;

    @Schema(description = "原价", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer originalPrice;

    @Schema(description = "商品价", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer price;

    @Schema(description = "有效期开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime validFrom;

    @Schema(description = "有效期结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime validTo;

    @Schema(description = "有效期天数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer validDays;

    @Schema(description = "限制时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String limitTime;

    @Schema(description = "每人最大购买数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer maxPurchasePerUser;

    @Schema(description = "库存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer stock;

    @Schema(description = "使用规则")
    private String useRules;

    @Schema(description = "退款政策")
    private String refundPolicy;

    @Schema(description = "官方认证")
    private Boolean certification;

    @Schema(description = "平均评分")
    private Integer rate;

    @Schema(description = "总评价数")
    private Integer totalReviews;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private String status;

    @Schema(description = "商品图片")
    private List<String> images;

    @Schema(description = "是否推荐")
    private Boolean recommend;

    @Schema(description = "已售")
    private Integer soldCount;

    @ColumnDef(comment = "是否支持直接退款")
    private Boolean directRefund;

}
