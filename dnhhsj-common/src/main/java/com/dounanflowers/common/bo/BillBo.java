package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(title = "账单")
public class BillBo {
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "商品Id")
    private Long entityId;

    @Schema(title = "商品模型")
    private String entityModel;

    @Schema(title = "商品名称")
    private Object entityObj;

    @Schema(title = "商品类型")
    private String type;

    @Schema(title = "用户Id")
    private Long userId;

    @Schema(title = "用户类型")
    private String userType;

    @Schema(title = "用户模型")
    private String userModel;

    @Schema(title = "原价金额")
    private Integer originalMoneyCent;

    @Schema(title = "优惠券抵扣金额")
    private Integer couponDiscountCent;

    @Schema(title = "积分抵扣金额")
    private Integer pointDiscountCent;

    @Schema(title = "订单金额")
    private Integer orderMoneyCent;

    @Schema(title = "实付金额")
    private Integer paidMoneyCent;

    @Schema(title = "已退款金额", defaultValue = "0")
    private Integer refundedMoneyCent;

    @Schema(title = "付款时间")
    private LocalDateTime paidAt;

    @Schema(title = "支付状态")
    private String payStatus;

    @Schema(title = "通联支付流水号")
    private String payTrxid;

    @Schema(title = "支付手续费")
    private Integer payFeeCent;

    @Schema(title = "退款列表")
    private List<BillRefundBo> refundList;

    @Schema(title = "备注")
    private String remark;

    @Schema(title = "关闭时间")
    private LocalDateTime closedAt;

    @Schema(title = "关闭原因")
    private String closedReason;

    @Schema(title = "其他数据")
    private Object otherData;

    @Schema(title = "订单附加凭证")
    private List<String> images;

    private LocalDateTime createdAt;

    private InvoiceBo invoiceObj;


    private UserObjBo userObj;
}
