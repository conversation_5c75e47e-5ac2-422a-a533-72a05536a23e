package com.dounanflowers.common.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class EnergyCellBalanceBo {
    @Schema(description = "店铺编号")
    private String roomCode;
    
    @Schema(description = "店铺名称")
    private String roomName;
    
    @Schema(description = "商户名称")
    private String merchantName;
    
    @Schema(description = "店铺状态")
    private String roomState;
    
    @Schema(description = "联系电话")
    private String telephone;
    
    @Schema(description = "余额")
    private String moneyLeft;
    
    @Schema(description = "预收费用")
    private String preCost;
    
    @Schema(description = "充值总额")
    private String chargeTotal;
    
    @Schema(description = "开始时间")
    private String startTime;
    
    @Schema(description = "更新时间")
    private String updateTime;
}
