package com.dounanflowers.common.bo;

import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.security.bean.IUser;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(title = "用户信息")
public class ClientUserBo implements IUser<Long> {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "昵称")
    private String nickname;

    @Schema(title = "真名")
    private String realname;

    @Schema(title = "手机号")
    private String mobile;

    @Schema(title = "头像")
    private String avatar;

    @Schema(title = "WxOpenId")
    @JsonProperty("wxOpenId")
    private String openId;

    @Schema(title = "最后登录时间")
    private LocalDateTime lastLoginAt;

    @Schema(title = "最后登录Ip")
    private String lastLoginIp;

    @Schema(title = "性别")
    private String gender;

    @Schema(title = "收藏个数")
    private Integer favCount;

    @Schema(title = "优惠券个数")
    private Integer couponCount;

    private LocalDateTime createdAt;

    @Schema(title = "购物车个数")
    private Integer cartCount;

    //
    // @Schema(title = "商家类型")
    // private String businessType;

    @Override
    public IUser<Long> clearSensitive() {
        ClientUserBo copy = BeanUtils.copy(this, ClientUserBo.class);
        copy.setOpenId(null);
        return copy;
    }

}

