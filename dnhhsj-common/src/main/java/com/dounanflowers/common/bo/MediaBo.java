package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(title = "媒体")
public class MediaBo {

    @JsonProperty("_id")
    private String id;

    @Schema(title = "名称")
    private String name;

    @Schema(title = "占位文字")
    private String alt;

    @Schema(title = "描述")
    @JsonProperty("desc")
    private String description;

    @Schema(title = "资源地址")
    private String url;

    @Schema(title = "类型", description = "video,image")
    private String type;

    @Schema(title = "byte大小")
    private Integer size;

    @Schema(title = "宽度")
    private Integer width;

    @Schema(title = "高度")
    private Integer height;

    @Schema(title = "视频时长")
    private Double duration;

    @Schema(title = "创建时间")
    private LocalDateTime createdAt;

}

