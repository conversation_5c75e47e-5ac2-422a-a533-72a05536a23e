package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(title = "购物车")
public class ShoppingCartStoreBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "用户ID")
    private Long userId;

    @Schema(title = "商铺ID")
    private Long storeId;

    @Schema(title = "商铺对象")
    private StoreBo storeObj;

    private List<ShoppingCartBo> items;

}
