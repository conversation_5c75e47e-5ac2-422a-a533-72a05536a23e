package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(title = "蓝卡订单支付记录")
public class LcOrderChargeBo {
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "支付订单号")
    private String payNo;

    @Schema(title = "支付金额")
    private String payCharge;

    @Schema(title = "支付类型")
    private String payKind;

    @Schema(title = "支付渠道")
    private String payChannel;

    @Schema(title = "线上交易流水号，每笔交易生成唯一流水号（支付结果下发）")
    private String transactionId;

    @Schema(title = "结算时间")
    private LocalDateTime getTime;

    @Schema(title = "备注")
    private String memo;

    @Schema(title = "订单号")
    private String lcOrderId;
}

