package com.dounanflowers.common.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "车辆月卡Bo")
public class VehicleMonthlyCardBo {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "月卡名称")
    private String name;

    @Schema(description = "月卡说明")
    private String description;

    @Schema(description = "有效时长(天)")
    private Integer duration;

    @Schema(description = "提醒天数")
    private Integer remindDays;

    @Schema(description = "价格(分)")
    private Integer price;

    @Schema(description = "创建时间")
    private String createdAt;
}
