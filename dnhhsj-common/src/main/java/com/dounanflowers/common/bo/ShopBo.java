package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(title = "商铺")
public class ShopBo {
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "商铺号")
    private String cellNo;

    @Schema(title = "装修")
    private String decoration;

    @Schema(title = "楼层")
    private Integer floor;

    @Schema(title = "户型")
    private List<String> houseType; // 户型

    @Schema(title = "图片")
    private List<String> images; // 图片

    @Schema(title = "朝向 1-东；2-南；3-西；4-北；5-东南；6-西南；7-西北；8-东北")
    private String orientation;

    @Schema(title = "付款方式")
    private String payment;

    @Schema(title = "标签")
    private List<String> tags; // 标签

    @Schema(title = "标题")
    private String title;

    @Schema(title = "视频")
    private List<String> videos;

    @Schema(title = "单元ID")
    private Long cellId;

    @Schema(title = "单元名称")
    private String cellName;

    @Schema(title = "房产性质 1-空置; 2-已租; 3-业主自营; 4-自用; 5-不租; 6-安置办公; 7-其它1; 8-其它2; 9-其它3")
    private String cellProperty;

    @Schema(title = "套内面积")
    private Double cellArea;

    @Schema(title = "建筑面积")
    private Double buildingArea;

    @Schema(title = "单元状态 1-返租; 2-业主自持; 3-未售; 4-待定; 5-公司自持; 7-取消; 8-无产权房; 10-场地; 11-阳台; 12-仕杰公司; 13-档口")
    private String cellStatus;

    @Schema(title = "合同开始日期")
    private LocalDateTime contractStartDate;

    @Schema(title = "合同结束日期")
    private LocalDateTime contractEndDate;

    @Schema(title = "合同编号")
    private String contractNo;

    @Schema(title = "经营类型")
    private List<String> businessType;

    @Schema(title = "单元备注")
    private String cellRemark;

    @Schema(title = "附件名称")
    private String attachmentNames;

    @Schema(title = "附件路径")
    private String attachmentPaths;

    @Schema(title = "费用名称")
    private String costName;

    @Schema(title = "单价")
    private Double unitPrice;

    @Schema(title = "付款方式 1-押三付三；2-押三付六；3-1年付；4-3年付；5-面议")
    private String billingCycle;

    @Schema(title = "费用金额")
    private Double chargeAmount;

    @Schema(title = "租户ID")
    private String terantId;

    @Schema(title = "租户名称")
    private String tenantName;

    @Schema(title = "租户电话")
    private String tenantPhone;

    @Schema(title = "联系人")
    private String contactPerson;

    @Schema(title = "联系人电话")
    private String contactPhone;

    @Schema(title = "租户类型 1-个人；2-公司")
    private String tenantType;

    @Schema(title = "租户ID")
    private Long tenantId;

    @Schema(title = "租户身份证号")
    private String tenantIdCard;

    @Schema(title = "租户地址")
    private String tenantAddress;

    @Schema(title = "营业执照号")
    private String businessLicense;

    @Schema(title = "租户备注")
    private String tenantRemark;

    @Schema(title = "区域")
    private List<String> region;

    @Schema(title = "是否上架")
    private Boolean onShelf;

    @Schema(title = "上架时间")
    private LocalDateTime onShelfAt;

    @Schema(title = "浏览量")
    private Integer viewCount;

    @Schema(title = "收藏量")
    private Integer favouriteCount;

    @Schema(title = "通话量，经纪人被拨打次数")
    private Integer brokerCalledCount;

    @Schema(title = "推荐值")
    private Integer recommend;

    @Schema(title = "排名")
    private Integer overallRank;

    @Schema(title = "排名分数")
    private Integer overallRankScore;

    @Schema(title = "编辑时间")
    private LocalDateTime editedAt;

    @Schema(title = "分享图片")
    private String shareImageUrl;

    @Schema(title = "分享图片版本")
    private String shareImageVersion;
}
