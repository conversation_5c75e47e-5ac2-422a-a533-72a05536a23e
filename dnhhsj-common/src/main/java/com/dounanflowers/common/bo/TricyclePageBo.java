package com.dounanflowers.common.bo;

import com.dounanflowers.framework.bean.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class TricyclePageBo extends Page<TricycleBo> {

    private Integer waitPayDepositCount;

    private Integer waitCheckCount;

    private Integer normalDepositCount;

    private Integer unclaimedCount;

    private Integer makingCount;

    private Integer waitingMakeCount;

}
