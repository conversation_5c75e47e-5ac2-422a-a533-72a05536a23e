package com.dounanflowers.common.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "车辆月卡订单Bo")
public class VehicleMonthlyCardOrderBo {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "机动车ID")
    private Long tricycleId;

    @Schema(description = "月卡ID")
    private Long monthlyCardId;

    @Schema(description = "优惠码ID")
    private Long couponId;

    @Schema(description = "原价(分)")
    private Integer originalPrice;

    @Schema(description = "实付金额(分)")
    private Integer actualPrice;

    @Schema(description = "订单状态(0:待支付,1:已支付,2:已取消)")
    private Integer status;

    @Schema(description = "支付时间")
    private String paidAt;

    @Schema(description = "生效时间")
    private String effectiveAt;

    @Schema(description = "到期时间")
    private String expireAt;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "创建时间")
    private String createdAt;

    @Schema(description = "月卡信息")
    private VehicleMonthlyCardBo monthlyCard;

    @Schema(description = "用户信息")
    private AdminUserBo user;
}
