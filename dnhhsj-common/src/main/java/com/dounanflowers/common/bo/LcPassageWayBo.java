package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "停车场区域")
public class LcPassageWayBo {
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "场库")
    private String lcParkId;

    @Schema(title = "通道Id")
    private String passageWayId;

    @Schema(title = "通道名称")
    private String passageWayName;

    @Schema(title = "设备号")
    private String deviceId;

    @Schema(title = "匹配级别id")
    private String matchClassId;

    @Schema(title = "开闸类型")
    private String openBarriorTypeId;

    @Schema(title = "源区域Id")
    private String sourceAreaId;

    @Schema(title = "目标区域Id")
    private String targetAreaId;

    @Schema(title = "停车场编号")
    private String parkNumber;
}

