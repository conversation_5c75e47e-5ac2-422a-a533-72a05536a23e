package com.dounanflowers.common.bo;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.enums.IsEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(title = "优惠券")
public class CouponBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "标题")
    private String title;

    @Schema(title = "优惠券编码")
    @JsonProperty("couponCode")
    private String code;

    @Schema(title = "使用场景", description = "Coupon_useSceneType")
    @JsonProperty("useSceneType")
    private String scene;

    @Schema(title = "类型", description = "Coupon_type “0”小时券,”1”金额券 '2'折扣券")
    private String type;

    @Schema(title = "面值", description = "小时券,单位分钟，金额券,单位分 折扣券 0表示免费10表示1折85表示85折90表示9折")
    private Integer parValue;

    @Schema(title = "截至时间")
    private LocalDateTime endAt;

    @Schema(title = "优惠券模板id")
    private Long couponTplId;

    @Schema(title = "用户id")
    private Long userId;

    private UserObjBo userObj;

    @Schema(title = "使用时间", description = "存在此项表示已使用")
    private LocalDateTime usedAt;

    private LocalDateTime createdAt;

    @Schema(title = "来源ID")
    @JsonProperty("sourceId")
    private Long sourceUserId;

    @Schema(title = "是否可叠加")
    private Boolean isStackable;
}

