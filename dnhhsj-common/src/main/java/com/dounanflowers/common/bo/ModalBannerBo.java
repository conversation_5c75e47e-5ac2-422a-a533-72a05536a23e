package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(title = "弹窗横幅")
public class ModalBannerBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "标题")
    private String title;

    @Schema(title = "客户端类型", description = "client-用户端；shop-商家端")
    private String client;

    @Schema(title = "位置")
    private String place;

    @Schema(title = "开始时间")
    private LocalDateTime startAt;

    @Schema(title = "结束时间")
    private LocalDateTime endAt;

    @Schema(title = "关闭后不再弹出的时间（小时）")
    private Integer hideHours;

    @Schema(title = "跳转小程序内地址")
    private String linkUrl;

    @Schema(title = "图片URL")
    private String imageUrl;

    @Schema(title = "是否启用")
    private Boolean status;

    @Schema(title = "创建时间")
    private LocalDateTime createdAt;

    @Schema(title = "修改时间")
    private LocalDateTime updatedAt;
}
