package com.dounanflowers.common.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class LcOrderFeeBo {
    @Schema(title = "停车订单ID")
    private Long lcOrderId;

    @Schema(title = "入场时间")
    private String inTime;

    @Schema(title = "停车费原价", description = "单位分")
    private int moneyCent;

    @Schema(title = "优惠券抵扣金额", description = "单位分")
    private int discountCent;

    @Schema(title = "实际待付金额", description = "单位分")
    private int payMoneyCent;

    @Schema(title = "之前已抵扣金额", description = "单位分")
    private int baseDiscountCent;

    @Schema(title = "此次抵扣金额", description = "单位分")
    private int currentDiscountCent;

    @Schema(title = "当前折扣")
    private double scale;

    @Schema(title = "已支付")
    private int paidTotal;

}
