package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class BannerShowBo {

    @JsonProperty("_id")
    private String id;

    @Schema(title = "类型", description = "1-图片；2-视频")
    private String type;

    @Schema(title = "位置", description = "1-首页顶部")
    private String place;

    @Schema(title = "URL", description = "资源地址")
    private String url;

    @Schema(title = "跳转方式", description = "1-小程序；2-H5")
    private String linkType;

    @Schema(title = "跳转地址")
    private String linkUrl;

}

