package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(title = "账单退款")
public class BillRefundBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "账单ID")
    private Long billId;

    @Schema(title = "退款金额")
    private Integer moneyCent;

    @Schema(title = "退款手续费")
    private Integer feeCent;

    @Schema(title = "通联退款流水号")
    private String trxid;

    @Schema(title = "退款操作时间")
    private LocalDateTime operatedAt;

    @Schema(title = "退款成功时间")
    private LocalDateTime refundedAt;

    @Schema(title = "退款原因")
    private String reason;
}
