package com.dounanflowers.common.bo;

import com.dounanflowers.common.entity.AdminUser;
import com.dounanflowers.common.entity.Bill;
import com.dounanflowers.common.entity.ClientUser;
import com.dounanflowers.common.enums.InvoiceHeaderTypeEnum;
import com.dounanflowers.common.enums.InvoiceStatusEnum;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 发票业务对象
 */
@Data
@Accessors(chain = true)
public class InvoiceBo {

    /**
     * 发票ID
     */
    private Long id;

    /**
     * 抬头类型
     */
    private InvoiceHeaderTypeEnum headerType;

    /**
     * 抬头名称
     */
    private String headerName;

    /**
     * 税号
     */
    private String taxNumber;

    /**
     * 发票内容
     */
    private String content;

    /**
     * 发票金额(分)
     */
    private Integer amountCent;

    /**
     * 开票状态
     */
    private InvoiceStatusEnum status;

    /**
     * 拒绝理由
     */
    private String rejectReason;

    /**
     * 处理时间
     */
    private LocalDateTime processedAt;

    /**
     * 处理人
     */
    private String processor;

    /**
     * 备注
     */
    private String remark;

    /**
     * 下载地址
     */
    private String downloadUrl;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 关联的账单ID列表
     */
    private List<Long> billIds;


    private UserObjBo userObj;

    private List<BillBo> BillObjList;

    @Schema(title = "用户类型")
    private String userType;
}
