package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(title = "字典")
public class DictBo implements Serializable {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "键名")
    private String key;

    @Schema(title = "名称")
    private String name;

    @Schema(title = "描述")
    private String description;

    private List<DictOptionBo> options;

    @Schema(description = "状态")
    private Boolean status;

    @Schema(description = "标签")
    private List<String> tags;

}

