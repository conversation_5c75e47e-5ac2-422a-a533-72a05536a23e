package com.dounanflowers.common.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "三轮车违规规则")
public class TricycleIllegalRuleBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "规则名称")
    @JsonProperty("name")
    private String title;

    @Schema(title = "延时处罚分钟数")
    @JsonProperty("punishDelayMinutes")
    private Integer punishDelayMinute;

    @Schema(title = "罚款金额", description = "单位分")
    @JsonProperty("fineCent")
    private Integer fine;

    @Schema(title = "是否需要拖走", description = "拖走的多一个出库步骤")
    private Boolean needTow;

    @Schema(title = "是否启用")
    private Boolean status;

}

