package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.PermissionTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "admin_permission", comment = "管理员权限表")
@Accessors(chain = true)
public class AdminPermission extends SoftDeletedEntity {

    @ColumnDef(comment = "权限名称", nullable = false)
    private String name;

    @ColumnDef(comment = "权限编码", nullable = false)
    private String code;

    @ColumnDef(comment = "权限描述")
    private String description;

    @ColumnDef(comment = "权限类型")
    private PermissionTypeEnum type;

}
