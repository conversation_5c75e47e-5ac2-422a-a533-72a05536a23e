package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "dict", comment = "字典表")
public class Dict extends SoftDeletedEntity {

    @ColumnDef(comment = "字典键", nullable = false)
    private String key;

    @ColumnDef(comment = "字典名称", nullable = false)
    private String name;

    @ColumnDef(comment = "字典描述")
    private String description;

    @ColumnDef(comment = "状态", defaultValue = "1", nullable = false)
    private IsEnum status;

    @RelationOneToMany(
            selfField = "id",
            targetField = "dictId",
            targetTable = "dict_tag_r",
            valueField = "tag"
    )
    @ColumnDef(ignore = true)
    private List<String> tags;

    @RelationOneToMany(
            selfField = "id",
            targetField = "dictId",
            targetTable = "dict_option"
    )
    @ColumnDef(ignore = true)
    private List<DictOption> options;

}
