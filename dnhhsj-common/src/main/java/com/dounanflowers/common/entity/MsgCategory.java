package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.MsgTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "msg_category", comment = "消息分类表")
public class MsgCategory extends SoftDeletedEntity {

    @ColumnDef(comment = "分类编码", nullable = false)
    private String code;

    @ColumnDef(comment = "父级分类ID", defaultValue = "0")
    private Long parentId;

    @ColumnDef(comment = "分类标题", nullable = false)
    private String title;

    @ColumnDef(comment = "分类图标")
    private String icon;

    @ColumnDef(comment = "分类颜色")
    private String color;

    @ColumnDef(comment = "分类描述", type = "text")
    private String description;

    @ColumnDef(comment = "消息类型", nullable = false)
    private MsgTypeEnum type;

}
