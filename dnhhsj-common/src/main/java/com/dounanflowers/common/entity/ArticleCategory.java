package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "article_category_r", comment = "文章分类关联表")
public class ArticleCategory extends BaseEntity {

    @ColumnDef(comment = "文章ID", nullable = false)
    private Long articleId;

    @ColumnDef(comment = "分类名称", nullable = false)
    private String category;

}
