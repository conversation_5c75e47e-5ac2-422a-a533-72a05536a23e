package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.ProductStatusEnum;
import com.dounanflowers.common.enums.ProductTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("product")
@Accessors(chain = true)
public class Product extends SoftDeletedEntity {

    @ColumnDef(comment = "商铺ID", nullable = false)
    private Long storeId;

    @ColumnDef(comment = "标题", nullable = false)
    private String title;

    @ColumnDef(comment = "副标题")
    private String subTitle;

    @ColumnDef(comment = "商品类型", nullable = false)
    private ProductTypeEnum type;

    @ColumnDef(comment = "描述")
    private String description;

    @ColumnDef(comment = "原价", nullable = false)
    private Integer originalPrice;

    @ColumnDef(comment = "有效期天数")
    private Integer validDays;

    @ColumnDef(comment = "限制时间")
    private String limitTime;

    @ColumnDef(comment = "商品价", nullable = false)
    private Integer price;

    @ColumnDef(comment = "有效期开始时间", nullable = false)
    private LocalDateTime validFrom;

    @ColumnDef(comment = "有效期结束时间", nullable = false)
    private LocalDateTime validTo;

    @ColumnDef(comment = "每人最大购买数量", nullable = false)
    private Integer maxPurchasePerUser;

    @ColumnDef(comment = "库存数量", nullable = false)
    private Integer stock;

    @ColumnDef(comment = "已售")
    private Integer soldCount;

    @ColumnDef(comment = "使用规则")
    private String useRules;

    @ColumnDef(comment = "退款政策")
    private String refundPolicy;

    @ColumnDef(comment = "官方认证")
    private IsEnum certification;

    @ColumnDef(comment = "平均评分")
    private Integer rate;

    @ColumnDef(comment = "总评价数")
    private Integer totalReviews;

    @ColumnDef(comment = "状态", nullable = false)
    private ProductStatusEnum status;

    @RelationOneToMany(
            selfField = "id",
            targetField = "outerId",
            targetTable = "file_info",
            valueField = "url"
    )
    @ColumnDef(ignore = true)
    private List<String> images;

    @ColumnDef(comment = "是否推荐")
    private IsEnum recommend;

    @ColumnDef(comment = "是否支持直接退款")
    private IsEnum directRefund;

}
