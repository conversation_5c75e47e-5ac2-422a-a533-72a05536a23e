package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "shop_subscribe", comment = "商铺订阅表")
public class ShopSubscribe extends SoftDeletedEntity {

    @ColumnDef(comment = "订阅标题", nullable = false)
    private String title;

    @ColumnDef(comment = "订阅过滤条件", nullable = false)
    private String filter;

    @ColumnDef(comment = "订阅人ID(商家)", nullable = false)
    private Long adminUserId;

}
