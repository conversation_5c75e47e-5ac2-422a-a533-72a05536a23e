package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "coupon_tpl_user_r", comment = "优惠券模板推送人关联表")
public class CouponTplUserR extends BaseEntity {

    @ColumnDef(comment = "优惠券模板ID", nullable = false)
    private Long couponTplId;

    @ColumnDef(comment = "推送人ID", nullable = false)
    private Long userId;

}
