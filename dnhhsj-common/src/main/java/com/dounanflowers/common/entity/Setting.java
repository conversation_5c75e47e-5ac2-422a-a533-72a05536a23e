package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "setting", comment = "系统配置表")
public class Setting extends SoftDeletedEntity {

    @ColumnDef(comment = "配置键名", nullable = false)
    private String key;

    @ColumnDef(comment = "父级配置键名")
    private String parentKey;

    @ColumnDef(comment = "配置值", type = "text")
    private String value;

}
