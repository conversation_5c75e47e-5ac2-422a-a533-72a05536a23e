package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户车牌号
 * userId -> clientUserId
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table("lc_plate")
public class LcPlate extends SoftDeletedEntity {

    @ColumnDef(comment = "用户ID")
    private Long clientUserId;

    @ColumnDef(comment = "车牌号")
    private String plate;

    @ColumnDef(comment = "最后订单时间")
    private LocalDateTime lastOrderAt;
}
