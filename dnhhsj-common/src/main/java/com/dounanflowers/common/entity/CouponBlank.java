package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.RelationOneToOne;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "coupon_blank", comment = "优惠券空白表")
public class CouponBlank extends SoftDeletedEntity {

    @ColumnDef(comment = "生成数量", nullable = false)
    private Integer count;

    @ColumnDef(comment = "优惠券模板ID", nullable = false)
    private Long couponTplId;

    @ColumnDef(comment = "下载链接")
    private String downloadUrl;

    @ColumnDef(comment = "归属用户ID", nullable = false)
    private Long belongUserId;

    @RelationOneToOne(
            selfField = "belongUserId",
            targetField = "id",
            targetTable = "admin_user"
    )
    @ColumnDef(ignore = true)
    private AdminUser belongUser;

    @RelationOneToMany(
            selfField = "id",
            targetField = "couponBlankId",
            targetTable = "coupon_blank_code_r",
            valueField = "code"
    )
    @ColumnDef(ignore = true)
    private List<String> couponCodes;

}
