package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "bill_refund", comment = "账单退款表")
public class BillRefund extends BaseEntity {

    @ColumnDef(comment = "账单ID", nullable = false)
    private Long billId;

    @ColumnDef(comment = "退款金额", nullable = false)
    private Integer moneyCent;

    @ColumnDef(comment = "退款手续费")
    private Integer feeCent;

    @ColumnDef(comment = "通联退款流水号")
    private String trxid;

    @ColumnDef(comment = "退款操作时间")
    private LocalDateTime operatedAt;

    @ColumnDef(comment = "退款成功时间")
    private LocalDateTime refundedAt;

    @ColumnDef(comment = "退款原因")
    private String reason;

}
