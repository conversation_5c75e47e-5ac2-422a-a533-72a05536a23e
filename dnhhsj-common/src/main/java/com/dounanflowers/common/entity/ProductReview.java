package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("product_review")
@Accessors(chain = true)
public class ProductReview extends SoftDeletedEntity {

    @ColumnDef(comment = "用户ID", nullable = false)
    private Long userId;

    @ColumnDef(comment = "商品ID", nullable = false)
    private Long productId;

    @ColumnDef(comment = "订单ID", nullable = false)
    private Long orderId;

    @ColumnDef(comment = "评分(1-5)", nullable = false)
    private Integer rating;

    @ColumnDef(comment = "评价内容")
    private String content;

    @RelationOneToMany(
            selfField = "id",
            targetField = "outerId",
            targetTable = "file_info",
            valueField = "url"
    )
    @ColumnDef(ignore = true)
    private List<String> images;

    @ColumnDef(comment = "是否匿名评价", nullable = false)
    private IsEnum isAnonymous;

    @ColumnDef(comment = "商家回复内容")
    private String replyContent;

    @ColumnDef(comment = "商家回复时间")
    private LocalDateTime replyTime;

    @ColumnDef(comment = "是否显示", nullable = false, defaultValue = "1")
    private IsEnum show;

}
