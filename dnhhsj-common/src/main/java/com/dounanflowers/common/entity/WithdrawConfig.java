package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "withdraw_config", comment = "提现配置表")
public class WithdrawConfig extends SoftDeletedEntity {

    private Long userId;

    @ColumnDef(comment = "账户姓名")
    private String name;

    @ColumnDef(comment = "开户行信息")
    private String bank;

    @ColumnDef(comment = "支行信息")
    private String branch;

    @ColumnDef(comment = "账号")
    private String number;

}
