package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "client_user_points", comment = "用户积分表")
public class ClientUserPoints extends SoftDeletedEntity {

    @ColumnDef(comment = "用户ID", nullable = false)
    private Long userId;

    @ColumnDef(comment = "总积分", nullable = false)
    private Integer totalPoints;

    @ColumnDef(comment = "可用积分", nullable = false)
    private Integer availablePoints;

    @ColumnDef(comment = "已使用积分", nullable = false)
    private Integer usedPoints;

    @ColumnDef(comment = "已过期积分", nullable = false)
    private Integer expiredPoints;
}
