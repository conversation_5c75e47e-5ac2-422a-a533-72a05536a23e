package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "vehicle_monthly_card", comment = "车辆月卡表")
public class VehicleMonthlyCard extends SoftDeletedEntity {

    @ColumnDef(comment = "月卡名称")
    private String name;

    @ColumnDef(comment = "月卡说明")
    private String description;

    @ColumnDef(comment = "有效时长(天)")
    private Integer duration;

    @ColumnDef(comment = "价格")
    private Integer price;

    @ColumnDef(comment = "提醒天数")
    private Integer remindDays;

    @ColumnDef(ignore = true)
    private CouponTpl couponTpl;

}
