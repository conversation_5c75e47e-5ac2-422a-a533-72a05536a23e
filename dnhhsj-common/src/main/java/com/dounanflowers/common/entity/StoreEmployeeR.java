package com.dounanflowers.common.entity;

import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("store_employee_r")
@Accessors(chain = true)
public class StoreEmployeeR extends BaseEntity {

    private Long storeId;

    private Long userId;

    private String name;

    private String mobile;

    private IsEnum status;

}
