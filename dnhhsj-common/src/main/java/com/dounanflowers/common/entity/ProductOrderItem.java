package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.cglib.core.Local;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("product_order_item")
@Accessors(chain = true)
public class ProductOrderItem extends SoftDeletedEntity {

    @ColumnDef(comment = "订单ID", nullable = false)
    private Long orderId;

    @ColumnDef(comment = "商品ID", nullable = false)
    private Long productId;

    @ColumnDef(comment = "购买数量", nullable = false)
    private Integer quantity;

    @ColumnDef(comment = "单价", nullable = false)
    private Integer unitPrice;

    @ColumnDef(comment = "总金额", nullable = false)
    private Integer totalAmount;

    private Integer realAmount;

    @ColumnDef(comment = "过期时间")
    private LocalDateTime expireTime;

    @ColumnDef(comment = "商品券号")
    private String code;

    @ColumnDef(comment = "使用时间")
    private LocalDateTime useTime;

    @ColumnDef(comment = "快照")
    private String snapshot;

    @ColumnDef(comment = "评价时间")
    private LocalDateTime reviewTime;

    @ColumnDef(comment = "核销人ID")
    private Long checkAdminUserId;

    @ColumnDef(ignore = true)
    private ProductOrder order;

}
