package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.TricycleCheckStatusEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "tricycle", comment = "三轮车信息表")
public class Tricycle extends SoftDeletedEntity {

    @ColumnDef(comment = "三轮车编号")
    private String number;

    @ColumnDef(comment = "所属用户ID")
    private Long userId;

    @ColumnDef(comment = "押金金额(分)", defaultValue = "0", nullable = false)
    private Integer deposit;

    @ColumnDef(comment = "押金退还时间")
    private LocalDateTime depositRefundedAt;

    @RelationOneToMany(
            selfField = "id",
            targetField = "outerId",
            targetTable = "file_info",
            valueField = "url"
    )
    @ColumnDef(ignore = true)
    private List<String> images;

    @ColumnDef(comment = "审核状态", nullable = false)
    private TricycleCheckStatusEnum checkStatus;

    @ColumnDef(comment = "批次ID")
    private Long batchId;

    @ColumnDef(comment = "生效时间")
    private LocalDateTime effectiveAt;

}
