package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.GenderEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "admin_user", comment = "管理员用户表")
public class AdminUser extends SoftDeletedEntity {

    @ColumnDef(comment = "用户名")
    private String username;

    @ColumnDef(comment = "昵称")
    private String nickname;

    @ColumnDef(comment = "头像")
    private String avatar;

    @ColumnDef(comment = "密码")
    private String password;

    @ColumnDef(comment = "是否认证")
    private IsEnum isCert;

    @ColumnDef(comment = "最后登录时间")
    private LocalDateTime lastLoginAt;

    @ColumnDef(comment = "最后登录IP")
    private String lastLoginIp;

    @ColumnDef(comment = "微信OpenID")
    private String openId;

    @ColumnDef(comment = "微信UnionID")
    private String unionId;

    @ColumnDef(comment = "手机号")
    private String mobile;

    @ColumnDef(comment = "邮箱")
    private String email;

    @ColumnDef(comment = "真实姓名")
    private String realname;

    @ColumnDef(comment = "性别", defaultValue = "0", nullable = false)
    private GenderEnum gender;

    @ColumnDef(comment = "是否启用", nullable = false, defaultValue = "1")
    private IsEnum isEnabled;

    @ColumnDef(comment = "是否虚拟用户", nullable = false, defaultValue = "0")
    private IsEnum fake;

    @RelationOneToMany(
            selfField = "id",
            targetField = "userId",
            targetTable = "admin_user_role_r",
            valueField = "role"
    )
    @ColumnDef(ignore = true)
    private List<String> roles;

    private Long boundBrokerId;

}
