package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 停车场设备
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table("lc_device")
public class LcDevice extends SoftDeletedEntity {

    @ColumnDef(comment = "设备编号")
    private Integer deviceId;

    @ColumnDef(comment = "通道名称")
    private String name;

    @ColumnDef(comment = "设备类型")
    private String deviceTypeId;

    @ColumnDef(comment = "场库")
    private Long lcParkId;

    @ColumnDef(comment = "场库编号")
    private String parkNumber;

    @ColumnDef(comment = "是否在线 1-在线；2-离线")
    private String state;

    @ColumnDef(comment = "设备IP")
    private String ip;

    @ColumnDef(comment = "上次在线时间")
    private LocalDateTime lastTime;
}
