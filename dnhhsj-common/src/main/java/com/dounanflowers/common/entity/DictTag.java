package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "dict_tag_r", comment = "字典标签关联表")
public class DictTag extends BaseEntity {

    @ColumnDef(nullable = false, comment = "字典ID")
    private Long dictId;

    @ColumnDef(nullable = false, comment = "标签")
    private String tag;

}
