package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.TricycleDepositTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "tricycle_deposit", comment = "三轮车押金记录表")
public class TricycleDeposit extends SoftDeletedEntity {

    @ColumnDef(comment = "三轮车ID", nullable = false)
    private Long tricycleId;

    @ColumnDef(comment = "押金变更类型", nullable = false)
    private TricycleDepositTypeEnum type;

    @ColumnDef(comment = "变更后余额(分)", nullable = false)
    private Integer balance;

    @ColumnDef(comment = "变更金额(分)", nullable = false)
    private Integer change;

    @ColumnDef(comment = "备注说明")
    private String remark;

    @ColumnDef(comment = "是否虚拟记录", nullable = false, defaultValue = "0")
    private IsEnum fake;

}
