package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.PriceTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "park_service", comment = "园区服务")
public class ParkService extends SoftDeletedEntity {

    @ColumnDef(nullable = false, comment = "服务名称")
    private String name;

    @ColumnDef(nullable = false, comment = "价格类型")
    private PriceTypeEnum priceType;

    @ColumnDef(comment = "可预约天数")
    private Integer days;

    @ColumnDef(comment = "价格")
    private Integer price;

    @ColumnDef(comment = "价格")
    private Integer priceMax;

    @ColumnDef(comment = "服务时长（分钟）")
    private Integer minutes;

    @RelationOneToMany(selfField = "id", targetField = "outerId", targetTable = "file_info", extraCondition = "field = 'images'", valueField = "url")
    @ColumnDef(ignore = true)
    private List<String> images;

    @ColumnDef(comment = "描述")
    private String description;

    @RelationOneToMany(selfField = "id", targetField = "serviceId", targetTable = "park_service_times")
    @ColumnDef(ignore = true)
    private List<ParkServiceTimes> times;

}
