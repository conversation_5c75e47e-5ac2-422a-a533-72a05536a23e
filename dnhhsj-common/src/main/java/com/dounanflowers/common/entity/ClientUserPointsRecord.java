package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.PointsRecordTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "client_user_points_record", comment = "用户积分记录表")
public class ClientUserPointsRecord extends SoftDeletedEntity {

    @ColumnDef(comment = "用户ID", nullable = false)
    private Long userId;

    @ColumnDef(comment = "积分变动", nullable = false)
    private Integer points;

    @ColumnDef(comment = "变动类型", nullable = false)
    private PointsRecordTypeEnum type;

    @ColumnDef(comment = "变动描述")
    private String description;

    @ColumnDef(comment = "变动原因")
    private String reason;

    @ColumnDef(comment = "关联订单ID")
    private Long orderId;

    @ColumnDef(comment = "过期时间")
    private LocalDateTime expireTime;

    @ColumnDef(comment = "备注")
    private String remark;
}
