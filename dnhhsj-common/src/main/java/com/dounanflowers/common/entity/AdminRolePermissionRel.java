package com.dounanflowers.common.entity;

import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "admin_role_permission_r", comment = "管理员角色表")
@Accessors(chain = true)
public class AdminRolePermissionRel extends BaseEntity {

    private String role;

    private String permission;

}
