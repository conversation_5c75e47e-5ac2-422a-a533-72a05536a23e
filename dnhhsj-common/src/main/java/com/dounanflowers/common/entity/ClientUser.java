package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.GenderEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "client_user", comment = "客户用户表")
public class ClientUser extends SoftDeletedEntity {

    @ColumnDef(comment = "用户名")
    private String username;

    @ColumnDef(comment = "昵称")
    private String nickname;

    @ColumnDef(comment = "头像")
    private String avatar;

    @ColumnDef(comment = "密码")
    private String password;

    @ColumnDef(comment = "最后登录时间")
    private LocalDateTime lastLoginAt;

    @ColumnDef(comment = "最后登录IP")
    private String lastLoginIp;

    @ColumnDef(comment = "微信openid")
    private String openId;

    @ColumnDef(comment = "微信unionid")
    private String unionId;

    @ColumnDef(comment = "手机号")
    private String mobile;

    @ColumnDef(comment = "邮箱")
    private String email;

    @ColumnDef(comment = "真实姓名")
    private String realname;

    @ColumnDef(comment = "性别", defaultValue = "0", nullable = false)
    private GenderEnum gender;

    @ColumnDef(comment = "绑定的经纪人ID")
    private Long boundBrokerId;

    @ColumnDef(comment = "是否虚拟用户", defaultValue = "0", nullable = false)
    private IsEnum fake;

}
