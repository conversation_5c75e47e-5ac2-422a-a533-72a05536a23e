package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 蓝卡订单支付记录
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table("lc_order_charge")
public class LcOrderCharge extends BaseEntity {
    @ColumnDef(comment = "订单号")
    private Long lcOrderId;

    @ColumnDef(comment = "支付订单号")
    private String payNo;

    @ColumnDef(comment = "支付金额")
    private String payCharge;

    @ColumnDef(comment = "支付类型")
    private String payKind;

    @ColumnDef(comment = "支付渠道")
    private String payChannel;

    @ColumnDef(comment = "线上交易流水号，每笔交易生成唯一流水号（支付结果下发）")
    private String transactionId;

    @ColumnDef(comment = "结算时间")
    private LocalDateTime getTime;

    @ColumnDef(comment = "备注")
    private String memo;


}
