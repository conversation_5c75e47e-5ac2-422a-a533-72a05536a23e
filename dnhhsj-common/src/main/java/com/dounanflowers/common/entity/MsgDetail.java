package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.MsgChannelEnum;
import com.dounanflowers.common.enums.MsgProcessStatusEnum;
import com.dounanflowers.common.enums.MsgTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "msg_detail", comment = "消息详情表")
public class MsgDetail extends SoftDeletedEntity {

    @ColumnDef(comment = "消息类别ID", nullable = false, defaultValue = "0")
    private Long categoryId;

    @ColumnDef(comment = "消息标题", nullable = false)
    private String title;

    @ColumnDef(comment = "消息内容", type = "text")
    private String content;

    @ColumnDef(comment = "消息类型", nullable = false)
    private MsgTypeEnum type;

    @ColumnDef(comment = "消息渠道", nullable = false)
    private MsgChannelEnum channel;

    @ColumnDef(comment = "消息处理状态", nullable = false)
    private MsgProcessStatusEnum status;

    @ColumnDef(comment = "是否已读", defaultValue = "0")
    private IsEnum read;

    @ColumnDef(comment = "发送者用户ID", nullable = false)
    private Long sendUserId;

    @ColumnDef(comment = "接收者用户ID", nullable = false)
    private Long receiveUserId;

    @ColumnDef(comment = "处理者用户ID")
    private Long processUserId;

    @ColumnDef(comment = "过期时间")
    private LocalDateTime expiredAt;

    @ColumnDef(comment = "外部类型")
    private String outerType;

    @ColumnDef(comment = "外部关联ID")
    private Long outerId;

    @ColumnDef(comment = "额外信息", type = "text")
    private String extra;

}
