package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.MsgChannelEnum;
import com.dounanflowers.common.enums.MsgTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "msg_template", comment = "消息模板表")
public class MsgTemplate extends SoftDeletedEntity {

    @ColumnDef(comment = "消息分类ID", nullable = false, defaultValue = "0")
    private Long categoryId;

    @ColumnDef(comment = "模板编码", nullable = false)
    private String code;

    @ColumnDef(comment = "模板标题", nullable = false)
    private String title;

    @ColumnDef(comment = "模板内容", type = "text")
    private String content;

    @ColumnDef(comment = "消息类型", nullable = false)
    private MsgTypeEnum type;

    @ColumnDef(comment = "发送渠道", nullable = false)
    private MsgChannelEnum channel;

}
