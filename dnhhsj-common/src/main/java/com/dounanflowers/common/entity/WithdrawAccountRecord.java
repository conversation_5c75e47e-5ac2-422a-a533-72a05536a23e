package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.AccountDirectionEnum;
import com.dounanflowers.common.enums.AccountRecordTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "withdraw_account_record", comment = "账目记录表")
public class WithdrawAccountRecord extends SoftDeletedEntity {

    @ColumnDef(comment = "用户ID")
    private Long userId;

    @ColumnDef(comment = "关联记录ID")
    private Long relatedId;

    @ColumnDef(comment = "记录类型")
    private AccountRecordTypeEnum type;

    @ColumnDef(comment = "资金方向")
    private AccountDirectionEnum direction;

    @ColumnDef(comment = "变动金额（分）")
    private Integer amount;

    @ColumnDef(comment = "变动手续费")
    private Integer fee;

    @ColumnDef(comment = "变动前余额（分）")
    private Integer beforeBalance;

    @ColumnDef(comment = "变动后余额（分）")
    private Integer afterBalance;

    @ColumnDef(comment = "备注")
    private String remark;
}
