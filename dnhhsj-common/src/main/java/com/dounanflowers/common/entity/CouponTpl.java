package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.CouponSceneEnum;
import com.dounanflowers.common.enums.CouponTplEndTypeEnum;
import com.dounanflowers.common.enums.CouponTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "coupon_tpl", comment = "优惠券模板表")
public class CouponTpl extends SoftDeletedEntity {

    @ColumnDef(comment = "优惠券标题", nullable = false)
    private String title;

    @ColumnDef(comment = "优惠券编码")
    private String code;

    @ColumnDef(comment = "领取标题", nullable = false)
    private String receiveTitle;

    @ColumnDef(comment = "优惠券类型", nullable = false)
    private CouponTypeEnum type;

    @ColumnDef(comment = "使用场景")
    private CouponSceneEnum scene;

    @ColumnDef(comment = "过期类型", nullable = false)
    private CouponTplEndTypeEnum endType;

    @ColumnDef(comment = "绝对过期时间")
    private LocalDateTime absoluteEndAt;

    @ColumnDef(comment = "相对过期时间(分钟)")
    private Integer relativeEndAt;

    @ColumnDef(comment = "面值", nullable = false)
    private Integer parValue;

    @ColumnDef(comment = "发布数量", defaultValue = "0", nullable = false)
    private Integer publishCount;

    @ColumnDef(comment = "已领取数量", defaultValue = "0", nullable = false)
    private Integer receivedCount;

    @ColumnDef(comment = "已使用数量", defaultValue = "0", nullable = false)
    private Integer usedCount;

    @RelationOneToMany(
            selfField = "id",
            targetField = "couponTplId",
            targetTable = "coupon_tpl_user_r",
            valueField = "userId"
    )
    @ColumnDef(ignore = true)
    private List<Long> pusherIds;

    @ColumnDef(comment = "状态", defaultValue = "1", nullable = false)
    private IsEnum status;

    @ColumnDef(comment = "游客可见", defaultValue = "0", nullable = false)
    private IsEnum clientPublic;

    @ColumnDef(comment = "游客可领取个数", defaultValue = "0", nullable = false)
    private Integer clientReceiveLimit;

    @ColumnDef(comment = "可叠加", defaultValue = "0", nullable = false)
    private IsEnum isStackable;

    @ColumnDef(comment = "绑定的月卡")
    private String boundVehicleMonthlyCardIds;

}
