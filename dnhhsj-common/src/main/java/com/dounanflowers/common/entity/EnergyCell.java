package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToOne;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "energy_cell", comment = "水电费店铺")
@Accessors(chain = true)
public class EnergyCell extends SoftDeletedEntity {

    @ColumnDef(comment = "管理公司", nullable = false)
    private String companyName;

    @ColumnDef(comment = "管理公司编码", nullable = false)
    private String companyCode;

    @ColumnDef(comment = "店铺名称", nullable = false)
    private String cellName;

    @ColumnDef(comment = "店铺编号", nullable = false)
    private String cellNo;

    @ColumnDef(comment = "店铺ID", nullable = false)
    private String cellId;

    @ColumnDef(comment = "店主姓名", nullable = false)
    private String masterName;

    @ColumnDef(comment = "店主手机号", nullable = false)
    private String masterMobile;

    @ColumnDef(comment = "店主身份证号", nullable = false)
    private String masterIdCardNo;

    @ColumnDef(comment = "店主所在层")
    private String layerName;

    @ColumnDef(comment = "店铺层门牌号")
    private String layerNo;

    @ColumnDef(comment = "用户Id", nullable = false)
    private Long adminUserId;

    @ColumnDef(comment = "是否订阅", nullable = false, defaultValue = "0")
    private IsEnum subscribe;

    @ColumnDef(comment = "备注")
    private String remark;

    @ColumnDef(comment = "物业店铺")
    private Long propertyCellId;

    @RelationOneToOne(
            selfField = "propertyCellId",
            targetField = "id",
            targetTable = "property_cell"
    )
    @ColumnDef(ignore = true)
    private PropertyCell propertyCellObj;

    @ColumnDef(comment = "是否验证", nullable = false, defaultValue = "0")
    private IsEnum isVerify;

}
