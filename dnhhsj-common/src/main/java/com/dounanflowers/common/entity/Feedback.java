package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.ChannelEnum;
import com.dounanflowers.common.enums.FeedbackTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "feedback", comment = "反馈表")
@Accessors(chain = true)
public class Feedback extends SoftDeletedEntity {

    @ColumnDef(comment = "反馈内容", type = "text", nullable = false)
    private String content;

    @ColumnDef(comment = "用户ID")
    private Long userId;

    @ColumnDef(comment = "反馈类型", nullable = false)
    private FeedbackTypeEnum type;

    @ColumnDef(comment = "联系电话")
    private String mobile;

    @ColumnDef(comment = "反馈渠道", nullable = false)
    private ChannelEnum channel;

    @ColumnDef(comment = "处理时间")
    private LocalDateTime handledAt;

    @ColumnDef(comment = "备注", type = "text")
    private String remark;

    @RelationOneToMany(
            selfField = "id",
            targetField = "outerId",
            targetTable = "file_info",
            valueField = "url"
    )
    @ColumnDef(ignore = true)
    private List<String> images;

}
