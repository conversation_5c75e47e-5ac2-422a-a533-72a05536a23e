package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "dict_option", comment = "字典选项表")
public class DictOption extends BaseEntity {

    @ColumnDef(comment = "字典ID", nullable = false)
    private Long dictId;

    @ColumnDef(comment = "父级ID")
    private Long parentId;

    @ColumnDef(comment = "选项键", nullable = false)
    private String key;

    @ColumnDef(comment = "选项标签", nullable = false)
    private String label;

    @ColumnDef(comment = "选项值", nullable = false)
    private String value;

    @ColumnDef(comment = "状态", defaultValue = "1", nullable = false)
    private IsEnum status;

}
