package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "article", comment = "文章")
public class Article extends SoftDeletedEntity {

    @ColumnDef(nullable = false, comment = "标题")
    private String title;

    @ColumnDef(comment = "简介")
    private String intro;

    @ColumnDef(comment = "作者")
    private String author;

    @RelationOneToMany(
            selfField = "id",
            targetField = "articleId",
            targetTable = "article_category_r",
            valueField = "category"
    )
    @ColumnDef(ignore = true)
    private List<String> categories;

    @RelationOneToMany(
            selfField = "id",
            targetField = "articleId",
            targetTable = "article_tag_r",
            valueField = "tag"
    )
    @ColumnDef(ignore = true)
    private List<String> tags;

    @ColumnDef(comment = "横向图片")
    private String cover;

    @ColumnDef(comment = "纵向图片")
    private String coverV;

    @ColumnDef(comment = "跳转H5链接")
    private String webViewUrl;

    @ColumnDef(comment = "内容", type = "TEXT")
    private String content;

    @ColumnDef(nullable = false, defaultValue = "0", comment = "浏览数")
    private Integer viewCount;

    @ColumnDef(nullable = false, defaultValue = "0", comment = "收藏数")
    private Integer favouriteCount;

    @ColumnDef(nullable = false, defaultValue = "0", comment = "点赞数")
    private Integer likeCount;

    @ColumnDef(nullable = false, defaultValue = "0", comment = "0:否 1:是")
    private IsEnum isRecommend;

    @ColumnDef(nullable = false, defaultValue = "0", comment = "0:否 1:是")
    private IsEnum status;

    @RelationOneToMany(
            selfField = "id",
            targetField = "outerId",
            targetTable = "file_info",
            extraCondition = "field = 'images'",
            valueField = "url"
    )
    @ColumnDef(ignore = true)
    private List<String> images; // 图片

    @ColumnDef(comment = "小程序内部跳转链接")
    private String pages;

}