package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.VehiclePlateChangeStatusEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "vehicle_plate_change", comment = "机动车车牌变更表")
public class VehiclePlateChange extends SoftDeletedEntity {
    @ColumnDef(comment = "用户ID", nullable = false)
    private Long userId;

    @ColumnDef(comment = "机动车ID", nullable = false)
    private Long vehicleId;

    @ColumnDef(comment = "变更前车牌", nullable = false)
    private String oldPlate;

    @ColumnDef(comment = "变更后车牌", nullable = false)
    private String newPlate;

    @ColumnDef(comment = "变更时间")
    private LocalDateTime modifiedAt;

    @ColumnDef(comment = "变更状态")
    private VehiclePlateChangeStatusEnum changeStatus;

    @ColumnDef(comment = "账单号")
    private Long billId;

    @ColumnDef(comment = "拒绝理由")
    private String rejectReason;

    @ColumnDef(comment = "修改时间")
    private LocalDateTime changedAt;
}
