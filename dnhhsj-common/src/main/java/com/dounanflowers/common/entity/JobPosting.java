package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.EmploymentTypeEnum;
import com.dounanflowers.common.enums.JobPostingStatusEnum;
import com.dounanflowers.common.enums.PriceTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "job_posting", comment = "用工发布")
public class JobPosting extends SoftDeletedEntity {

    @ColumnDef(nullable = false, comment = "用户ID")
    private Long userId;

    @ColumnDef(nullable = false, comment = "岗位名称")
    private String position;

    @ColumnDef(nullable = false, comment = "待遇")
    private Integer salary;

    @ColumnDef(comment = "待遇")
    private Integer salaryMax;

    @ColumnDef(nullable = false, comment = "待遇类型")
    private PriceTypeEnum salaryType;

    @RelationOneToMany(selfField = "id", targetField = "jobPostingId", targetTable = "job_posting_tag_r", valueField = "tag")
    @ColumnDef(ignore = true)
    private List<String> tags;

    @ColumnDef(nullable = false, comment = "招聘方")
    private String employer;

    @ColumnDef(nullable = false, comment = "联系方式")
    private String contact;

    @ColumnDef(nullable = false, comment = "审核状态")
    private JobPostingStatusEnum status;

    private Long checkUserId;

    @ColumnDef(comment = "审核备注")
    private String remark;

    @ColumnDef(nullable = false, comment = "用工类型")
    private EmploymentTypeEnum employmentType;

    @ColumnDef(comment = "职位描述")
    private String description;

    @ColumnDef(comment = "是否显示")
    private IsEnum show;

    @ColumnDef(comment = "推荐值", defaultValue = "0", nullable = false)
    private Integer recommend;

    @ColumnDef(comment = "是否官方", defaultValue = "0", nullable = false)
    private IsEnum isOfficial;

}
