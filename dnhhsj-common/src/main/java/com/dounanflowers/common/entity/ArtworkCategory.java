package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "artwork_category_r", comment = "作品分类关联表")
public class ArtworkCategory extends BaseEntity {

    @ColumnDef(comment = "作品ID", nullable = false)
    private Long artworkId;

    @ColumnDef(comment = "分类名称", nullable = false)
    private String category;

}
