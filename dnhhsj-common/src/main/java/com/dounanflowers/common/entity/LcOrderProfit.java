package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 停车场订单优惠记录
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table("lc_order_profit")
public class LcOrderProfit extends BaseEntity {
    @ColumnDef(comment = "停车场")
    private Long lcOrderId;

    @ColumnDef(comment = "优惠码")
    private String profitCode;

    @ColumnDef(comment = "优惠时间")
    private String profitTime;

    @ColumnDef(comment = "商户名称")
    private String shopName;

    @ColumnDef(comment = "优惠金额面值")
    private String profitCharge;

    @ColumnDef(comment = "生效金额")
    private String profitChargeValue;

    @ColumnDef(comment = "优惠下发时间")
    private LocalDateTime getTime;

    @ColumnDef(comment = "备注")
    private String memo;

}
