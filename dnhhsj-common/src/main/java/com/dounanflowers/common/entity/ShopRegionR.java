package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "shop_region_r", comment = "商铺区域关联表")
public class ShopRegionR extends BaseEntity {

    @ColumnDef(comment = "商铺ID", nullable = false)
    private Long shopId;

    @ColumnDef(comment = "所属区域", nullable = false)
    private String region;

}
