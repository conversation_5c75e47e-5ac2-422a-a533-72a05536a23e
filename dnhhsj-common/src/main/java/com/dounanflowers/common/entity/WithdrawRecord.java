package com.dounanflowers.common.entity;

import java.time.LocalDateTime;
import java.util.List;

import com.dounanflowers.common.enums.WithdrawStatusEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "withdraw_record", comment = "提现记录表")
public class WithdrawRecord extends SoftDeletedEntity {

    @ColumnDef(comment = "用户ID")
    private Long userId;

    @ColumnDef(comment = "提现金额")
    private Integer amount;

    @ColumnDef(comment = "手续费")
    private Integer fee;

    @ColumnDef(comment = "账户姓名")
    private String name;

    @ColumnDef(comment = "开户行信息")
    private String bank;

    @ColumnDef(comment = "支行信息")
    private String branch;

    @ColumnDef(comment = "账号")
    private String number;

    @ColumnDef(comment = "状态")
    private WithdrawStatusEnum status;

    @ColumnDef(comment = "审核时间")
    private LocalDateTime checkedAt;

    @ColumnDef(comment = "备注")
    private String remark;

    @ColumnDef(comment = "审核人ID")
    private Long checkAdminUserId;

    @RelationOneToMany(
            selfField = "id",
            targetField = "outerId",
            targetTable = "file_info",
            extraCondition = "field = 'files'",
            valueField = "url"
    )
    @ColumnDef(ignore = true)
    private List<String> files;

}
