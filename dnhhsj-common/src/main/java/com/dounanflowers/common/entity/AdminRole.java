package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.PermissionTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "admin_role", comment = "管理员角色表")
@Accessors(chain = true)
public class AdminRole extends SoftDeletedEntity {

    @ColumnDef(comment = "角色名称", nullable = false)
    private String name;

    @ColumnDef(comment = "角色编码", nullable = false)
    private String code;

    private String home;

    @ColumnDef(comment = "角色描述")
    private String description;

    @ColumnDef(comment = "权限类型")
    private PermissionTypeEnum type;

}
