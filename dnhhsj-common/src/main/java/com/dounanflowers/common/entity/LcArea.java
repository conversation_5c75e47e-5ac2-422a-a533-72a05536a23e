package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 停车场区域
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table("lc_area")
public class LcArea extends SoftDeletedEntity {

    @ColumnDef(comment = "场库")
    private Long lcParkId;

    @ColumnDef(comment = "场库编号")
    private String parkNumber;

    @ColumnDef(comment = "区域Id")
    private String areaId;

    @ColumnDef(comment = "区域名称")
    private String areaName;


    @ColumnDef(comment = "区域车位数")
    private Integer spaceCount;

    @ColumnDef(comment = "区域空位数")
    private Integer lastSpaceCount;

    @ColumnDef(comment = "场库可预约数")
    private Integer bookSpaceCount;

    @ColumnDef(comment = "场库在场预约数")
    private Integer bookInParkCount;
}
