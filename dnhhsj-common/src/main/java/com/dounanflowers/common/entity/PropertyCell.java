package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "property_cell", comment = "物业费店铺")
@Accessors(chain = true)
public class PropertyCell extends SoftDeletedEntity {

    @ColumnDef(comment = "管理公司", nullable = false)
    private String companyName;

    @ColumnDef(comment = "管理公司编码", nullable = false)
    private String companyCode;

    @ColumnDef(comment = "店铺名称", nullable = false)
    private String cellName;

    @ColumnDef(comment = "店铺编号", nullable = false)
    private String cellNo;

    @ColumnDef(comment = "店铺ID", nullable = false)
    private String cellId;

    @ColumnDef(comment = "店主姓名", nullable = false)
    private String masterName;

    @ColumnDef(comment = "店主手机号", nullable = false)
    private String masterMobile;

    @ColumnDef(comment = "店主身份证号", nullable = false)
    private String masterIdCardNo;

    @ColumnDef(comment = "店主所在层")
    private String layerName;

    @ColumnDef(comment = "店铺层门牌号")       
    private String layerNo;

    @ColumnDef(comment = "营业执照")
    private String businessLicense;

    @ColumnDef(comment = "折叠起来", defaultValue = "0", nullable = false)
    private IsEnum hide;

    @ColumnDef(comment = "是否待更新", defaultValue = "0", nullable = false)
    private IsEnum waitUpdate;

    @ColumnDef(comment = "Ocr结果")
    private String ocrBusinessLicenseResult;
}
