package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "job_posting_tag_r", comment = "用工发布标签关联表")
public class JobPostingTagR extends SoftDeletedEntity {

    @ColumnDef(nullable = false, comment = "用工发布ID")
    private Long jobPostingId;

    @ColumnDef(nullable = false, comment = "标签")
    private String tag;
}
