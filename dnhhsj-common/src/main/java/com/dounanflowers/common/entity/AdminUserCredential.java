package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "admin_user_credential", comment = "管理员用户实名表")
public class AdminUserCredential extends SoftDeletedEntity {

    @ColumnDef(comment = "用户ID", nullable = false)
    private Long userId;

    @ColumnDef(comment = "真实姓名")
    private String realname;

    @ColumnDef(comment = "身份证号")
    private String idCardNum;

    @ColumnDef(comment = "身份证正面照片")
    private String idCardUrl1;

    @ColumnDef(comment = "身份证反面照片")
    private String idCardUrl2;

}
