package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "artwork", comment = "作品表")
public class Artwork extends SoftDeletedEntity {

    @ColumnDef(comment = "标题", nullable = false)
    private String title;

    @ColumnDef(comment = "艺术家")
    private String artist;

    @ColumnDef(comment = "作品内容", type = "TEXT")
    private String content;

    @ColumnDef(comment = "作品尺寸")
    private String size;

    @RelationOneToMany(
            selfField = "id",
            targetField = "artworkId",
            targetTable = "artwork_category_r",
            valueField = "category"
    )
    @ColumnDef(ignore = true)
    private List<String> categories;

    @ColumnDef(comment = "浏览次数", defaultValue = "0")
    private Integer viewCount;

    @ColumnDef(comment = "状态", nullable = false)
    private IsEnum status;

    @ColumnDef(comment = "价格")
    private Integer price;

}
