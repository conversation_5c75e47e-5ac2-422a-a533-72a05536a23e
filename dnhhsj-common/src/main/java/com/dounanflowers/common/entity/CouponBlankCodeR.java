package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "coupon_blank_code_r", comment = "优惠券空白码关联表")
public class CouponBlankCodeR extends BaseEntity {

    @ColumnDef(comment = "优惠券空白ID", nullable = false)
    private Long couponBlankId;

    @ColumnDef(comment = "优惠券编码", nullable = false)
    private String code;

}
