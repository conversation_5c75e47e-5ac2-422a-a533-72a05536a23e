package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.InvoiceHeaderTypeEnum;
import com.dounanflowers.common.enums.InvoiceStatusEnum;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "invoice", comment = "发票申请")
public class Invoice extends SoftDeletedEntity {

    @ColumnDef(comment = "抬头类型", nullable = false)
    private InvoiceHeaderTypeEnum headerType;

    @ColumnDef(comment = "抬头名称", nullable = false)
    private String headerName;

    @ColumnDef(comment = "税号")
    private String taxNumber;

    @ColumnDef(comment = "发票内容", nullable = false)
    private String content;

    @ColumnDef(comment = "发票金额(分)", nullable = false)
    private Integer amountCent;

    @ColumnDef(comment = "开票状态", nullable = false)
    private InvoiceStatusEnum status;

    @ColumnDef(comment = "拒绝理由")
    private String rejectReason;

    @ColumnDef(comment = "处理时间")
    private LocalDateTime processedAt;

    @ColumnDef(comment = "处理人")
    private Long processorId;

    @ColumnDef(comment = "备注")
    private String remark;

    @ColumnDef(comment = "下载地址")
    private String downloadUrl;

    @ColumnDef(comment = "用户类型")
    private UserTypeEnum userType;

    @ColumnDef(comment = "用户ID")
    private Long userId;

    @ColumnDef(comment = "账单ID列表")
    private String billIds;

    @ColumnDef(comment = "订单数", defaultValue = "0")
    private Integer billCount;
}

