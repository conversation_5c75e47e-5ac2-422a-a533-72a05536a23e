package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.VehicleCheckStatusEnum;
import com.dounanflowers.common.enums.VehicleRefundStatusEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "vehicle", comment = "机动车信息表")
public class Vehicle extends SoftDeletedEntity {

    @ColumnDef(comment = "机动车编号", nullable = false)
    private String number;

    @ColumnDef(comment = "所属用户ID", nullable = false)
    private Long userId;

    @RelationOneToMany(
            selfField = "id",
            targetField = "outerId",
            targetTable = "file_info",
            valueField = "url"
    )
    @ColumnDef(ignore = true)
    private List<String> images;

    @ColumnDef(comment = "审核状态", nullable = false)
    private VehicleCheckStatusEnum checkStatus;

    @ColumnDef(comment = "月卡ID")
    private Long monthlyCardId;

    @ColumnDef(comment = "月卡开始时间")
    private LocalDateTime monthlyCardStartAt;

    @ColumnDef(comment = "月卡到期时间")
    private LocalDateTime monthlyCardExpireAt;

    private Long couponId;

    @ColumnDef(comment = "隐藏状态", nullable = false, defaultValue = "1")
    private IsEnum show;

    @ColumnDef(comment = "退款状态", nullable = false, defaultValue = "0")
    private VehicleRefundStatusEnum refundStatus;

    @ColumnDef(comment = "退款处理时间")
    private LocalDateTime refundProcessedAt;

    @ColumnDef(comment = "退款处理人")
    private Long refundProcessorId;

    @ColumnDef(comment = "退款金额")
    private Integer refundMoneyCent;

    @ColumnDef(comment = "退款拒绝理由")
    private String refundRejectReason;

    @ColumnDef(comment = "退款备注")
    private String refundRemark;

    @ColumnDef(comment = "退款图片")
    private String refundImages;

    @ColumnDef(comment = "之前的蓝卡白名单")
    private String beforeLcWhite;

    @ColumnDef(comment = "审核拒绝理由")
    private String checkRejectReason;

    @ColumnDef(comment = "审核备注")
    private String checkRemark;

    @ColumnDef(comment = "根ID")
    private Long rootId;


}
