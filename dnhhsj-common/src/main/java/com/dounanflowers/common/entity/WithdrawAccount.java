package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "withdraw_account", comment = "提现账户表")
public class WithdrawAccount extends SoftDeletedEntity {

    @ColumnDef(comment = "用户ID")
    private Long userId;

    @ColumnDef(comment = "总收益")
    private Integer totalIncome;

    @ColumnDef(comment = "总提现金额")
    private Integer totalWithdraw;

    @ColumnDef(comment = "总手续费")
    private Integer totalFee;

    @ColumnDef(comment = "总订单数")
    private Integer totalOrders;

    @ColumnDef(comment = "可提现金额")
    private Integer availableAmount;

    @ColumnDef(comment = "冻结金额")
    private Integer frozenAmount;
}
