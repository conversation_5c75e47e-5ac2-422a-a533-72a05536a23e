package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "article_tag_r", comment = "文章标签关联表")
public class ArticleTag extends BaseEntity {

    @ColumnDef(comment = "文章ID", nullable = false)
    private Long articleId;

    @ColumnDef(comment = "标签名称", nullable = false)
    private String tag;

}
