package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.ProductTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("store")
@Accessors(chain = true)
public class Store extends SoftDeletedEntity {

    @ColumnDef(comment = "商铺编号", nullable = false)
    private String cellNo;

    @ColumnDef(comment = "商铺Id")
    private Long shopId;

    @ColumnDef(comment = "店铺名称", nullable = false)
    private String name;

    @ColumnDef(comment = "商品类型")
    private ProductTypeEnum type;

    @ColumnDef(comment = "店铺状态", nullable = false)
    private IsEnum status;

    @ColumnDef(comment = "店铺地址")
    private String address;

    @ColumnDef(comment = "店铺坐标")
    private String location;

    @ColumnDef(comment = "管理员用户ID")
    private Long adminUserId;

    @ColumnDef(comment = "联系人")
    private String contact;

    @ColumnDef(comment = "联系电话")
    private String mobile;

    @ColumnDef(comment = "评分")
    private Integer rate;

    @ColumnDef(comment = "评价数")
    private Integer reviewCount;

    @ColumnDef(comment = "开店时间")
    private String openAt;

    @ColumnDef(comment = "最低价格")
    private Integer minPrice;

    @ColumnDef(comment = "闭店时间")
    private String closeAt;

    @ColumnDef(comment = "已售")
    private Integer soldCount;

    @RelationOneToMany(
            selfField = "id",
            targetField = "outerId",
            targetTable = "file_info",
            valueField = "url"
    )
    @ColumnDef(ignore = true)
    private List<String> images;

    @RelationOneToMany(
            selfField = "id",
            targetField = "storeId",
            targetTable = "store_tag_r",
            valueField = "tag"
    )
    @ColumnDef(ignore = true)
    private List<String> tags;

    @RelationOneToMany(
            selfField = "id",
            targetField = "storeId",
            targetTable = "store_employee_r"
    )
    @ColumnDef(ignore = true)
    private List<StoreEmployeeR> employees;

    @ColumnDef(comment = "是否推荐")
    private IsEnum recommend;

    @ColumnDef(comment = "手续费率 ‰", defaultValue = "0")
    private Integer feeRate;

}
