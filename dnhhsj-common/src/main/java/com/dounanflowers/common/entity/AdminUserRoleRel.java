package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "admin_user_role_r", comment = "管理员用户角色关联表")
public class AdminUserRoleRel extends BaseEntity {

    @ColumnDef(comment = "角色编码", nullable = false)
    private String role;

    @ColumnDef(comment = "用户ID", nullable = false)
    private Long userId;

}
