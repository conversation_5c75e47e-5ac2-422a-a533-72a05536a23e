package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.FavouriteTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "favourite", comment = "收藏表")
@Accessors(chain = true)
public class Favourite extends SoftDeletedEntity {

    @ColumnDef(comment = "收藏类型", nullable = false)
    private FavouriteTypeEnum type;

    @ColumnDef(comment = "收藏标题", nullable = false)
    private String title;

    @ColumnDef(comment = "用户ID", nullable = false)
    private Long userId;

    @ColumnDef(comment = "用户模型", nullable = false)
    private String userModel;

    @ColumnDef(comment = "实体ID", nullable = false)
    private Long entityId;

    @ColumnDef(comment = "实体模型", nullable = false)
    private String entityModel;

}
