package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.RefundStatusEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("product_refund_apply")
@Accessors(chain = true)
public class ProductRefundApply extends SoftDeletedEntity {

    @ColumnDef(comment = "订单ID", nullable = false)
    private Long orderId;

    @ColumnDef(comment = "订单项ID")
    private Long orderItemId;


    @ColumnDef(comment = "用户ID", nullable = false)
    private Long userId;

    @ColumnDef(comment = "商品ID")
    private Long productId;

    @ColumnDef(comment = "店铺ID", nullable = false)
    private Long storeId;

    @ColumnDef(comment = "退款金额(分)", nullable = false)
    private Integer refundAmount;

    @ColumnDef(comment = "申请原因")
    private String reason;

    @ColumnDef(comment = "说明")
    private String description;

    @ColumnDef(comment = "退款状态", nullable = false)
    private RefundStatusEnum status;

    @ColumnDef(comment = "处理时间")
    private LocalDateTime handleTime;

    @ColumnDef(comment = "处理说明")
    private String handleNote;

    @ColumnDef(comment = "是否已退款")
    private IsEnum refunded;

    @RelationOneToMany(
            selfField = "id",
            targetField = "outerId",
            targetTable = "file_info",
            valueField = "url"
    )
    @ColumnDef(ignore = true)
    private List<String> images;
}
