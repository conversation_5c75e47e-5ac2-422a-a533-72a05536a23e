package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.*;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("shop")
@Accessors(chain = true)
public class Shop extends SoftDeletedEntity {

    @ColumnDef(comment = "商铺号")
    private String cellNo;

    @ColumnDef(comment = "装修")
    private String decoration;

    @ColumnDef(comment = "楼层")
    private Integer floor;

    @RelationOneToMany(
            selfField = "id",
            targetField = "outerId",
            targetTable = "file_info",
            extraCondition = "field = 'houseType'",
            valueField = "url"
    )
    @ColumnDef(ignore = true)
    private List<String> houseType; // 户型

    @RelationOneToMany(
            selfField = "id",
            targetField = "outerId",
            targetTable = "file_info",
            extraCondition = "field = 'images'",
            valueField = "url"
    )
    @ColumnDef(ignore = true)
    private List<String> images; // 图片

    @ColumnDef(comment = "朝向 1-东；2-南；3-西；4-北；5-东南；6-西南；7-西北；8-东北")
    private ShopOrientationEnum orientation;

    @ColumnDef(comment = "付款方式")
    private String payment;

    @RelationOneToMany(
            selfField = "id",
            targetField = "shopId",
            targetTable = "shop_tag_r",
            valueField = "tag"
    )
    @ColumnDef(ignore = true)
    private List<String> tags;

    @ColumnDef(comment = "标题")
    private String title;

    @RelationOneToMany(
            selfField = "id",
            targetField = "outerId",
            targetTable = "file_info",
            extraCondition = "field = 'videos'",
            valueField = "url"
    )
    @ColumnDef(ignore = true)
    private List<String> videos;

    @ColumnDef(comment = "单元ID")
    private Long cellId;

    @ColumnDef(comment = "单元名称")
    private String cellName;

    @ColumnDef(comment = "房产性质 1-空置; 2-已租; 3-业主自营; 4-自用; 5-不租; 6-安置办公; 7-其它1; 8-其它2; 9-其它3")
    private ShopCellPropertyEnum cellProperty;

    @ColumnDef(comment = "套内面积")
    private Double cellArea;

    @ColumnDef(comment = "建筑面积")
    private Double buildingArea;

    @ColumnDef(comment = "单元状态")
    private ShopCellStatusEnum cellStatus;

    @ColumnDef(comment = "合同开始日期")
    private LocalDateTime contractStartDate;

    @ColumnDef(comment = "合同结束日期")
    private LocalDateTime contractEndDate;

    @ColumnDef(comment = "合同编号")
    private String contractNo;

    @RelationOneToMany(
            selfField = "id",
            targetField = "shopId",
            targetTable = "shop_business_type_r",
            valueField = "businessType"
    )
    @ColumnDef(ignore = true)
    private List<String> businessType; // 经营类型

    @ColumnDef(comment = "单元备注")
    private String cellRemark;

    @ColumnDef(comment = "附件名称")
    private String attachmentNames;

    @ColumnDef(comment = "附件路径")
    private String attachmentPaths;

    @ColumnDef(comment = "费用名称")
    private String costName;

    @ColumnDef(comment = "单价")
    private Double unitPrice;

    @ColumnDef(comment = "付款方式 1-押三付三；2-押三付六；3-1年付；4-3年付；5-面议")
    private ShopBillingCycleEnum billingCycle;

    @ColumnDef(comment = "费用金额")
    private Double chargeAmount;

    @ColumnDef(comment = "租户ID")
    private String terantId;

    @ColumnDef(comment = "租户名称")
    private String tenantName;

    @ColumnDef(comment = "租户电话")
    private String tenantPhone;

    @ColumnDef(comment = "联系人")
    private String contactPerson;

    @ColumnDef(comment = "联系人电话")
    private String contactPhone;

    @ColumnDef(comment = "租户类型 1-个人；2-公司")
    private ShopTenantTypeEnum tenantType;

    @ColumnDef(comment = "租户ID")
    private Long tenantId;

    @ColumnDef(comment = "租户身份证号")
    private String tenantIdCard;

    @ColumnDef(comment = "租户地址")
    private String tenantAddress;

    @ColumnDef(comment = "营业执照号")
    private String businessLicense;

    @ColumnDef(comment = "租户备注")
    private String tenantRemark;

    @RelationOneToMany(
            selfField = "id",
            targetField = "shopId",
            targetTable = "shop_region_r",
            valueField = "region"
    )
    @ColumnDef(ignore = true)
    private List<String> region; // 区域

    @ColumnDef(comment = "是否上架")
    private IsEnum onShelf;

    @ColumnDef(comment = "上架时间")
    private LocalDateTime onShelfAt;


    @ColumnDef(comment = "浏览量", nullable = false, defaultValue = "0")
    private Integer viewCount;

    @ColumnDef(comment = "收藏量", nullable = false, defaultValue = "0")
    private Integer favouriteCount;

    @ColumnDef(comment = "通话量，经纪人被拨打次数", nullable = false, defaultValue = "0")
    private Integer brokerCalledCount;

    @ColumnDef(comment = "推荐值", nullable = false, defaultValue = "0")
    private Integer recommend;

    @ColumnDef(comment = "排名")
    private Integer overallRank;

    @ColumnDef(comment = "排名分数")
    private Integer overallRankScore;

    @ColumnDef(comment = "编辑时间")
    private LocalDateTime editedAt;

    @ColumnDef(comment = "分享图片")
    private String shareImageUrl;

    @ColumnDef(comment = "分享图片版本")
    private String shareImageVersion;
}
