package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 停车场通道
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table("lc_passage_way")
public class LcPassageWay extends SoftDeletedEntity {
    @ColumnDef(comment = "场库")
    private Long lcParkId;

    @ColumnDef(comment = "通道Id")
    private String passageWayId;

    @ColumnDef(comment = "通道名称")
    private String passageWayName;

    @ColumnDef(comment = "设备号")
    private String deviceId;

    @ColumnDef(comment = "匹配级别id")
    private String matchClassId;

    @ColumnDef(comment = "开闸类型")
    private String openBarriorTypeId;

    @ColumnDef(comment = "源区域Id")
    private String sourceAreaId;

    @ColumnDef(comment = "目标区域Id")
    private String targetAreaId;

    @ColumnDef(comment = "停车场编号")
    private String parkNumber;

}
