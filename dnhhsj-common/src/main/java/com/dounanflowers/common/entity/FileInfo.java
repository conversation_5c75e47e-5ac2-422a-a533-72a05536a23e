package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.FileTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "file_info", comment = "文件信息表")
public class FileInfo extends SoftDeletedEntity {

    @ColumnDef(comment = "文件类型")
    private FileTypeEnum type;

    @ColumnDef(comment = "文件名称")
    private String name;

    @ColumnDef(comment = "文件对象", type = "text")
    private String object;

    @ColumnDef(comment = "文件URL", type = "text", nullable = false)
    private String url;

    @ColumnDef(comment = "文件大小(字节)")
    private Long size;

    @ColumnDef(comment = "关联字段名称")
    private String field;

    @ColumnDef(comment = "文件描述", type = "text")
    private String description;

    @ColumnDef(comment = "外部ID")
    private Long outerId;

    @ColumnDef(comment = "序号", defaultValue = "0")
    private Integer seq;

    @ColumnDef(comment = "图片宽度(像素)")
    private Long width;

    @ColumnDef(comment = "图片高度(像素)")
    private Long height;

    @ColumnDef(comment = "视频时长(秒)")
    private Long duration;

}
