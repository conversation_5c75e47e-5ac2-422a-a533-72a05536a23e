package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.BillCloseReasonEnum;
import com.dounanflowers.common.enums.BillPayStatusEnum;
import com.dounanflowers.common.enums.BillTypeEnum;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.RelationManyToOne;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "bill", comment = "账单表")
public class Bill extends SoftDeletedEntity {

    @ColumnDef(comment = "商品Id", nullable = false)
    private Long entityId;

    @ColumnDef(comment = "商品模型")
    private String entityModel;

    @ColumnDef(comment = "商品类型", nullable = false)
    private BillTypeEnum type;

    @ColumnDef(comment = "用户Id", nullable = false)
    private Long userId;

    @ColumnDef(comment = "用户类型", nullable = false)
    private UserTypeEnum userType;

    @ColumnDef(comment = "用户模型")
    private String userModel;

    @ColumnDef(comment = "原价金额")
    private Integer originalMoneyCent;

    @ColumnDef(comment = "优惠券抵扣金额")
    private Integer couponDiscountCent;

    @ColumnDef(comment = "积分抵扣金额")
    private Integer pointDiscountCent;

    @ColumnDef(comment = "订单金额")
    private Integer orderMoneyCent;

    @ColumnDef(comment = "实付金额")
    private Integer paidMoneyCent;

    @ColumnDef(comment = "已退款金额", defaultValue = "0")
    private Integer refundedMoneyCent;

    @ColumnDef(comment = "付款时间")
    private LocalDateTime paidAt;

    @ColumnDef(comment = "支付状态", nullable = false)
    private BillPayStatusEnum payStatus;

    @ColumnDef(comment = "通联支付流水号")
    private String payTrxid;

    @ColumnDef(comment = "支付手续费")
    private Integer payFeeCent;

    @RelationOneToMany(
            selfField = "id",
            targetField = "billId",
            targetTable = "bill_refund"
    )
    @ColumnDef(ignore = true)
    private List<BillRefund> refundList;

    @ColumnDef(comment = "备注")
    private String remark;

    @ColumnDef(comment = "关闭时间")
    private LocalDateTime closedAt;

    @ColumnDef(comment = "关闭原因")
    private BillCloseReasonEnum closedReason;

    @ColumnDef(comment = "其他数据")
    private String otherData;

    @RelationOneToMany(
            selfField = "id",
            targetField = "outerId",
            targetTable = "file_info",
            valueField = "url"
    )
    @ColumnDef(ignore = true)
    private List<String> images;

    @ColumnDef(comment = "发票ID")
    private Long invoiceId;

    @RelationManyToOne(
            selfField = "invoiceId",
            targetField = "id",
            targetTable = "invoice"
    )
    @ColumnDef(ignore = true)
    private Invoice invoiceObj;

}
