package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("shopping_cart")
@Accessors(chain = true)
public class ShoppingCart extends BaseEntity {

    @ColumnDef(comment = "用户ID", nullable = false)
    private Long userId;

    @ColumnDef(comment = "商品ID", nullable = false)
    private Long productId;

    @ColumnDef(comment = "商铺ID", nullable = false)
    private Long storeId;

    @ColumnDef(comment = "数量", nullable = false)
    private Integer quantity;

    @ColumnDef(comment = "加入时间", nullable = false)
    private LocalDateTime addTime;

}
