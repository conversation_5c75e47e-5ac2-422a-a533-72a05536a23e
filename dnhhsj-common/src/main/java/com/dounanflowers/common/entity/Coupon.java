package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.CouponSceneEnum;
import com.dounanflowers.common.enums.CouponTypeEnum;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToOne;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "coupon", comment = "优惠券表")
public class Coupon extends SoftDeletedEntity {

    @ColumnDef(comment = "优惠券模板ID", nullable = false)
    private Long couponTplId;

    @ColumnDef(comment = "优惠券编码", nullable = false)
    private String code;

    @ColumnDef(comment = "优惠券标题", nullable = false)
    private String title;

    @ColumnDef(comment = "用户类型", defaultValue = "0", nullable = false)
    private UserTypeEnum userType;

    @ColumnDef(comment = "用户ID")
    private Long userId;

    @RelationOneToOne(
            selfField = "userId",
            targetField = "id",
            targetTable = "client_user"
    )
    @ColumnDef(ignore = true)
    private ClientUser user;

    @ColumnDef(comment = "优惠券类型", nullable = false)
    private CouponTypeEnum type;

    @ColumnDef(comment = "使用场景")
    private CouponSceneEnum scene;

    @ColumnDef(comment = "面值", nullable = false)
    private Integer parValue;

    @ColumnDef(comment = "有效期结束时间")
    private LocalDateTime endAt;

    @ColumnDef(comment = "使用时间")
    private LocalDateTime usedAt;

    @ColumnDef(comment = "来源用户ID")
    private Long sourceUserId;

    @ColumnDef(comment = "是否即时生成", defaultValue = "0", nullable = false)
    private IsEnum isInstantGen;

    @ColumnDef(comment = "订单ID")
    private Long orderId;

    @ColumnDef(comment = "订单模型")
    private String orderModel;

    @ColumnDef(comment = "可叠加", defaultValue = "0", nullable = false)
    private IsEnum isStackable;

}
