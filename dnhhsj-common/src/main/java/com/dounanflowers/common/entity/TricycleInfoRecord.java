package com.dounanflowers.common.entity;

import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "tricycle_info_record", comment = "三轮车信息表")
public class TricycleInfoRecord extends SoftDeletedEntity {

    private Long tricycleId;

    private Long userId;

    private String realname;

    private String mobile;

    private String images;

}
