package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "store_tag_r", comment = "商铺标签关联表")
public class StoreTagR extends BaseEntity {

    @ColumnDef(comment = "商铺ID", nullable = false)
    private Long storeId;

    @ColumnDef(comment = "标签名称", nullable = false)
    private String tag;

}
