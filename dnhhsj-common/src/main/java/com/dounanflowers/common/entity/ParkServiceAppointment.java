package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.AppointmentStatusEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "park_service_appointment", comment = "园区服务预约")
public class ParkServiceAppointment extends SoftDeletedEntity {

    @ColumnDef(nullable = false, comment = "服务ID")
    private Long serviceId;

    @ColumnDef(nullable = false, comment = "用户ID")
    private Long userId;

    @ColumnDef(nullable = false, comment = "手机号")
    private String phone;

    @ColumnDef(nullable = false, comment = "状态")
    private AppointmentStatusEnum status;

    private Long checkUserId;

    @ColumnDef(comment = "备注")
    private String remark;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

}
