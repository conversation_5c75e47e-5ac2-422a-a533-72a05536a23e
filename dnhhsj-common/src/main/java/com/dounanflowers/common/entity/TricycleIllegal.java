package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.TricycleIllegalStatsuEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "tricycle_illegal", comment = "三轮车违规记录表")
public class TricycleIllegal extends SoftDeletedEntity {

    @ColumnDef(comment = "违规用户ID", nullable = false)
    private Long userId;

    @ColumnDef(comment = "三轮车ID", nullable = false)
    private Long tricycleId;

    @ColumnDef(comment = "三轮车编号", nullable = false)
    private String tricycleNo;

    @ColumnDef(comment = "违规状态", nullable = false)
    private TricycleIllegalStatsuEnum status;

    @ColumnDef(comment = "押金记录ID", nullable = false)
    private Long depositId;

    @ColumnDef(comment = "违规规则ID", nullable = false)
    private Long ruleId;

    @ColumnDef(comment = "违规规则名称", nullable = false)
    private String ruleName;

    @ColumnDef(comment = "罚款金额(分)", nullable = false)
    private Integer fine;

    @ColumnDef(comment = "缴费时间")
    private LocalDateTime paidAt;

    @ColumnDef(comment = "违规结束时间")
    private LocalDateTime endAt;

    @ColumnDef(comment = "处罚延迟时间(分钟)")
    private Integer punishDelayMinute;

    @ColumnDef(comment = "是否已结束", nullable = false, defaultValue = "0")
    private IsEnum isEnd;

    @ColumnDef(comment = "是否需要拖车", nullable = false, defaultValue = "0")
    private IsEnum needTow;

    @ColumnDef(comment = "处罚时间")
    private LocalDateTime punishedAt;

    @ColumnDef(comment = "处罚人ID")
    private Long punishedUserId;

    @ColumnDef(comment = "取消时间")
    private LocalDateTime canceledAt;

    @ColumnDef(comment = "取消人ID")
    private Long canceledUserId;

    @ColumnDef(comment = "通过时间")
    private LocalDateTime passedAt;

    @ColumnDef(comment = "通过人ID")
    private Long passedUserId;

    @RelationOneToMany(
            selfField = "id",
            targetField = "outerId",
            targetTable = "file_info",
            valueField = "url"
    )
    @ColumnDef(ignore = true)
    private List<String> images;

}
