package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("lc_park")
public class LcPark extends SoftDeletedEntity {

    @ColumnDef(comment = "场库编号")
    private String parkNumber;

    @ColumnDef(comment = "场库名称")
    private String parkName;

    @ColumnDef(comment = "场库总车位数")
    private Integer spaceCount;

    @ColumnDef(comment = "场库空车位数")
    private Integer freeSpaceCount;

    @ColumnDef(comment = "场库可预约数")
    private Integer bookSpaceCount;

    @ColumnDef(comment = "场库在场预约数")
    private Integer bookInParkCount;
}
