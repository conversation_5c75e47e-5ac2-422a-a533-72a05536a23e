package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(value = "shop_sync_conflict", comment = "商铺同步冲突记录表")
public class ShopSyncConflict extends SoftDeletedEntity {

    @ColumnDef(comment = "编号")
    private String cellNo;

    @ColumnDef(comment = "冲突字段")
    private String conflictField;

    @ColumnDef(comment = "远程值")
    private String remoteValue;

    @ColumnDef(comment = "本地值")
    private String localValue;

    @ColumnDef(comment = "解决冲突之后的值")
    private String rightValue;

    @ColumnDef(comment = "是否已处理", defaultValue = "0", nullable = false)
    private IsEnum handled;

    @ColumnDef(comment = "处理时间")
    private LocalDateTime handledAt;

}
