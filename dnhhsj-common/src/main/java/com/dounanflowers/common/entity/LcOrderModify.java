package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 蓝卡订单修改记录
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table("lc_order_modify")
public class LcOrderModify extends BaseEntity {
    @ColumnDef(comment = "停车订单号")
    private Long lcOrderId;

    @ColumnDef(comment = "旧蓝卡订单号")
    private String oldBcOrderId;

    @ColumnDef(comment = "新蓝卡订单号")
    private String newBcOrderId;

    @ColumnDef(comment = "旧车牌号")
    private String oldPlate;

    @ColumnDef(comment = "新车牌号")
    private String newPlate;

    @ColumnDef(comment = "修改时间")
    private LocalDateTime modifyTime;
}
