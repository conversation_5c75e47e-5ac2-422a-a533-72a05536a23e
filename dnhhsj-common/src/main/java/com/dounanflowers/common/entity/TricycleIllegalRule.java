package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "tricycle_illegal_rule", comment = "三轮车违规规则表")
public class TricycleIllegalRule extends SoftDeletedEntity {

    @ColumnDef(comment = "罚款金额(分)", nullable = false, defaultValue = "1")
    private Integer fine;

    @ColumnDef(comment = "规则标题", nullable = false)
    private String title;

    @ColumnDef(comment = "是否需要拖车", nullable = false, defaultValue = "0")
    private IsEnum needTow;

    @ColumnDef(comment = "处罚延迟时间(分钟)", nullable = false, defaultValue = "0")
    private Integer punishDelayMinute;

    @ColumnDef(comment = "规则状态(0:禁用,1:启用)", nullable = false, defaultValue = "0")
    private IsEnum status;

}
