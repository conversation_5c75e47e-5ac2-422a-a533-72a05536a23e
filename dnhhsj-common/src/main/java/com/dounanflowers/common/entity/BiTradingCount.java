package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.BiTradingCountTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "bi_trading_count", comment = "大屏订单统计")
public class BiTradingCount extends BaseEntity {

    @ColumnDef(comment = "日期", nullable = false)
    private LocalDate date;

    @ColumnDef(comment = "订单数", nullable = false)
    private Integer orderCount;

    @ColumnDef(comment = "订单金额", nullable = false)
    private Integer amountCount;

    @ColumnDef(comment = "订单总数", nullable = false)
    private Integer orderCountTotal;

    @ColumnDef(comment = "订单金额总数", nullable = false)
    private Integer amountCountTotal;

    @ColumnDef(comment = "类型", nullable = false)
    private BiTradingCountTypeEnum type;
}
