package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.BannerLinkTypeEnum;
import com.dounanflowers.common.enums.BannerPlaceEnum;
import com.dounanflowers.common.enums.BannerTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "banner_info", comment = "轮播图信息表")
public class BannerInfo extends SoftDeletedEntity {

    @ColumnDef(comment = "标题", nullable = false)
    private String title;

    @ColumnDef(comment = "图片URL", nullable = false)
    private String url;

    @ColumnDef(comment = "位置")
    private BannerPlaceEnum place;

    @ColumnDef(comment = "类型")
    private BannerTypeEnum type;

    @ColumnDef(comment = "链接类型")
    private BannerLinkTypeEnum linkType;

    @ColumnDef(comment = "跳转链接")
    private String linkUrl;

    @ColumnDef(comment = "状态", defaultValue = "1", nullable = false)
    private IsEnum status;

    @ColumnDef(comment = "排序", defaultValue = "99", nullable = false)
    private Integer seq;

}
