package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 字段更改
 * orderId -> bcOrderId
 * userId > clientUserId
 * lcCouponId -> couponId
 */

@EqualsAndHashCode(callSuper = true)
@Data
@Table("lc_order")
public class LcOrder extends SoftDeletedEntity {

    @ColumnDef(comment = "入场记录编号")
    private String bcOrderId;

    @ColumnDef(comment = "车牌")
    private String plate;

    @ColumnDef(comment = "无牌车票号")
    private String ticketCode;

    @ColumnDef(comment = "车牌颜色")
    private String plateColor;

    @ColumnDef(comment = "入场时间")
    private LocalDateTime inTime;

    @ColumnDef(comment = "入场通道名称")
    private String inChannel;

    @ColumnDef(comment = "入场图片名")
    private String inImage;

    @ColumnDef(comment = "访问事由")
    private String visitReason;

    @ColumnDef(comment = "放行类型: 自动抬杆/手动放行,无信息时填''")
    private String openGateMode;

    @ColumnDef(comment = "匹配模式: 全字匹配、近似匹配、人工匹配、人工纠正,无信息时填''")
    private String matchMode;

    @ColumnDef(comment = "车牌识别可信度")
    private Integer confidence;


    @ColumnDef(comment = "车辆类型")
    private String carType;

    @ColumnDef(comment = "证件号码")
    private String userInfoIdCard;

    @ColumnDef(comment = "车主姓名")
    private String userInfoUserName;

    @ColumnDef(comment = "联系电话")
    private String userInfoPhone;

    @ColumnDef(comment = "地址")
    private String userInfoAddress;

    @ColumnDef(comment = "车位信息")
    private Long lcAreaId;

    @ColumnDef(comment = "是否开闸: “开闸”“未开闸”")
    private String barriorOpen;

    @ColumnDef(comment = "入场开闸耗时: 压地感到抬杆时间")
    private Integer inCostTime;

    @ColumnDef(comment = "空位数")
    private Integer spaceCount;


    @ColumnDef(comment = "其他联动设备图片信息")
    private String imageList; // TODO 迁移时转字符串

    @ColumnDef(comment = "图片名")
    private String imageName;

    @RelationOneToMany(selfField = "id", targetField = "lcOrderId")
    @ColumnDef(ignore = true)
    private List<LcOrderModify> modifyList; // 修改记录

    @ColumnDef(comment = "场库")
    private Long lcParkId;

    @ColumnDef(comment = "场库编号")
    private String parkNumber;

    @ColumnDef(comment = "操作员Id")
    private String operatorId;

    @ColumnDef(comment = "操作员姓名")
    private String operatorName;

    @ColumnDef(comment = "发票号码")
    private String invoiceNo;

    @ColumnDef(comment = "出场时间")
    private LocalDateTime outTime;

    @ColumnDef(comment = "出场图片名")
    private String outImage;

    @ColumnDef(comment = "出口通道名称")
    private String outChannel;

    @ColumnDef(comment = "总停车费")
    private String charge;

    @ColumnDef(comment = "线下总收费")
    private String offLineCharge;

    @ColumnDef(comment = "线下累计优惠金额总面值")
    private String offLineProfitChargeNum;

    @ColumnDef(comment = "线下累计优惠金额总抵扣值")
    private String offLineProfitChargeValue;

    @ColumnDef(comment = "线下累计优惠时间")
    private String offLineProfitTimeNum;

    @ColumnDef(comment = "线下累计优惠时间总抵扣值")
    private String offLineProfitTimeValue;

    @ColumnDef(comment = "线上总收费")
    private String onLineCharge;

    @ColumnDef(comment = "线上累计优惠金额总面值")
    private String onLineProfitChargeNum;

    @ColumnDef(comment = "线上累计优惠金额总抵扣值")
    private String onLineProfitChargeValue;

    @ColumnDef(comment = "线上累计优惠时间")
    private String onLineProfitTimeNum;

    @ColumnDef(comment = "线上累计优惠时间总抵扣值")
    private String onLineProfitTimeValue;

    @ColumnDef(comment = "线上线下金额和时间优惠累计抵扣值")
    private String profitChargeTotal;

    @ColumnDef(comment = "出场开闸耗时: 压地感到抬杆时间")
    private String outCostTime;

    @ColumnDef(comment = "已支付")
    private IsEnum paid;

    @ColumnDef(comment = "用户")
    private Long clientUserId;

    @ColumnDef(comment = "优惠券")
    private Long couponId;

}



