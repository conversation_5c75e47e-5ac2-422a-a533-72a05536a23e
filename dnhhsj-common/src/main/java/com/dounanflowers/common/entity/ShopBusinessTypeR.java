package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "shop_business_type_r", comment = "商铺经营类型关联表")
public class ShopBusinessTypeR extends BaseEntity {

    @ColumnDef(comment = "商铺ID", nullable = false)
    private Long shopId;

    @ColumnDef(comment = "经营类型", nullable = false)
    private String businessType;

}
