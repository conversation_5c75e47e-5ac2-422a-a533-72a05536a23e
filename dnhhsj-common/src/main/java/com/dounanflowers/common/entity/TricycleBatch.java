package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.TricycleBatchTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "tricycle_batch", comment = "三轮车批次表")
public class TricycleBatch extends SoftDeletedEntity {

    @ColumnDef(comment = "批次名称")
    private String name;

    @ColumnDef(comment = "批次描述")
    private String description;

    @ColumnDef(comment = "备注")
    private String remark;

    @ColumnDef(comment = "创建数量")
    private Integer count = 0;

    @ColumnDef(comment = "完成数量")
    private Integer completedCount = 0;

    @ColumnDef(comment = "批次类型", nullable = false, defaultValue = "1")
    private TricycleBatchTypeEnum type;

}
