package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "broker_info", comment = "经纪人信息表")
public class BrokerInfo extends SoftDeletedEntity {

    @ColumnDef(comment = "姓名", nullable = false)
    private String name;

    @ColumnDef(comment = "头像")
    private String avatar;

    @ColumnDef(comment = "职位")
    private String jobPosition;

    @ColumnDef(comment = "职称")
    private String jobTitle;

    @ColumnDef(comment = "手机号", nullable = false)
    private String mobile;

    @ColumnDef(comment = "历史成交量", defaultValue = "0")
    private Integer pastVolumeCount;

    @ColumnDef(comment = "状态", defaultValue = "1", nullable = false)
    private IsEnum status;

    @ColumnDef(comment = "成功率", defaultValue = "0")
    private BigDecimal winPercent;

}
