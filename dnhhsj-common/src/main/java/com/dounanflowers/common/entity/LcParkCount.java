package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("lc_park_count")
public class LcParkCount extends SoftDeletedEntity {

    @ColumnDef(comment = "场库名称")
    private Long lcParkId;

    @ColumnDef(comment = "在场车辆数")
    private Integer inParkCount;

    @ColumnDef(comment = "场库总车位数")
    private Integer spaceCount;

    @ColumnDef(comment = "场库空车位数")
    private Integer freeSpaceCount;

    @ColumnDef(comment = "分钟时间戳")
    private Integer minutes;

}
