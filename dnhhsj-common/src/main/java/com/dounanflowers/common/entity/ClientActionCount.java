package com.dounanflowers.common.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "client_action_count", comment = "客户行为次数表")
public class ClientActionCount extends SoftDeletedEntity {

    @ColumnDef(comment = "键", nullable = false)
    private String key;

    @ColumnDef(comment = "次数", defaultValue = "0", nullable = false)
    private Integer count;

    @ColumnDef(comment = "时间", nullable = false)
    private LocalDateTime time;

    @ColumnDef(comment = "时间单位", nullable = false)
    private String timeUnit;

}