package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.ClientTypeEnum;
import com.dounanflowers.common.enums.ModalBannerPlaceEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "modal_banner", comment = "弹窗")
public class ModalBanner extends SoftDeletedEntity {

    @ColumnDef(nullable = false,comment = "标题")
    private String title;

    @ColumnDef(nullable = false,comment = "客户端类型")
    private ClientTypeEnum client;

    @ColumnDef(nullable = false,comment = "弹窗位置")
    private ModalBannerPlaceEnum place;

    @ColumnDef(comment = "开始时间")
    private LocalDateTime startAt;


    @ColumnDef(comment = "结束时间")
    private LocalDateTime endAt;

    @ColumnDef(nullable = false, defaultValue = "0", comment = "关闭后不再弹出的时间")
    private Integer hideHours;


    @ColumnDef(comment = "跳转小程序内地址")
    private String linkUrl;


    @ColumnDef(nullable = false, comment = "图片URL")
    private String imageUrl;


    @ColumnDef(nullable = false, defaultValue = "0", comment = "0:否 1:是")
    private IsEnum status;

}
