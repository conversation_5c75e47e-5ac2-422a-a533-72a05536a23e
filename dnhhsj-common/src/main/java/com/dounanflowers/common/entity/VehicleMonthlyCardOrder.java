package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.BillPayStatusEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "vehicle_monthly_card_order", comment = "车辆月卡购买记录表")
public class VehicleMonthlyCardOrder extends SoftDeletedEntity {

    @ColumnDef(comment = "用户ID", nullable = false)
    private Long userId;

    @ColumnDef(comment = "机动车ID", nullable = false)
    private Long vehicleId;

    @ColumnDef(comment = "月卡ID", nullable = false)
    private Long monthlyCardId;

    @ColumnDef(comment = "优惠码ID")
    private Long couponId;

    @ColumnDef(comment = "原价(分)", nullable = false)
    private Integer originalPrice;

    @ColumnDef(comment = "实付金额(分)", nullable = false)
    private Integer actualPrice;

    @ColumnDef(comment = "订单状态(0:待支付,1:已支付,2:已取消)", defaultValue = "0", nullable = false)
    private BillPayStatusEnum status;

    @ColumnDef(comment = "生效时间")
    private LocalDateTime effectiveAt;

    @ColumnDef(comment = "到期时间")
    private LocalDateTime expireAt;
}
