package com.dounanflowers.common.entity;

import com.dounanflowers.common.enums.OrderStatusEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("product_order")
@Accessors(chain = true)
public class ProductOrder extends SoftDeletedEntity {

    @ColumnDef(comment = "用户ID", nullable = false)
    private Long userId;

    @ColumnDef(comment = "父ID", nullable = false, defaultValue = "0")
    private Long parentId;

    @ColumnDef(comment = "商铺ID", nullable = false)
    private Long storeId;

    @ColumnDef(comment = "退款ID")
    private Long refundId;

    @ColumnDef(comment = "订单编号", nullable = false)
    private String orderNo;

    @ColumnDef(comment = "总金额", nullable = false)
    private Integer totalAmount;

    @ColumnDef(comment = "实付金额", nullable = false)
    private Integer realAmount;

    @ColumnDef(comment = "订单状态", nullable = false)
    private OrderStatusEnum status;

    @ColumnDef(comment = "支付时间")
    private LocalDateTime payTime;

    @ColumnDef(comment = "过期时间", nullable = false)
    private LocalDateTime expireTime;

    @ColumnDef(comment = "退款原因")
    private String refundReason;

    @ColumnDef(comment = "退款时间")
    private LocalDateTime refundTime;

    @ColumnDef(comment = "备注")
    private String remark;

    @RelationOneToMany(
            selfField = "id",
            targetField = "orderId",
            targetTable = "product_order_item"
    )
    @ColumnDef(ignore = true)
    private List<ProductOrderItem> items;

    @ColumnDef(comment = "是否多店铺合并订单")
    private IsEnum merge;

}
