package com.dounanflowers.common.repo;

import com.dounanflowers.common.entity.Shop;
import com.dounanflowers.framework.repo.BaseRepo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

public interface ShopRepo extends BaseRepo<Shop> {

    @Update("update shop set view_count = view_count + 1 where id = #{id}")
    void updateViewCount(@Param("id") Long id);

}
