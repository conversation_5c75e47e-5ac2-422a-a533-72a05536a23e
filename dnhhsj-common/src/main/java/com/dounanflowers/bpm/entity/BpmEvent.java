package com.dounanflowers.bpm.entity;

import com.dounanflowers.bpm.enums.EventTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public abstract class BpmEvent extends BaseEntity {

    @ColumnDef(comment = "事件编码", nullable = false)
    private String eventCode;

    @ColumnDef(comment = "事件名称", nullable = false)
    private String eventName;

    @ColumnDef(type = "TEXT", comment = "事件参数")
    private String params;

    @ColumnDef(comment = "事件类型", nullable = false)
    private EventTypeEnum type;

    @ColumnDef(comment = "事件排序", nullable = false)
    private Integer seq;

}
