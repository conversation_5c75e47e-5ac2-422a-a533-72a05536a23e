package com.dounanflowers.bpm.entity;

import com.dounanflowers.bpm.enums.ActionTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "bpm_action", comment = "流程动作表")
public class BpmAction extends SoftDeletedEntity {

    @ColumnDef(comment = "节点ID", nullable = false)
    private Long nodeId;

    @ColumnDef(comment = "动作名称", nullable = false)
    private String name;

    @ColumnDef(comment = "动作编码")
    private String code;

    @ColumnDef(type = "TEXT", comment = "动作参数")
    private String params;

    @ColumnDef(comment = "动作类型", nullable = false)
    private ActionTypeEnum type;

    @ColumnDef(comment = "目标节点ID")
    private Long toNodeId;

    @RelationOneToMany(
            selfField = "id",
            targetField = "actionId",
            targetTable = "bpm_action_event"
    )
    @ColumnDef(ignore = true)
    private List<BpmActionEvent> events;

    @ColumnDef(comment = "动作排序", nullable = false, defaultValue = "99")
    private Integer seq;

}