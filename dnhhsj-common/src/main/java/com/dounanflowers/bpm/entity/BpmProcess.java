package com.dounanflowers.bpm.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "bpm_process", comment = "流程定义表")
public class BpmProcess extends SoftDeletedEntity {

    @ColumnDef(comment = "流程编码", nullable = false)
    private String code;

    @ColumnDef(comment = "流程名称", nullable = false)
    private String name;

}
