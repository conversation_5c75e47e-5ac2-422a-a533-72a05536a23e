package com.dounanflowers.bpm.entity;

import com.dounanflowers.bpm.enums.NodeRoleEnum;
import com.dounanflowers.bpm.enums.NodeStatusEnum;
import com.dounanflowers.bpm.enums.NodeTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "bpm_node_instance", comment = "流程节点实例表")
public class BpmNodeInstance extends SoftDeletedEntity {

    @ColumnDef(comment = "实例ID", nullable = false)
    private Long instanceId;

    @ColumnDef(comment = "节点状态", nullable = false)
    private NodeStatusEnum status;

    private NodeTypeEnum type;

    private NodeRoleEnum role;

    private String roleValue;

    @ColumnDef(comment = "节点ID", nullable = false)
    private Long nodeId;

    private Long stepId;

    private Long prevId;

    private Long nextId;

    @ColumnDef(type = "text")
    private String remark;

    @ColumnDef(type = "TEXT", comment = "节点数据")
    private String data;

    private LocalDateTime operatedAt;

    private Long operatedUserId;

    private Long operatedActionId;

    @ColumnDef(type = "text")
    private String operatedReason;

}
