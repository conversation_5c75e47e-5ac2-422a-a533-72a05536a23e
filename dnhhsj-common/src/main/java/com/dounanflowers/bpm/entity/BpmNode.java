package com.dounanflowers.bpm.entity;

import com.dounanflowers.bpm.enums.NodeRoleEnum;
import com.dounanflowers.bpm.enums.NodeTypeEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.RelationOneToMany;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "bpm_node", comment = "流程节点表")
public class BpmNode extends SoftDeletedEntity {

    @ColumnDef(comment = "流程ID", nullable = false)
    private Long processId;

    private Long stepId;

    @ColumnDef(comment = "节点名称", nullable = false)
    private String name;

    @ColumnDef(comment = "节点类型", nullable = false)
    private NodeTypeEnum type;

    @ColumnDef(comment = "节点编码")
    private String code;

    @ColumnDef(type = "TEXT", comment = "节点参数")
    private String params;

    private NodeRoleEnum role;

    private String roleValue;

    @RelationOneToMany(
            selfField = "id",
            targetField = "nodeId",
            targetTable = "bpm_node_event"
    )
    @ColumnDef(ignore = true)
    private List<BpmNodeEvent> events;

}
