package com.dounanflowers.bpm.entity;

import com.dounanflowers.bpm.enums.StatusEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "bpm_instance", comment = "流程实例表")
public class BpmInstance extends SoftDeletedEntity {

    @ColumnDef(comment = "流程ID", nullable = false)
    private Long processId;

    @ColumnDef(comment = "执行对象用户ID", nullable = false)
    private Long userId;

    @ColumnDef(comment = "提交人用户ID", nullable = false)
    private Long submitUserId;

    @ColumnDef(comment = "实例状态", nullable = false)
    private StatusEnum status;

    @ColumnDef(comment = "批次ID")
    private String batchId;

    @ColumnDef(comment = "当前节点ID", nullable = false)
    private Long currentNodeId;

    @ColumnDef(comment = "业务数据", type = "TEXT")
    private String data;

    @ColumnDef(comment = "外部ID", nullable = false)
    private Long outerId;

    @ColumnDef(comment = "业务类型", nullable = false)
    private String outerType;

    @ColumnDef(comment = "完成时间")
    private LocalDateTime endAt;

}
