package com.dounanflowers.bpm.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "bpm_action_event", comment = "流程动作事件表")
public class BpmActionEvent extends BpmEvent {

    @ColumnDef(comment = "动作ID", nullable = false)
    private Long actionId;

}
