package com.dounanflowers.bpm.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.SoftDeletedEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "bpm_step", comment = "流程步骤表")
public class BpmStep extends SoftDeletedEntity {

    @ColumnDef(comment = "流程ID", nullable = false)
    private Long processId;

    @ColumnDef(comment = "步骤名称", nullable = false)
    private String name;

    @ColumnDef(comment = "步骤编码")
    private String code;

    @ColumnDef(type = "TEXT", comment = "步骤参数")
    private String params;

    @ColumnDef(comment = "序号", nullable = false)
    private Integer seq;

}
