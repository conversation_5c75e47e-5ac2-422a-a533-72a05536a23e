package com.dounanflowers.bpm.entity;

import com.dounanflowers.bpm.enums.NodeStatusEnum;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(value = "bpm_node_event", comment = "流程节点事件表")
public class BpmNodeEvent extends BpmEvent {

    @ColumnDef(comment = "节点ID", nullable = false)
    private Long nodeId;

    private NodeStatusEnum status;

}
