package com.dounanflowers.bpm.handler;

import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.framework.utils.JsonUtils;

import java.lang.reflect.Type;

public abstract class ConditionHandler<M, D> {

    public boolean condition(EventContext ctx) {
        return condition(ctx, model(ctx), data(ctx));
    }

    @SuppressWarnings("unchecked")
    protected M model(EventContext ctx) {
        Type genericSuperclass = getClass().getGenericSuperclass();
        Type[] actualTypeArguments = ((java.lang.reflect.ParameterizedType) genericSuperclass).getActualTypeArguments();
        Class<M> modelClz = (Class<M>) actualTypeArguments[0];
        return JsonUtils.toObject(ctx.getInstance().getData(), modelClz);
    }

    @SuppressWarnings("unchecked")
    protected D data(EventContext ctx) {
        Type genericSuperclass = getClass().getGenericSuperclass();
        Type[] actualTypeArguments = ((java.lang.reflect.ParameterizedType) genericSuperclass).getActualTypeArguments();
        Class<D> dataClz = (Class<D>) actualTypeArguments[1];
        Object data = ctx.getData() == null ? ctx.getNodeInstance().getData() : ctx.getData();
        return data == null ? null : JsonUtils.toObject(JsonUtils.toJson(data), dataClz);
    }

    public abstract String code();

    protected boolean condition(EventContext ctx, M model, D data) {
        return false;
    }

}
