package com.dounanflowers.bpm.handler;

import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.framework.utils.JsonUtils;

public abstract class EventHandler<M, D> extends ConditionHandler<M, D> {

    public Object handle(EventContext ctx) {
        M model = model(ctx);
        D dataObj = data(ctx);
        ctx.setModel(model);
        Object handle = handle(ctx, model, dataObj);
        ctx.getInstance().setData(JsonUtils.toJson(model));
        ctx.getNodeInstance().setData(JsonUtils.toJson(dataObj));
        return handle;
    }

    protected abstract Object handle(EventContext ctx, M model, D data);

}
