package com.dounanflowers.bpm.handler.action;

import com.dounanflowers.bpm.dto.HandleContext;
import com.dounanflowers.bpm.entity.BpmAction;
import com.dounanflowers.bpm.entity.BpmInstance;
import com.dounanflowers.bpm.entity.BpmNode;
import com.dounanflowers.bpm.entity.BpmNodeInstance;
import com.dounanflowers.bpm.enums.ActionTypeEnum;
import com.dounanflowers.bpm.enums.EventTypeEnum;
import com.dounanflowers.bpm.enums.NodeStatusEnum;
import com.dounanflowers.bpm.enums.StatusEnum;
import com.dounanflowers.bpm.handler.ActionHandler;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.JsonUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
public class CancelActionHandler extends ActionHandler {

    @Override
    protected void handle(HandleContext ctx) {
        BpmInstance instance = ctx.getInstance();
        BpmNodeInstance current = ctx.getNodeInstance();

        // 获取执行的动作
        BpmAction action = null;
        // 有当前节点权限，先看是否当前节点有取消动作
        if (checkRole(instance, current, ctx.getUserId())) {
            try {
                BpmNode node = getNode(current.getNodeId());
                action = getActionByNode(node.getId(), ctx);
            } catch (Exception ignored) {
            }
        }
        // 没找到取消，从历史操作节点中找取消动作
        if (action == null) {
            List<BpmNodeInstance> nodeInstances = bpmManager.fetchNodeInstanceByInstanceId(instance.getId());
            List<Long> nodeIds = nodeInstances.stream().filter(v -> ctx.getUserId().equals(v.getOperatedUserId())).map(BpmNodeInstance::getNodeId).toList();
            List<BpmAction> actions = bpmManager.fetchActionsByNodeIdsAndType(nodeIds, ActionTypeEnum.CANCEL);
            // 没找到并且不是强制操作，抛出异常
            if (actions.isEmpty() && !ctx.isForce()) {
                throw new BaseException("无权限操作");
            } else if (!actions.isEmpty()) {
                action = actions.getFirst();
            }
        }
        // 执行动作前事件
        if (action != null) {
            handleActionEvent(action, EventTypeEnum.PRE, ctx);
        }

        // 更新当前节点实例信息
        LocalDateTime now = LocalDateTime.now();
        current.setOperatedAt(now);
        current.setOperatedUserId(ctx.getUserId());
        current.setRemark(ctx.getRemark());
        current.setOperatedReason(ctx.getReason());
        if (action != null) {
            current.setOperatedActionId(action.getId());
        }
        saveNodeStatus(ctx, NodeStatusEnum.CANCELED);

        // 保存实例信息
        instance.setStatus(StatusEnum.CANCELED);
//        instance.setData(JsonUtils.toJson(ctx.getData()));
        instance.setEndAt(now);
        bpmManager.saveInstance(instance);
        // 执行动作后事件
        if (action != null) {
            handleActionEvent(action, EventTypeEnum.POST, ctx);
        }
    }

    @Override
    public String actionType() {
        return ActionTypeEnum.CANCEL.name();
    }

}
