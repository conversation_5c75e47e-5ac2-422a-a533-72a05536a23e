package com.dounanflowers.bpm.handler.action;

import com.dounanflowers.bpm.dto.HandleContext;
import com.dounanflowers.bpm.entity.BpmAction;
import com.dounanflowers.bpm.entity.BpmInstance;
import com.dounanflowers.bpm.entity.BpmNode;
import com.dounanflowers.bpm.entity.BpmNodeInstance;
import com.dounanflowers.bpm.enums.*;
import com.dounanflowers.bpm.handler.ActionHandler;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.IdUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class RejectActionHandler extends ActionHandler {

    @Override
    public void handle(HandleContext ctx) {
        BpmInstance instance = ctx.getInstance();
        BpmNode node = ctx.getNode();
        BpmNodeInstance current = ctx.getNodeInstance();
        // 判断权限
        if (!ctx.isForce() && !checkRole(instance, current, ctx.getUserId())) {
            throw new BaseException("无权限操作");
        }
        // 获取执行的动作
        BpmAction action = getActionByNode(node.getId(), ctx);

        // 执行动作前事件
        handleActionEvent(action, EventTypeEnum.PRE, ctx);

        Long nextId = IdUtils.nextId();

        // 更新当前节点实例信息
        LocalDateTime now = LocalDateTime.now();
        current.setOperatedAt(now);
        current.setOperatedUserId(ctx.getUserId());
        current.setRemark(ctx.getRemark());
        current.setNextId(nextId);
        current.setOperatedActionId(action.getId());
        current.setOperatedReason(ctx.getReason());
        saveNodeStatus(ctx, NodeStatusEnum.REJECTED);

        Long toNodeId = action.getToNodeId();
        getPreNode(current.getPrevId());
        BpmNode nextNode;
        if (toNodeId != null) {
            nextNode = getNode(toNodeId);
        } else {
            nextNode = getPreNode(current.getPrevId());
        }
        BpmNodeInstance next = initNodeInstance(nextId, ctx.getInstance().getId(), current.getId(), nextNode);
//        instance.setData(JsonUtils.toJson(ctx.getData()));

        NodeStatusEnum status = NodeStatusEnum.WAITING;
        if (NodeTypeEnum.END.equals(nextNode.getType())) {
            instance.setStatus(StatusEnum.DONE);
            instance.setEndAt(now);
            next.setOperatedAt(now);
            next.setOperatedUserId(0L);
            status = NodeStatusEnum.AUTO;
        } else {
            current.setNextId(next.getId());
            instance.setCurrentNodeId(next.getId());
        }

        bpmManager.saveInstance(instance);

        // 保存下一个节点信息
        ctx.setNode(nextNode);
        ctx.setNodeInstance(next);
        saveNodeStatus(ctx, status);

        // 执行动作后事件
        handleActionEvent(action, EventTypeEnum.POST, ctx);
    }

    private BpmNode getPreNode(Long prevId) {
        BpmNodeInstance nodeInstance = getNodeInstance(prevId);
        return getNode(nodeInstance.getNodeId());
    }

    @Override
    public String actionType() {
        return ActionTypeEnum.REJECT.name();
    }

}
