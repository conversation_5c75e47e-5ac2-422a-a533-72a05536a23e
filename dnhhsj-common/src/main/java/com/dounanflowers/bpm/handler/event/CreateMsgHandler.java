package com.dounanflowers.bpm.handler.event;

import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.bpm.entity.BpmInstance;
import com.dounanflowers.bpm.entity.BpmNodeInstance;
import com.dounanflowers.bpm.enums.NodeRoleEnum;
import com.dounanflowers.bpm.handler.EventHandler;
import com.dounanflowers.common.dto.MsgSendByTemplateDto;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.common.service.MsgService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class CreateMsgHandler extends EventHandler<Object, Object> {

    private final AdminUserManager adminUserManager;

    private final MsgService msgService;

    @Override
    public String code() {
        return "approvalMsg";
    }

    protected List<Long> getRoleUserList(BpmInstance instance, NodeRoleEnum role, String roleValue) {
        return switch (role) {
            case ROLE -> adminUserManager.fetchUserIdsByRole(roleValue);
            case USER -> List.of(Long.parseLong(roleValue));
            case SELF -> List.of(instance.getUserId());
            default -> List.of();
        };
    }

    @Override
    protected Object handle(EventContext ctx, Object model, Object data) {
        BpmInstance instance = ctx.getInstance();
        BpmNodeInstance nodeInstance = ctx.getNodeInstance();
        List<Long> userIds = getRoleUserList(instance, nodeInstance.getRole(), nodeInstance.getRoleValue());
        MsgSendByTemplateDto enter = new MsgSendByTemplateDto()
                .setTemplateCode(ctx.getEvent().getParams())
                .setContext(ctx)
                .setOuterId(nodeInstance.getId())
                .setOuterType("bpmNodeInstance")
                .setSendUserId(ctx.getUserId())
                .setReceiveUserIds(userIds);
        msgService.sendMessageByTemplate(enter);
        return data;
    }

}
