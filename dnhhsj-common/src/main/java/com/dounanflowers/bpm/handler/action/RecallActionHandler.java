package com.dounanflowers.bpm.handler.action;

import com.dounanflowers.bpm.dto.HandleContext;
import com.dounanflowers.bpm.enums.ActionTypeEnum;
import com.dounanflowers.bpm.handler.ActionHandler;
import org.springframework.stereotype.Component;

@Component
public class RecallActionHandler extends ActionHandler {

    @Override
    protected void handle(HandleContext ctx) {
    }

    @Override
    public String actionType() {
        return ActionTypeEnum.RECALL.name();
    }

}
