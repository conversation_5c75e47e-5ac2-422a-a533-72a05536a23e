package com.dounanflowers.bpm.handler.action;

import com.dounanflowers.bpm.dto.ActionContext;
import com.dounanflowers.bpm.dto.BpmHandleDto;
import com.dounanflowers.bpm.dto.HandleContext;
import com.dounanflowers.bpm.entity.BpmAction;
import com.dounanflowers.bpm.entity.BpmInstance;
import com.dounanflowers.bpm.entity.BpmNode;
import com.dounanflowers.bpm.enums.NodeStatusEnum;
import com.dounanflowers.bpm.enums.NodeTypeEnum;
import com.dounanflowers.bpm.handler.ActionHandler;
import com.dounanflowers.bpm.handler.ConditionHandler;
import com.dounanflowers.bpm.service.BpmService;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.SpringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CodeAutoHandler extends ActionHandler {

    @Override
    protected void handle(HandleContext ctx) {
        BpmInstance instance = ctx.getInstance();
        BpmNode node = ctx.getNode();
        if (node.getType() != NodeTypeEnum.CODE) {
            throw new BaseException("非自动节点");
        }
        BpmAction action = getAction(ctx, node);
        BpmService bpm = SpringUtils.getBean(BpmService.class);
        saveNodeStatus(ctx, NodeStatusEnum.AUTO);
        bpm.doAction(new BpmHandleDto()
                .setInstanceId(instance.getId())
                .setActionType(action.getType().name())
                .setActionCode(action.getCode())
                .setForce(true)
                .setRemark("自动执行")
                .setUserId(0L)
        );
    }

    private BpmAction getAction(HandleContext ctx, BpmNode node) {
        List<BpmAction> actions = bpmManager.fetchActionsByNode(node.getId());
        if (actions == null || actions.isEmpty()) {
            throw new BaseException("节点未配置动作");
        }
        for (BpmAction one : actions) {
            ActionContext actionCtx = new ActionContext(ctx);
            actionCtx.setAction(one);
            if (StringUtils.isBlank(one.getCode())) {
                continue;
            }
            ConditionHandler<?, ?> conditionHandler = findConditionHandler(one.getCode());
            boolean condition = conditionHandler.condition(actionCtx);
            if (condition) {
                return one;
            }
        }
        throw new BaseException("未找到匹配的动作");
    }

    @Override
    public String actionType() {
        return "CODE";
    }

}
