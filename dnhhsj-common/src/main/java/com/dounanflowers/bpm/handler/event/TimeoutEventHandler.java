package com.dounanflowers.bpm.handler.event;

import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.bpm.handler.EventHandler;

import java.time.LocalDateTime;

public abstract class TimeoutEventHandler<M, D> extends EventHandler<M, D> {

    @Override
    protected Object handle(EventContext ctx, M model, D data) {
        return timeoutAt(ctx, model, data);
    }

    protected abstract LocalDateTime timeoutAt(EventContext ctx, M model, D data);

}
