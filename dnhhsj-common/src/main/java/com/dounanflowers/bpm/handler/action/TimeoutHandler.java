package com.dounanflowers.bpm.handler.action;

import com.dounanflowers.bpm.dto.ActionContext;
import com.dounanflowers.bpm.dto.HandleContext;
import com.dounanflowers.bpm.entity.*;
import com.dounanflowers.bpm.enums.*;
import com.dounanflowers.bpm.handler.ActionHandler;
import com.dounanflowers.framework.utils.IdUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class TimeoutHandler extends ActionHandler {

    @Override
    protected void handle(HandleContext ctx) {
        BpmInstance instance = ctx.getInstance();
        BpmNode node = ctx.getNode();
        BpmAction action = getActionByNode(node.getId(), ctx);
        // 判断是否超时
        if (!isTimeout(action, ctx)) {
            return;
        }
        BpmNodeInstance current = ctx.getNodeInstance();

        // 执行动作前事件
        handleActionEvent(action, EventTypeEnum.PRE, ctx);

        Long nextId = IdUtils.nextId();

        // 更新当前节点实例信息
        LocalDateTime now = LocalDateTime.now();
        current.setOperatedAt(now);
        current.setOperatedUserId(ctx.getUserId());
        current.setRemark("超时自动流转");
        current.setOperatedActionId(action.getId());
        current.setNextId(nextId);
        saveNodeStatus(ctx, NodeStatusEnum.TIMEOUT);

        // 获取下一个节点信息
        BpmNode nextNode = getNode(action.getToNodeId());
        BpmNodeInstance next = initNodeInstance(nextId, instance.getId(), current.getId(), nextNode);
        NodeStatusEnum status = NodeStatusEnum.WAITING;
        if (NodeTypeEnum.END.equals(nextNode.getType())) {
            instance.setStatus(StatusEnum.DONE);
            instance.setEndAt(now);
            next.setOperatedAt(now);
            next.setOperatedUserId(0L);
            status = NodeStatusEnum.AUTO;
        } else {
            current.setNextId(next.getId());
            instance.setCurrentNodeId(next.getId());
        }
//        instance.setData(JsonUtils.toJson(ctx.getData()));

        // 保存实例信息
        bpmManager.saveInstance(instance);

        // 保存下一个节点信息
        ctx.setNode(nextNode);
        ctx.setNodeInstance(next);
        saveNodeStatus(ctx, status);

        // 执行动作后事件
        handleActionEvent(action, EventTypeEnum.POST, ctx);
    }


    protected boolean isTimeout(BpmAction action, HandleContext ctx) {
        if (CollectionUtils.isEmpty(action.getEvents())) {
            return false;
        }
        BpmActionEvent event = action.getEvents().stream().filter(v -> EventTypeEnum.TIMEOUT.equals(v.getType())).findFirst().orElse(null);
        ActionContext eventCtx = new ActionContext(ctx);
        eventCtx.setEvent(event);
        eventCtx.setAction(action);
        LocalDateTime timeout = (LocalDateTime) handleCustom(eventCtx);
        return LocalDateTime.now().isAfter(timeout);
    }

    @Override
    public String actionType() {
        return ActionTypeEnum.TIMEOUT.name();
    }

}
