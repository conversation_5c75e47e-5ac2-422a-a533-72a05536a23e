package com.dounanflowers.bpm.handler.action;

import com.dounanflowers.bpm.dto.HandleContext;
import com.dounanflowers.bpm.entity.BpmInstance;
import com.dounanflowers.bpm.entity.BpmNode;
import com.dounanflowers.bpm.entity.BpmNodeInstance;
import com.dounanflowers.bpm.entity.BpmStep;
import com.dounanflowers.bpm.enums.ActionTypeEnum;
import com.dounanflowers.bpm.enums.NodeStatusEnum;
import com.dounanflowers.bpm.handler.ActionHandler;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.IdUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class StartActionHandler extends ActionHandler {

    @Override
    protected void handle(HandleContext ctx) {
        BpmInstance instance = ctx.getInstance();

        List<BpmStep> steps = bpmManager.fetchStepsByProcess(ctx.getProcess().getId());
        if (steps == null || steps.isEmpty()) {
            throw new BaseException("流程未配置步骤");
        }
        BpmStep first = steps.getFirst();
        List<BpmNode> nodes = bpmManager.fetchNodesByStep(first.getId());
        if (nodes == null || nodes.isEmpty()) {
            throw new BaseException("流程未配置开始节点");
        }
        BpmNode start = nodes.getFirst();

        Long niId = IdUtils.nextId();

        BpmNodeInstance ni = initNodeInstance(niId, ctx.getInstance().getId(), 0L, start);
        ctx.setNode(start);
        ctx.setNodeInstance(ni);
        saveNodeStatus(ctx, NodeStatusEnum.WAITING);

        // 保存实例信息
        instance.setCurrentNodeId(ni.getId());
        bpmManager.saveInstance(instance);
    }

    @Override
    public String actionType() {
        return ActionTypeEnum.START.name();
    }

}
