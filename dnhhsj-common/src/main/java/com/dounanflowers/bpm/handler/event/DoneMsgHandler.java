package com.dounanflowers.bpm.handler.event;

import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.bpm.handler.EventHandler;
import com.dounanflowers.common.service.MsgService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DoneMsgHandler extends EventHandler<Object, Object> {

    private final MsgService msgService;

    @Override
    public String code() {
        return "approvalDone";
    }

    @Override
    protected Object handle(EventContext ctx, Object model, Object data) {
        msgService.doneTodoMsgByOuterTypeAndId("bpmNodeInstance", ctx.getNodeInstance().getId(), ctx.getUserId());
        return data;
    }

}
