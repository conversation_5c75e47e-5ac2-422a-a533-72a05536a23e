package com.dounanflowers.bpm.handler.event;

import com.dounanflowers.bpm.dto.EventContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class DefaultMinuteTimeoutHandler extends TimeoutEventHandler<Object, Object> {

    @Override
    protected LocalDateTime timeoutAt(EventContext ctx, Object model, Object data) {
        String params = ctx.getEvent().getParams();
        if (StringUtils.isBlank(params)) {
            throw new IllegalArgumentException("未设置超时时间");
        }
        return ctx.getNodeInstance().getCreatedAt().plusMinutes(Long.parseLong(params));
    }

    @Override
    public String code() {
        return "minuteTimeout";
    }

}
