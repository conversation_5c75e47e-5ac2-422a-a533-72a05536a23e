package com.dounanflowers.bpm.handler;

import com.dounanflowers.bpm.dto.ActionContext;
import com.dounanflowers.bpm.dto.BpmHandleDto;
import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.bpm.dto.HandleContext;
import com.dounanflowers.bpm.entity.*;
import com.dounanflowers.bpm.enums.EventTypeEnum;
import com.dounanflowers.bpm.enums.NodeStatusEnum;
import com.dounanflowers.bpm.manager.BpmManager;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.framework.utils.SpringUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;

public abstract class ActionHandler {

    @Resource
    protected BpmManager bpmManager;

    @Resource
    protected AdminUserManager adminUserManager;

    @Transactional(rollbackFor = Exception.class)
    public Object handle(BpmHandleDto dto) {
        HandleContext ctx = new HandleContext();
        Long instanceId = dto.getInstanceId();
        BpmInstance instance = getInstance(instanceId);
        if (instance.getEndAt() != null) {
            throw new BaseException("流程已结束");
        }
        BpmProcess process = getProcess(instance.getProcessId());
        ctx.setProcess(process)
                .setInstance(instance);
        if (instance.getCurrentNodeId() != 0L) {
            BpmNodeInstance nodeInstance = getNodeInstance(instance.getCurrentNodeId());
            BpmNode node = getNode(nodeInstance.getNodeId());
            if (dto.getData() == null && StringUtils.isNotBlank(nodeInstance.getData())) {
                dto.setData(JsonUtils.toMap(nodeInstance.getData()));
            }
            ctx.setNodeInstance(nodeInstance)
                    .setNode(node)
                    .setStepId(node.getStepId());
        }
        ctx.setActionCode(dto.getActionCode())
                .setReason(dto.getReason())
                .setRemark(dto.getRemark())
                .setUserId(dto.getUserId())
                .setForce(dto.isForce())
                .setData(dto.getData());
        handle(ctx);
        return ctx.getData();
    }

    public abstract String actionType();

    protected abstract void handle(HandleContext ctx);

    protected ConditionHandler<?, ?> findConditionHandler(String code) {
        List<ConditionHandler> conditionHandlers = SpringUtils.getBeans(ConditionHandler.class);
        for (ConditionHandler<?, ?> conditionHandler : conditionHandlers) {
            if (code.equals(conditionHandler.code())) {
                return conditionHandler;
            }
        }
        throw new BaseException("未找到条件处理器");
    }

    protected Object handleCustom(EventContext ctx) {
        String code = ctx.getEvent().getEventCode();
        if (code == null) {
            return ctx.getData();
        }
        EventHandler<?, ?> eventHandler = findCustomHandler(code);
        return eventHandler.handle(ctx);
    }

    protected EventHandler<?, ?> findCustomHandler(String code) {
        List<EventHandler> eventHandlers = SpringUtils.getBeans(EventHandler.class);
        for (EventHandler<?, ?> eventHandler : eventHandlers) {
            if (code.equals(eventHandler.code())) {
                return eventHandler;
            }
        }
        throw new BaseException("未找到自定义处理器");
    }

    protected BpmInstance getInstance(Long instanceId) {
        BpmInstance instance = bpmManager.fetchInstance(instanceId);
        if (instance == null) {
            throw new BaseException("流程实例不存在");
        }
        return instance;
    }

    protected BpmProcess getProcess(Long processId) {
        BpmProcess process = bpmManager.fetchProcess(processId);
        if (process == null) {
            throw new BaseException("流程实例不存在");
        }
        return process;
    }

    protected BpmNodeInstance getNodeInstance(Long nodeInstanceId) {
        BpmNodeInstance nodeInstance = bpmManager.fetchNodeInstance(nodeInstanceId);
        if (nodeInstance == null) {
            throw new BaseException("节点实例不存在");
        }
        return nodeInstance;
    }

    protected BpmNode getNode(Long nodeId) {
        BpmNode node = bpmManager.fetchNode(nodeId);
        if (node == null) {
            throw new BaseException("节点不存在");
        }
        return node;
    }

    protected BpmAction getActionByNode(Long nodeId, HandleContext ctx) {
        List<BpmAction> actions = bpmManager.fetchActionsByNode(nodeId);
        if (actions == null || actions.isEmpty()) {
            throw new BaseException("节点未配置动作");
        }
        List<BpmAction> typeActions = actions.stream().filter(a -> Objects.equals(a.getType().name(), actionType())).toList();
        if (typeActions.isEmpty()) {
            throw new BaseException("节点未配置动作");
        }
        if (typeActions.size() == 1) {
            return typeActions.getFirst();
        }
        String actionCode = ctx.getActionCode();
        if (StringUtils.isNotBlank(actionCode)) {
            BpmAction one = typeActions.stream().filter(v -> actionCode.equals(v.getCode())).findFirst().orElse(null);
            if (one == null) {
                throw new BaseException("未找到匹配的动作");
            }
            return one;
        }
        for (BpmAction one : typeActions) {
            ActionContext actionCtx = new ActionContext(ctx);
            actionCtx.setAction(one);
            if (StringUtils.isBlank(one.getCode())) {
                continue;
            }
            ConditionHandler<?, ?> conditionHandler = findConditionHandler(one.getCode());
            boolean condition = conditionHandler.condition(actionCtx);
            if (condition) {
                return one;
            }
        }
        throw new BaseException("未找到匹配的动作");
    }

    protected boolean checkRole(BpmInstance instance, BpmNodeInstance nodeInstance, Long userId) {
        return switch (nodeInstance.getRole()) {
            case SYSTEM -> 0L == userId;
            case SELF -> Objects.equals(userId, instance.getUserId());
            case USER -> Objects.equals(userId, Long.parseLong(nodeInstance.getRoleValue()));
            case ROLE -> {
                List<String> roles = adminUserManager.fetchRoleCodeByUserId(userId);
                yield roles.contains(nodeInstance.getRoleValue());
            }
        };
    }

    protected BpmNodeInstance initNodeInstance(Long id, Long instanceId, Long prevId, BpmNode node) {
        BpmNodeInstance instance = new BpmNodeInstance();
        instance.setId(id);
        instance.setInstanceId(instanceId);
        instance.setStatus(NodeStatusEnum.WAITING);
        instance.setRole(node.getRole());
        instance.setType(node.getType());
        instance.setStepId(node.getStepId());
        instance.setRoleValue(node.getRoleValue());
        instance.setNodeId(node.getId());
        instance.setPrevId(prevId);
        instance.setNextId(0L);
        return instance;
    }

    protected void handleActionEvent(BpmAction action, EventTypeEnum type, HandleContext ctx) {
        if (action == null) {
            return;
        }
        if (type == EventTypeEnum.PRE) {
            try {
                ActionContext eventCtx = new ActionContext(ctx);
                eventCtx.setEvent(new BpmActionEvent().setEventCode(action.getCode()).setType(type));
                eventCtx.setAction(action);
                ctx.setData(handleCustom(eventCtx));
            } catch (Exception ignored) {
            }
        }
        if (CollectionUtils.isEmpty(action.getEvents())) {
            return;
        }
        List<BpmActionEvent> postEvents = action.getEvents().stream().filter(v -> type.equals(v.getType()))
                .sorted(Comparator.comparing(BpmActionEvent::getSeq)).toList();
        for (BpmActionEvent event : postEvents) {
            ActionContext eventCtx = new ActionContext(ctx);
            eventCtx.setEvent(event);
            eventCtx.setAction(action);
            ctx.setData(handleCustom(eventCtx));
        }
    }

    protected void saveNodeStatus(HandleContext ctx, NodeStatusEnum status) {
        BpmNode node = ctx.getNode();
        NodeStatusEnum preStatus = ctx.getNodeInstance().getStatus();
        if (preStatus != status) {
            handleNodeEvent(ctx, preStatus, EventTypeEnum.POST);
        }
        ctx.getNodeInstance().setStatus(status);
        ctx.getNodeInstance().setData(JsonUtils.toJson(ctx.getData()));
        handleNodeEvent(ctx, status, EventTypeEnum.PRE);
        bpmManager.saveNodeInstance(ctx.getNodeInstance());
        if (StringUtils.isNotBlank(node.getCode())) {
            EventContext eventCtx = new EventContext(ctx);
            eventCtx.setEvent(new BpmNodeEvent().setEventCode(node.getCode()).setType(EventTypeEnum.POST));
            eventCtx.setNode(node);
            ctx.setData(handleCustom(eventCtx));
        }
    }

    protected void handleNodeEvent(HandleContext ctx, NodeStatusEnum status, EventTypeEnum type) {
        BpmNode node = ctx.getNode();
        if (node == null || CollectionUtils.isEmpty(node.getEvents())) {
            return;
        }
        List<BpmNodeEvent> postEvents = node.getEvents().stream().filter(v -> v.getStatus().equals(status)).filter(v -> type.equals(v.getType()))
                .sorted(Comparator.comparing(BpmNodeEvent::getSeq)).toList();
        for (BpmNodeEvent event : postEvents) {
            EventContext eventCtx = new ActionContext(ctx);
            eventCtx.setEvent(event);
            ctx.setData(handleCustom(eventCtx));
        }
    }

}
