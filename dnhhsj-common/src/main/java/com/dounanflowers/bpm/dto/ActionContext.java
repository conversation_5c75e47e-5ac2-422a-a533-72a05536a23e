package com.dounanflowers.bpm.dto;

import com.dounanflowers.bpm.entity.BpmAction;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Accessors(chain = true)
public class ActionContext extends EventContext {
    private BpmAction action;

    public ActionContext(HandleContext ctx) {
        super(ctx);
    }

}
