package com.dounanflowers.bpm.dto;

import com.dounanflowers.bpm.entity.BpmInstance;
import com.dounanflowers.bpm.entity.BpmNode;
import com.dounanflowers.bpm.entity.BpmNodeInstance;
import com.dounanflowers.bpm.entity.BpmProcess;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class HandleContext {
    private BpmProcess process;
    private BpmInstance instance;
    private String actionCode;
    private BpmNode node;
    private BpmNodeInstance nodeInstance;
    private String remark;
    private String reason;
    private Long stepId;
    private Object model;
    private Object data;
    private Long userId;
    private boolean force;
}
