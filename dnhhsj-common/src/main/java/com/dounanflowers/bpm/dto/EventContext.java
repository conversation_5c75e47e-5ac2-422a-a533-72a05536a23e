package com.dounanflowers.bpm.dto;

import com.dounanflowers.bpm.entity.BpmEvent;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Accessors(chain = true)
public class EventContext extends HandleContext {
    private BpmEvent event;

    public EventContext(HandleContext ctx) {
        this.setProcess(ctx.getProcess());
        this.setNode(ctx.getNode());
        this.setInstance(ctx.getInstance());
        this.setNodeInstance(ctx.getNodeInstance());
        this.setData(ctx.getData());
        this.setActionCode(ctx.getActionCode());
        this.setForce(ctx.isForce());
        this.setUserId(ctx.getUserId());
        this.setReason(ctx.getReason());
        this.setRemark(ctx.getRemark());
    }

}
