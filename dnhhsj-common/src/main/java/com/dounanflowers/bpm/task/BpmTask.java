package com.dounanflowers.bpm.task;

import com.dounanflowers.bpm.service.BpmService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class BpmTask {

    private final BpmService bpmService;

    @Scheduled(cron = "0/30 * * * * ?")
    public void autoExecute() {
        bpmService.autoExecute();
    }

}
