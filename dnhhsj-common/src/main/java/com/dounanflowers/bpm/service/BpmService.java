package com.dounanflowers.bpm.service;

import com.dounanflowers.bpm.dto.BpmHandleDto;
import com.dounanflowers.bpm.dto.BpmSubmitDto;
import com.dounanflowers.bpm.entity.BpmAction;
import com.dounanflowers.bpm.entity.BpmInstance;
import com.dounanflowers.bpm.entity.BpmNodeInstance;
import com.dounanflowers.bpm.entity.BpmProcess;
import com.dounanflowers.bpm.enums.ActionTypeEnum;
import com.dounanflowers.bpm.enums.NodeStatusEnum;
import com.dounanflowers.bpm.enums.StatusEnum;
import com.dounanflowers.bpm.handler.ActionHandler;
import com.dounanflowers.bpm.manager.BpmManager;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.IdUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.framework.utils.SpringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class BpmService {

    private final BpmManager bpmManager;

    public BpmInstance submit(BpmSubmitDto dto) {
        String processCode = dto.getProcessCode();
        String data = JsonUtils.toJson(dto.getData());
        Long outerId = dto.getOuterId();
        String outerType = dto.getOuterType();
        BpmProcess process = bpmManager.fetchProcessByCode(processCode);
        if (process == null) {
            throw new BaseException("流程不存在");
        }
        Long processId = process.getId();
        BpmInstance instance = new BpmInstance();
        instance.setId(IdUtils.nextId());
        instance.setProcessId(processId);
        instance.setData(data);
        instance.setSubmitUserId(dto.getSubmitUserId());
        instance.setUserId(dto.getUserId());
        instance.setStatus(StatusEnum.WAITING);
        instance.setOuterId(outerId);
        instance.setOuterType(outerType);
        instance.setCurrentNodeId(0L);
        bpmManager.saveInstance(instance);
        doAction(new BpmHandleDto()
                .setActionType(ActionTypeEnum.START.name())
                .setInstanceId(instance.getId())
                .setUserId(dto.getSubmitUserId()));
        return instance;
    }

    public Object doAction(BpmHandleDto dto) {
        List<ActionHandler> handlers = SpringUtils.getBeans(ActionHandler.class);
        ActionHandler handler = handlers.stream().filter(v -> v.actionType().equals(dto.getActionType())).findFirst()
                .orElseThrow(() -> new BaseException("未找到对应动作类型"));
        return handler.handle(dto);
    }

    public BpmInstance getInstanceByOuter(String type, Long outerId) {
        return bpmManager.fetchInstanceByOuter(type, outerId);
    }

    public void autoExecute() {
        List<BpmNodeInstance> instances = bpmManager.fetchWaitingAutoNodeInstance();
        for (BpmNodeInstance nodeInstance : instances) {
            log.info("自动执行节点实例:{}", nodeInstance);
            doAction(new BpmHandleDto()
                    .setActionType("CODE")
                    .setInstanceId(nodeInstance.getInstanceId())
                    .setUserId(0L));
        }
        List<BpmAction> actions = bpmManager.fetchTimeoutActions();
        List<Long> nodeIds = actions.stream().map(BpmAction::getNodeId).toList();
        List<BpmNodeInstance> nodeInstances = bpmManager.fetchNodeInstanceByNodeIdAndStatus(nodeIds, NodeStatusEnum.WAITING);
        for (BpmNodeInstance nodeInstance : nodeInstances) {
            log.info("自动执行节点实例:{}", nodeInstance);
            doAction(new BpmHandleDto()
                    .setActionType(ActionTypeEnum.TIMEOUT.name())
                    .setInstanceId(nodeInstance.getInstanceId())
                    .setUserId(0L));
        }
    }

    public List<BpmInstance> getInstanceByOuterIds(String key, List<Long> list) {
        return bpmManager.fetchInstanceByOuterIds(key, list);
    }

    public void stopProcess(Long id) {
        BpmInstance instance = bpmManager.fetchInstance(id);
        if (instance == null) {
            throw new BaseException("流程实例不存在");
        }
        instance.setStatus(StatusEnum.STOP);
        bpmManager.saveInstance(instance);
    }

}
