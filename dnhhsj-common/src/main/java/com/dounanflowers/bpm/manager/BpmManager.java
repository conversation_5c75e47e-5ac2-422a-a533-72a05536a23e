package com.dounanflowers.bpm.manager;

import com.dounanflowers.bpm.entity.*;
import com.dounanflowers.bpm.enums.ActionTypeEnum;
import com.dounanflowers.bpm.enums.NodeStatusEnum;
import com.dounanflowers.bpm.enums.NodeTypeEnum;
import com.dounanflowers.bpm.enums.StatusEnum;
import com.dounanflowers.bpm.repo.*;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class BpmManager {

    private final BpmProcessRepo bpmProcessRepo;
    private final BpmStepRepo bpmStepRepo;
    private final BpmNodeRepo bpmNodeRepo;
    private final BpmActionRepo bpmActionRepo;
    private final BpmInstanceRepo bpmInstanceRepo;
    private final BpmNodeInstanceRepo bpmNodeInstanceRepo;


    public BpmProcess fetchProcess(Long processId) {
        return bpmProcessRepo.selectOneById(processId);
    }

    public List<BpmStep> fetchStepsByProcess(Long processId) {
        return bpmStepRepo.selectListByQuery(QueryWrapper.create().eq(BpmStep::getProcessId, processId));
    }

    public List<BpmNode> fetchNodesByStep(Long id) {
        return bpmNodeRepo.selectListWithRelationsByQuery(QueryWrapper.create().eq(BpmNode::getStepId, id));
    }

    public void saveNodeInstance(BpmNodeInstance nodeInstance) {
        bpmNodeInstanceRepo.save(nodeInstance);
    }

    public void saveInstance(BpmInstance instance) {
        bpmInstanceRepo.save(instance);
    }

    public BpmInstance fetchInstance(Long instanceId) {
        return bpmInstanceRepo.selectOneById(instanceId);
    }

    public BpmNodeInstance fetchNodeInstance(Long nodeInstanceId) {
        return bpmNodeInstanceRepo.selectOneById(nodeInstanceId);
    }

    public BpmNode fetchNode(Long nodeId) {
        return bpmNodeRepo.selectOneWithRelationsById(nodeId);
    }

    public List<BpmAction> fetchActionsByNode(Long id) {
        return bpmActionRepo.selectListWithRelationsByQuery(QueryWrapper.create().eq(BpmAction::getNodeId, id).orderBy(BpmAction::getSeq).asc());
    }

    public BpmProcess fetchProcessByCode(String processCode) {
        return bpmProcessRepo.selectOneByQuery(QueryWrapper.create().eq(BpmProcess::getCode, processCode));
    }

    public BpmInstance fetchInstanceByOuter(String type, Long outerId) {
        return bpmInstanceRepo.selectOneByQuery(QueryWrapper.create().eq(BpmInstance::getOuterType, type).eq(BpmInstance::getOuterId, outerId).orderBy(BpmInstance::getId).desc());
    }

    public List<BpmNodeInstance> fetchNodeInstanceByInstanceId(Long id) {
        return bpmNodeInstanceRepo.selectListByQuery(QueryWrapper.create().eq(BpmNodeInstance::getInstanceId, id));
    }

    public List<BpmAction> fetchActionsByNodeIdsAndType(List<Long> nodeIds, ActionTypeEnum type) {
        if (CollectionUtils.isEmpty(nodeIds)) {
            return List.of();
        }
        return bpmActionRepo.selectListWithRelationsByQuery(QueryWrapper.create().in(BpmAction::getNodeId, nodeIds).eq(BpmAction::getType, type).orderBy(BpmAction::getSeq).asc());
    }

    public List<BpmNodeInstance> fetchWaitingAutoNodeInstance() {
        return bpmNodeInstanceRepo.selectListByQuery(QueryWrapper.create().eq(BpmNodeInstance::getStatus, StatusEnum.WAITING).eq(BpmNodeInstance::getType, NodeTypeEnum.CODE));
    }

    public List<BpmNodeInstance> fetchWaitingDelayNodeInstance() {
        return bpmNodeInstanceRepo.selectListByQuery(QueryWrapper.create().eq(BpmNodeInstance::getStatus, StatusEnum.WAITING).eq(BpmNodeInstance::getType, NodeTypeEnum.DELAY));
    }

    public List<BpmAction> fetchTimeoutActions() {
        return bpmActionRepo.selectListByQuery(QueryWrapper.create().eq(BpmAction::getType, ActionTypeEnum.TIMEOUT));
    }

    public List<BpmNodeInstance> fetchNodeInstanceByNodeIdAndStatus(List<Long> nodeIds, NodeStatusEnum status) {
        if (CollectionUtils.isEmpty(nodeIds)) {
            return List.of();
        }
        return bpmNodeInstanceRepo.selectListByQuery(QueryWrapper.create().in(BpmNodeInstance::getNodeId, nodeIds).eq(BpmNodeInstance::getStatus, status));
    }

    public Page<BpmInstance> pageList(PageRequest pageRequest) {
        return bpmInstanceRepo.selectPageByQuery(pageRequest);
    }

    public List<BpmNodeInstance> fetchNodeInstancesByInstanceIds(List<Long> instanceIds) {
        if (CollectionUtils.isEmpty(instanceIds)) {
            return List.of();
        }
        return bpmNodeInstanceRepo.selectListByQuery(QueryWrapper.create().in(BpmNodeInstance::getInstanceId, instanceIds));
    }

    public List<BpmNode> fetchNodeByIds(List<Long> nodeIds) {
        if (CollectionUtils.isEmpty(nodeIds)) {
            return List.of();
        }
        return bpmNodeRepo.selectListByQuery(QueryWrapper.create().in(BpmNode::getId, nodeIds));
    }

    public List<BpmProcess> fetchProcessByIds(List<Long> processIds) {
        if (CollectionUtils.isEmpty(processIds)) {
            return List.of();
        }
        return bpmProcessRepo.selectListByIds(processIds);
    }

    public BpmAction fetchActionById(Long actionId) {
        return bpmActionRepo.selectOneById(actionId);
    }

    public List<BpmAction> fetchActionByIds(List<Long> actionIds) {
        if (CollectionUtils.isEmpty(actionIds)) {
            return List.of();
        }
        return bpmActionRepo.selectListByIds(actionIds);
    }

    public List<BpmInstance> fetchInstanceByOuterIds(String key, List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return List.of();
        }
        return bpmInstanceRepo.selectListByQuery(QueryWrapper.create().eq(BpmInstance::getOuterType, key).in(BpmInstance::getOuterId, list));
    }

}
