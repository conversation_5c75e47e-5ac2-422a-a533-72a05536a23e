<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.dounanflowers</groupId>
        <artifactId>dnhhsj-java</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>dnhhsj-common</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.dounanflowers</groupId>
            <artifactId>dnhhsj-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dounanflowers</groupId>
            <artifactId>dnhhsj-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dounanflowers</groupId>
            <artifactId>dnhhsj-third</artifactId>
        </dependency>
        <dependency>
            <groupId>com.drewnoakes</groupId>
            <artifactId>metadata-extractor</artifactId>
            <version>2.18.0</version>
        </dependency>
        <dependency>
            <groupId>org.thymeleaf</groupId>
            <artifactId>thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.79</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/net.coobird/thumbnailator -->
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.20</version>
        </dependency>

    </dependencies>

</project>