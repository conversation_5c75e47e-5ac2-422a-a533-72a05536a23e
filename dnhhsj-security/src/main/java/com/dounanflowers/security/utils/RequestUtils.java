package com.dounanflowers.security.utils;

import com.dounanflowers.security.enums.TokenTypeEnum;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;

public class RequestUtils {

    public static String getToken(HttpServletRequest request, TokenTypeEnum tokenType, String tokenName) {
        return switch (tokenType) {
            case HEADER -> {
                String token = request.getHeader(tokenName);
                if ("Authorization".equals(tokenName) && token != null && token.startsWith("Bearer ")) {
                    token = token.substring(7);
                }
                yield token;
            }
            case PARAMETER -> request.getParameter(tokenName);
            case COOKIE -> {
                Cookie[] cookies = request.getCookies();
                if (cookies == null) {
                    yield null;
                }
                for (Cookie cookie : cookies) {
                    if (tokenName.equals(cookie.getName())) {
                        yield cookie.getValue();
                    }
                }
                yield null;
            }
        };
    }

}
