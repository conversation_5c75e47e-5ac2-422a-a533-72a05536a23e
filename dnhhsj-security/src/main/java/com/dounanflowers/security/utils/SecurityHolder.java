package com.dounanflowers.security.utils;


import com.dounanflowers.security.bean.IUser;
import com.dounanflowers.security.bean.SecurityContext;
import com.dounanflowers.security.bean.SecurityToken;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.enums.TokenTypeEnum;
import com.dounanflowers.security.session.MapSessionCache;
import com.dounanflowers.security.session.SessionCache;

import java.lang.constant.ConstantDesc;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

public class SecurityHolder {

    private final Map<String, SecurityContext> contextMap = new ConcurrentHashMap<>();

    private static SecurityToken token = new SecurityToken();

    private static Function<String, SessionCache> defaultSessionCacheFactory = (name) -> new MapSessionCache();

    protected SecurityHolder() {
        initContext("default");
    }

    private static final class InstanceHolder {
        private static final SecurityHolder INSTANCE = new SecurityHolder();
    }

    public static void setDefaultSessionCacheFactory(Function<String, SessionCache> factory) {
        SecurityHolder.defaultSessionCacheFactory = factory;
    }

    public static void clear() {
        token.clear();
    }

    public static void init(String token) {
        SecurityHolder.token.init(token);
    }

    public static String token() {
        return SecurityHolder.token.token();
    }

    public static <ID extends ConstantDesc, USER extends IUser<ID>> void initTokenSession(SessionInfo<ID, USER> session) {
        SecurityHolder.token.session(session);
    }

    public static <ID extends ConstantDesc, USER extends IUser<ID>> SessionInfo<ID, USER> tokenSession() {
        return SecurityHolder.token.session();
    }

    public static void setToken(SecurityToken token) {
        SecurityHolder.token = token;
    }

    public synchronized void initContext(SecurityContext context) {
        if (contextMap.containsKey(context.getName())) {
            return;
        }
        contextMap.put(context.getName(), context);
    }

    public void initContext(String name) {
        SecurityContext context = new SecurityContext.Builder().name(name).sessionCache(defaultSessionCacheFactory.apply(name)).build();
        initContext(context);
    }

    public static void createContext(SecurityContext context) {
        InstanceHolder.INSTANCE.initContext(context);
    }

    public static void createContext(String name) {
        SecurityContext context = new SecurityContext.Builder().name(name).sessionCache(defaultSessionCacheFactory.apply(name)).build();
        InstanceHolder.INSTANCE.initContext(context);
    }

    public static List<SecurityContext> contexts() {
        return List.copyOf(InstanceHolder.INSTANCE.contextMap.values());
    }

    public static SecurityContext context() {
        return InstanceHolder.INSTANCE.contextMap.get("default");
    }

    public static SecurityContext context(String name) {
        return InstanceHolder.INSTANCE.contextMap.get(name);
    }

    public static TokenTypeEnum tokenType() {
        return context().getTokenType();
    }

    public static TokenTypeEnum tokenType(String name) {
        return context(name).getTokenType();
    }

    public static <ID extends ConstantDesc, USER extends IUser<ID>> SessionInfo<ID, USER> session() {
        SessionInfo<ID, USER> session = tokenSession();
        if (session != null) {
            return session;
        }
        Set<String> keys = InstanceHolder.INSTANCE.contextMap.keySet();
        for (String key : keys) {
            session = InstanceHolder.INSTANCE.contextMap.get(key).getSession(token());
            if (session != null) {
                initTokenSession(session);
                break;
            }
        }
        return session;
    }

    public static <ID extends ConstantDesc, USER extends IUser<ID>> SessionInfo<ID, USER> session(String name) {
        SessionInfo<ID, USER> session = context(name).getSession(token());
        if (session != null) {
            initTokenSession(session);
        }
        return session;
    }

    public static <ID extends ConstantDesc, USER extends IUser<ID>> void update(SessionInfo<ID, USER> session) {
        if (session == null) {
            return;
        }
        Set<String> keys = InstanceHolder.INSTANCE.contextMap.keySet();
        for (String key : keys) {
            SecurityContext securityContext = InstanceHolder.INSTANCE.contextMap.get(key);
            SessionInfo<ID, USER> current = securityContext.getSession(token());
            if (current != null) {
                securityContext.getSessionCache().update(token(), session);
                initTokenSession(session);
                break;
            }
        }
    }

    public static <ID extends ConstantDesc, USER extends IUser<ID>> SessionInfo<ID, USER> login(USER user) {
        return context().login(user);
    }

    public static <ID extends ConstantDesc, USER extends IUser<ID>> SessionInfo<ID, USER> login(String name, USER user) {
        return context(name).login(user);
    }

    public static <ID extends ConstantDesc, USER extends IUser<ID>> boolean hasPermission(String permission) {
        SessionInfo<ID, USER> session = session();
        if (session == null) {
            return false;
        }
        return session.getPermissions().contains(permission);
    }

    public static <ID extends ConstantDesc, USER extends IUser<ID>> boolean hasAnyPermission(String... permissions) {
        if (permissions == null || permissions.length == 0) {
            return false;
        }
        SessionInfo<ID, USER> session = session();
        if (session == null) {
            return false;
        }
        return Arrays.stream(permissions).anyMatch(session.getPermissions()::contains);
    }

    public static <ID extends ConstantDesc, USER extends IUser<ID>> boolean hasAllPermission(String... permissions) {
        if (permissions == null || permissions.length == 0) {
            return false;
        }
        SessionInfo<ID, USER> session = session();
        if (session == null) {
            return false;
        }
        return Arrays.stream(permissions).allMatch(session.getPermissions()::contains);
    }


    public static <ID extends ConstantDesc, USER extends IUser<ID>> boolean hasRole(String role) {
        SessionInfo<ID, USER> session = session();
        if (session == null) {
            return false;
        }
        return session.getRoles().contains(role);
    }

    public static <ID extends ConstantDesc, USER extends IUser<ID>> boolean hasAnyRole(String... roles) {
        if (roles == null || roles.length == 0) {
            return false;
        }
        SessionInfo<ID, USER> session = session();
        if (session == null) {
            return false;
        }
        return Arrays.stream(roles).anyMatch(session.getRoles()::contains);
    }

    public static <ID extends ConstantDesc, USER extends IUser<ID>> boolean hasAllRole(String... roles) {
        if (roles == null || roles.length == 0) {
            return false;
        }
        SessionInfo<ID, USER> session = session();
        if (session == null) {
            return false;
        }
        return Arrays.stream(roles).allMatch(session.getRoles()::contains);
    }

    public static void logout() {
        context().logout(token());
    }

    public static void logout(String name) {
        context(name).logout(token());
    }

}
