package com.dounanflowers.security.filter;

import com.dounanflowers.security.bean.SecurityContext;
import com.dounanflowers.security.utils.RequestUtils;
import com.dounanflowers.security.utils.SecurityHolder;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Slf4j
public class SecurityFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        try {
            for (SecurityContext context : SecurityHolder.contexts()) {
                String token = RequestUtils.getToken(request, context.getTokenType(), context.getTokenName());
                if (token != null) {
                    SecurityHolder.init(token);
                    break;
                }
            }
            filterChain.doFilter(request, response);
        } finally {
            SecurityHolder.clear();
        }
    }

}
