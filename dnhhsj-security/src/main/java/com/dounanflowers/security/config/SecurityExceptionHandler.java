package com.dounanflowers.security.config;

import com.dounanflowers.framework.bean.Result;
import com.dounanflowers.framework.filter.LogFilter;
import com.dounanflowers.framework.utils.ServletUtils;
import com.dounanflowers.security.exception.ForbiddenException;
import com.dounanflowers.security.exception.UnauthorizedException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.Map;

@Slf4j
@ControllerAdvice
@Order(1)
public class SecurityExceptionHandler {

    @ExceptionHandler(ForbiddenException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Result<?> methodArgumentNotValidHandler(ForbiddenException e) {
        log.error(e.getMessage());
        setErrorCode("403");
        return Result.error(403, "无权限访问");
    }

    @ExceptionHandler(UnauthorizedException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Result<?> runHandler(UnauthorizedException e) {
        setErrorCode("401");
        return Result.error(401, e.getMessage());
    }

    private static void setErrorCode(String number) {
        HttpServletRequest request = ServletUtils.getRequest();
        Map<String, String> extraMap = (Map<String, String>) request.getAttribute(LogFilter.LOG_EXTRA_MAP);
        extraMap.put("errorCode", number);
    }


}
