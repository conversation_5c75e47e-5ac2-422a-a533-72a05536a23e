package com.dounanflowers.security.config;

import com.dounanflowers.security.session.RedisSessionCache;
import com.dounanflowers.security.utils.SecurityHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;

@Slf4j
@Order(Integer.MIN_VALUE)
@Configuration
public class SecurityInit {

    public SecurityInit(@Value("${spring.application.name}") String applicationName, StringRedisTemplate redisTemplate) {
        log.info("init redis security cache");
        SecurityHolder.setDefaultSessionCacheFactory((name) -> new RedisSessionCache(applicationName, name, redisTemplate));
    }

}
