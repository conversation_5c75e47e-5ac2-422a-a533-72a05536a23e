package com.dounanflowers.security.config;

import com.dounanflowers.security.bean.SecurityAuditor;
import com.dounanflowers.security.filter.SecurityFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class SecurityConfig {

    @Bean
    @Primary
    public SecurityAuditor auditorAware() {
        return new SecurityAuditor();
    }

    @Bean
    public FilterRegistrationBean<SecurityFilter> securityFilter() {
        FilterRegistrationBean<SecurityFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new SecurityFilter());
        registration.addUrlPatterns("/*");
        registration.setName("securityFilter");
        registration.setOrder(Integer.MIN_VALUE + 1);
        return registration;
    }

}
