package com.dounanflowers.security.config;

import com.dounanflowers.framework.utils.IdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class IdConfig {

    public IdConfig(@Value("${dnhhsj.worker:1}") Long workerId, @Value("${dnhhsj.datacenter:1}") Long datacenterId) {
        IdUtils.init(workerId, datacenterId);
        log.info("IdUtils init success");
    }

}
