package com.dounanflowers.security.event;


import com.dounanflowers.security.bean.IUser;
import com.dounanflowers.security.bean.SessionInfo;

import java.lang.constant.ConstantDesc;

public interface SecurityEventListener<ID extends ConstantDesc, USER extends IUser<ID>> {

    default void onLogin(SessionInfo<ID, USER> sessionInfo) {
    }

    default void onLogout(SessionInfo<ID, USER> sessionInfo) {
    }

}
