package com.dounanflowers.security.event;


import com.dounanflowers.security.bean.SessionInfo;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

@SuppressWarnings({"rawtypes", "unchecked"})
public class SecurityEventBus {

    private final List<SecurityEventListener> listeners = new CopyOnWriteArrayList<>();

    public void register(SecurityEventListener listener) {
        listeners.add(listener);
    }

    public void unregister(SecurityEventListener listener) {
        listeners.remove(listener);
    }

    public void onLogin(SessionInfo sessionInfo) {
        listeners.forEach(listener -> listener.onLogin(sessionInfo));
    }

    public void onLogout(SessionInfo sessionInfo) {
        listeners.forEach(listener -> listener.onLogout(sessionInfo));
    }

}
