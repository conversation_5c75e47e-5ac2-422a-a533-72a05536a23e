package com.dounanflowers.security.intercptor;

import com.dounanflowers.framework.filter.LogFilter;
import com.dounanflowers.framework.utils.ServletUtils;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.bean.IUser;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.exception.UnauthorizedException;
import com.dounanflowers.security.utils.SecurityHolder;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;


@Slf4j
@Aspect
@Component
public class AuthorizedInterceptor {

    @Pointcut("@annotation(com.dounanflowers.security.annotation.Authorized)")
    public void cutMethod() {
    }

    @Pointcut("@within(com.dounanflowers.security.annotation.Authorized)")
    public void cutTarget() {
    }

    @SuppressWarnings("unchecked")
    @Around("cutMethod() || cutTarget()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Object target = joinPoint.getTarget();
        Method currentMethod = target.getClass()
                .getMethod(methodSignature.getName(), methodSignature.getParameterTypes());
        Authorized annotation = currentMethod.getAnnotation(Authorized.class);
        if (annotation == null) {
            annotation = target.getClass().getAnnotation(Authorized.class);
        }
        String[] names = annotation.value();
        for (String name : names) {
            SessionInfo<Long, IUser<Long>> session = SecurityHolder.session(name);
            if (session != null) {
                HttpServletRequest request = ServletUtils.getRequest();
                Map<String, String> extraMap = (Map<String, String>) request.getAttribute(LogFilter.LOG_EXTRA_MAP);
                extraMap.put("userId", session.getUserId().toString());
                return joinPoint.proceed();
            }
        }
        throw new UnauthorizedException("请登录");
    }

}