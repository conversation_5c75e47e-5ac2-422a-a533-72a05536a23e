package com.dounanflowers.security.intercptor;

import com.dounanflowers.security.annotation.Permission;
import com.dounanflowers.security.bean.IUser;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.enums.MatchTypeEnum;
import com.dounanflowers.security.exception.ForbiddenException;
import com.dounanflowers.security.exception.UnauthorizedException;
import com.dounanflowers.security.utils.SecurityHolder;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Aspect
@Component
public class PermissionInterceptor {

    @Pointcut("@annotation(com.dounanflowers.security.annotation.Permission)")
    public void cut() {
    }

    @Around("cut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Object target = joinPoint.getTarget();
        Method method = target.getClass()
                .getMethod(methodSignature.getName(), methodSignature.getParameterTypes());
        Permission annotation = method.getAnnotation(Permission.class);
        if (annotation == null) {
            annotation = target.getClass().getAnnotation(Permission.class);
        }
        String[] permissions = annotation.value();
        MatchTypeEnum match = annotation.match();
        SessionInfo<Long, IUser<Long>> session = SecurityHolder.session();
        if (session == null) {
            throw new UnauthorizedException();
        }
        if (match == MatchTypeEnum.ALL) {
            if (!SecurityHolder.hasAllPermission(permissions)) {
                throw new ForbiddenException();
            }
        } else if (match == MatchTypeEnum.ANY) {
            if (!SecurityHolder.hasAnyPermission(permissions)) {
                throw new ForbiddenException();
            }
        }
        return joinPoint.proceed();
    }

}