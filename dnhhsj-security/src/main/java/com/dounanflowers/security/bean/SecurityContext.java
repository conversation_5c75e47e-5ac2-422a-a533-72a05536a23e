package com.dounanflowers.security.bean;


import com.dounanflowers.framework.utils.DateUtils;
import com.dounanflowers.security.enums.KickedTypeEnum;
import com.dounanflowers.security.enums.TokenTypeEnum;
import com.dounanflowers.security.event.SecurityEventBus;
import com.dounanflowers.security.session.SessionCache;
import com.dounanflowers.security.utils.MD5Utils;
import lombok.Getter;

import java.lang.constant.ConstantDesc;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;

public class SecurityContext {

    @Getter
    private final String name;

    @Getter
    private final String tokenName;

    @Getter
    private final TokenTypeEnum tokenType;

    private final Integer sessionCount;

    @Getter
    private final SessionCache sessionCache;

    private final KickedTypeEnum kickedType;

    private final Long expire;

    @Getter
    private final SecurityEventBus eventBus = new SecurityEventBus();

    public void kick(Long id) {
        List<SessionInfo<ConstantDesc, IUser<ConstantDesc>>> users = sessionCache.getByUserId(id);
        for (SessionInfo<ConstantDesc, IUser<ConstantDesc>> user : users) {
            user.setKicked(true);
            sessionCache.update(user.getToken(), user);
        }
    }

    public void logoutByUserId(Long id) {
        List<SessionInfo<ConstantDesc, IUser<ConstantDesc>>> users = sessionCache.getByUserId(id);
        for (SessionInfo<ConstantDesc, IUser<ConstantDesc>> user : users) {
            logout(user.getToken());
        }
    }

    public static class Builder {
        private String name;
        private String tokenName = "Authorization";
        private TokenTypeEnum tokenType = TokenTypeEnum.HEADER;
        private SessionCache sessionCache;
        private Integer sessionCount = 3;
        private KickedTypeEnum kickedType = KickedTypeEnum.REMOVE;
        private Long expire = SessionCache.DEFAULT_EXPIRE;

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder tokenName(String tokenName) {
            this.tokenName = tokenName;
            return this;
        }

        public Builder tokenType(TokenTypeEnum tokenType) {
            this.tokenType = tokenType;
            return this;
        }

        public Builder sessionCache(SessionCache sessionCache) {
            this.sessionCache = sessionCache;
            return this;
        }

        public Builder sessionCount(Integer sessionCount) {
            this.sessionCount = sessionCount;
            return this;
        }

        public Builder kickedType(KickedTypeEnum kickedType) {
            this.kickedType = kickedType;
            return this;
        }

        public Builder expire(Long expire) {
            this.expire = expire;
            return this;
        }

        public SecurityContext build() {
            return new SecurityContext(this);
        }

    }

    private SecurityContext(Builder builder) {
        this.name = builder.name;
        this.tokenName = builder.tokenName;
        this.tokenType = builder.tokenType;
        this.sessionCache = builder.sessionCache;
        this.sessionCount = builder.sessionCount;
        this.kickedType = builder.kickedType;
        this.expire = builder.expire;
    }

    public <ID extends ConstantDesc, USER extends IUser<ID>> SessionInfo<ID, USER> getSession(String token) {
        return sessionCache.get(token);
    }

    public <ID extends ConstantDesc, USER extends IUser<ID>> SessionInfo<ID, USER> login(USER user) {
        String token = UUID.randomUUID().toString().replace("-", "");
        SessionInfo<ID, USER> sessionInfo = new SessionInfo<>();
        sessionInfo.setToken(token);
        sessionInfo.setUserInfo(user);
        sessionInfo.setName(this.name);
        sessionInfo.setRoles(user.getRoles());
        sessionInfo.setPermissions(user.getPermissions());
        if (expire > 0) {
            sessionInfo.setExpiredAt(DateUtils.getTimestamp(LocalDateTime.now().plusSeconds(expire)));
        }
        sessionInfo.setOrder(System.currentTimeMillis());
        if (sessionCount > 0) {
            List<SessionInfo<ID, IUser<ID>>> users = sessionCache.getByUserId(user.getId());
            users.sort(Comparator.comparing(SessionInfo::getOrder));
            if (users.size() >= sessionCount) {
                while (users.size() >= sessionCount) {
                    switch (kickedType) {
                        case REMOVE -> {
                            SessionInfo<ID, IUser<ID>> remove = users.removeFirst();
                            sessionCache.remove(remove.getToken());
                        }
                        case KICKED -> {
                            SessionInfo<ID, IUser<ID>> remove = users.removeFirst();
                            remove.setKicked(true);
                            sessionCache.update(remove.getToken(), remove);
                        }
                    }
                }
            }
        }
        sessionCache.set(token, sessionInfo, expire);
        eventBus.onLogin(sessionInfo);
        return sessionInfo;
    }

    public <ID extends ConstantDesc, USER extends IUser<ID>> void logout(String token) {
        SessionInfo<ID, USER> remove = sessionCache.remove(token);
        if (remove != null) {
            eventBus.onLogout(remove);
        }
    }

    public String encryptPassword(String password) {
        return MD5Utils.encrypt(password + "dounanflowers");
    }

}

