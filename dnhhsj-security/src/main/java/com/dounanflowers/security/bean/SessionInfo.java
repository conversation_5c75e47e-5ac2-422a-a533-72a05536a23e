package com.dounanflowers.security.bean;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.lang.constant.ConstantDesc;
import java.util.List;


@SuppressWarnings({"unchecked"})
@Data
public class SessionInfo<ID extends ConstantDesc, USER extends IUser<ID>> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String name;

    private String token;

    private Long expiredAt;

    private USER userInfo;

    private long order;

    private boolean kicked;

    private List<String> permissions;

    private List<String> roles;

    public ID getUserId() {
        if (userInfo == null) {
            return null;
        }
        return userInfo.getId();
    }

    public SessionInfo<ID, USER> clearSensitive() {
        SessionInfo<ID, USER> sessionInfo = new SessionInfo<>();
        sessionInfo.name = this.name;
        sessionInfo.token = this.token;
        sessionInfo.permissions = this.permissions;
        sessionInfo.roles = this.roles;
        sessionInfo.expiredAt = this.expiredAt;
        sessionInfo.userInfo = (USER) this.userInfo.clearSensitive();
        return sessionInfo;
    }

}
