package com.dounanflowers.security.bean;

import java.lang.constant.ConstantDesc;

@SuppressWarnings({"rawtypes", "unchecked"})
public class SecurityToken {

    private static final ThreadLocal<String> tokenLocal = InheritableThreadLocal.withInitial(() -> "");

    private static final ThreadLocal<SessionInfo> sessionLocal = InheritableThreadLocal.withInitial(() -> null);

    public void clear() {
        tokenLocal.remove();
        sessionLocal.remove();
    }

    public void init(String token) {
        if (token == null || token.trim().isEmpty()) {
            return;
        }
        tokenLocal.set(token);
    }

    public String token() {
        return tokenLocal.get();
    }

    public <ID extends ConstantDesc, USER extends IUser<ID>> void session(SessionInfo<ID, USER> session) {
        sessionLocal.set(session);
    }

    public <ID extends ConstantDesc, USER extends IUser<ID>> SessionInfo<ID, USER> session() {
        return (SessionInfo<ID, USER>) sessionLocal.get();
    }

}
