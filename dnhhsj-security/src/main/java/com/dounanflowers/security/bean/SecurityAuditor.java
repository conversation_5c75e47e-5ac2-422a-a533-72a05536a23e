package com.dounanflowers.security.bean;


import com.dounanflowers.framework.bean.BaseAuditor;
import com.dounanflowers.security.utils.SecurityHolder;

public class SecurityAuditor extends BaseAuditor {

    public Long getCurrentAuditor() {
        if (SecurityHolder.session() == null) {
            return 0L;
        }
        return SecurityHolder.<Long, IUser<Long>>session().getUserId();
    }

}
