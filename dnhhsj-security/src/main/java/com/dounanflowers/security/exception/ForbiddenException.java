package com.dounanflowers.security.exception;

public class ForbiddenException extends RuntimeException {

    public ForbiddenException(String code, String message) {
        super(message);
        this.code = code;
    }

    public ForbiddenException(String message) {
        super(message);
        this.code = "401";
    }

    public ForbiddenException() {
        super("No Permission");
        this.code = "403";
    }

    private final String code;

    public String getCode() {
        return code;
    }

}
