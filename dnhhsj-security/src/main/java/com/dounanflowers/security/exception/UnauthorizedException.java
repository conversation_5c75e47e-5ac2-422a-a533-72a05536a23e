package com.dounanflowers.security.exception;

public class UnauthorizedException extends RuntimeException {

    public UnauthorizedException(String code, String message) {
        super(message);
        this.code = code;
    }

    public UnauthorizedException(String message) {
        super(message);
        this.code = "401";
    }

    public UnauthorizedException() {
        super("Unauthorized");
        this.code = "401";
    }

    private final String code;

    public String getCode() {
        return code;
    }

}
