package com.dounanflowers.security.session;

import com.dounanflowers.security.bean.IUser;
import com.dounanflowers.security.bean.SessionInfo;

import java.lang.constant.ConstantDesc;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

@SuppressWarnings({"unchecked"})
public class MapSessionCache implements SessionCache {

    Map<String, ConstantDesc> dataMap = new ConcurrentHashMap<>();

    Map<String, Long> expireMap = new ConcurrentHashMap<>();

    Map<ConstantDesc, Map<String, Object>> userMap = new ConcurrentHashMap<>();

    @Override
    public <ID extends ConstantDesc, USER extends IUser<ID>> void set(String key, SessionInfo<ID, USER> value, long timeout) {
        if (timeout == 0 || timeout < NEVER_EXPIRE) {
            return;
        }
        ID userId = value.getUserId();
        dataMap.put(key, userId);
        Map<String, Object> map = userMap.computeIfAbsent(userId, k -> new ConcurrentHashMap<>());
        map.put(key, value);
        expireMap.put(key, (timeout == NEVER_EXPIRE) ? (NEVER_EXPIRE) : (System.currentTimeMillis() + timeout * 1000));
    }

    @Override
    public void update(String key, Object value) {
        if (get(key) == null) {
            return;
        }
        ConstantDesc id = dataMap.get(key);
        Map<String, Object> map = userMap.get(id);
        if (map != null) {
            map.put(key, value);
        }
    }

    @Override
    public <ID extends ConstantDesc, USER extends IUser<ID>> SessionInfo<ID, USER> get(String key) {
        if (key == null || key.isEmpty()) {
            return null;
        }
        Long l = expireMap.get(key);
        if (l != null && l != NEVER_EXPIRE && l < System.currentTimeMillis()) {
            ConstantDesc remove = dataMap.remove(key);
            expireMap.remove(key);
            if (remove == null) {
                return null;
            }
            Map<String, Object> map = userMap.get(remove);
            if (map != null) {
                map.remove(key);
            }
            return null;
        }
        ConstantDesc id = dataMap.get(key);
        if (id == null) {
            return null;
        }
        Map<String, Object> map = userMap.get(id);
        if (map != null && map.containsKey(key)) {
            return (SessionInfo<ID, USER>) map.get(key);
        } else {
            dataMap.remove(key);
            expireMap.remove(key);
        }
        return null;
    }

    @Override
    public <ID extends ConstantDesc, USER extends IUser<ID>> List<SessionInfo<ID, USER>> getByUserId(ID userId) {
        Map<String, Object> map = userMap.get(userId);
        if (map == null) {
            return new ArrayList<>();
        }
        Collection<Object> values = map.values();
        List<SessionInfo<ID, USER>> result = new ArrayList<>();
        for (Object value : values) {
            result.add((SessionInfo<ID, USER>) value);
        }
        return result;
    }

    @Override
    public <ID extends ConstantDesc, USER extends IUser<ID>> SessionInfo<ID, USER> remove(String key) {
        ConstantDesc remove = dataMap.remove(key);
        expireMap.remove(key);
        Map<String, Object> map = userMap.get(remove);
        if (map != null) {
            return (SessionInfo<ID, USER>) map.remove(key);
        }
        return null;
    }

    @Override
    public <ID extends ConstantDesc, USER extends IUser<ID>> List<SessionInfo<ID, USER>> removeByUserId(ID userId) {
        Map<String, Object> map = userMap.remove(userId);
        if (map == null) {
            return null;
        }
        Collection<Object> values = map.values();
        List<SessionInfo<ID, USER>> result = new CopyOnWriteArrayList<>();
        for (Object value : values) {
            SessionInfo<ID, USER> v = (SessionInfo<ID, USER>) value;
            dataMap.remove(v.getToken());
            expireMap.remove(v.getToken());
            result.add(v);
        }
        return result;
    }

    @Override
    public void clear() {
        dataMap.clear();
        expireMap.clear();
        userMap.clear();
    }

}
