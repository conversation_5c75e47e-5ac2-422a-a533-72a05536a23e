package com.dounanflowers.security.session;


import com.dounanflowers.security.bean.IUser;
import com.dounanflowers.security.bean.SessionInfo;

import java.lang.constant.ConstantDesc;
import java.util.List;

public interface SessionCache {

    long NEVER_EXPIRE = -1L;

    long DEFAULT_EXPIRE = 60 * 60 * 24 * 365L;

    <ID extends ConstantDesc, USER extends IUser<ID>> void set(String key, SessionInfo<ID, USER> value, long timeout);

    void update(String key, Object value);

    <ID extends ConstantDesc, USER extends IUser<ID>> SessionInfo<ID, USER> get(String key);

    <ID extends ConstantDesc, US<PERSON> extends IUser<ID>> List<SessionInfo<ID, USER>> getByUserId(ID userId);

    <ID extends ConstantDesc, USER extends IUser<ID>> SessionInfo<ID, USER> remove(String key);

    <ID extends ConstantDesc, USER extends IUser<ID>> List<SessionInfo<ID, USER>> removeByUserId(ID userId);

    void clear();

}
