package com.dounanflowers.security.session;

import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.SerializableUtils;
import com.dounanflowers.security.bean.IUser;
import com.dounanflowers.security.bean.SessionInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.lang.constant.ConstantDesc;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class RedisSessionCache implements SessionCache {

    private final static String prefix = "session:";

    private final String name;

    private final StringRedisTemplate redisTemplate;

    public RedisSessionCache(String application, String name, StringRedisTemplate redisTemplate) {
        if (application == null) {
            application = "default";
        }
        if (name == null) {
            name = "default";
        }
        this.name = application + ":" + name + ":";
        this.redisTemplate = redisTemplate;
    }

    @Override
    public <ID extends ConstantDesc, USER extends IUser<ID>> void set(String key, SessionInfo<ID, USER> value, long timeout) {
        ID userId = value.getUserId();
        String idKey = valueToString(userId);
        redisTemplate.opsForValue().set(key(key), idKey);
        redisTemplate.opsForHash().put(userId(idKey), key, valueToString(value));
        if (timeout > 0) {
            redisTemplate.expire(key(key), timeout, java.util.concurrent.TimeUnit.SECONDS);
            redisTemplate.expire(userId(idKey), timeout, java.util.concurrent.TimeUnit.SECONDS);
        }
    }

    private String valueToString(Object value) {
        if (value == null) {
            return null;
        }
        if (BeanUtils.isPrimitive(value.getClass())) {
            return value.toString();
        }
        return SerializableUtils.toString(value);
    }

    private Object valueFromString(String value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        return SerializableUtils.fromString(value);
    }


    @Override
    public void update(String key, Object value) {
        String idKey = redisTemplate.opsForValue().get(key(key));
        if (idKey == null) {
            return;
        }
        redisTemplate.opsForHash().put(userId(idKey), key, valueToString(value));
    }

    @SuppressWarnings("unchecked")
    @Override
    public <ID extends ConstantDesc, USER extends IUser<ID>> SessionInfo<ID, USER> get(String key) {
        if (key == null || key.isEmpty()) {
            return null;
        }
        try {
            String idKey = redisTemplate.opsForValue().get(key(key));
            if (idKey == null) {
                return null;
            }
            String value = (String) redisTemplate.opsForHash().get(userId(idKey), key);
            if (value == null) {
                return null;
            }
            return (SessionInfo<ID, USER>) valueFromString(value);
        } catch (Exception e) {
            log.error("Failed to get session info", e);
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public <ID extends ConstantDesc, USER extends IUser<ID>> List<SessionInfo<ID, USER>> getByUserId(ID userId) {
        String idKey = valueToString(userId);
        try {
            return redisTemplate.opsForHash().values(userId(idKey)).stream().map(v -> (String) v).map(this::valueFromString).map(o -> (SessionInfo<ID, USER>) o).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Failed to get session info", e);
            return new ArrayList<>();
        }
    }

    private String key(String key) {
        return prefix + name + key;
    }

    private String userId(String key) {
        return prefix + name + "user:" + key;
    }

    @Override
    public <ID extends ConstantDesc, USER extends IUser<ID>> SessionInfo<ID, USER> remove(String key) {
        String idKey = redisTemplate.opsForValue().get(key(key));
        if (idKey == null) {
            return null;
        }
        redisTemplate.opsForHash().delete(userId(idKey), key);
        redisTemplate.delete(key(key));
        return null;
    }

    @Override
    public <ID extends ConstantDesc, USER extends IUser<ID>> List<SessionInfo<ID, USER>> removeByUserId(ID userId) {
        String idKey = valueToString(userId);
        List<SessionInfo<ID, USER>> sessions = getByUserId(userId);
        redisTemplate.delete(userId(idKey));
        for (SessionInfo<ID, USER> session : sessions) {
            redisTemplate.delete(key(session.getToken()));
        }
        return sessions;
    }

    @Override
    public void clear() {
        Set<String> keys = redisTemplate.keys(prefix + name + ":*");
        if (keys == null) {
            return;
        }
        redisTemplate.delete(keys);
    }

}
