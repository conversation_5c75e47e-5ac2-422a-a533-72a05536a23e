server:
  undertow:
    buffer-size: 1024
    direct-buffers: true
  port: 8888
spring:
  application:
    name: dnhhsj
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  datasource:
    primary:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: org.postgresql.Driver
      url: ${DB_URL:***************************************}
      hikari:
        pool-name: DatebookHikariCP
        minimum-idle: 5
        maximum-pool-size: 15
        max-lifetime: 1800000
        connection-timeout: 30000
        username: ${DB_USER:postgres}
        password: ${DB_PASS:HbmApVW7REMBAPyjpy5H}
    mysql:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ${MYSQL_DB_URL:*****************************************************************************************************}
      hikari:
        pool-name: DatebookHikariCP
        minimum-idle: 5
        maximum-pool-size: 15
        max-lifetime: 1800000
        connection-timeout: 30000
        username: ${MYSQL_DB_USER:root}
        password: ${MYSQL_DB_PASS:Dnhh@123}
  data:
    redis:
      host: ${REDIS_HOST:********}
      port: ${REDIS_PORT:7490}
      database: ${REDIS_DB:4}
      password: ${REDIS_PASSWORD:}
  rabbitmq:
    host: ${MQ_HOST:********}
    port: ${MQ_PORT:6783}
    username: ${MQ_USER:rabbit}
    password: ${MQ_PASS:rabbit321.}
    virtual-host: ${MQ_VHOST:default}

mybatis-flex:
  configuration:
    mapUnderscoreToCamelCase: true

weather:
  key: 366474a7570f48b7a608a52e2804512e

park:
  apiPrefix: http://swapi.bluecardsoft.com/bcopenapi/out/
  apiKey: 65f2c3b83aaace0641c6e46f40fc93be

isProd: ${IS_PROD:false}

bi:
  people:
#    url: ${BI_PEOPLE_URL:http://**************:7041}
    url: ${BI_PEOPLE_URL:http://********:7041}
    login:
      name: ${BI_PEOPLE_LOGIN_NAME:system}
      password: ${BI_PEOPLE_LOGIN_PASSWORD:Public8118-}