package com.dounanflowers.config;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * 测试环境配置类
 * 用于在测试环境中排除WebSocket相关配置
 */
@Configuration
public class TestConfig {
    
    /**
     * 提供一个模拟的ServerEndpointExporter，在测试环境中使用
     * 这个Bean会覆盖WebSocketConfiguration中的serverEndpointExporter
     */
    @MockBean
    private ServerEndpointExporter serverEndpointExporter;
}
