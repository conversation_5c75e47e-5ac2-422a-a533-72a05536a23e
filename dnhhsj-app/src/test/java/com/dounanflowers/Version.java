package com.dounanflowers;

import org.apache.commons.lang3.StringUtils;

import java.nio.file.Files;
import java.nio.file.Path;

public class Version {
    public static void main(String[] args) {
        try {
            Path path = Path.of("./version");
            if (!Files.exists(path)) {
                Files.createDirectories(path.getParent());
                Files.createFile(path);
            }
            String version = Files.readString(path);
            if (StringUtils.isNotBlank(version)) {
                version = patch(version);
                // version = minor(version);
                // version = major(version);
                Files.writeString(path, version);
            } else {
                // set the version to 1.0.0
                Files.writeString(path, "1.0.0");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static String patch(String version) {
        // patch the version
        String[] parts = version.split("\\.");
        int patch = Integer.parseInt(parts[2]);
        patch++;
        version = parts[0] + "." + parts[1] + "." + patch;
        return version;
    }
    // 升中版本号

    private static String minor(String version) {
        // patch the version
        String[] parts = version.split("\\.");
        int minor = Integer.parseInt(parts[1]);
        minor++;
        version = parts[0] + "." + minor + ".0";
        return version;
    }

    private static String major(String version) {
        // patch the version
        String[] parts = version.split("\\.");
        int major = Integer.parseInt(parts[0]);
        major++;
        version = major + ".0.0";
        return version;
    }
}
