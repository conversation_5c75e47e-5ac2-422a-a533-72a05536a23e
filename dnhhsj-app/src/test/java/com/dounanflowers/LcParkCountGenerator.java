package com.dounanflowers;

import com.dounanflowers.common.entity.LcPark;
import com.dounanflowers.common.entity.LcParkCount;
import com.dounanflowers.common.manager.LcManager;
import com.dounanflowers.common.repo.LcParkCountRepo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
@Slf4j
public class LcParkCountGenerator {

    @Autowired
    private LcManager lcManager;

    @Autowired
    private LcParkCountRepo lcParkCountRepo;

    @Test
    public void insertFakeData() {
        LocalDateTime start = LocalDateTime.now().plusMinutes(-60 * 24 * 30);
        LcPark lcPark = lcManager.fetchFistLcPark();
        Integer freeSpaceCount = lcPark.getFreeSpaceCount();
        Integer spaceCount = lcPark.getSpaceCount();


        List<LcParkCount> lcParkCountList = new ArrayList<>();
        int endIndex = 60 * 24 * 90;
        for (int i = 0; i <= endIndex; i++) {
            int diff = (int) (Math.round(Math.random() * 20) - 10);
            freeSpaceCount = Math.max(0, Math.min(spaceCount, freeSpaceCount + diff));

            LcParkCount lcParkCount = new LcParkCount();
            lcParkCount.setLcParkId(lcPark.getId());
            lcParkCount.setFreeSpaceCount(freeSpaceCount);
            lcParkCount.setSpaceCount(spaceCount);
            lcParkCount.setInParkCount(spaceCount - freeSpaceCount);
            long epochMilli = start.plusMinutes(i).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            lcParkCount.setMinutes(Math.round((float) epochMilli / 1000 / 60));
            lcParkCount.setCreatedAt(start.plusMinutes(i));
            lcParkCountList.add(lcParkCount);

            if (i % 1000 == 0 || i == endIndex) {
                System.out.println(i);
                lcParkCountRepo.insertBatchSelective(lcParkCountList);
                lcParkCountList = new ArrayList<>();
            }
        }

    }
}
