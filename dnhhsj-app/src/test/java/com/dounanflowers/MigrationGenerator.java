package com.dounanflowers;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.generator.bean.GenColumn;
import com.dounanflowers.generator.bean.PGColumn;
import com.dounanflowers.generator.bean.PGTable;
import com.dounanflowers.generator.repo.GenRepo;
import com.mybatisflex.annotation.Table;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.util.ClassUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest(classes = TestApplication.class)
@Slf4j
public class MigrationGenerator {

    @Autowired
    GenRepo genRepo;

    @Test
    void generateTableSql() {
        String version = getVersion();
        Map<String, Class<?>> handlerMap = new HashMap<>();
        String packageName = MigrationGenerator.class.getPackage().getName();
        // spring工具类，可以获取指定路径下的全部类
        ResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
        Path path = Path.of("../migration/" + version + "/table.sql");
        try {
            String pattern = ResourcePatternResolver.CLASSPATH_ALL_URL_PREFIX +
                    ClassUtils.convertClassNameToResourcePath(packageName) + "/**/*.class";
            Resource[] resources = resourcePatternResolver.getResources(pattern);
            // MetadataReader 的工厂类
            MetadataReaderFactory readerfactory = new CachingMetadataReaderFactory(resourcePatternResolver);
            for (Resource resource : resources) {
                // 用于读取类信息
                MetadataReader reader = readerfactory.getMetadataReader(resource);
                // 扫描到的class
                String classname = reader.getClassMetadata().getClassName();
                Class<?> clazz = Class.forName(classname);
                // 判断是否有指定主解
                Table anno = clazz.getAnnotation(Table.class);
                if (anno != null && BaseEntity.class.isAssignableFrom(clazz)) {
                    // 将注解中的类型值作为key，对应的类作为 value
                    handlerMap.put(anno.value(), clazz);
                }
            }
        } catch (IOException | ClassNotFoundException e) {
            e.printStackTrace();
        }
        List<PGTable> o = genRepo.selectTable();
        Set<String> tableNames = o.stream().map(PGTable::getTableName).collect(Collectors.toSet());


        for (Map.Entry<String, Class<?>> entry : handlerMap.entrySet()) {
            List<String> sqlList = new ArrayList<>();
            String tableName = entry.getKey();
            Class<?> clazz = entry.getValue();
            List<PGColumn> pgColumns = genRepo.selectColumn(tableName);
            List<GenColumn> classColumnTypes = getFields(clazz);
            if (tableNames.contains(tableName)) {
                // 表存在，对比字段并生成修改语句
                sqlList.addAll(generateAlterTableStatements(tableName, pgColumns, classColumnTypes));
            } else {
                sqlList.addAll(generateCreateTableStatement(tableName, classColumnTypes));
            }
            if (!sqlList.isEmpty()) {
                sqlList.addFirst("------------------------");
                sqlList.add("\n");
                sqlList = sqlList.stream().toList();
                String content = String.join("\n", sqlList);
                try {
                    genRepo.executeSql(content);
                    if (!Files.exists(path)) {
                        Files.createDirectories(path.getParent());
                        Files.createFile(path);
                    }
                    String fileContent = Files.readString(path).replace("\r\n", "\n");
                    if (fileContent.contains(content)) {
                        continue;
                    }
                    Files.write(path, sqlList, StandardOpenOption.APPEND);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    private String getVersion() {
        try {
            Path versionPath = Path.of("../version");
            if (!Files.exists(versionPath)) {
                return "1.0.0";
            }
            return Files.readString(versionPath);
        } catch (Exception ignored) {
        }
        return "1.0.0";
    }

    private List<String> generateAlterTableStatements(String tableName, List<PGColumn> pgColumns, List<GenColumn> classColumns) {
        List<String> sqlList = new ArrayList<>();
        // 找出需要添加的列
        Map<String, PGColumn> dbColumnTypes = pgColumns.stream().collect(Collectors.toMap(PGColumn::getName, c -> c));
        Map<String, GenColumn> classColumnMap = classColumns.stream().collect(Collectors.toMap(GenColumn::getName, c -> c));
        Set<String> columnsToAdd = new HashSet<>(classColumnMap.keySet());
        columnsToAdd.removeAll(dbColumnTypes.keySet());

        // 找出需要删除的列
        Set<String> columnsToDrop = new HashSet<>(dbColumnTypes.keySet());
        columnsToDrop.removeAll(classColumnMap.keySet());

        // 找出需要修改的列
        Set<String> columnsToModify = new HashSet<>();
        Set<String> columnsToModifyNotNull = new HashSet<>();
        Set<String> columnsToModifyNull = new HashSet<>();
        Set<String> columnsToModifyComment = new HashSet<>();

        for (String column : dbColumnTypes.keySet()) {
            if (!classColumnMap.containsKey(column)) {
                continue;
            }
            PGColumn pgColumn = dbColumnTypes.get(column);
            String type = getSqlTypeFromPgType(pgColumn.getType());
            GenColumn classColumn = classColumnMap.get(column);

            // 检查类型是否需要修改
            if (!type.equalsIgnoreCase(classColumn.getType())) {
                columnsToModify.add(column);
            }

            // 检查null约束是否需要修改
            if (!pgColumn.isNullable() == classColumn.isNullable() && !classColumn.isPrimary()) {
                if (classColumn.isNullable()) {
                    columnsToModifyNull.add(column);
                } else {
                    columnsToModifyNotNull.add(column);
                }
            }

            // 检查注释是否需要修改
            if (!Objects.equals(pgColumn.getComment(), classColumn.getComment())) {
                columnsToModifyComment.add(column);
            }

        }

        // 生成添加列的SQL语句
        if (CollectionUtils.isNotEmpty(columnsToAdd)) {
            sqlList.add("-- add " + tableName + " columns");
            for (String column : columnsToAdd) {
                GenColumn genColumn = classColumnMap.get(column);
                String sql = "alter table if exists " + tableName + " add column if not exists " + column + " " + genColumn.getType();
                if (StringUtils.isNotBlank(genColumn.getDefaultValue())) {
                    sql += " default " + genColumn.getDefaultValue();
                }
                if (!genColumn.isNullable()) {
                    sql += " not null";
                }
                sql += ";";
                sqlList.add(sql);
            }
        }

        // if (CollectionUtils.isNotEmpty(columnsToDrop)) {
        //     sqlList.add("-- drop " + tableName + " columns");
        //     // 生成删除列的SQL语句
        //     for (String column : columnsToDrop) {
        //         String sql = "alter table if exists " + tableName + " drop column if exists " + column + ";";
        //         sqlList.add(sql);
        //     }
        // }

        // 生成修改列类型的SQL语句
        if (CollectionUtils.isNotEmpty(columnsToModify)) {
            sqlList.add("-- modify " + tableName + " columns type");
            for (String column : columnsToModify) {
                GenColumn genColumn = classColumnMap.get(column);
                String sql = "alter table if exists " + tableName + " alter column " + column + " type " + genColumn.getType() + ";";
                sqlList.add(sql);
            }
        }

        // 生成修改null约束的SQL语句
        if (CollectionUtils.isNotEmpty(columnsToModifyNotNull)) {
            sqlList.add("-- modify " + tableName + " columns not null");
            for (String column : columnsToModifyNotNull) {
                GenColumn genColumn = classColumnMap.get(column);
                if (StringUtils.isNotBlank(genColumn.getDefaultValue())) {
                    String sql = "alter table if exists " + tableName + " alter column " + column + " set default " + genColumn.getDefaultValue() + ";";
                    sqlList.add(sql);
                }
                String sql = "alter table if exists " + tableName + " alter column " + column + " set not null;";
                sqlList.add(sql);
            }
        }

        if (CollectionUtils.isNotEmpty(columnsToModifyNull)) {
            sqlList.add("-- modify " + tableName + " columns null");
            for (String column : columnsToModifyNull) {
                GenColumn genColumn = classColumnMap.get(column);
                if (StringUtils.isNotBlank(genColumn.getDefaultValue())) {
                    String sql = "alter table if exists " + tableName + " alter column " + column + " set default " + genColumn.getDefaultValue() + ";";
                    sqlList.add(sql);
                } else {
                    String sql = "alter table if exists " + tableName + " alter column " + column + " drop default;";
                    sqlList.add(sql);
                }
                String sql = "alter table if exists " + tableName + " alter column " + column + " drop not null;";
                sqlList.add(sql);
            }
        }

        // 生成修改注释的SQL语句
        if (CollectionUtils.isNotEmpty(columnsToModifyComment)) {
            sqlList.add("-- modify " + tableName + " columns comment");
            for (String column : columnsToModifyComment) {
                GenColumn genColumn = classColumnMap.get(column);
                String sql = "comment on column " + tableName + "." + column + " is '" + genColumn.getComment() + "';";
                sqlList.add(sql);
            }
        }

        return sqlList;
    }

    private String getSqlType(Class<?> fieldType) {
        if (fieldType.equals(String.class)) {
            return "varchar";
        } else if (fieldType.equals(Integer.class) || fieldType.equals(int.class)) {
            return "integer";
        } else if (fieldType.equals(Long.class) || fieldType.equals(long.class)) {
            return "bigint";
        } else if (fieldType.equals(Float.class) || fieldType.equals(float.class) || BigDecimal.class.equals(fieldType)) {
            return "numeric";
        } else if (fieldType.equals(Double.class) || fieldType.equals(double.class)) {
            return "numeric";
        } else if (fieldType.equals(Boolean.class) || fieldType.equals(boolean.class)) {
            return "boolean";
        } else if (fieldType.equals(Date.class) || fieldType.equals(LocalDateTime.class) || fieldType.equals(LocalDate.class)) {
            return "timestamp";
        } else if (BaseEnum.class.isAssignableFrom(fieldType)) {
            return "smallint";
        } else if (fieldType.equals(byte[].class)) {
            return "bytea";
        } else {
            throw new IllegalArgumentException("Unsupported field type: " + fieldType.getName());
        }
    }

    private String getSqlTypeFromPgType(String pgType) {
        pgType = pgType.replace("character varying", "varchar");
        pgType = pgType.replace("timestamp without time zone", "timestamp");
        return pgType;
    }

    private List<String> generateCreateTableStatement(String tableName, List<GenColumn> classColumnTypes) {
        List<String> sqlList = new ArrayList<>();
        // 生成创建表的SQL语句
        StringBuilder sql = new StringBuilder("-- " + tableName + " table\n");
        StringBuilder comment = new StringBuilder();
        sql.append("create table if not exists ").append(tableName).append(" (");
        for (GenColumn column : classColumnTypes) {
            sql.append("\n    ").append(column.getName()).append(" ").append(column.getType());
            if (StringUtils.isNotBlank(column.getDefaultValue())) {
                sql.append(" default ").append(column.getDefaultValue());
            }
            if (!column.isNullable()) {
                sql.append(" not null");
            }
            if (column.isPrimary()) {
                sql.append(" primary key");
            }
            sql.append(",");
            if (StringUtils.isNotBlank(column.getComment())) {
                comment.append("comment on column ").append(tableName).append(".").append(column.getName()).append(" is '").append(column.getComment()).append("';\n");
            }
        }
        sql.setLength(sql.length() - 1); // 去掉最后一个逗号
        sql.append("\n);\n");
        sqlList.add(sql.toString());
        sqlList.add("-- " + tableName + " comment");
        sqlList.add(comment.toString());
        return sqlList;
    }

    private List<GenColumn> getFields(Class<?> clazz) {
        List<GenColumn> fields = new ArrayList<>();
        Class<?> superclass = clazz.getSuperclass();
        if (superclass != null) {
            fields.addAll(getFields(superclass));
        }
        Field[] declaredFields = clazz.getDeclaredFields();
        for (Field field : declaredFields) {
            if (field.getName().equals("serialVersionUID")) {
                continue;
            }
            ColumnDef columnDef = field.getAnnotation(ColumnDef.class);
            if (columnDef != null) {
                if (columnDef.ignore()) {
                    continue;
                }
            }
            GenColumn genColumn = new GenColumn(field.getName(), getSqlType(field.getType()));
            if (columnDef != null) {
                genColumn.updateByDef(columnDef);
            }
            fields.add(genColumn);
        }
        return fields;
    }

}
