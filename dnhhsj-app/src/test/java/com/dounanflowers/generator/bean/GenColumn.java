package com.dounanflowers.generator.bean;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.google.common.base.CaseFormat;
import lombok.Data;

@Data
public class GenColumn {

    private String name;

    private String type;

    private String defaultValue;

    private boolean nullable = true;

    private String comment;

    private boolean primary = false;

    public void updateByDef(ColumnDef def) {
        if (!def.name().isEmpty()) {
            this.name = def.name();
        }
        if (!def.type().isEmpty()) {
            this.type = def.type().toLowerCase();
        }
        this.nullable = def.nullable();
        this.defaultValue = def.defaultValue();
        this.comment = def.comment();
        this.primary = def.primary();
    }

    public GenColumn(String name, String type) {
        this.name = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, name);
        this.type = type;
    }

}
