package com.dounanflowers;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration;
import org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

/**
 * 测试专用的应用配置类
 * 排除WebSocket相关配置
 */
@SpringBootApplication
@EnableAutoConfiguration(exclude = {
    WebSocketServletAutoConfiguration.class
})
@ComponentScan(
    basePackages = "com.dounanflowers",
    excludeFilters = {
        @ComponentScan.Filter(
            type = FilterType.REGEX,
            pattern = "com\\.dounanflowers\\.admin\\.config\\.WebSocketConfiguration"
        )
    }
)
public class TestApplication {
    public static void main(String[] args) {
        SpringApplication.run(TestApplication.class, args);
    }
}
