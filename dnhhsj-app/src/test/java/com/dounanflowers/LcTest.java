package com.dounanflowers;

import com.dounanflowers.admin.bpm.enums.ProcessEnum;
import com.dounanflowers.admin.service.AdminProductService;
import com.dounanflowers.admin.service.AdminUserService;
import com.dounanflowers.bpm.entity.BpmInstance;
import com.dounanflowers.bpm.entity.BpmNodeInstance;
import com.dounanflowers.bpm.repo.BpmNodeInstanceRepo;
import com.dounanflowers.bpm.service.BpmService;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.*;
import com.dounanflowers.common.manager.*;
import com.dounanflowers.common.repo.*;
import com.dounanflowers.common.service.ClientActionCountService;
import com.dounanflowers.common.service.LcService;
import com.dounanflowers.common.entity.Setting;
import com.dounanflowers.common.manager.SystemManager;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.ThirdPartyHolder;
import com.dounanflowers.third.bo.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.swing.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SpringBootTest(classes = TestApplication.class)
@Slf4j
public class LcTest {
    @Autowired
    private AdminUserManager adminUserManager;

    @Autowired
    private LcService lcService;
    @Autowired
    private ClientUserManager clientUserManager;
    @Autowired
    private BillManager billManager;
    @Autowired
    private VehicleManager vehicleManager;
    @Autowired
    private VehicleRepo vehicleRepo;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private BpmNodeInstanceRepo bpmNodeInstanceRepo;


    @Autowired
    private WithdrawManager withdrawManager;

    @Autowired
    private ClientActionCountService clientActionCountService;
    @Autowired
    private ClientActionCountRepo clientActionCountRepo;

    @Autowired
    private AdminUserService adminUserService;

    @Autowired
    private ProductManager productManager;

    @Autowired
    private StoreRepo storeRepo;

    @Autowired
    private ProductOrderRepo productOrderRepo;

    @Autowired
    private WithdrawAccountRepo withdrawAccountRepo;

    @Autowired
    private WithdrawAccountRecordRepo withdrawAccountRecordRepo;
    @Autowired
    private AdminProductService adminProductService;

    @Autowired
    private ProductOrderItemRepo productOrderItemRepo;

    @Autowired
    private SystemManager systemManager;

    @Autowired
    private EnergyCellRepo energyCellRepo;

    @Autowired
    private PropertyCellRepo propertyCellRepo;

    @Autowired
    private PropertyCellManager propertyCellManager;


    @Test
    public void testWhitelistSave() {
        AdminUser user = adminUserManager.fetchByMobile("***********");
        lcService.whitelistSave(
            "川A12345",
            user.getId(),
            LocalDateTime.now(),
            LocalDateTime.now().plusDays(5)
        );
    }

    @Test
    public void testWhitelistQuery() {
        ParkWhiteBo res = lcService.whitelistQuery("川A12345");
        System.out.println(res);
    }

    @Test
    public void testBillInsert() {
        ClientUser user = clientUserManager.fetchById(1337135745248399360L);
        for (int i = 0; i < 10; i++) {
            Bill bill = new Bill();
            bill.setUserId(user.getId());
            bill.setUserType(UserTypeEnum.CLIENT);
            bill.setType(BillTypeEnum.PARK);
            bill.setEntityId(1337135745248399360L);
            bill.setEntityModel("lc_order");
            bill.setUserModel("client_user");
            bill.setOriginalMoneyCent(10);
            bill.setCouponDiscountCent(0);
            bill.setOrderMoneyCent(10);
            bill.setPayStatus(BillPayStatusEnum.UNPAID);
            bill.setPayStatus(BillPayStatusEnum.PAID);
            bill.setPaidMoneyCent(10);
            bill.setPaidAt(LocalDateTime.now());
            billManager.saveBill(bill);
        }

    }

    @Test
    public void vehicleRejectReason() {

        List<Vehicle> vehicles = vehicleRepo.selectListByQuery(
                QueryWrapper.create().eq(Vehicle::getCheckStatus, VehicleCheckStatusEnum.REJECTED).isNull(Vehicle::getCheckRejectReason)
        );

        for (Vehicle vehicle : vehicles) {
            System.out.println(vehicle.getId());
            BpmInstance bi = bpmService.getInstanceByOuter(ProcessEnum.VEHICLE_CHECK.getKey(), vehicle.getId());
            if(bi != null) {
                BpmNodeInstance node = bpmNodeInstanceRepo.selectOneByQuery(QueryWrapper.create().eq(BpmNodeInstance::getInstanceId, bi.getId()).isNotNull(BpmNodeInstance::getOperatedReason));
                if(node != null) {
                    vehicle.setCheckRejectReason(node.getOperatedReason());
                    vehicle.setCheckRemark(node.getRemark());
                    vehicleManager.save(vehicle);
                    System.out.println(vehicle.getCheckRejectReason());
                }
            }
        }
    }

    @Test
    public void vehicleRootId() {
        List<Vehicle> vehicles = vehicleRepo.selectListByQuery(QueryWrapper.create().isNull(Vehicle::getRootId).orderBy(Vehicle::getId, true));
        Map<String, Long> plateMap = new HashMap<>();
        for (Vehicle vehicle : vehicles) {
            if(plateMap.get(vehicle.getNumber()) != null) {
                vehicle.setRootId(plateMap.get(vehicle.getNumber()));
            } else {
                vehicle.setRootId(vehicle.getId());
            }
            vehicleManager.save(vehicle);
            plateMap.put(vehicle.getNumber(), vehicle.getRootId());
        }
    }

    @Test
    public void mockSomeClientActionCount() {
        LocalDateTime startDate = LocalDateTime.now().toLocalDate().atStartOfDay().minusDays(10);
        LocalDateTime endDate = LocalDateTime.now().toLocalDate().atStartOfDay().plusDays(10);
        List<String> keys = List.of(
                "游乐:首页点击Tabbar",
                "游乐:我的点击Tabbar",
                "游乐:首页去看看",
                "游乐列表:首页门票预约",
                "游乐列表:首页美食推荐",
                "游乐列表:分享"
        );

        while (startDate.isBefore(endDate)) {

            for (String key : keys) {
                Integer count = (int) (Math.random() * 10);
                clientActionCountRepo.insert(new ClientActionCount().setTime(startDate).setKey(key).setCount(count).setTimeUnit("h"));
                ClientActionCount dayCount = clientActionCountRepo.selectOneByQuery(
                        QueryWrapper.create()
                                .eq(ClientActionCount::getTime, startDate.toLocalDate().atStartOfDay())
                                .eq(ClientActionCount::getKey, key)
                                .eq(ClientActionCount::getTimeUnit, "d")
                );
                if(dayCount == null) {
                    clientActionCountRepo.insert(new ClientActionCount().setTime(startDate).setKey(key).setCount(count).setTimeUnit("d"));
                } else {
                    dayCount.setCount(dayCount.getCount() + count);
                    clientActionCountRepo.save(dayCount);
                }
            }
            startDate = startDate.plusHours(1);
        }
    }

    @Test
    public void mockLogin() {
        String mobile = "13888135678";
//        // client
//        ClientUser user = clientUserManager.fetchByMobile(mobile);
//        ClientUserBo bo = BeanUtils.copy(user, ClientUserBo.class);
//        SessionInfo<Long, ClientUserBo> session = SecurityHolder.login(bo);
//        System.out.println(session.getToken());

        // shop
        AdminUserBo bo = adminUserService.loginByMobile(mobile);
        SessionInfo<Long, AdminUserBo> session = SecurityHolder.context("shop").login(bo);
        System.out.println(session.getToken());


    }

    /** 核销订单 **/
    @Test
    public void testCheckOrder() {
       Long productOrderItemId = 1360363307835461632L;
       ProductOrderItem productOrderItem = productManager.fetchOrderItemById(productOrderItemId);
       ProductOrder order = productManager.getOrderWithItems(productOrderItem.getOrderId());
       Store store = storeRepo.selectOneById(order.getStoreId());
       System.out.println(store);
       adminProductService.checkOrder(productOrderItem.getCode(), store.getAdminUserId());

    }

    @Test
    public void testWgw() {
//        ThirdPartyHolder.wuguanwangService().getUser("13888135678", "362329199610013824");
//        ThirdPartyHolder.wuguanwangService().getUser("18314527830", "362329199610013823");
//        ThirdPartyHolder.wuguanwangService().getCell("18314527830", "362329199610013823");
        ThirdPartyHolder.wuguanwangService().queryCellEnergyLeft("PWGL_1","6-1-127");
    }

    @Test
    public void testQueryRoomCheckLog() {
        // 测试获取单元/商铺结算扣费记录
        List<WgwEnergyCheckLogBo> logs = ThirdPartyHolder.wuguanwangService().queryRoomCheckLog(
                "PWGL_1", // 商铺编号
                "1-1-1008", // 商铺编号
                LocalDate.now().minusMonths(1), // 一个月前
                LocalDate.now() // 当前日期
        );
        System.out.println("Found " + logs.size() + " records");
        logs.forEach(System.out::println);
    }

    @Test
    public void testQueryRoomPayLog() {
        // 测试查询单元/商铺历史充值记录
        List<WgwEnergyPayLogBo> logs = ThirdPartyHolder.wuguanwangService().queryRoomPayLog(
                "PWGL_1",
                "1-1-1008", // 商铺编号
                LocalDate.now().minusMonths(1), // 一个月前
                LocalDate.now() // 当前日期
        );
        System.out.println("Found " + logs.size() + " recharge records");
        logs.forEach(System.out::println);
    }

    @Test
    public void testGetUserWithdrawAccount() {
        Long userId = 1357421254801494016L;
        WithdrawAccount account = withdrawManager.getAccountByUserId(userId);
        System.out.println(account);
    }

//    @Test
//    public void testWhitelistDelete() {
//        lcService.whitelistDelete("川A12345");
//    }
    @Test
    public void testWithdrawAccount() {
        List<Store> stores = storeRepo.selectAll();
        Map<Long, Store> storeMap = stores.stream().collect(Collectors.toMap(Store::getId, v -> v));



        List<ProductOrderItem> productOrderItems = productOrderItemRepo.selectListByQuery(QueryWrapper.create().isNotNull(ProductOrderItem::getUseTime));
        for (ProductOrderItem orderItem : productOrderItems) {
            ProductOrder order = productOrderRepo.selectOneById(orderItem.getOrderId());
            Store store = storeMap.get(order.getStoreId());
            WithdrawAccountRecord war = withdrawAccountRecordRepo.selectOneByQuery(QueryWrapper.create().eq(WithdrawAccountRecord::getRelatedId, orderItem.getId()));

            if(war == null) {
                Product product = productManager.fetchProductById(orderItem.getProductId());
                String title = product.getTitle();

                int fee = orderItem.getRealAmount() * store.getFeeRate() / 1000;
                int amount = orderItem.getRealAmount() - fee;
                WithdrawAccountRecord beforeWar = withdrawAccountRecordRepo.selectOneByQuery(QueryWrapper.create().eq(WithdrawAccountRecord::getUserId, store.getAdminUserId()).orderBy(WithdrawAccountRecord::getCreatedAt, false).limit(1));
                int beforeBalance = beforeWar != null ? beforeWar.getAfterBalance() : 0;
                int afterBalance = beforeBalance + amount;
                WithdrawAccountRecord accountRecord = new WithdrawAccountRecord()
                        .setUserId(store.getAdminUserId())
                        .setRelatedId(orderItem.getId())
                        .setType(AccountRecordTypeEnum.PRODUCT_ORDER_INCOME)
                        .setDirection(AccountDirectionEnum.IN)
                        .setAmount(amount)
                        .setFee(fee)
                        .setBeforeBalance(beforeBalance)
                        .setAfterBalance(afterBalance)
                        .setRemark(title);
                withdrawAccountRecordRepo.save(accountRecord);
            }
            WithdrawAccount account = withdrawAccountRepo.selectOneByQuery(QueryWrapper.create().eq(WithdrawAccount::getUserId, store.getAdminUserId()));
            if(account == null) {
                account = new WithdrawAccount()
                        .setUserId(store.getAdminUserId())
                        .setTotalIncome(0)
                        .setTotalWithdraw(0)
                        .setTotalFee(0)
                        .setTotalOrders(0)
                        .setAvailableAmount(0)
                        .setFrozenAmount(0);
                withdrawAccountRepo.insert(account);
            }
        }
        List<WithdrawAccount> accounts = withdrawAccountRepo.selectAll();
            for (WithdrawAccount account : accounts) {
                List<WithdrawAccountRecord> withdrawAccountRecords = withdrawAccountRecordRepo.selectListByQuery(QueryWrapper.create().eq(WithdrawAccountRecord::getUserId, account.getUserId()));
                int totalIncome = withdrawAccountRecords.stream().filter(v -> v.getType().equals(AccountRecordTypeEnum.PRODUCT_ORDER_INCOME)).mapToInt(WithdrawAccountRecord::getAmount).sum();
                long totalOrders = withdrawAccountRecords.stream().filter(v -> v.getType().equals(AccountRecordTypeEnum.PRODUCT_ORDER_INCOME)).count();
                int availableAmount = withdrawAccountRecords.stream().mapToInt(WithdrawAccountRecord::getAmount).sum();
                account.setTotalIncome(totalIncome)
                        .setTotalOrders((int) totalOrders)
                        .setAvailableAmount(availableAmount);
                withdrawAccountRepo.update(account);
            }

    }

    /**
     * 生成用于2FA扫码的字符串
     * 该字符串可以被Google Authenticator或其他TOTP应用扫描
     */
    @Test
    public void generate2FAQRCodeString() {
        try {
            // 从数据库获取2FA密钥
            Setting setting = systemManager.fetchSetting("Security", "TwoFactorSecret");
            if (setting == null || setting.getValue() == null) {
                System.out.println("错误: 2FA密钥未配置在数据库中");
                return;
            }

            // 去除JSON格式的引号
            String secretKey = setting.getValue().replace("\"", "");

            // 应用名称和账户名称
            String issuer = "dnhhsj";
            String accountName = "admin";

            // URL编码参数
            String encodedIssuer = URLEncoder.encode(issuer, StandardCharsets.UTF_8.toString());
            String encodedAccountName = URLEncoder.encode(accountName, StandardCharsets.UTF_8.toString());

            // 构建otpauth URL
            String otpauthUrl = String.format("otpauth://totp/%s:%s?secret=%s&issuer=%s&algorithm=HmacSHA1&digits=6&period=30",
                    encodedIssuer, encodedAccountName, secretKey, encodedIssuer);
            System.out.println("\n2FA扫码字符串:\n" + otpauthUrl);
            System.out.println("\n请使用Google Authenticator或其他TOTP应用扫描此字符串生成的二维码\n");

            // 提供一个可以生成二维码的网站链接
            String qrCodeUrl = "https://www.qr-code-generator.com/";
            System.out.println("您可以访问 " + qrCodeUrl + " 生成二维码");
            System.out.println("将上面的otpauth URL粘贴到二维码生成器中，然后用手机扫描生成的二维码");

        } catch (Exception e) {
            System.err.println("生成2FA扫码字符串时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void queryEnergyLeft() {
        WgwEnergyLeftBo pwgl2 = ThirdPartyHolder.wuguanwangService().queryCellEnergyLeft("PWGL_2", "11-3-A-07");
        System.out.println(pwgl2);

    }

    @Test
    public void createPropertyCellIfNotExist() {
        List<EnergyCell> cells = energyCellRepo.selectAll();
        for (EnergyCell cell : cells) {
            if(cell.getPropertyCellId() == null) {
                PropertyCell propertyCell = propertyCellRepo.selectOneByQuery(QueryWrapper.create().eq(PropertyCell::getCellId, cell.getCellId()).eq(PropertyCell::getCompanyCode, cell.getCompanyCode()));
                if(propertyCell == null) {
                    propertyCell = new PropertyCell();
                    propertyCell.setCompanyName(cell.getCompanyName());
                    propertyCell.setCompanyCode(cell.getCompanyCode());
                    propertyCell.setCellName(cell.getCellName());
                    propertyCell.setCellNo(cell.getCellNo());
                    propertyCell.setCellId(cell.getCellId());
                    propertyCell.setMasterName(cell.getMasterName());
                    propertyCell.setMasterMobile(cell.getMasterMobile());
                    propertyCell.setMasterIdCardNo(cell.getMasterIdCardNo());
                    propertyCell.setLayerName(cell.getLayerName());
                    propertyCell.setLayerNo(cell.getLayerNo());
                    propertyCellRepo.save(propertyCell);

                    propertyCellManager.hideSameCellNoItems(propertyCell);
                }
                cell.setPropertyCellId(propertyCell.getId());
                energyCellRepo.update(cell);
            }
        }
    }

    @Test
    public void ocrBusinessLicense() {
        propertyCellRepo.selectAll().forEach(propertyCell -> {
            log.info("ocr营业执照开始"+propertyCell.getId() + (propertyCell.getOcrBusinessLicenseResult() == null));
            if(propertyCell.getOcrBusinessLicenseResult() == null && propertyCell.getBusinessLicense() != null) {
                String s = "\"{}\"";
                try {
                    OcrBusinessLicenseBo ocrBusinessLicenseBo = ThirdPartyHolder.aliyunSmsService().ocrBusinessLicense(propertyCell.getBusinessLicense());
                    s = JsonUtils.toJson(ocrBusinessLicenseBo);
                    log.info("ocr营业执照成功"+propertyCell.getId()+" "+ s);
                } catch (Exception e) {
                    log.error("ocr营业执照失败"+propertyCell.getId(), e);
                }
                propertyCell.setOcrBusinessLicenseResult(s);
                propertyCellRepo.save(propertyCell);
            }
        });
    }






}
