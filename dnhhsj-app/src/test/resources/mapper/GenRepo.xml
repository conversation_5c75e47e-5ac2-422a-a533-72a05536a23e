<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dounanflowers.generator.repo.GenRepo">
    <select id="executeSql" resultType="map">
        ${sql}
    </select>
    <select id="selectTable" resultType="com.dounanflowers.generator.bean.PGTable">
        select tablename
        from pg_tables
    </select>
    <select id="selectColumn" resultType="com.dounanflowers.generator.bean.PGColumn">
        SELECT a.attnum                             as index,
               a.attname                            AS name,
               format_type(a.atttypid, a.atttypmod) AS type,
               NOT a.attnotnull                     AS nullable,
               d.description                        AS comment
        FROM pg_class c
                 join pg_attribute a on a.attrelid = c.oid
                 join pg_type t on a.atttypid = t.oid
                 left join pg_description d on d.objoid = c.oid AND d.objsubid = a.attnum
        WHERE c.relname = #{tableName}
          and a.attnum > 0
        ORDER BY a.attnum;
    </select>
</mapper>