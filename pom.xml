<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.dounanflowers</groupId>
    <artifactId>dnhhsj-java</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>dnhhsj-java</name>

    <modules>
        <module>dnhhsj-app</module>
        <module>dnhhsj-admin</module>
        <module>dnhhsj-client</module>
        <module>dnhhsj-common</module>
        <module>dnhhsj-third</module>
        <module>dnhhsj-security</module>
        <module>dnhhsj-framework</module>
    </modules>

    <properties>
        <dounanflowers.version>1.0-SNAPSHOT</dounanflowers.version>

        <encoding>UTF-8</encoding>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <maven.compiler.release>21</maven.compiler.release>

        <compiler-plugin.version>3.13.0</compiler-plugin.version>
        <surefire-plugin.version>3.2.5</surefire-plugin.version>
        <skipITs>true</skipITs>

        <spring-boot.version>3.3.4</spring-boot.version>
        <springdoc.version>2.6.0</springdoc.version>
        <guava.version>33.3.1-jre</guava.version>
        <collections4.version>4.4</collections4.version>
        <commons-io.version>2.17.0</commons-io.version>
        <commons-lang3.version>3.17.0</commons-lang3.version>
        <poi.version>5.2.5</poi.version>
        <druid.version>1.2.23</druid.version>
        <oshi.version>6.6.3</oshi.version>
        <bitwalker.version>1.21</bitwalker.version>

        <lombok.version>1.18.34</lombok.version>
        <mybatis-flex.version>1.9.7</mybatis-flex.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.dounanflowers</groupId>
                <artifactId>dnhhsj-admin</artifactId>
                <version>${dounanflowers.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dounanflowers</groupId>
                <artifactId>dnhhsj-client</artifactId>
                <version>${dounanflowers.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dounanflowers</groupId>
                <artifactId>dnhhsj-app</artifactId>
                <version>${dounanflowers.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dounanflowers</groupId>
                <artifactId>dnhhsj-common</artifactId>
                <version>${dounanflowers.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dounanflowers</groupId>
                <artifactId>dnhhsj-third</artifactId>
                <version>${dounanflowers.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dounanflowers</groupId>
                <artifactId>dnhhsj-security</artifactId>
                <version>${dounanflowers.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dounanflowers</groupId>
                <artifactId>dnhhsj-framework</artifactId>
                <version>${dounanflowers.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-lang3 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-collections4 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.springdoc/springdoc-openapi-starter-webmvc-ui -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.springdoc/springdoc-openapi-starter-webflux-ui -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webflux-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>
            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thymeleaf</groupId>
                <artifactId>thymeleaf</artifactId>
                <version>3.1.0.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-dependencies</artifactId>
                <version>${mybatis-flex.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${encoding}</encoding>
                    <parameters>true</parameters>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>com.mybatis-flex</groupId>
                            <artifactId>mybatis-flex-processor</artifactId>
                            <version>1.9.7</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${surefire-plugin.version}</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
