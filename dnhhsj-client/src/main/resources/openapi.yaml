openapi: 3.0.1
info:
  title: 花花世界
  description: ''
  version: 1.0.0
tags:
  - name: 小程序
  - name: 小程序/登录
  - name: 小程序/用户
  - name: 小程序/商铺
  - name: 小程序/商铺/经纪人
  - name: 小程序/商铺/订阅
  - name: 小程序/文章
  - name: 小程序/艺术品
  - name: 小程序/轮播
  - name: 小程序/收藏
  - name: 小程序/投诉建议
  - name: 小程序/其他
  - name: 小程序/停车
  - name: 小程序/操场
  - name: 小程序/拍卖
  - name: 小程序/优惠券
paths:
  /mini/loginByWechat:
    post:
      summary: 微信自动登录
      deprecated: false
      description: 用户打开小程序执行一次
      tags:
        - 小程序/登录
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                  description: wx.login获取到的code
                  title: 微信code
              required:
                - code
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                        title: token
                      expiredAt:
                        type: integer
                        title: 过期时间
                        description: token过期时间
                      userInfo: &ref_0
                        $ref: '#/components/schemas/TUser'
                    title: 返回数据
                    required:
                      - token
                      - userInfo
                      - expiredAt
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/loginByMobileCode:
    post:
      summary: 手机验证码登录(开发用)
      deprecated: false
      description: ''
      tags:
        - 小程序/登录
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                mobile:
                  type: string
                  title: 手机号
                smsCode:
                  type: string
                  title: 验证码
              required:
                - mobile
                - smsCode
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                      expiredAt:
                        type: string
                        title: 过期时间
                      userInfo: *ref_0
                    title: 返回数据
                    required:
                      - token
                      - expiredAt
                      - userInfo
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/myUserInfoGet:
    post:
      summary: 获取自己用户信息
      deprecated: false
      description: ''
      tags:
        - 小程序/用户
      parameters: [ ]
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_0
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/myUserInfoUpdate:
    post:
      summary: 修改自己用户信息
      deprecated: false
      description: ''
      tags:
        - 小程序/用户
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                nickname:
                  type: string
                  title: 昵称
                avatar:
                  type: string
                  title: 头像
                gender:
                  type: string
                  title: 性别
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_0
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /:
    post:
      summary: 热门获取
      deprecated: false
      description: 小程序获取热门列表，在首页展示，主要是攻略和动态文章
      tags:
        - 小程序/其他
      parameters: [ ]
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    title: 返回数据
                    type: object
                    properties:
                      _id:
                        type: string
                      title:
                        type: string
                        title: 标题
                      cover:
                        type: string
                        title: 封面
                      intro:
                        type: string
                        title: 简介
                      content:
                        type: string
                        title: 内容
                      categoryId:
                        type: string
                        title: 分类
                      categoryObj:
                        type: string
                        title: 分类对象
                        enum:
                          - 动态
                          - 攻略
                      tags:
                        type: string
                        title: 标签
                      viewCount:
                        type: integer
                        title: 查看数
                      favouriteCount:
                        type: string
                        title: 收藏数
                      createdById:
                        type: string
                        title: 创建人
                      createdAt:
                        type: string
                        title: 创建时间
                    required:
                      - _id
                      - title
                      - cover
                      - intro
                      - content
                      - tags
                      - createdAt
                      - createdById
                      - categoryId
                      - categoryObj
                      - viewCount
                      - favouriteCount
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/myMobileBindByWechat:
    post:
      summary: 微信绑定手机号
      deprecated: false
      description: ''
      tags:
        - 小程序/用户
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                phoneNumberCode:
                  type: string
                  title: 微信code
                  description: 使用微信getphonenumber获取到的code
                code:
                  type: string
                  title: 微信登录code
                  description: 使用wx.login获取到的code
              required:
                - phoneNumberCode
                - code
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                      expiredAt:
                        type: integer
                      userInfo: *ref_0
                    title: 返回数据
                    required:
                      - token
                      - expiredAt
                      - userInfo
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/userPrivateGet:
    post:
      summary: 获取用户私密信息（废弃）
      deprecated: false
      description: 参数type：auction(表示拍卖)
      tags:
        - 小程序/用户
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  title: 类型
                  description: auction-拍卖用
              required:
                - type
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    title: 返回数据
                    $ref: '#/components/schemas/TUserPrivate'
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/userRealnameVerify:
    post:
      summary: 身份证实名认证
      deprecated: false
      description: |-
        身份证需要调取拍照，可以上传拍照的缩略图  
        返回的userInfo中realname字段为最新实名认证的姓名，可用于判断是否实名认证
      tags:
        - 小程序/用户
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                realname:
                  type: string
                  title: 真实姓名
                idCardNum:
                  type: string
                  title: 身份证号
                idCardUrl1:
                  type: string
                  title: 身份证人像面
                idCardUrl2:
                  type: string
                  title: 身份证国徽面
              required:
                - realname
                - idCardNum
                - idCardUrl1
                - idCardUrl2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      userInfo: *ref_0
                    title: 返回数据
                    required:
                      - userInfo
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/brokerGetOne:
    post:
      summary: 加载一位经纪人
      deprecated: false
      description: 随机加载一个经纪人，或根据条件优先选择一位经纪人
      tags:
        - 小程序/商铺/经纪人
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties: { }
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    title: 返回数据
                    $ref: '#/components/schemas/TBroker'
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/brokerCalled:
    post:
      summary: 经纪人被打电话
      deprecated: false
      description: |-
        - 将brokerId写入用户boundBrokerId，表示用户和经纪人绑定
        - 商铺brokerCalledCount+1
      tags:
        - 小程序/商铺/经纪人
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                brokerId:
                  type: string
                  title: 经纪人id
                shopId:
                  type: string
                  title: 商铺
                  description: 非必填
              required:
                - brokerId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/shopSubscribePageList:
    post:
      summary: 商铺订阅分页列表
      deprecated: false
      description: ''
      tags:
        - 小程序/商铺/订阅
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                pageNum:
                  type: integer
                pageSize:
                  type: integer
              required:
                - pageNum
                - pageSize
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: &ref_1
                          $ref: '#/components/schemas/TShopSubscribe'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/shopSubscribeAdd:
    post:
      summary: 商铺订阅新增
      deprecated: false
      description: ''
      tags:
        - 小程序/商铺/订阅
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  title: 订阅标题
                filter:
                  type: array
                  items:
                    type: object
                    properties:
                      field:
                        type: string
                      type:
                        type: string
                      value:
                        type: string
                    required:
                      - field
                      - type
                      - value
                  title: 订阅的过滤器
              required:
                - title
                - filter
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_1
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/shopSubscribeDeleteById:
    post:
      summary: 商铺订阅删除
      deprecated: false
      description: ''
      tags:
        - 小程序/商铺/订阅
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      success:
                        type: boolean
                    title: 返回数据
                    required:
                      - success
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/shopGetById:
    post:
      summary: 商铺详情
      deprecated: false
      description: ''
      tags:
        - 小程序/商铺
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                  description: ID 编号
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: &ref_2
                    title: 返回数据
                    $ref: '#/components/schemas/TShop'
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
              examples:
                '1':
                  summary: 成功示例
                  value:
                    videos:
                      - url1
                      - url2
                    images:
                      - url1
                      - url2
                    houseType:
                      - url1
                      - url2
                    tags:
                      - 标签1
                      - 标签2
                    title: 业sdhfgj
                    rent: 1200
                    area: 23.4
                    payment: '1'
                    orientation: '3'
                    floor: 3
                    decoration: 中等
                    desc: 三德科技泸沽湖见多识广辉丰股份岁的法国
                    bid:
                      earnestMoney: 300
                      startingPrice: 500
                      increment: '2'
                      cycle: '3'
                      delayCycle: '4'
                    personTime:
                      apply: 30
                      bid: 20
                      remind: 56
                      onlooker: 234
          headers: { }
      security:
        - bearer: [ ]
  /mini/shopPageList:
    post:
      summary: 商铺分页列表
      deprecated: false
      description: |-
        ### filter说明
        filter是个数组
        - type 查询类型，暂定like(模糊搜索)，range(匹配范围)，in(匹配多个)，inRange(匹配多个范围)
        - field 查询的字段，一般为单独字段字符串，type为like时，是多个字段数组
        - value 查询的值
      tags:
        - 小程序/商铺
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                pageNum:
                  type: integer
                pageSize:
                  type: integer
                filter:
                  type: array
                  items:
                    type: object
                    properties:
                      field:
                        type: string
                      type:
                        type: string
                      value:
                        type: string
                    required:
                      - field
                      - type
                      - value
                  minItems: 0
                  maxItems: 0
                sort:
                  type: array
                  items:
                    type: object
                    properties:
                      field:
                        type: string
                      order:
                        type: string
                    required:
                      - field
                      - order
                  minItems: 0
                  maxItems: 0
              required:
                - pageNum
                - pageSize
                - filter
                - sort
            example:
              filter:
                price:
                  - 1000,2000
                  - 2000,3000
                area:
                  - 10,20
                  - 20,30
                status:
                  - '1'
                  - '2'
                  - '3'
                orientation:
                  - '1'
                  - '2'
                  - '3'
                tags:
                  - '1'
                  - '2'
                  - '3'
                region:
                  - 1-2
                  - 2-4-3
              sort:
                - - price
                  - DESC
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: *ref_2
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
              examples:
                '1':
                  summary: 成功示例
                  value:
                    - cellNo: 1-23-2
                      title: sdfh
                      cover: url
                      area: 33.4
                      rent: 1200
                      status: '3'
                      tags:
                        - '1'
                        - '2'
                        - '235'
          headers: { }
      security:
        - bearer: [ ]
  /mini/shopShareImage:
    post:
      summary: 获取商铺分享图（暂不使用）
      deprecated: false
      description: ''
      tags:
        - 小程序/商铺
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                shopId:
                  type: string
                  title: 商铺Id
              required:
                - shopId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      url:
                        type: string
                        title: 分享图地址
                    title: 返回数据
                    required:
                      - url
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/articlePageList:
    post:
      summary: 文章分页列表
      deprecated: false
      description: ''
      tags:
        - 小程序/文章
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                pageNum:
                  type: integer
                pageSize:
                  type: integer
                filter:
                  type: array
                  items:
                    type: object
                    properties:
                      field:
                        type: string
                      type:
                        type: string
                      value:
                        type: string
                    required:
                      - field
                      - type
                      - value
                  minItems: 0
                  maxItems: 0
                sort:
                  type: array
                  items:
                    type: object
                    properties:
                      field:
                        type: string
                      order:
                        type: string
                    required:
                      - field
                      - order
                  minItems: 0
                  maxItems: 0
              required:
                - pageNum
                - pageSize
                - filter
                - sort
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: &ref_3
                          $ref: '#/components/schemas/TArticle'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/articleGetById:
    post:
      summary: 文章详情
      deprecated: false
      description: ''
      tags:
        - 小程序/文章
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                title:
                  type: string
                  title: 按标题查询
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_3
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/artworkPageList:
    post:
      summary: 艺术品分页列表
      deprecated: false
      description: |-
        ```js
        [
           {field: 'categoryId', type: 'in', value: ['2']}, // 朝鲜文化
           {field: 'categoryId', type: 'in', value: ['21']}, // 功勋画家
        ]
        ```
      tags:
        - 小程序/艺术品
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                pageNum:
                  type: integer
                pageSize:
                  type: integer
                filter:
                  type: array
                  items:
                    type: object
                    properties:
                      field:
                        type: string
                      type:
                        type: string
                      value:
                        type: string
                    required:
                      - field
                      - type
                      - value
                  minItems: 0
                  maxItems: 0
                sort:
                  type: array
                  items:
                    type: object
                    properties:
                      field:
                        type: string
                      order:
                        type: string
                    required:
                      - field
                      - order
                  minItems: 0
                  maxItems: 0
              required:
                - pageNum
                - pageSize
                - filter
                - sort
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: &ref_4
                          $ref: '#/components/schemas/TArtwork'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/artworkGetById:
    post:
      summary: 艺术品详情
      deprecated: false
      description: ''
      tags:
        - 小程序/艺术品
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_4
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/bannerList:
    post:
      summary: 轮播列表
      deprecated: false
      description: ''
      tags:
        - 小程序/轮播
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                place:
                  type: string
                  title: 位置
                  description: 展示位置
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                        type:
                          type: string
                          title: 类型
                          description: 1-图片；2-视频
                          enum:
                            - '1'
                            - '2'
                        place:
                          type: string
                          title: 位置
                          description: 1-首页顶部
                          enum:
                            - '1'
                        url:
                          type: string
                          title: URL
                          description: 资源地址
                        linkType:
                          type: string
                          title: 跳转方式
                          description: 1-小程序；2-H5
                          enum:
                            - '1'
                            - '2'
                        linkUrl:
                          type: string
                          title: 跳转地址
                      required:
                        - _id
                        - type
                        - place
                        - url
                        - linkType
                        - linkUrl
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/favouritePageList:
    post:
      summary: 收藏分页列表
      deprecated: false
      description: ''
      tags:
        - 小程序/收藏
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                pageNum:
                  type: integer
                pageSize:
                  type: integer
                filter:
                  type: array
                  items:
                    type: object
                    properties:
                      field:
                        type: string
                      type:
                        type: string
                      value:
                        type: string
                    required:
                      - field
                      - type
                      - value
                  minItems: 0
                  maxItems: 0
                sort:
                  type: array
                  items:
                    type: object
                    properties:
                      field:
                        type: string
                      order:
                        type: string
                    required:
                      - field
                      - order
                  minItems: 0
                  maxItems: 0
              required:
                - pageNum
                - pageSize
                - filter
                - sort
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: string
                        title: 总数
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/TFavourite'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/favouriteToggle:
    post:
      summary: 切换收藏状态
      deprecated: false
      description: forceAction字段不传时，自动切换收藏状态，否则根据此字段bool值添加收藏或取消收藏
      tags:
        - 小程序/收藏
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  title: 类型
                  description: 1-商铺(Shop); 2-攻略(Article); 3-景点动态(Article)
                entityId:
                  type: string
                  title: 实体Id
                title:
                  type: string
                  title: 标题
                  description: 保存标题用于搜索
                forceAction:
                  type: boolean
                  title: 强制操作
                  description: 字段不传时，自动切换收藏状态，否则根据此字段bool值添加收藏或取消收藏
              required:
                - entityId
                - type
                - title
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/favouriteCheck:
    post:
      summary: 检查是否收藏
      deprecated: false
      description: ''
      tags:
        - 小程序/收藏
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  title: 类型
                entityId:
                  type: string
                  title: 实体ID
              required:
                - type
                - entityId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      isFavourite:
                        type: boolean
                        title: 是否收藏
                    title: 返回数据
                    required:
                      - isFavourite
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/dictGetMap:
    post:
      summary: 获取字典Map
      deprecated: false
      description: 可以传入tags获取相关字典Map, Map的key是字典的key, Map的value是字典的options
      tags:
        - 小程序/其他
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tags:
                  type: array
                  items:
                    type: string
                key:
                  type: array
                  items:
                    type: string
                isOr:
                  type: boolean
                  description: 条件之间是否是或
                  title: 条件连接
              required:
                - key
                - isOr
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties: { }
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/weatherGetByLocation:
    post:
      summary: 通过位置获取天气
      deprecated: false
      description: ''
      tags:
        - 小程序/其他
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                location:
                  type: string
              required:
                - location
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    title: 返回数据
                    $ref: '#/components/schemas/TWeather'
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/sendSmsCode:
    post:
      summary: 发送短信验证码
      deprecated: false
      description: ''
      tags:
        - 小程序/其他
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                mobile:
                  type: string
                  title: 手机号
                imgCode:
                  type: string
                  title: 图片验证码
                  description: 发送过于频繁时需要图片验证码
              required:
                - mobile
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/dictGet:
    post:
      summary: 获取单个字典
      deprecated: false
      description: ''
      tags:
        - 小程序/其他
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  title: 名称
                key:
                  type: string
                  title: 类型
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        label:
                          type: string
                        value:
                          type: string
                        status:
                          type: boolean
                      required:
                        - label
                        - value
                        - status
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/upload:
    post:
      summary: 上传文件
      deprecated: false
      description: ''
      tags:
        - 小程序/其他
      parameters: [ ]
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  format: binary
                  type: string
                  example: ''
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/systemVar:
    post:
      summary: 获取系统变量
      deprecated: false
      description: ''
      tags:
        - 小程序/其他
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties: { }
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    title: 返回数据
                    $ref: '#/components/schemas/TSystemVar'
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/feedbackCreate:
    post:
      summary: 投诉建议提交
      deprecated: false
      description: ''
      tags:
        - 小程序/投诉建议
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  description: Feedback_type
                  title: 类型
                content:
                  type: string
                  title: 内容
                images:
                  type: array
                  items:
                    type: string
                  title: 图片
                mobile:
                  type: string
                  title: 用户输入手机号
              required:
                - content
                - type
                - images
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    title: 返回数据
                    $ref: '#/components/schemas/TFeedback'
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/lcPlateList:
    post:
      summary: 车牌列表
      deprecated: false
      description: |-
        用户已经绑定的车牌号列表  
        用于付款停车费时选择
      tags:
        - 小程序/停车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties: { }
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: array
                    items:
                      type: string
                    title: 返回数据
                    description: 车牌号字符串数组
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/lcOrderFee:
    post:
      summary: 停车费订单费用获取
      deprecated: false
      description: 车牌号和无牌车票号必选一个
      tags:
        - 小程序/停车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                plate:
                  type: string
                  title: 车牌号
                couponId:
                  type: string
                  title: 优惠券ID
              required:
                - plate
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    title: 返回数据
                    $ref: '#/components/schemas/TLcOrderFee'
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/lcOrderPay:
    post:
      summary: 停车费订单支付
      deprecated: false
      description: ''
      tags:
        - 小程序/停车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                lcOrderId:
                  type: string
                  title: 订单ID
                couponId:
                  type: string
                  title: 优惠券ID
              required:
                - lcOrderId
                - couponId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    title: 返回数据
                    $ref: '#/components/schemas/TWxPayParams'
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/lcPlateAdd:
    post:
      summary: 添加车牌
      deprecated: false
      description: ''
      tags:
        - 小程序/停车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                plate:
                  type: string
              required:
                - plate
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/lcPlateDelete:
    post:
      summary: 删除立牌
      deprecated: false
      description: ''
      tags:
        - 小程序/停车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                plate:
                  type: string
              required:
                - plate
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/lcOrderPageList:
    post:
      summary: 停车费订单分页列表
      deprecated: false
      description: ''
      tags:
        - 小程序/停车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: &ref_5
              $ref: '#/components/schemas/TPageListReq'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: &ref_8
                          title: 订单
                          $ref: '#/components/schemas/TLcOrder'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/playgroundPay:
    post:
      summary: 测试支付
      deprecated: false
      description: ''
      tags:
        - 小程序/操场
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties: { }
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/playgroundPayNotify:
    post:
      summary: 手动通知
      deprecated: false
      description: ''
      tags:
        - 小程序/操场
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                a:
                  type: string
              required:
                - a
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/auctionBid:
    post:
      summary: 拍卖出价
      deprecated: false
      description: ''
      tags:
        - 小程序/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                auctionId:
                  type: string
                  title: 拍卖ID
                bidCent:
                  type: integer
                  title: 出价金额
                  description: 单位分
              required:
                - auctionId
                - bidCent
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/auctionDealPaidCheck:
    post:
      summary: 是否支付拍卖尾款
      deprecated: false
      description: ''
      tags:
        - 小程序/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                auctionId:
                  type: string
                  title: 拍卖ID
              required:
                - auctionId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      paid:
                        type: boolean
                        title: 是否支付
                    title: 返回数据
                    required:
                      - paid
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/auctionDealPay:
    post:
      summary: 支付尾款
      deprecated: false
      description: ''
      tags:
        - 小程序/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                auctionId:
                  type: string
                  title: 拍卖ID
              required:
                - auctionId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties: { }
                    title: 返回数据
                    description: 返回用于微信支付的参数对象
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/auctionDepositPay:
    post:
      summary: 支付保证金
      deprecated: false
      description: ''
      tags:
        - 小程序/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                auctionId:
                  type: string
                  title: 拍卖ID
                realname:
                  type: string
                  title: 姓名
                idCard:
                  type: string
                  title: 身份证号
                mobile:
                  type: string
                  title: 手机号
                address:
                  type: string
                  title: 地址
              required:
                - auctionId
                - realname
                - idCard
                - mobile
                - address
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties: { }
                    title: 返回数据
                    description: 返回用于微信支付的参数对象
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/auctionDepositRefundedCheck:
    post:
      summary: 是否已退款拍卖保证金
      deprecated: false
      description: ''
      tags:
        - 小程序/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                auctionId:
                  type: string
                  title: 拍卖id
              required:
                - auctionId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      refunded:
                        type: boolean
                        title: 是否退款
                    title: 返回数据
                    required:
                      - refunded
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/auctionDepositPaidCheck:
    post:
      summary: 是否已支付拍卖保证金
      deprecated: false
      description: ''
      tags:
        - 小程序/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                auctionId:
                  type: string
                  title: 拍卖ID
              required:
                - auctionId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      paid:
                        type: boolean
                        title: 是否支付
                    title: 返回数据
                    required:
                      - paid
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/auctionJoin:
    post:
      summary: 拍卖报名
      deprecated: false
      description: 报名后，如果拍卖开始会收到短信
      tags:
        - 小程序/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                auctionId:
                  type: string
                  title: 拍卖
              required:
                - auctionId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      success:
                        type: boolean
                    title: 返回数据
                    required:
                      - success
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/auctionJoinedCheck:
    post:
      summary: 是否报名拍卖
      deprecated: true
      description: 检查是否已经报名拍卖
      tags:
        - 小程序/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                auctionId:
                  type: string
                  title: 拍卖id
              required:
                - auctionId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      joined:
                        type: boolean
                        title: 是否已报名
                    title: 返回数据
                    required:
                      - joined
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/auctionPageList:
    post:
      summary: 拍卖分页列表
      deprecated: false
      description: >-
        filter: [{type: 'custom', field: 'joinedAuctionStatus', value:
        'xxx'}]   

        xxx = 待交保/待开拍/竞价中/已结束/待付尾款/待交割/已交割/尾款违约
      tags:
        - 小程序/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_5
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    title: 返回数据
                    type: object
                    properties:
                      code:
                        type: integer
                        title: 错误码
                        description: 大于0表示错误
                      msg:
                        type: string
                        title: 错误信息
                      data:
                        type: object
                        properties:
                          total:
                            type: number
                            title: 总数
                          list:
                            type: array
                            items: &ref_6
                              $ref: '#/components/schemas/TAuction'
                              title: 最后一条拍卖
                            title: 列表
                        title: 返回数据
                        required:
                          - total
                          - list
                      traceId:
                        type: string
                        title: 追溯ID
                        description: 方便在日志里定位的ID
                    required:
                      - code
                      - msg
                      - data
                      - traceId
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/auctionDepositMyList:
    post:
      summary: 已交保证金拍卖列表
      deprecated: false
      description: ''
      tags:
        - 小程序/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties: { }
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: array
                    items: *ref_6
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/auctionGet:
    post:
      summary: 获取拍卖详情
      deprecated: false
      description: 根据_id或者targetModel+targetId获取拍卖
      tags:
        - 小程序/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                targetModel:
                  type: string
                targetId:
                  type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/couponPageList:
    post:
      summary: 用户优惠券分页列表
      deprecated: false
      description: |-
        ```js
        // filter
        [
          {type: 'eq', field: 'useSceneType', value: 'xxx'}, // 使用场景类型 字典Coupon_useSceneType
          {type: 'custom', field: 'useStatus', value: 'xxx'}, // 使用状态：待使用/已使用/已过期
        ]
        ```
      tags:
        - 小程序/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_5
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: &ref_7
                          $ref: '#/components/schemas/TCoupon'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/couponReceive:
    post:
      summary: 用户优惠券领取
      deprecated: false
      description: ''
      tags:
        - 小程序/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                qrCode:
                  type: string
                  title: 领取code
              required:
                - qrCode
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_7
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /mini/couponReceiveTest:
    post:
      summary: 用户优惠券试算
      deprecated: false
      description: ''
      tags:
        - 小程序/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                qrCode:
                  type: string
              required:
                - qrCode
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_7
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
components:
  schemas:
    TPageListReq:
      type: object
      properties:
        pageNum:
          type: integer
        pageSize:
          type: integer
        filter:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
              type:
                type: string
              value:
                type: string
            required:
              - field
              - type
              - value
          minItems: 0
          maxItems: 0
        sort:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
              order:
                type: string
            required:
              - field
              - order
          minItems: 0
          maxItems: 0
      title: 分页请求
      required:
        - pageNum
        - pageSize
        - filter
        - sort
    TPageListRes:
      title: 分页响应
      type: object
      properties:
        code:
          type: integer
          title: 错误码
          description: 大于0表示错误
        msg:
          type: string
          title: 错误信息
        data:
          type: object
          properties:
            total:
              type: number
              title: 总数
            list:
              type: array
              items:
                type: string
              title: 列表
          title: 返回数据
          required:
            - total
            - list
        traceId:
          type: string
          title: 追溯ID
          description: 方便在日志里定位的ID
      required:
        - code
        - msg
        - data
        - traceId
    HttpRes:
      type: object
      properties:
        code:
          type: integer
          title: 错误码
          description: 大于0表示错误
        msg:
          type: string
          title: 错误信息
        data:
          type: string
        traceId:
          type: string
          title: 追溯ID
          description: 方便在日志里定位的ID
      required:
        - code
        - msg
        - data
        - traceId
      title: 默认响应
    TLcCouponTpl:
      type: object
      properties:
        _id:
          type: string
        type:
          type: string
          title: 类型
          description: LcCoupon_type, “0”小时券,”1”金额券 "2"折扣券
        parValue:
          type: integer
          title: 面值
          description: 小时券,单位分钟，金额券,单位分 折扣券 0表示免费10表示1折85表示85折90表示9折
        endAtType:
          type: string
          title: 过期类型
          description: LcCoupon_endAtType
        relativeEndAt:
          type: integer
          title: 相对过期
          description: 单位分钟
        absoluteEndAt:
          type: string
          title: 绝对过期
        publishCount:
          type: integer
          title: 总发布数量
        receivedCount:
          type: integer
          title: 已领取数量
        usedCount:
          type: integer
          title: 已使用数量
        status:
          type: boolean
          title: 启用状态
        title:
          type: string
          title: 标题
      title: 停车优惠券模板（废弃）
      required:
        - _id
        - type
        - parValue
        - endAtType
        - relativeEndAt
        - absoluteEndAt
        - publishCount
        - receivedCount
        - usedCount
        - status
        - title
    TLcOrder:
      type: object
      properties:
        _id:
          type: string
        orderId:
          type: string
          title: 入场记录编号
        plate:
          type: string
          title: 车牌
        ticketCode:
          type: string
          title: 无牌车票号
        plateColor:
          type: string
          title: 车牌颜色
        inTime:
          type: string
          title: 入场时间
        inChannel:
          type: string
          title: 入场通道名称
        visitReason:
          type: string
          title: 访问事由
        openGateMode:
          type: string
          title: 放行类型
          description: 自动抬杆/手动放行,无信息时填''
        matchMode:
          type: string
          description: 全字匹配、近似匹配、人工匹配、人工纠正,无信息时填''
          title: 匹配模式
        confidence:
          type: number
          title: 车牌识别可信度
        carType:
          type: string
          title: 车辆类型
        userInfo_idCard:
          type: string
          title: 证件号码
        userInfo_userName:
          type: string
          title: 车主姓名
        userInfo_phone:
          type: string
          title: 联系电话
        userInfo_address:
          type: string
          title: 地址
        placeInfo:
          type: array
          items:
            type: object
            properties: { }
          title: 车位信息
          description: 暂时用不到
        barriorOpen:
          type: string
          description: “开闸”“未开闸”
          title: 是否开闸
        inCostTime:
          type: number
          title: 入场开闸耗时
          description: 压地感到抬杆时间
        spaceCount:
          type: number
          title: 空位数
        imageList:
          type: array
          items:
            type: string
          title: 其他联动设备图片信息
          description: 暂时用不到
        imageName:
          type: string
          title: 图片名
          description: 暂时用不到
        modifyList:
          type: array
          items:
            type: object
            properties:
              oldOrderId:
                type: string
                title: 旧订单id
              newOrderId:
                type: string
                title: 新订单id
              oldPlate:
                type: string
                title: 旧车牌
              newPlate:
                type: string
                title: 新车牌
              modifyTime:
                type: string
                title: 修改时间
            required:
              - oldOrderId
              - newOrderId
              - oldPlate
              - newPlate
              - modifyTime
          title: 手动修改记录
        lcParkObj:
          type: string
          title: 场库
        lcParkId:
          type: string
        parkNumber:
          type: string
          title: 场库编号
        operatorId:
          type: string
          title: 操作员Id
        operatorName:
          type: string
          title: 操作员姓名
        invoiceNo:
          type: string
          title: 发票号码
        outTime:
          type: string
          title: 出场时间
        outImage:
          type: string
          title: 出场图片名
        outChannel:
          type: string
          title: 出口通道名称
        charge:
          type: number
          title: 总停车费
          description: 原价
        offLineCharge:
          type: number
          title: 线下总收费
        offLineProfitChargeNum:
          type: number
          title: 线下累计优惠金额总面值
        offLineProfitChargeValue:
          type: number
          title: 线下累计优惠金额总抵扣值
        offLineProfitTimeNum:
          type: number
          title: 线下累计优惠时间
        offLineProfitTimeValue:
          type: number
          title: 线下累计优惠时间总抵扣值
        onLineCharge:
          type: number
          title: 线上总收费
          description: 实付价
        onLineProfitChargeNum:
          type: number
          title: 线上累计优惠金额总面值
        onLineProfitChargeValue:
          type: number
          title: 线上累计优惠金额总抵扣值
        onLineProfitTimeNum:
          type: number
          title: 线上累计优惠时间
        onLineProfitTimeValue:
          type: number
          title: 线上累计优惠时间总抵扣值
        profitChargeTotal:
          type: number
          title: 线上线下金额和时间优惠累计抵扣值
          description: 抵扣金额
        outCostTime:
          type: number
          title: 出场开闸耗时
        profitList:
          type: array
          items:
            type: object
            properties: { }
          title: 优惠明细
        chargeList:
          type: array
          items:
            $ref: '#/components/schemas/TLcOrderCharge'
          title: 收费明细
      title: 停车场订单
      required:
        - orderId
        - _id
        - plate
        - ticketCode
        - plateColor
        - inTime
        - inChannel
        - visitReason
        - openGateMode
        - matchMode
        - carType
        - userInfo_idCard
        - userInfo_userName
        - userInfo_phone
        - userInfo_address
        - placeInfo
        - barriorOpen
        - inCostTime
        - spaceCount
        - imageList
        - imageName
        - modifyList
        - lcParkObj
        - parkNumber
        - operatorId
        - operatorName
        - invoiceNo
        - outTime
        - outImage
        - offLineCharge
        - outChannel
        - charge
        - offLineProfitTimeNum
        - offLineProfitChargeNum
        - offLineProfitChargeValue
        - onLineCharge
        - offLineProfitTimeValue
        - onLineProfitChargeNum
        - onLineProfitChargeValue
        - profitChargeTotal
        - onLineProfitTimeValue
        - onLineProfitTimeNum
        - outCostTime
        - profitList
        - chargeList
        - lcParkId
    TLcOrderCharge:
      type: object
      properties:
        payNo:
          type: string
          title: 支付订单号
        payCharge:
          type: string
          title: 支付金额
        payKind:
          type: string
          title: 支付类型
        payChannel:
          type: string
          title: 支付渠道
        transactionId:
          type: string
          description: 每笔交易生成唯一流水号（支付结果下发）
          title: 线上交易流水号
        getTime:
          type: string
          title: 结算时间
        memo:
          type: string
          title: 备注
        lcOrderId:
          type: string
        lcOrderObj: *ref_8
        orderId:
          type: string
          title: 订单号
        createdAt:
          type: string
      title: 停车订单支付
      required:
        - payNo
        - payCharge
        - payKind
        - payChannel
        - transactionId
        - getTime
        - memo
        - lcOrderObj
        - orderId
        - createdAt
        - lcOrderId
    TLcOrderProfit:
      type: object
      properties:
        profitCode:
          type: string
          title: 优惠码
        profitTime:
          type: string
          title: 优惠时间
        shopName:
          type: string
          title: 商户名称
        profitCharge:
          type: string
          title: 优惠金额面值
        profitChargeValue:
          type: string
          title: 生效金额
        getTime:
          type: string
          title: 优惠下发时间
        memo:
          type: string
          title: 备注
        lcOrderId:
          type: string
        lcOrderObj: *ref_8
        orderId:
          type: string
          title: 订单号
        createdAt:
          type: string
      title: 停车订单优惠
      required:
        - profitCode
        - profitTime
        - shopName
        - profitCharge
        - profitChargeValue
        - getTime
        - memo
        - orderId
        - createdAt
        - lcOrderId
        - lcOrderObj
    TUser:
      type: object
      properties:
        _id:
          type: string
        nickname:
          type: string
          title: 昵称
        realname:
          type: string
          title: 真名
        mobile:
          type: string
          title: 手机号
        avatar:
          type: string
          title: 头像
        wxOpenId:
          type: string
          title: WxOpenId
        lastLoginAt:
          type: string
          title: 最后登录时间
        lastLoginIp:
          type: string
          title: 最后登录Ip
        gender:
          type: string
          title: 性别
          description: User_gender
        favCount:
          type: integer
          title: 收藏个数
        businessType:
          type: string
          description: User_businessType
          title: 商家类型
      title: 用户信息
      required:
        - _id
        - nickname
        - realname
        - mobile
        - avatar
        - wxOpenId
        - lastLoginAt
        - lastLoginIp
        - gender
        - favCount
        - businessType
    TLcOrderFee:
      type: object
      properties:
        lcOrderId:
          type: string
          title: 停车订单ID
        inTime:
          type: string
          title: 入场时间
        moneyCent:
          type: string
          description: 单位分
          title: 停车费原价
        discountCent:
          type: string
          description: 单位分
          title: 优惠券抵扣金额
        payMoneyCent:
          type: string
          title: 实际待付金额
          description: 单位分
      title: 停车订单费用
      required:
        - lcOrderId
        - inTime
        - moneyCent
        - discountCent
        - payMoneyCent
    TAuction:
      type: object
      properties:
        _id:
          type: string
        title:
          type: string
          title: 标题
        targetId:
          type: string
          title: 拍卖物品id
        targetObj:
          type: object
          properties: { }
          title: 拍卖物品对象
        targetModel:
          type: string
          description: Auction_targetModel 商铺;摊位;广告位
          title: 拍卖物品类型
        depositCent:
          type: number
          title: 保证金
          description: 单位分
        bidStartCent:
          type: number
          title: 起拍价
          description: 单位分
        bidIncCent:
          type: number
          title: 竞价幅度
          description: 单位分
        startAt:
          type: string
          title: 开始时间
        freeCycleMins:
          type: string
          title: 自由竞价周期
          description: Auction_bidCycle分钟数字符串
        delayCycleMins:
          type: string
          description: Auction_delayCycle分钟数字符串
          title: 延时竞价周期
        endAt:
          type: string
          title: 结束时间
          description: 存在此值表示拍卖结束
        status:
          type: boolean
          title: 启用状态
          description: 是否启用
        result:
          type: string
          title: 拍卖结果
          description: Auction_result 正常结束;流拍;提前终止
        dealUserId:
          type: string
          title: 买受人id
        dealUserObj: *ref_0
        dealCent:
          type: number
          title: 成交价格
          description: '单位分 '
        viewCount:
          type: number
          title: 浏览次数
        createdAt:
          type: string
          title: 创建时间
        manualStopAt:
          type: string
          title: 手动终止时间
        manualStopReason:
          type: string
          title: 手动终止理由
        myDepositOrderObj:
          title: 我的保证金订单
          $ref: '#/components/schemas/TOrder'
        lastBidCent:
          type: number
          title: 最后出价
          description: 单位分
        tailPaidAt:
          type: string
          title: 尾款支付时间
          description: 小程序仅买受人可见
        tailBreakDefaultAt:
          type: string
          title: 尾款违约默认时间
          description: 小程序仅买受人可见
        tailBreakAt:
          type: string
          title: 尾款违约时间
          description: 小程序仅买受人可见
        tailBreakRemark:
          type: string
          title: 尾款违约备注
          description: 小程序不可见
        deliveredAt:
          type: string
          title: 交割时间
          description: 小程序仅买受人可见
        deliveredRemark:
          type: string
          description: 小程序不可见
          title: 交割备注
        deliveredFiles:
          type: array
          items:
            type: string
          title: 交割附件
          description: 小程序不可见
        runStatusText:
          type: string
          title: 运行状态
          description: 小程序特定接口才有，待开拍/竞价中/已结束(买受人细分为：尾款违约/已交割/待交割/待付尾款)
        contentObj:
          type: object
          properties:
            bulletin:
              type: string
              title: 拍卖公告
            thingsToKnow:
              type: string
              title: 竞买须知
          required:
            - bulletin
            - thingsToKnow
      title: 拍卖
      required:
        - _id
        - title
        - targetObj
        - targetModel
        - targetId
        - depositCent
        - bidStartCent
        - bidIncCent
        - startAt
        - freeCycleMins
        - delayCycleMins
        - endAt
        - status
        - result
        - dealUserId
        - dealUserObj
        - dealCent
        - viewCount
        - createdAt
        - manualStopAt
        - manualStopReason
        - myDepositOrderObj
        - lastBidCent
        - runStatusText
        - tailPaidAt
        - tailBreakDefaultAt
        - tailBreakAt
        - tailBreakRemark
        - deliveredRemark
        - deliveredAt
        - deliveredFiles
        - contentObj
    TOrder:
      type: object
      properties:
        _id:
          type: string
        entityId:
          type: string
          title: 商品id
        entityObj:
          type: object
          properties: { }
          title: 商品
        userId:
          type: string
          title: 用户Id
        userObj: *ref_0
        originalMoneyCent:
          type: number
          title: 原价金额
          description: 单位分
        couponDiscountCent:
          type: number
          title: 优惠券抵扣金额
          description: 单位分
        pointDiscountCent:
          type: number
          title: 积分抵扣金额
          description: 单位分
        orderMoneyCent:
          type: number
          title: 订单金额
          description: 单位分，应付金额
        paidMoneyCent:
          type: number
          title: 实付金额
          description: 单位分
        payStatus:
          type: string
          title: 支付状态
          description: Order_payStatus 未支付；已支付；已退款；部分退款；已关闭
        paidAt:
          type: string
          title: 付款时间
        closedAt:
          type: string
          title: 关闭时间
        closedReason:
          type: string
          title: 关闭原因
        remark:
          type: string
          title: 备注
        createdAt:
          type: string
          title: 创建时间
        refundList:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                title: 退款交易单号
              moneyCent:
                type: string
                title: 退款金额
              feeCent:
                type: string
                title: 退款手续费
              trxid:
                type: string
                title: 通联退款流水号
              operatedAt:
                type: string
                title: 退款操作时间
              refundedAt:
                type: string
                title: 退款成功时间
              reason:
                type: string
                title: 退款原因
            required:
              - id
              - moneyCent
              - feeCent
              - trxid
              - operatedAt
              - refundedAt
              - reason
          title: 退款列表
      title: 通用订单
      required:
        - _id
        - entityId
        - entityObj
        - userId
        - userObj
        - orderMoneyCent
        - paidMoneyCent
        - payStatus
        - paidAt
        - closedAt
        - closedReason
        - remark
        - createdAt
        - refundList
        - originalMoneyCent
        - pointDiscountCent
        - couponDiscountCent
    TUserPrivate:
      type: object
      properties:
        _id:
          type: string
        userId:
          type: string
        realname:
          type: string
          title: 姓名
        idCard:
          type: string
          title: 身份证号
        mobile:
          type: string
          title: 手机号
        address:
          type: string
          title: 地址
        auctionId:
          type: string
          title: 拍卖ID
          description: 此值存在说明是拍卖专用
        orderId:
          type: string
          title: 订单ID
          description: 用于绑定订单
      title: 用户私密信息
      required:
        - _id
        - userId
        - realname
        - idCard
        - mobile
        - address
    TCoupon:
      type: object
      properties:
        _id:
          type: string
        title:
          type: string
          title: 标题
        couponCode:
          type: string
          title: 优惠券编码
        useSceneType:
          type: string
          description: Coupon_useSceneType
          title: 使用场景
        type:
          type: string
          description: Coupon_type “0”小时券,”1”金额券 "2"折扣券
          title: 类型
        parValue:
          type: number
          title: 面值
          description: 小时券,单位分钟，金额券,单位分 折扣券 0表示免费10表示1折85表示85折90表示9折
        endAt:
          type: string
          title: 截至时间
        couponTplId:
          type: string
          title: 优惠券模板id
        couponTplObj:
          title: 优惠券模板
          $ref: '#/components/schemas/TLcCouponTpl'
        userId:
          type: string
          title: 用户id
        userObj: *ref_0
        orderId:
          type: string
          title: 订单id
        orderObj: *ref_8
        plate:
          type: string
          title: 车牌号
        usedAt:
          type: string
          title: 使用时间
          description: 存在此项表示已使用
        sourceType:
          type: string
          description: Coupon_sourceType
          title: 来源类型
        sourceId:
          type: string
          title: 来源ID
        sourceModel:
          type: string
          title: 来源模型
          description: Coupon_sourceModel
      title: 优惠券
      required:
        - _id
        - couponCode
        - type
        - parValue
        - endAt
        - couponTplObj
        - userObj
        - orderObj
        - orderId
        - plate
        - usedAt
        - couponTplId
        - userId
        - useSceneType
        - sourceType
        - sourceId
        - sourceModel
        - title
    TShop:
      type: object
      properties:
        _id:
          type: string
          title: ID
        cellNo:
          type: string
          title: 商铺编码
        title:
          type: string
          title: 标题
        cover:
          type: string
          title: 封面
        videos:
          type: array
          items:
            type: string
          title: 视频
        images:
          type: array
          items:
            type: string
          title: 图片
        houseType:
          type: array
          items:
            type: string
          title: 户型
        tags:
          type: array
          items:
            type: string
          title: 标签
        payment:
          type: string
          title: 付款方式
        orientation:
          type: string
          title: 朝向
        floor:
          type: integer
          title: 楼层
        decoration:
          type: string
          title: 装修
        cellId:
          type: integer
          title: 单元ID
        cellName:
          type: string
          title: 单元名称
        cellProperty:
          type: string
          title: 房产性质
        cellArea:
          type: number
          title: 套内面积
        buildingArea:
          type: number
          title: 建筑面积
        cellStatus:
          type: string
          title: 单元状态
        contractStartDate:
          type: string
          title: 合同开始日期
        contractEndDate:
          type: string
          title: 合同结束日期
        contractNo:
          type: string
          title: 合同编号
        businessType:
          type: array
          items:
            type: string
          title: 经营类型
        cellRemark:
          type: string
          title: 单元备注
        attachmentNames:
          type: string
          title: 附件名称
        attachmentPaths:
          type: string
          title: 附件路径
        costName:
          type: string
          title: 费用名称
        unitPrice:
          type: string
          title: 单价
        billingCycle:
          type: string
          title: 付款方式
        chargeAmount:
          type: string
          title: 费用金额
        tenantId:
          type: string
          title: 租户Id
        tenantName:
          type: string
          title: 租户名称
        tenantPhone:
          type: string
          title: 租户电话
        contactPerson:
          type: string
          title: 联系人
        contactPhone:
          type: string
          title: 联系人电话
        tenantType:
          type: string
          title: 租户类型
          description: 1-个人；2-公司
        tenantIdCard:
          type: string
          title: 租户身份证号
        tenantAddress:
          type: string
          title: 租户地址
        businessLicense:
          type: string
          title: 营业执照号
        tenantRemark:
          type: string
          title: 租户备注
        onShelf:
          type: boolean
          title: 是否上架
        auctionObj: *ref_6
        region:
          type: array
          items:
            type: string
          title: 区域
        onShelfAt:
          type: string
          title: 上架时间
        viewCount:
          type: integer
          title: 浏览量
        favouriteCount:
          type: string
          title: 收藏量
        brokerCalledCount:
          type: string
          title: 通话量
        recommend:
          type: number
          title: 推荐值
          description: 大于零表示推荐
        overallRank:
          type: integer
          title: 总排名
        inAreaRank:
          type: integer
          title: 同馆排名
      required:
        - cellId
        - cellNo
        - cellName
        - cellProperty
        - cellArea
        - buildingArea
        - cellStatus
        - contractStartDate
        - contractEndDate
        - contractNo
        - businessType
        - videos
        - images
        - houseType
        - tags
        - title
        - payment
        - orientation
        - floor
        - decoration
        - _id
        - cover
        - onShelf
        - region
        - onShelfAt
        - favouriteCount
        - brokerCalledCount
        - recommend
        - overallRank
        - inAreaRank
        - auctionObj
      title: 商铺
    TFavourite:
      type: object
      properties:
        _id:
          type: string
        type:
          type: string
          title: 类型
          description: 1-商铺(Tenant); 2-攻略(Article); 3-景点动态(Article)
        title:
          type: string
          title: 标题
          description: 添加一个标题可用于搜索
        entityModel:
          type: string
          title: 实体Model
        entityId:
          type: string
          title: 实体ID
        entityObj:
          type: object
          properties: { }
          title: 实体
          description: 根据type决定加载的实体类型
        userId:
          type: string
          title: 用户id
        userObj:
          type: string
          title: 用户
      title: 收藏
      required:
        - _id
        - type
        - entityModel
        - entityId
        - entityObj
        - title
        - userId
        - userObj
    TArticle:
      type: object
      properties:
        _id:
          type: string
        title:
          type: string
          title: 标题
        cover:
          type: string
          title: 封面
        intro:
          type: string
          title: 简介
        webViewUrl:
          type: string
          title: 跳转链接
          description: 此项不为空，则文章详情页将跳转到该链接
        content:
          type: string
          title: 内容
        categoryId:
          type: array
          items:
            type: string
          title: 分类
        tags:
          type: array
          items:
            type: string
          title: 标签
        viewCount:
          type: integer
          title: 查看数
        favouriteCount:
          type: string
          title: 收藏数
        createdById:
          type: string
          title: 创建人
        createdByObj:
          $ref: '#/components/schemas/TSimpleUserInfo'
        createdAt:
          type: string
          title: 创建时间
        isRecommend:
          type: boolean
          title: 是否热点推荐
      title: 文章
      required:
        - _id
        - title
        - cover
        - intro
        - content
        - tags
        - createdAt
        - createdById
        - categoryId
        - viewCount
        - favouriteCount
        - createdByObj
        - isRecommend
        - webViewUrl
    TSimpleUserInfo:
      type: object
      properties:
        _id:
          type: string
        nickname:
          type: string
          title: 昵称
        avatar:
          type: string
          title: 头像
      title: 简单用户信息
      required:
        - _id
        - nickname
        - avatar
    TWeather:
      type: object
      properties:
        obsTime:
          type: string
        temp:
          type: string
        icon:
          type: string
        text:
          type: string
        wind360:
          type: string
        windDir:
          type: string
        windScale:
          type: string
        windSpeed:
          type: string
        humidity:
          type: string
        precip:
          type: string
        pressure:
          type: string
        cloud:
          type: string
        dew:
          type: string
      required:
        - obsTime
        - temp
        - icon
        - text
        - wind360
        - windDir
        - windScale
        - windSpeed
        - humidity
        - precip
        - pressure
        - cloud
        - dew
      title: 天气
    TBroker:
      type: object
      properties:
        _id:
          type: string
        name:
          type: string
          title: 姓名
        avatar:
          type: string
          title: 头像
        mobile:
          type: string
          title: 手机号
        jobTitle:
          type: string
          title: 头衔
        jobPosition:
          type: string
          title: 职位
        pastVolumeCount:
          type: integer
          title: 历史成交量
        winPercent:
          type: string
          title: 战胜比例
          description: 每天跑个任务，排序之后更新，
      title: 经纪人
      required:
        - _id
        - name
        - jobTitle
        - avatar
        - mobile
        - winPercent
        - jobPosition
        - pastVolumeCount
    TShopSubscribe:
      type: object
      properties:
        _id:
          type: string
        title:
          type: string
          title: 订阅标题
        filter:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
              type:
                type: string
              value:
                type: string
            required:
              - field
              - type
              - value
          title: 订阅的过滤器
        userId:
          type: string
          title: 用户ID
        createdAt:
          type: string
          title: 创建时间
      title: 商铺订阅
      required:
        - _id
        - filter
        - title
        - userId
        - createdAt
    TFeedback:
      type: object
      properties:
        _id:
          type: string
        type:
          type: string
          description: Feedback_type
          title: 类型
        content:
          type: string
          title: 内容
        images:
          type: array
          items:
            type: string
          title: 图片
        mobile:
          type: string
          title: 用户输入手机号
        userId:
          type: string
          title: 用户id
        userObj:
          anyOf:
            - type: string
              title: 用户id
            - *ref_0
          title: 用户
          description: 用户id或用户信息
        remark:
          type: string
          title: 备注
        handledAt:
          type: string
          title: 处理时间
        createdAt:
          type: string
          title: 创建时间
      title: 投诉建议
      required:
        - _id
        - content
        - type
        - images
        - userObj
        - createdAt
        - remark
        - handledAt
        - userId
        - mobile
    TSystemVar:
      type: object
      properties:
        serviceTel:
          type: string
          title: 服务电话
        auctionTailCycleDays:
          type: number
          title: 拍卖尾款支付周期
          description: 单位天
        homeNavIcons:
          type: array
          items:
            type: object
            properties:
              icon:
                type: string
              label:
                type: string
              url:
                type: string
              appid:
                type: string
            required:
              - icon
              - label
              - url
              - appid
              - 01J8RVBQMJK93KB3WGP1N6HA8Q
          title: 首页图标导航
        artworkServiceTel:
          type: string
          title: 艺术展品服务电话
      title: 用户小程序系统变量
      required:
        - serviceTel
        - auctionTailCycleDays
        - homeNavIcons
        - artworkServiceTel
    TWxPayParams:
      type: object
      properties:
        paid:
          type: boolean
          title: 是否已支付
          description: 一般是0元订单，自动支付，不再调用微信支付
        appid:
          description: 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致
          type: string
          title: AppId
        noncestr:
          type: string
          title: 随机字符串
        package:
          type: string
          title: 固定值
        partnerid:
          type: string
          title: 微信支付商户号
        prepayid:
          type: string
          title: 统一下单订单号
        timestamp:
          description: 单位：秒
          type: integer
          title: 时间戳
        sign:
          description: 这里用的 MD5/RSA 签名
          type: string
          title: 签名
      required:
        - appid
        - noncestr
        - package
        - partnerid
        - prepayid
        - timestamp
        - sign
      title: 微信支付参数
    TArtwork:
      type: object
      properties:
        _id:
          type: string
        title:
          type: string
          title: 标题
        artist:
          type: string
          title: 艺术家
        images:
          type: array
          items:
            type: object
            properties:
              url:
                type: string
                title: 地址
              width:
                type: number
                title: 宽
              height:
                type: number
                title: 高
            required:
              - url
              - width
              - height
          title: 图片
        categoryId:
          type: array
          items:
            type: string
          title: 分类
          description: 字典 artworkCategory
        content:
          type: string
          title: 详细介绍
        viewCount:
          type: string
          title: 浏览量
        status:
          type: boolean
          title: 是否发布
        createdAt:
          type: string
          title: 创建时间
        size:
          type: string
          title: 尺寸
      title: 艺术品
      required:
        - _id
        - images
        - title
        - artist
        - categoryId
        - content
        - viewCount
        - status
        - createdAt
        - size
  securitySchemes:
    bearer:
      type: http
      scheme: bearer
servers: [ ]
