package com.dounanflowers.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ClientLcOrderFeeDto {

    @Schema(title = "车牌号")
    private String plate;

    @Schema(title = "优惠券ID")
    private Long couponId;

    @Schema(title = "蓝卡订单ID，测试用")
    private String bcOrderId;

    @Schema(title = "订单ID")
    private Long lcOrderId;

    @Schema(title = "优惠券IDs")
    private List<Long> couponIds;

}

