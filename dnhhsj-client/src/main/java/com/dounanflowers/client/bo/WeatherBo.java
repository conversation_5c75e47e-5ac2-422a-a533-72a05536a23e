package com.dounanflowers.client.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "天气")
public class WeatherBo {

    private String obsTime;

    private String temp;

    private String feelsLike;

    private String icon;

    private String text;

    private String wind360;

    private String windDir;

    private String windScale;

    private String windSpeed;

    private String humidity;

    private String precip;

    private String pressure;

    private String vis;

    private String cloud;

    private String dew;

}

