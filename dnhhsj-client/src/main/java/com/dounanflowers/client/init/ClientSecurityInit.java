package com.dounanflowers.client.init;

import com.dounanflowers.security.utils.SecurityHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ClientSecurityInit implements ApplicationListener<ApplicationStartedEvent> {

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        log.info("init client security config");
        SecurityHolder.createContext("client");
    }

}
