package com.dounanflowers.client.api;

import com.dounanflowers.common.bo.ModalBannerBo;
import com.dounanflowers.common.enums.ClientTypeEnum;
import com.dounanflowers.common.service.ModalBannerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/client/modalBanner")
@Tag(name = "小程序/弹窗横幅管理")
@RequiredArgsConstructor
public class ClientModalBannerApi {

    private final ModalBannerService modalBannerService;

    @PostMapping("/showList")
    @Operation(summary = "弹窗横幅列表")
    public List<ModalBannerBo> modalBannerList() {
        return modalBannerService.getModalBannerShowList(ClientTypeEnum.CLIENT);
    }
}
