package com.dounanflowers.client.api;

import com.dounanflowers.common.bo.ClientUserBo;
import com.dounanflowers.common.bo.FavouriteBo;
import com.dounanflowers.common.dto.FavouriteCheckDto;
import com.dounanflowers.common.dto.FavouriteToggleDto;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.common.service.FavouriteService;
import com.dounanflowers.common.vo.FavouriteCheckVo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/client")
@Tag(name = "小程序/收藏")
@RequiredArgsConstructor
public class ClientFavouriteApi {

    private final FavouriteService favouriteService;

    @PostMapping("/favouritePageList")
    @Operation(summary = "收藏分页列表")
    public Page<FavouriteBo> favouritePageList(@RequestBody PageRequest pageRequest) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        pageRequest.addFilter(new PageFilter().setField("userId").setType("eq").setValue(userId));
        return favouriteService.page(pageRequest);
    }

    @PostMapping("/favouriteToggle")
    @Operation(summary = "切换收藏状态", description = "forceAction字段不传时，自动切换收藏状态，否则根据此字段bool值添加收藏或取消收藏")
    public Boolean favouriteToggle(@RequestBody FavouriteToggleDto dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        favouriteService.toggle(dto, userId, UserTypeEnum.CLIENT);
        return true;
    }

    @PostMapping("/favouriteCheck")
    @Operation(summary = "检查是否收藏")
    public FavouriteCheckVo favouriteCheck(@RequestBody FavouriteCheckDto dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        Boolean isFavourite = favouriteService.check(dto, userId, UserTypeEnum.CLIENT);
        FavouriteCheckVo vo = new FavouriteCheckVo();
        vo.setIsFavourite(isFavourite);
        return vo;
    }

}
