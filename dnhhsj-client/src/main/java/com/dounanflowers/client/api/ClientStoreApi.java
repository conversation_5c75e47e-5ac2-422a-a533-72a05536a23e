package com.dounanflowers.client.api;

import com.dounanflowers.client.service.ClientStoreService;
import com.dounanflowers.common.bo.StoreBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/client/store")
@Tag(name = "小程序/店铺")
@RequiredArgsConstructor
public class ClientStoreApi {

    private final ClientStoreService storeService;

    @PostMapping("/page")
    @Operation(summary = "店铺列表")
    public Page<StoreBo> page(@RequestBody PageRequest pageRequest) {
        return storeService.page(pageRequest);
    }

    @PostMapping("/storeGetById")
    @Operation(summary = "店铺详情")
    public StoreBo storeGetById(@RequestBody EditIdDto dto) {
        return storeService.getById(dto.getId());
    }
}
