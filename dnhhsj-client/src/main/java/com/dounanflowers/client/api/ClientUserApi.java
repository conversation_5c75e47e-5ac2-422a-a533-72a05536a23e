package com.dounanflowers.client.api;

import com.dounanflowers.client.dto.ClientUserUpdateSelfDto;
import com.dounanflowers.client.service.ClientProductService;
import com.dounanflowers.common.bo.ClientUserBo;
import com.dounanflowers.common.bo.ProductReviewBo;
import com.dounanflowers.common.dto.UserRealVerifyDto;
import com.dounanflowers.common.manager.ShoppingCartManager;
import com.dounanflowers.common.service.ClientUserService;
import com.dounanflowers.common.service.CouponService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/client")
@Tag(name = "小程序/用户")
@RequiredArgsConstructor
public class ClientUserApi {

    private final ClientUserService clientUserService;

    private final ClientProductService clientProductService;

    private final ShoppingCartManager cartManager;

    private final CouponService couponService;

    @PostMapping("/session")
    @Operation(summary = "获取自己用户信息")
    @Authorized
    public ClientUserBo myUserInfoGet() {
        ClientUserBo userInfo = SecurityHolder.<Long, ClientUserBo>session().getUserInfo();
        userInfo.setCouponCount(couponService.countByUserId(userInfo.getId()));
        userInfo.setCartCount(cartManager.countByUserId(userInfo.getId()));
        SecurityHolder.update(SecurityHolder.session());
        return userInfo;
    }

    @PostMapping("/myUserInfoUpdate")
    @Operation(summary = "修改自己用户信息")
    public ClientUserBo myUserInfoUpdate(@RequestBody ClientUserUpdateSelfDto dto) {
        return clientUserService.updateSelf(dto.getNickname(), dto.getAvatar(), dto.getGender());
    }

    @PostMapping("/userRealnameVerify")
    @Operation(summary = "身份证实名认证", description = "身份证需要调取拍照，可以上传拍照的缩略图  \n返回的userInfo中realname字段为最新实名认证的姓名，可用于判断是否实名认证")
    public SessionInfo<Long, ClientUserBo> userRealnameVerify(@RequestBody UserRealVerifyDto dto) {
        clientUserService.realnameVerify(dto);
        return SecurityHolder.session();
    }

    @PostMapping("/myReviewList")
    @Operation(summary = "我的评价列表")
    public Page<ProductReviewBo> myReviewList(@RequestBody PageRequest dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        dto.addFilter(new PageFilter().setField("userId").setType("eq").setValue(userId));
        return clientProductService.myReviewList(dto);
    }

    @PostMapping("/unbind/mobile")
    @Operation(summary = "解绑手机号")
    @Authorized
    public SessionInfo<Long, ClientUserBo> unbindMobile() {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        clientUserService.unbindMobile(userId);
        return SecurityHolder.session();
    }

    @PostMapping("/unbind/openid")
    @Operation(summary = "解绑手机号")
    @Authorized
    public SessionInfo<Long, ClientUserBo> unbindOpenid() {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        clientUserService.unbindOpenid(userId);
        return SecurityHolder.session();
    }

    @PostMapping("/user/delete")
    @Operation(summary = "删除账号")
    @Authorized
    public void logoutAccount() {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        clientUserService.deleteUser(userId);
        SecurityHolder.logout();
    }

}
