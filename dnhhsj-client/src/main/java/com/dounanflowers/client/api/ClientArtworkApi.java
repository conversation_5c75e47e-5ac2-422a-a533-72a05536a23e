package com.dounanflowers.client.api;

import com.dounanflowers.common.bo.ArtworkBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.service.ArtworkService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/client")
@Tag(name = "小程序/艺术品")
@RequiredArgsConstructor
public class ClientArtworkApi {

    private final ArtworkService artworkService;

    @PostMapping("/artworkPageList")
    @Operation(summary = "艺术品分页列表", description = "```js\n[\n   {field: 'categoryId', type: 'in', value: ['2']}, // 朝鲜文化\n   {field: 'categoryId', type: 'in', value: ['21']}, // 功勋画家\n]\n```")
    public Page<ArtworkBo> artworkPageList(@RequestBody PageRequest dto) {
        return artworkService.artworkPageList(dto);
    }

    @PostMapping("/artworkGetById")
    @Operation(summary = "艺术品详情")
    public ArtworkBo artworkGetById(@RequestBody EditIdDto dto) {
        return artworkService.getById(dto.getId());
    }

}
