package com.dounanflowers.client.api;

import com.dounanflowers.client.dto.ClientLcOrderFeeDto;
import com.dounanflowers.client.dto.ClientLcOrderPayDto;
import com.dounanflowers.client.dto.ClientLcPlateEditDto;
import com.dounanflowers.common.bo.ClientUserBo;
import com.dounanflowers.common.bo.LcOrderBo;
import com.dounanflowers.common.bo.LcOrderFeeBo;
import com.dounanflowers.common.bo.LcPlateBo;
import com.dounanflowers.common.dto.HandleStatusBo;
import com.dounanflowers.common.service.LcService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.bo.WxPayParamsBo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/client")
@Tag(name = "小程序/停车")
@RequiredArgsConstructor
public class ClientLcApi {

    private final LcService lcService;

    @PostMapping("/lcPlateList")
    @Operation(summary = "车牌列表", description = "用户已经绑定的车牌号列表  \n用于付款停车费时选择")
    @Authorized
    public List<String> lcPlateList() {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        return lcService.lcPlateListByClientUserId(userId);
    }


    @PostMapping("/lcOrderFee")
    @Operation(summary = "停车费订单费用获取", description = "车牌号和无牌车票号必选一个")
    public LcOrderFeeBo lcOrderFee(@RequestBody ClientLcOrderFeeDto dto) {
        return lcService.lcOrderFee(dto.getPlate(), dto.getCouponId(), dto.getLcOrderId());
    }

    @PostMapping("/lcOrderPay")
    @Operation(summary = "停车费订单支付")
    @Authorized
    public WxPayParamsBo lcOrderPay(@RequestBody ClientLcOrderPayDto dto) {
        ClientUserBo userInfo = SecurityHolder.<Long, ClientUserBo>session().getUserInfo();
        return lcService.lcOrderPay(dto.getLcOrderId(), dto.getCouponId(), userInfo);
    }


    @PostMapping("/lcOrderFee2")
    @Operation(summary = "停车费订单费用获取", description = "车牌号和无牌车票号必选一个")
    public LcOrderFeeBo lcOrderFee2(@RequestBody ClientLcOrderFeeDto dto) {
        return lcService.lcOrderFee2(dto.getPlate(), dto.getCouponIds(), dto.getLcOrderId());
    }

    @PostMapping("/lcOrderPay2")
    @Operation(summary = "停车费订单支付")
    @Authorized
    public WxPayParamsBo lcOrderPay2(@RequestBody ClientLcOrderPayDto dto) {
        ClientUserBo userInfo = SecurityHolder.<Long, ClientUserBo>session().getUserInfo();
        return lcService.lcOrderPay2(dto.getLcOrderId(), dto.getCouponIds(), userInfo);
    }


    @PostMapping("/lcPlateAdd")
    @Operation(summary = "添加车牌")
    @Authorized
    public LcPlateBo lcPlateAdd(@RequestBody ClientLcPlateEditDto dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        return lcService.lcPlateAdd(dto.getPlate(), userId);
    }

    @PostMapping("/lcPlateDelete")
    @Operation(summary = "删除车牌")
    @Authorized
    public HandleStatusBo lcPlateDelete(@RequestBody ClientLcPlateEditDto dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        lcService.lcPlateDelete(dto.getPlate(), userId);
        return HandleStatusBo.success();
    }

    @PostMapping("/lcOrderPageList")
    @Operation(summary = "停车费订单分页列表")
    @Authorized
    public Page<LcOrderBo> lcOrderPageList(@RequestBody PageRequest pageRequest) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        PageFilter pageFilter = new PageFilter();
        pageFilter.setField("clientUserId");
        pageFilter.setType("eq");
        pageFilter.setValue(userId);
        pageRequest.addFilter(pageFilter);
        return lcService.lcOrderPage(pageRequest);
    }

    @PostMapping("/testPay")
    @Operation(summary = "停车费订单支付回调")
    public WxPayParamsBo testPay() {
        return lcService.testPay("o4PEK7W1bK2PmpXjOcjQnNDA_RB0");
    }

    @PostMapping("/lcOrderNoPlate")
    @Operation(summary = "未出库无牌车列表")
    public Page<LcOrderBo> LcOrderNoPlate(@RequestBody PageRequest pageRequest) {
        return lcService.lcOrderNoPlate(pageRequest);
    }

}
