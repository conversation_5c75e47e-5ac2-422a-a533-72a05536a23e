package com.dounanflowers.client.api;

import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.dto.InvoiceApplyDto;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.common.service.InvoiceService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/client/invoice")
@Tag(name = "小程序/发票")
@RequiredArgsConstructor
public class ClientInvoiceApi {
    private final InvoiceService invoiceService;
    

    @PostMapping("/page")
    @Operation(summary = "发票分页列表")
    @Authorized
    public Page<InvoiceBo> pageInvoices(@RequestBody PageRequest dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        dto.addFilter(new PageFilter().setField("userId").setType("eq").setValue(userId));
        return invoiceService.pageInvoices(dto);
    }

    @PostMapping("/apply")
    @Operation(summary = "发票申请")
    @Authorized
    public Boolean applyInvoice(@RequestBody InvoiceApplyDto dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        invoiceService.applyInvoice(dto, userId, UserTypeEnum.CLIENT);
        return true;
    }

    /**
     * 获取发票详情
     */
    @PostMapping("/get")
    @Operation(summary = "获取发票详情")
    @Authorized
    public InvoiceBo getInvoiceDetail(@RequestBody EditIdDto dto) {
        return invoiceService.fetchById(dto.getId());
    }

    @PostMapping("/pageBillsForInvoice")
    @Operation(summary = "可开发票账单分页列表")
    @Authorized
    public Page<BillBo> pageBillsForInvoice(@RequestBody PageRequest pageRequest) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        return invoiceService.pageBillsForInvoice(pageRequest, userId, UserTypeEnum.CLIENT);
    }


    @PostMapping("/headerInfoList")
    @Operation(summary = "既往发票抬头列表")
    @Authorized
    public List<InvoiceHeaderInfoBo> headerInfoList() {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        return invoiceService.invoiceHeaderInfoList( userId);
    }

    @PostMapping("/count")
    @Operation(summary = "发票统计")
    @Authorized
    public InvoiceCountBo count() {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        return invoiceService.count( userId, UserTypeEnum.CLIENT);
    }
}
