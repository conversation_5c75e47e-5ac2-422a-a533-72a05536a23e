package com.dounanflowers.client.api;

import com.dounanflowers.client.dto.WxLoginDto;
import com.dounanflowers.client.service.ClientWxService;
import com.dounanflowers.common.bo.ClientUserBo;
import com.dounanflowers.common.dto.CodeLoginDto;
import com.dounanflowers.common.dto.TwoFactorLoginDto;
import com.dounanflowers.common.service.ClientUserService;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/client/wx")
@Tag(name = "小程序/登录")
@RequiredArgsConstructor
public class ClientWxApi {

    private final ClientWxService clientWxService;

    private final ClientUserService clientUserService;

    @PostMapping("/login")
    @Operation(summary = "微信自动登录", description = "用户打开小程序执行一次")
    public SessionInfo<Long, ClientUserBo> login(@RequestBody WxLoginDto dto) {
        return clientWxService.login(dto.getCode(), dto.getPhoneNumberCode());
    }

    @PostMapping("/openId")
    @Operation(summary = "记录openId")
    @Authorized
    public SessionInfo<Long, ClientUserBo> openId(@RequestBody WxLoginDto dto) {
        return clientWxService.openId(dto.getCode());
    }

    @PostMapping("/loginByMobile")
    @Operation(summary = "单独通过手机号登录")
    public SessionInfo<Long, ClientUserBo> loginByMobile(@RequestBody WxLoginDto dto) {
        return clientWxService.loginByMobile(dto.getPhoneNumberCode());
    }

    @PostMapping("/mobile")
    @Operation(summary = "微信绑定手机号")
    @Authorized
    public SessionInfo<Long, ClientUserBo> updatePhone(@RequestBody WxLoginDto dto) {
        clientWxService.updatePhone(dto.getPhoneNumberCode());
        return SecurityHolder.session();
    }

    @PostMapping("/loginByMobileCode")
    @Operation(summary = "手机验证码登录(开发用)")
    public SessionInfo<Long, ClientUserBo> loginByMobileCode(@RequestBody CodeLoginDto dto) {
        return null;
    }

    @PostMapping("/logout")
    @Operation(summary = "退出登录")
    @Authorized
    public void logout() {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        clientWxService.logout(userId);
        SecurityHolder.logout();
    }

    @PostMapping("/openid")
    @Operation(summary = "绑定微信")
    @Authorized
    public SessionInfo<Long, ClientUserBo> bindWx(@RequestBody WxLoginDto dto) {
        clientWxService.bindWx(dto.getCode());
        return SecurityHolder.session();
    }

    @PostMapping("/loginBy2Fa")
    @Operation(summary = "2FA登录", description = "使用手机号+2FA验证码登录任意账号")
    public SessionInfo<Long, ClientUserBo> loginBy2Fa(@RequestBody TwoFactorLoginDto dto) {
        return clientUserService.loginBy2Fa(dto);
    }

}
