package com.dounanflowers.client.api;


import com.dounanflowers.client.dto.ClientActionCountIncDto;
import com.dounanflowers.common.service.ClientActionCountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/client/actionCount")
@Tag(name = "小程序/文章")
@RequiredArgsConstructor
public class ClientActionCountApi {
    private final ClientActionCountService clientActionCountService;

    @PostMapping("/inc")
    @Operation(summary = "添加访问次数")
    public boolean inc(@RequestBody ClientActionCountIncDto dto) {
         clientActionCountService.incCount(dto.getKey());
         return true;
    }

}
