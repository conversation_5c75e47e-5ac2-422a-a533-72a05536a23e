package com.dounanflowers.client.api;

import com.dounanflowers.client.dto.CartItemDto;
import com.dounanflowers.client.dto.CartUpdateDto;
import com.dounanflowers.client.service.ClientProductService;
import com.dounanflowers.common.bo.ClientUserBo;
import com.dounanflowers.common.bo.ShoppingCartBo;
import com.dounanflowers.common.bo.ShoppingCartStoreBo;
import com.dounanflowers.common.dto.CartSettlementDto;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.dto.ShoppingCartDto;
import com.dounanflowers.common.service.ShoppingCartService;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.bo.WxPayParamsBo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/client/cart")
@Tag(name = "小程序/购物车")
@RequiredArgsConstructor
@Authorized
public class ClientShoppingCartApi {

    private final ShoppingCartService shoppingCartService;

    private final ClientProductService clientProductService;

    @PostMapping("/add")
    @Operation(summary = "添加商品到购物车")
    public ShoppingCartBo addToCart(@RequestBody CartItemDto dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        // Check if the item already exists in cart
        List<ShoppingCartStoreBo> userCart = shoppingCartService.getByUserId(userId);
        for (ShoppingCartStoreBo store : userCart) {
            for (ShoppingCartBo item : store.getItems()) {
                if (item.getProductId().equals(dto.getProductId())) {
                    // Update quantity instead of adding new item
                    item.setQuantity(item.getQuantity() + dto.getQuantity());
                    return shoppingCartService.update(item.getId(), toDto(item));
                }
            }
        }
        // Add new item to cart
        ShoppingCartDto cartDto = new ShoppingCartDto();
        cartDto.setUserId(userId);
        cartDto.setProductId(dto.getProductId());
        cartDto.setQuantity(dto.getQuantity());
        return shoppingCartService.create(cartDto);
    }

    @PostMapping("/remove")
    @Operation(summary = "从购物车删除商品")
    public Boolean removeFromCart(@RequestBody EditIdDto dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        ShoppingCartBo cart = shoppingCartService.get(dto.getId());
        if (cart == null || !cart.getUserId().equals(userId)) {
            throw new BaseException("购物车项不存在或无权限删除");
        }
        shoppingCartService.delete(dto.getId());
        return true;
    }

    @PostMapping("/updateQuantity")
    @Operation(summary = "更新购物车商品数量")
    public ShoppingCartBo updateQuantity(@RequestBody CartUpdateDto dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        ShoppingCartBo cart = shoppingCartService.get(dto.getId());
        if (cart == null || !cart.getUserId().equals(userId)) {
            throw new BaseException("购物车项不存在或无权限修改");
        }
        ShoppingCartDto cartDto = toDto(cart);
        cartDto.setQuantity(dto.getQuantity());
        return shoppingCartService.update(cart.getId(), cartDto);
    }

    @PostMapping("/list")
    @Operation(summary = "获取当前用户的购物车列表")
    public List<ShoppingCartStoreBo> getCurrentUserCart() {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        return shoppingCartService.getByUserId(userId);
    }

    @PostMapping("/clear")
    @Operation(summary = "清空当前用户的购物车")
    public Boolean clearCart() {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        shoppingCartService.clearUserCart(userId);
        return true;
    }

    @PostMapping("/settlement")
    @Operation(summary = "购物车商品结算")
    public WxPayParamsBo cartSettlement(@RequestBody CartSettlementDto dto) {
        return clientProductService.cartSettlement(dto);
    }

    private ShoppingCartDto toDto(ShoppingCartBo bo) {
        ShoppingCartDto dto = new ShoppingCartDto();
        dto.setUserId(bo.getUserId());
        dto.setProductId(bo.getProductId());
        dto.setQuantity(bo.getQuantity());
        return dto;
    }
}
