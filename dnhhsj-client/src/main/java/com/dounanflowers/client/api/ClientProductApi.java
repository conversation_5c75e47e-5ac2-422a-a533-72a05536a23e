package com.dounanflowers.client.api;

import com.dounanflowers.client.service.ClientProductService;
import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.DirectPurchaseDto;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.dto.ProductRefundApplyDto;
import com.dounanflowers.common.dto.ProductReviewPageDto;
import com.dounanflowers.common.service.ProductRefundService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.bo.WxPayParamsBo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/client/product")
@Tag(name = "小程序/商品")
@RequiredArgsConstructor
public class ClientProductApi {

    private final ClientProductService clientProductService;

    private final ProductRefundService refundService;

    @PostMapping("/page")
    @Operation(summary = "分页列表")
    public Page<ProductBo> productPageList(@RequestBody PageRequest dto) {
        dto.addFilter(new PageFilter().setField("status").setType("in").setValue(List.of(1)));
        return clientProductService.productPageList(dto);
    }

    @PostMapping("/get")
    @Operation(summary = "详情")
    public ProductBo productGetById(@RequestBody EditIdDto dto) {
        return clientProductService.getById(dto.getId());
    }

    @PostMapping("/buy")
    @Operation(summary = "直接购买")
    @Authorized
    public WxPayParamsBo directPurchase(@RequestBody DirectPurchaseDto dto) {
        return clientProductService.directPurchase(dto);
    }

    @PostMapping("/myOrderPageList")
    @Operation(summary = "我的订单分页列表")
    @Authorized
    public Page<ProductOrderBo> orderPageList(@RequestBody PageRequest dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        dto.addFilter(new PageFilter().setField("userId").setType("eq").setValue(userId));
        dto.addFilter(new PageFilter().setField("merge").setType("eq").setValue(0));
        return clientProductService.orderPageList(dto);
    }

    @PostMapping("/orderDetail")
    @Operation(summary = "订单详情")
    public ProductOrderBo orderDetail(@RequestBody EditIdDto dto) {
        return clientProductService.getOrderDetail(dto.getId());
    }

    @PostMapping("/cancelOrder")
    @Operation(summary = "取消订单")
    @Authorized
    public void cancelOrder(@RequestBody EditIdDto dto) {
        clientProductService.cancelOrder(dto.getId());
    }

    @PostMapping("/payOrder")
    @Operation(summary = "支付订单")
    @Authorized
    public WxPayParamsBo payOrder(@RequestBody EditIdDto dto) {
        return clientProductService.payOrder(dto.getId());
    }

    @PostMapping("/reviewList")
    @Operation(summary = "商品评价列表")
    public Page<ProductReviewBo> productReviewList(@RequestBody ProductReviewPageDto dto) {
        if (dto.getProductId() != null) {
            dto.addFilter(new PageFilter().setField("productId").setType("eq").setValue(dto.getProductId()));
        } else if (dto.getStoreId() != null) {
            dto.addFilter(new PageFilter().setField("storeId").setType("custom").setValue(dto.getStoreId()));
        } else {
            throw new BaseException("productId或storeId必填");
        }
        dto.addFilter(new PageFilter().setField("content").setType("ne"));
        dto.addFilter(new PageFilter().setField("show").setType("eq").setValue(1));
        return clientProductService.productReviewList(dto);
    }

    @PostMapping("/review")
    @Operation(summary = "商品评价")
    @Authorized
    public void review(@RequestBody ProductReviewBo dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        dto.setUserId(userId);
        clientProductService.review(dto);
    }

    @PostMapping("/applyRefund")
    @Operation(summary = "申请退款")
    @Authorized
    public void apply(@RequestBody ProductRefundApplyDto dto) {
        refundService.applyRefund(dto);
    }

    @PostMapping("/refundDetail")
    @Operation(summary = "退款申请详情")
    @Authorized
    public ProductRefundApplyBo detail(@RequestBody EditIdDto dto) {
        return refundService.getDetail(dto.getId());
    }

    @PostMapping("/cancelRefund")
    @Operation(summary = "取消退款申请")
    @Authorized
    public void cancel(@RequestBody EditIdDto dto) {
        refundService.cancelRefund(dto.getId());
    }

    @PostMapping("/order/stats")
    @Operation(summary = "订单统计")
    @Authorized
    public ProductOrderStatsBo orderStats() {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        return clientProductService.orderStats(userId);
    }

}
