package com.dounanflowers.client.api;

import com.dounanflowers.common.bo.ArticleBo;
import com.dounanflowers.common.bo.ClientUserBo;
import com.dounanflowers.common.dto.ArticleGetDto;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.service.ArticleService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/client")
@Tag(name = "小程序/文章")
@RequiredArgsConstructor
public class ClientArticleApi {

    private final ArticleService articleService;

    @PostMapping("/articlePageList")
    @Operation(summary = "文章分页列表")
    public Page<ArticleBo> articlePageList(@RequestBody PageRequest dto) {
        return articleService.articlePageList(dto);
    }

    @PostMapping("/articleGetById")
    @Operation(summary = "文章详情")
    public ArticleBo articleGetById(@RequestBody ArticleGetDto dto) {
        if (dto.getId() != null) {
            return articleService.getById(dto.getId());
        } else if (StringUtils.isNotBlank(dto.getTitle())) {
            return articleService.getByTitle(dto.getTitle());
        }
        return null;
    }

    @PostMapping("/articleLike")
    @Operation(summary = "点赞/取消点赞")
    @Authorized
    public void toggleArticleLike(@RequestBody EditIdDto dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        articleService.toggleArticleLike(dto.getId(), userId);
    }

}
