package com.dounanflowers.client.api;

import com.dounanflowers.client.bo.WeatherBo;
import com.dounanflowers.client.dto.SendSmsDto;
import com.dounanflowers.client.dto.WeatherDto;
import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.*;
import com.dounanflowers.common.enums.ChannelEnum;
import com.dounanflowers.common.service.FileService;
import com.dounanflowers.common.service.SystemService;
import com.dounanflowers.framework.utils.HttpUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/client")
@Tag(name = "小程序/其他")
@RequiredArgsConstructor
public class ClientCommonApi {

    private final SystemService systemService;

    private final FileService fileService;

    @Value("${weather.key:''}")
    private String weatherKey;

    @PostMapping("/feedbackCreate")
    @Operation(summary = "投诉建议提交")
    public FeedbackBo feedbackCreate(@RequestBody FeedbackCreateDto dto) {
        SessionInfo<Long, ClientUserBo> session = SecurityHolder.session();
        if (session != null) {
            dto.setUserId(session.getUserId());
        }
        return systemService.feedbackCreate(dto, ChannelEnum.CLIENT);
    }

    @PostMapping("/feedbackGet")
    @Operation(summary = "投诉建议详情")
    public FeedbackBo feedbackGet(@RequestBody EditIdDto dto) {
        return systemService.feedbackGet(dto.getId());
    }

    @PostMapping("/bannerList")
    @Operation(summary = "轮播列表")
    public List<BannerShowBo> bannerList(@RequestBody BannerShowDto dto) {
        return systemService.getBannerShowList(dto.getPlace());
    }

    @PostMapping("/dictGetMap")
    @Operation(summary = "获取字典Map", description = "可以传入tags获取相关字典Map, Map的key是字典的key, Map的value是字典的options")
    public Map<String, DictBo> dictGetMap(@RequestBody DictMapGetDto dto) {
        return systemService.getDictMap(dto);
    }

    @PostMapping("/weatherGetByLocation")
    @Operation(summary = "通过位置获取天气")
    public WeatherBo weatherGetByLocation(@RequestBody WeatherDto weatherDto) {
        if (StringUtils.isBlank(weatherDto.getLocation())) {
            return null;
        }
        String url = "https://devapi.qweather.com/v7/weather/now?location=" + weatherDto.getLocation() + "&key=" + weatherKey;
        HttpUtils.HttpResult send = HttpUtils.get(url).send();
        Object parse = JsonUtils.parse(send.bodyAsString());
        String o = JsonUtils.toJson(((Map<?, ?>) parse).get("now"));
        return JsonUtils.toObject(o, WeatherBo.class);
    }

    @PostMapping("/sendSmsCode")
    @Operation(summary = "发送短信验证码")
    public String sendSmsCode(@RequestBody SendSmsDto dto) {
        // TODO: 验证图片验证码，发送短信
        // TODO: 检查发送频率
        return null;
    }

    @PostMapping("/dictGet")
    @Operation(summary = "获取单个字典")
    public List<DictOptionBo> dictGet(@RequestBody DictGetDto dto) {
        if (StringUtils.isNotBlank(dto.getKey())) {
            return systemService.getDictOptions(dto.getKey());
        } else {
            return systemService.getDictOptionsByName(dto.getName());
        }
    }

    @PostMapping("/upload")
    @Operation(summary = "上传文件")
    public FileBo upload(@RequestPart MultipartFile file) {
        return fileService.createFile(file);
    }

    @PostMapping("/systemVar")
    @Operation(summary = "获取系统变量")
    public Map<String, Object> systemVar() {
        return systemService.settingMap().getOrDefault("Mini", Map.of());
    }

}
