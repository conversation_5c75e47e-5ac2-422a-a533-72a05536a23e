package com.dounanflowers.client.api;

import com.dounanflowers.client.service.ClientProductService;
import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.DirectPurchaseDto;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.dto.ProductRefundApplyDto;
import com.dounanflowers.common.dto.ProductReviewPageDto;
import com.dounanflowers.common.service.BillService;
import com.dounanflowers.common.service.ProductRefundService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.bo.WxPayParamsBo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/client")
@Tag(name = "小程序/账单")
@RequiredArgsConstructor
public class ClientBillApi {

    private final BillService billService;


    @PostMapping("/billPageList")
    @Operation(summary = "订单分页列表")
    public Page<BillBo> billPageList(@RequestBody PageRequest pageRequest) {
        pageRequest.addFilter(new PageFilter().setField("userId").setType("eq").setValue(SecurityHolder.session().getUserId()));
        return billService.pageList(pageRequest);
    }
}
