package com.dounanflowers.client.api;

import com.dounanflowers.common.bo.ClientUserBo;
import com.dounanflowers.common.bo.CouponBo;
import com.dounanflowers.common.bo.CouponTplBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.dto.QrcodeDto;
import com.dounanflowers.common.enums.ClientTypeEnum;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.common.service.CouponService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/client")
@Tag(name = "小程序/优惠券")
@RequiredArgsConstructor
@Authorized
public class ClientCouponApi {

    private final CouponService couponService;



    @PostMapping("/couponPageList")
    @Operation(summary = "用户优惠券分页列表", description = "```js\n// filter\n[\n  {type: 'eq', field: 'useSceneType', value: 'xxx'}, // 使用场景类型 字典Coupon_useSceneType\n  {type: 'custom', field: 'useStatus', value: 'xxx'}, // 使用状态：待使用/已使用/已过期\n]\n```")
    public Page<CouponBo> couponPageList(@RequestBody PageRequest dto) {
        dto.addFilter(new PageFilter().setField("userId").setType("eq").setValue(SecurityHolder.session().getUserId()));
        return couponService.couponPageList(dto);
    }

    @PostMapping("/couponReceive")
    @Operation(summary = "用户优惠券领取")
    public CouponBo couponReceive(@RequestBody QrcodeDto qrcodeDto) {
        return couponService.couponReceive(qrcodeDto);
    }

    @PostMapping("/couponReceiveTest")
    @Operation(summary = "用户优惠券试算")
    public CouponBo couponReceiveTest(@RequestBody QrcodeDto qrcodeDto) {
        return couponService.couponReceiveTest(qrcodeDto);
    }

    @PostMapping("/couponTplPageList")
    @Operation(summary = "优惠券模板分页列表")
    public Page<CouponTplBo> couponTplPageList(@RequestBody PageRequest dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        return couponService.clientPublicCouponTplPage(dto, userId);
    }

    @PostMapping("/couponTplPublicReceive")
    @Operation(summary = "用户可见优惠券领取")
    public CouponBo couponTplPublicReceive(@RequestBody EditIdDto dto) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        return couponService.couponTplPublicReceive(dto.getId(), userId, UserTypeEnum.CLIENT);
    }

}
