package com.dounanflowers.client.service;

import cn.binarywang.wx.miniapp.api.WxMaUserService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import com.dounanflowers.common.bo.ClientUserBo;
import com.dounanflowers.common.entity.ClientUser;
import com.dounanflowers.common.enums.GenderEnum;
import com.dounanflowers.common.manager.ShoppingCartManager;
import com.dounanflowers.common.service.ClientUserService;
import com.dounanflowers.common.service.CouponService;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.IpUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.ThirdPartyHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class ClientWxService {

    private final ClientUserService clientUserService;

    private final CouponService couponService;

    private final ShoppingCartManager cartManager;

    public SessionInfo<Long, ClientUserBo> login(String code, String phoneNumberCode) {
        String sessionKey = null;
        String openId = null;
        try {
            WxMaJscode2SessionResult result = getWxService().getSessionInfo(code);
            sessionKey = result.getSessionKey();
            openId = result.getOpenid();
        } catch (Exception e) {
            log.error("获取sessionKey失败:{}", e.getMessage(), e);
        }

        if (sessionKey == null || openId == null) {
            throw new BaseException("获取sessionKey失败");
        }
        ClientUser user = clientUserService.fetchUserByOpenId(openId);

        if (user != null) {
            // 更新用户信息
            user.setLastLoginIp(IpUtils.getIpAddr());
            user.setLastLoginAt(LocalDateTime.now());
            if (StringUtils.isBlank(user.getMobile()) && StringUtils.isNotBlank(phoneNumberCode)) {
                try {
                    WxMaPhoneNumberInfo phoneNoInfo = getWxService().getPhoneNoInfo(phoneNumberCode);
                    String phoneNumber = phoneNoInfo.getPhoneNumber();
                    user.setMobile(phoneNumber);
                } catch (Exception e) {
                    log.error("获取手机号失败:{}", e.getMessage(), e);
                }
            }
            clientUserService.save(user);
        } else if (StringUtils.isNotBlank(phoneNumberCode)) {
            try {
                WxMaPhoneNumberInfo phoneNoInfo = getWxService().getPhoneNoInfo(phoneNumberCode);
                String phoneNumber = phoneNoInfo.getPhoneNumber();
                if (phoneNumber == null) {
                    throw new BaseException("获取手机号失败");
                }
                ClientUser clientUser = clientUserService.fetchByMobile(phoneNumber);
                if (clientUser != null) {
                    user = clientUser;
                    user.setOpenId(openId);
                    user.setLastLoginIp(IpUtils.getIpAddr());
                    user.setLastLoginAt(LocalDateTime.now());
                    clientUserService.save(user);
                } else {
                    // 创建用户
                    user = new ClientUser();
                    user.setOpenId(openId);
                    user.setUsername("wx_" + openId);
                    user.setLastLoginIp(IpUtils.getIpAddr());
                    user.setLastLoginAt(LocalDateTime.now());
                    user.setNickname("用户" + openId.substring(openId.length() - 3).toUpperCase());
                    user.setAvatar("");
                    user.setMobile(phoneNumber);
                    user.setGender(GenderEnum.NONE);
                    clientUserService.save(user);
                }
            } catch (BaseException e) {
                throw e;
            } catch (Exception e) {
                log.error("获取手机号失败:{}", e.getMessage(), e);
                throw new BaseException("获取手机号失败");
            }
        } else {
            // 创建用户
            user = new ClientUser();
            user.setOpenId(openId);
            user.setUsername("wx_" + openId);
            user.setLastLoginIp(IpUtils.getIpAddr());
            user.setLastLoginAt(LocalDateTime.now());
            user.setNickname("用户" + openId.substring(openId.length() - 3).toUpperCase());
            user.setAvatar("");
            user.setGender(GenderEnum.NONE);
            clientUserService.save(user);
        }
        ClientUserBo bo = BeanUtils.copy(user, ClientUserBo.class);
        bo.setCouponCount(couponService.countByUserId(user.getId()));
        bo.setCartCount(cartManager.countByUserId(user.getId()));
        SessionInfo<Long, ClientUserBo> session = SecurityHolder.login(bo);
        log.info("用户登录成功:{}", JsonUtils.toJson(session));
        return session;
    }

    public void updatePhone(String code) {
        ClientUserBo user = SecurityHolder.<Long, ClientUserBo>session().getUserInfo();
        if (user == null) {
            throw new BaseException("用户未登录");
        }
        ClientUser clientUser = clientUserService.fetchUserById(user.getId());
        String phoneNumber = null;
        try {
            WxMaPhoneNumberInfo phoneNoInfo = getWxService().getPhoneNoInfo(code);
            phoneNumber = phoneNoInfo.getPhoneNumber();
        } catch (Exception e) {
            log.error("获取手机号失败:{}", e.getMessage(), e);
        }
        if (phoneNumber == null) {
            throw new BaseException("获取手机号失败");
        }
        ClientUser u = clientUserService.fetchByMobile(phoneNumber);
        if (u != null) {
            throw new BaseException("手机号已被绑定");
        }
        clientUser.setMobile(phoneNumber);
        clientUserService.save(clientUser);
        SecurityHolder.<Long,ClientUserBo>session().getUserInfo().setMobile(phoneNumber);
        SecurityHolder.update(SecurityHolder.<Long,ClientUserBo>session());
    }

    private static WxMaUserService getWxService() {
        return ThirdPartyHolder.wxMaService("client").getUserService();
    }

    public void logout(Long userId) {
        ClientUser clientUser = clientUserService.fetchUserById(userId);
        if (clientUser != null && StringUtils.isNotBlank(clientUser.getOpenId())) {
            clientUser.setOpenId(clientUser.getOpenId() + "_logout");
            clientUserService.save(clientUser);
        }

    }

    public SessionInfo<Long, ClientUserBo> loginByMobile(String phoneNumberCode) {
        ClientUser user;
        try {
            WxMaPhoneNumberInfo phoneNoInfo = getWxService().getPhoneNoInfo(phoneNumberCode);
            String phoneNumber = phoneNoInfo.getPhoneNumber();
            if (phoneNumber == null) {
                throw new BaseException("获取手机号失败");
            }
            ClientUser clientUser = clientUserService.fetchByMobile(phoneNumber);
            if (clientUser != null) {
                user = clientUser;
                user.setLastLoginIp(IpUtils.getIpAddr());
                user.setLastLoginAt(LocalDateTime.now());
                clientUserService.save(user);
            } else {
                // 创建用户
                user = new ClientUser();
                user.setUsername("mobile_" + phoneNumber);
                user.setLastLoginIp(IpUtils.getIpAddr());
                user.setLastLoginAt(LocalDateTime.now());
                user.setNickname("用户" + phoneNumber.substring(phoneNumber.length() - 4).toUpperCase());
                user.setAvatar("");
                user.setMobile(phoneNumber);
                user.setGender(GenderEnum.NONE);
                clientUserService.save(user);
            }
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取手机号失败:{}", e.getMessage(), e);
            throw new BaseException("获取手机号失败");
        }
        ClientUserBo bo = BeanUtils.copy(user, ClientUserBo.class);
        bo.setCouponCount(couponService.countByUserId(user.getId()));
        bo.setCartCount(cartManager.countByUserId(user.getId()));
        SessionInfo<Long, ClientUserBo> session = SecurityHolder.login(bo);
        log.info("用户登录成功:{}", JsonUtils.toJson(session));
        return session;
    }

    public void bindWx(String code) {
        ClientUserBo user = SecurityHolder.<Long, ClientUserBo>session().getUserInfo();
        ClientUser clientUser = clientUserService.fetchUserById(user.getId());
        String openId = null;
        String sessionKey = null;
        try {
            WxMaJscode2SessionResult result = getWxService().getSessionInfo(code);
            sessionKey = result.getSessionKey();
            openId = result.getOpenid();
        } catch (Exception e) {
            log.error("获取sessionKey失败:{}", e.getMessage(), e);
        }
        if (sessionKey == null || openId == null) {
            throw new BaseException("获取sessionKey失败");
        }
        clientUser.setOpenId(openId);
        clientUserService.save(clientUser);
        SecurityHolder.<Long,ClientUserBo>session().getUserInfo().setOpenId(openId);
        SecurityHolder.update(SecurityHolder.<Long,ClientUserBo>session());
    }

    public SessionInfo<Long, ClientUserBo> loginByOpenid(String code) {
        String sessionKey = null;
        String openId = null;
        try {
            WxMaJscode2SessionResult result = getWxService().getSessionInfo(code);
            sessionKey = result.getSessionKey();
            openId = result.getOpenid();
        } catch (Exception e) {
            log.error("获取sessionKey失败:{}", e.getMessage(), e);
        }

        if (sessionKey == null || openId == null) {
            throw new BaseException("获取sessionKey失败");
        }
        ClientUser user = clientUserService.fetchUserByOpenId(openId);



        if (user != null) {
            // 更新用户信息
            user.setLastLoginIp(IpUtils.getIpAddr());
            user.setLastLoginAt(LocalDateTime.now());
            clientUserService.save(user);
        } else {
            // 创建用户
            user = new ClientUser();
            user.setOpenId(openId);
            user.setUsername("wx_" + openId);
            user.setLastLoginIp(IpUtils.getIpAddr());
            user.setLastLoginAt(LocalDateTime.now());
            user.setNickname("用户" + openId.substring(openId.length() - 3).toUpperCase());
            user.setAvatar("");
            user.setGender(GenderEnum.NONE);
            clientUserService.save(user);
        }
        ClientUserBo bo = BeanUtils.copy(user, ClientUserBo.class);
        bo.setCouponCount(couponService.countByUserId(user.getId()));
        bo.setCartCount(cartManager.countByUserId(user.getId()));
        SessionInfo<Long, ClientUserBo> session = SecurityHolder.login(bo);
        log.info("用户登录成功:{}", JsonUtils.toJson(session));
        return session;
    }

    public SessionInfo<Long, ClientUserBo> openId(String code) {
        String sessionKey = null;
        String openId = null;
        try {
            WxMaJscode2SessionResult result = getWxService().getSessionInfo(code);
            sessionKey = result.getSessionKey();
            openId = result.getOpenid();
        } catch (Exception e) {
            log.error("获取sessionKey失败:{}", e.getMessage(), e);
        }

        if (sessionKey == null || openId == null) {
            throw new BaseException("获取sessionKey失败");
        }
        SessionInfo<Long, ClientUserBo> session = SecurityHolder.session();
        session.getUserInfo().setOpenId(openId);
        SecurityHolder.update(session);
        return session;
    }

}
