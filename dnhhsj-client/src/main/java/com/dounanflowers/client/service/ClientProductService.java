package com.dounanflowers.client.service;

import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.CartSettlementDto;
import com.dounanflowers.common.dto.CartSettlementStoreDto;
import com.dounanflowers.common.dto.DirectPurchaseDto;
import com.dounanflowers.common.dto.ProductReviewPageDto;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.*;
import com.dounanflowers.common.manager.*;
import com.dounanflowers.common.service.CommonProductService;
import com.dounanflowers.common.service.FileService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.ThirdPartyHolder;
import com.dounanflowers.third.bo.WxPayParamsBo;
import com.dounanflowers.third.dto.PayParamsDto;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ClientProductService {

    private final CommonProductService commonProductService;
    private final ProductManager productManager;
    private final ClientUserManager clientUserManager;
    private final StoreManager storeManager;
    private final ShoppingCartManager cartManager;
    private final BillManager billManager;
    private final FileService fileService;

    public Page<ProductBo> productPageList(PageRequest dto) {
        Page<Product> list = productManager.productPageList(dto);
        List<Long> storeIds = list.getList().stream().map(Product::getStoreId).toList();
        List<Store> stores = storeManager.fetchByIds(storeIds);
        Map<Long, Store> storeMap = stores.stream().collect(Collectors.toMap(Store::getId, v -> v));
        return list.convert(v -> {
            ProductBo copy = BeanUtils.copy(v, ProductBo.class);
            copy.setImages(v.getImages());
            copy.setStoreObj(BeanUtils.copy(storeMap.get(v.getStoreId()), StoreBo.class));
            return copy;
        });
    }

    public ProductBo getById(Long id) {
        Product productProduct = productManager.fetchProductByIdWithRelation(id);
        if (productProduct == null) {
            throw new BaseException("商品不存在");
        }
        ProductBo copy = BeanUtils.copy(productProduct, ProductBo.class);
        copy.setImages(productProduct.getImages());
        Store store = storeManager.fetchByIdWithRelation(productProduct.getStoreId());
        if (store != null) {
            copy.setStoreObj(BeanUtils.copy(store, StoreBo.class));
        }

        return copy;
    }

    /**
     * 检查用户是否超过商品最大购买数量
     */
    private void checkMaxPurchaseLimit(Long userId, Long productId, Integer quantity) {
        // 1. 获取商品信息
        Product product = productManager.fetchProductById(productId);
        if (product == null) {
            throw new BaseException("商品不存在");
        }

        product.setStock(product.getStock() - quantity);
        product.setSoldCount(product.getSoldCount() + quantity);
        if (product.getStock() == 0) {
            product.setStatus(ProductStatusEnum.SOLD_OUT);
        }
        productManager.saveProduct(product);

        Store sdb = storeManager.fetchById(product.getStoreId());
        if (sdb == null) {
            throw new BaseException("店铺不存在");
        }
//        sdb.setSoldCount(sdb.getSoldCount() + quantity);
        storeManager.save(sdb);
        if (product.getMaxPurchasePerUser() <= 0) {
            return;
        }

        // 2. 获取用户已购买数量
        Integer purchasedQuantity = productManager.getUserPurchasedQuantity(userId, productId);

        // 3. 检查是否超过限购
        int totalQuantity = purchasedQuantity + quantity;
        if (totalQuantity > product.getMaxPurchasePerUser()) {
            throw new BaseException(String.format("“%s”商品已超过最大购买额(单用户限购%d件)，请认真核对后再下单",
                    product.getTitle(), product.getMaxPurchasePerUser()));
        }

        if (totalQuantity > product.getStock()) {
            throw new BaseException("商品库存不足");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public WxPayParamsBo cartSettlement(CartSettlementDto dto) {
        ClientUserBo userInfo = SecurityHolder.<Long, ClientUserBo>session().getUserInfo();
        Long userId = userInfo.getId();

        StringBuilder title = new StringBuilder();
        List<ProductOrder> orders = new ArrayList<>();
        int count = 0;
        for (CartSettlementStoreDto store : dto.getStores()) {
            // 1. 获取购物车商品
            List<ShoppingCart> carts = cartManager.fetchByIds(store.getCartIds());
            if (carts.isEmpty()) {
                throw new BaseException("购物车商品不存在");
            }


            // 2. 校验商品
            List<Long> productIds = carts.stream().map(ShoppingCart::getProductId).toList();
            List<Product> products = productManager.fetchProductByIdsWithRelation(productIds);
            if (products.size() != productIds.size()) {
                throw new BaseException("部分商品不存在");
            }
            // 3. 检查每个商品的购买限制
            for (ShoppingCart cart : carts) {
                checkMaxPurchaseLimit(userId, cart.getProductId(), cart.getQuantity());
            }

            // 4. 创建订单
            ProductOrder order = new ProductOrder()
                    .setUserId(userId)
                    .setStatus(OrderStatusEnum.PENDING_PAYMENT)
                    .setExpireTime(LocalDateTime.now().plusMinutes(30))
                    .setRemark(store.getRemark());

            // 5. 创建订单项
            List<ProductOrderItem> items = new ArrayList<>();
            int totalAmount = 0;
            int realAmount = 0;

            for (ShoppingCart cart : carts) {
                Product product = products.stream()
                        .filter(p -> p.getId().equals(cart.getProductId()))
                        .findFirst()
                        .orElseThrow(() -> new BaseException("商品不存在"));
                count++;
                if (title.isEmpty()) {
                    title.append(product.getTitle());
                }

                int itemTotal = product.getOriginalPrice() * cart.getQuantity();
                int itemReal = product.getPrice() * cart.getQuantity();
                totalAmount = totalAmount + itemTotal;
                realAmount = realAmount + itemReal;

                ProductOrderItem item = new ProductOrderItem()
                        .setProductId(product.getId())
                        .setQuantity(cart.getQuantity())
                        .setUnitPrice(product.getPrice())
                        .setTotalAmount(itemTotal)
                        .setSnapshot(JsonUtils.toJson(product))
                        .setRealAmount(itemReal);
                items.add(item);
            }

            order.setTotalAmount(totalAmount);
            order.setRealAmount(realAmount);
            order.setItems(items);
            order.setStoreId(store.getStoreId());
            order.setParentId(0L);

            // 6. 创建订单
            productManager.createOrder(order);

            // 7. 删除购物车商品
            cartManager.deleteByIds(store.getCartIds());
            orders.add(order);
            storeManager.calcSoldCount(order.getStoreId());
        }
        title.append("等").append(count).append("件商品");

        ProductOrder order;
        if (orders.size() > 1) {
            order = new ProductOrder();
            order.setUserId(userId);
            order.setStatus(OrderStatusEnum.PENDING_PAYMENT);
            order.setMerge(IsEnum.TRUE);
            order.setParentId(0L);
            order.setStoreId(0L);
            order.setExpireTime(LocalDateTime.now().plusMinutes(30));
            order.setRemark(JsonUtils.toJson(orders));
            order.setTotalAmount(orders.stream().map(ProductOrder::getTotalAmount).reduce(0, Integer::sum));
            order.setRealAmount(orders.stream().map(ProductOrder::getRealAmount).reduce(0, Integer::sum));
            productManager.createOrder(order);

            for (ProductOrder productOrder : orders) {
                productOrder.setParentId(order.getId());
                productManager.updateOrder(productOrder);
            }
        } else {
            order = orders.getFirst();
        }

        return pay(order, title.toString());
    }

    @Transactional(rollbackFor = Exception.class)
    public WxPayParamsBo directPurchase(DirectPurchaseDto dto) {
        ClientUserBo userInfo = SecurityHolder.<Long, ClientUserBo>session().getUserInfo();
        Long userId = userInfo.getId();

        // 1. 校验商品
        Product product = productManager.fetchProductByIdWithRelation(dto.getProductId());
        if (product == null) {
            throw new BaseException("商品不存在");
        }

        // 2. 检查购买限制
        checkMaxPurchaseLimit(userId, dto.getProductId(), dto.getQuantity());

        if (product.getStock() < dto.getQuantity()) {
            throw new BaseException("商品库存不足");
        }

        // 3. 创建订单
        int totalAmount = product.getOriginalPrice() * dto.getQuantity();
        int realAmount = product.getPrice() * dto.getQuantity();
        ProductOrder order = new ProductOrder()
                .setUserId(userId)
                .setStatus(OrderStatusEnum.PENDING_PAYMENT)
                .setExpireTime(LocalDateTime.now().plusMinutes(30))
                .setRemark(dto.getRemark())
                .setTotalAmount(totalAmount)
                .setParentId(0L)
                .setStoreId(product.getStoreId())
                .setRealAmount(realAmount);

        // 4. 创建订单项
        ProductOrderItem item = new ProductOrderItem()
                .setProductId(dto.getProductId())
                .setQuantity(dto.getQuantity())
                .setUnitPrice(product.getPrice())
                .setTotalAmount(totalAmount)
                .setSnapshot(JsonUtils.toJson(product))
                .setRealAmount(realAmount);

        order.setItems(Lists.newArrayList(item));

        productManager.createOrder(order);

        if(dto.getFake() == null || !dto.getFake()) {
            return pay(order, product.getTitle());
        } else {
            return fakePay(order, product.getTitle());
        }
    }

    private WxPayParamsBo fakePay(ProductOrder order, String title) {
        ClientUserBo userInfo = SecurityHolder.<Long, ClientUserBo>session().getUserInfo();
        Long userId = userInfo.getId();
        Bill bill = new Bill();
        bill.setType(BillTypeEnum.PRODUCT_ORDER);
        bill.setEntityId(order.getId());
        bill.setUserId(userId);
        bill.setUserType(UserTypeEnum.CLIENT);
        bill.setUserModel("client_user");
        bill.setOriginalMoneyCent(order.getTotalAmount());
        bill.setCouponDiscountCent(0);
        bill.setOrderMoneyCent(order.getRealAmount());
        bill.setOtherData(JsonUtils.toJson(order));
        bill.setPayStatus(BillPayStatusEnum.PAID);
        bill.setPaidAt(LocalDateTime.now());
        billManager.saveBill(bill);

        commonProductService.handleProductBill(bill);
        WxPayParamsBo wxPayParamsBo = new WxPayParamsBo();
        wxPayParamsBo.setPaid(true);
        return wxPayParamsBo;
    }

    private WxPayParamsBo pay(ProductOrder order, String title) {
        ClientUserBo userInfo = SecurityHolder.<Long, ClientUserBo>session().getUserInfo();
        Long userId = userInfo.getId();
        Bill bill = new Bill();
        bill.setType(BillTypeEnum.PRODUCT_ORDER);
        bill.setEntityId(order.getId());
        bill.setUserId(userId);
        bill.setUserType(UserTypeEnum.CLIENT);
        bill.setUserModel("client_user");
        bill.setOriginalMoneyCent(order.getTotalAmount());
        bill.setCouponDiscountCent(0);
        bill.setOrderMoneyCent(order.getRealAmount());
        bill.setPayStatus(BillPayStatusEnum.UNPAID);
        bill.setOtherData(JsonUtils.toJson(order));
        billManager.saveBill(bill);
        PayParamsDto params = new PayParamsDto();
        params.setTrxamt(bill.getOrderMoneyCent());
        params.setUnireqsn(String.valueOf(bill.getId()));
        params.setAcct(userInfo.getOpenId());
        params.setBody(title);
        params.setRemark(userInfo.getMobile());
        return ThirdPartyHolder.allinpayService("client").pay(params);
    }

    public ProductOrderBo getOrderDetail(Long id) {
        return commonProductService.getOrderDetail(id);
    }

    public Page<ProductOrderBo> orderPageList(PageRequest dto) {
        Page<ProductOrder> orderPage = productManager.getOrderPage(dto);
        List<Long> storeIds = orderPage.getList().stream().map(ProductOrder::getStoreId).distinct().toList();
        List<Store> stores = storeManager.fetchByIds(storeIds);
        Map<Long, Store> storeMap = stores.stream().collect(Collectors.toMap(Store::getId, v -> v));
        List<Long> orderIds = orderPage.getList().stream().map(ProductOrder::getId).toList();
        List<ProductRefundApply> refundApplyList = productManager.fetchRefundApplyByOrderIds(orderIds);
        Map<Long, ProductRefundApply> refundApplyMap = refundApplyList.stream().collect(Collectors.toMap(ProductRefundApply::getOrderId, v -> v));
        return orderPage.convert(order -> {
            ProductOrderBo one = BeanUtils.copy(order, ProductOrderBo.class);
            Store store = storeMap.get(order.getStoreId());
            if (store != null) {
                one.setStoreObj(BeanUtils.copy(store, StoreBo.class));
                one.getStoreObj().setImages(store.getImages());
            }
            ProductRefundApply refundApply = refundApplyMap.get(order.getId());
            if (refundApply != null) {
                one.setRefundId(refundApply.getId());
                one.setRefundObj(BeanUtils.copy(refundApply, ProductRefundApplyBo.class));
            }
            commonProductService.setOrderItems(one, order.getItems());
            return one;
        });
    }

    public Page<ProductReviewBo> productReviewList(ProductReviewPageDto dto) {
        Page<ProductReview> list = productManager.productReviewPageList(dto);
        List<Long> userIds = list.getList().stream().map(ProductReview::getUserId).toList();
        List<ClientUser> clientUsers = clientUserManager.fetchByIds(userIds);
        Map<Long, ClientUser> clientUserMap = clientUsers.stream().collect(Collectors.toMap(ClientUser::getId, v -> v));
        return list.convert(v -> {
            ProductReviewBo one = BeanUtils.copy(v, ProductReviewBo.class);
            one.setImages(v.getImages());
            if (one.getIsAnonymous()!=null && !one.getIsAnonymous()) {
                one.setUserObj(BeanUtils.copy(clientUserMap.get(v.getUserId()), ClientUserBo.class));
            }
            return one;
        });
    }

    public Page<ProductReviewBo> myReviewList(PageRequest dto) {
        Page<ProductReview> list = productManager.productReviewPageList(dto);
        List<Long> productIds = list.getList().stream().map(ProductReview::getProductId).toList();
        List<Product> productProducts = productManager.fetchProductByIdsWithRelation(productIds);
        Map<Long, Product> productProductMap = productProducts.stream()
                .collect(Collectors.toMap(Product::getId, v -> v));
        return list.convert(v -> {
            ProductReviewBo one = BeanUtils.copy(v, ProductReviewBo.class);
            one.setImages(v.getImages());
            one.setProductObj(BeanUtils.copy(productProductMap.get(v.getProductId()), ProductBo.class));
            return one;
        });
    }

    public void cancelOrder(Long id) {
        commonProductService.cancelOrder(id);
    }

    public ProductOrderStatsBo orderStats(Long userId) {
        List<ProductOrder> productOrders = productManager.orderStatsByUserId(userId);
        ProductOrderStatsBo bo = new ProductOrderStatsBo();
        bo.setCount(productOrders.size());
        for (ProductOrder order : productOrders) {
            switch (order.getStatus()) {
                case PENDING_PAYMENT:
                    bo.setPendingPaymentCount(bo.getPendingPaymentCount() + 1);
                    break;
                case PAID:
                    bo.setPaidCount(bo.getPaidCount() + 1);
                    break;
                case COMPLETED:
                    bo.setCompletedCount(bo.getCompletedCount() + 1);
                    break;
                case CANCELLED:
                    bo.setCancelledCount(bo.getCancelledCount() + 1);
                    break;
                case REFUNDING:
                    bo.setRefundingCount(bo.getRefundingCount() + 1);
                    break;
                case REFUNDED:
                    bo.setRefundedCount(bo.getRefundedCount() + 1);
                    break;
                case PENDING_REVIEW:
                    bo.setPendingReviewCount(bo.getPendingReviewCount() + 1);
                    break;
            }
        }
        return bo;
    }

    public WxPayParamsBo payOrder(Long id) {
        ProductOrder order = productManager.getOrderWithItems(id);
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        if (order.getStatus() != OrderStatusEnum.PENDING_PAYMENT) {
            throw new BaseException("订单状态不正确");
        }
        if (order.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new BaseException("订单已过期");
        }
        Long productId = order.getItems().getFirst().getProductId();
        Product product = productManager.fetchProductByIdWithRelation(productId);
        if (product == null) {
            throw new BaseException("商品不存在");
        }
        String title = product.getTitle();
        if (order.getItems().size() > 1) {
            title = "等" + order.getItems().size() + "件商品";
        }
        order.setParentId(0L);
        productManager.updateOrder(order);
        return pay(order, title);
    }

    @Transactional(rollbackFor = Exception.class)
    public void review(ProductReviewBo dto) {
        ProductOrder order = productManager.getOrderWithItems(dto.getOrderId());
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        if (order.getStatus() != OrderStatusEnum.PENDING_REVIEW) {
            throw new BaseException("订单状态不正确");
        }
        Product product = productManager.fetchProductByIdWithRelation(dto.getProductId());
        if (product == null) {
            throw new BaseException("商品不存在");
        }
        Store store = storeManager.fetchById(product.getStoreId());
        if (store == null) {
            throw new BaseException("商铺不存在");
        }
        ProductReview review = BeanUtils.copy(dto, ProductReview.class);
        review.setImages(dto.getImages());
        productManager.saveReview(review);
        fileService.updateFileLink(review.getId(), dto.getImages());
        Optional<ProductOrderItem> first = order.getItems().stream().filter(v -> v.getProductId().equals(dto.getProductId())).findFirst();
        if (first.isEmpty()) {
            throw new BaseException("订单项不存在");
        }
        ProductOrderItem item = first.get();
        item.setReviewTime(LocalDateTime.now());
        productManager.saveProductOrderItem(item);
        if (order.getItems().stream().allMatch(v -> v.getReviewTime() != null)) {
            order.setStatus(OrderStatusEnum.COMPLETED);
            productManager.updateOrder(order);
        }
        product.setTotalReviews(product.getTotalReviews() + 1);
        productManager.saveProduct(product);
        store.setReviewCount(store.getReviewCount() + 1);
        storeManager.save(store);
    }
}
