package com.dounanflowers.client.service;

import com.dounanflowers.common.bo.StoreBo;
import com.dounanflowers.common.entity.Store;
import com.dounanflowers.common.manager.StoreManager;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ClientStoreService {

    private final StoreManager storeManager;

    public StoreBo getById(Long id) {
        Store store = storeManager.fetchByIdWithRelation(id);
        if (store == null) {
            throw new BaseException("商铺不存在");
        }
        StoreBo copy = BeanUtils.copy(store, StoreBo.class);
        copy.setImages(store.getImages());
        copy.setFeeRate(null);
        return copy;
    }

    public Page<StoreBo> page(PageRequest pageRequest) {
        pageRequest.addFilter(new PageFilter().setField("status").setType("eq").setValue(1));
        Page<Store> list = storeManager.pageList(pageRequest);
        return list.convert(v -> {
            StoreBo copy = BeanUtils.copy(v, StoreBo.class);
            copy.setFeeRate(null);
            copy.setImages(v.getImages());
            copy.setTags(v.getTags());
            return copy;
        });
    }

}
