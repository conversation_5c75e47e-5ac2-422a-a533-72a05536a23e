package com.dounanflowers;

import com.google.common.base.CaseFormat;
import org.apache.commons.lang3.StringUtils;
import org.yaml.snakeyaml.LoaderOptions;
import org.yaml.snakeyaml.Yaml;

import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

public class ClientApiGenerator {

    private static final Map<String, String> generatedClasses = new HashMap<>();

    private static final Map<String, String> propertiesToBean = new HashMap<>();

    private static final Map<String, String> beanToProps = new HashMap<>();

    private static String ymlFilePath = "./dnhhsj-client/src/main/resources/openapi.yaml";

    private static String path = "./dnhhsj-client/src/main/java/com/dounanflowers/client/vo/";

    private static String toPath = "./dnhhsj-client/src/main/java/com/dounanflowers/client/api/";

    private static String basePackage = "com.dounanflowers.client";

    private static String basePath = "/client";

    private static String[] replacePath = new String[]{"/mini/"};

    private static Integer beanIndex = 2000;

    private static Integer apiIndex = 200;

    public static void main(String[] args) {
        try (InputStream inputStream = new FileInputStream(ymlFilePath)) {
            LoaderOptions loaderOptions = new LoaderOptions();
            loaderOptions.setMaxAliasesForCollections(1000);
            Yaml yaml = new Yaml(loaderOptions);
            Map<String, Object> obj = yaml.load(inputStream);
            generateBean(obj);
            generateSpringController(obj);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void generateBean(Map<String, Object> obj) {
        // Extract paths
        Map<String, Object> components = (Map<String, Object>) obj.get("components");
        if (components == null) {
            System.out.println("No paths found in the YML file.");
            return;
        }

        // Extract schemas
        Map<String, Object> schemas = (Map<String, Object>) components.get("schemas");
        if (schemas == null) {
            System.out.println("No schemas found in the components.");
            return;
        }

        for (Map.Entry<String, Object> schemaEntry : schemas.entrySet()) {
            String className = schemaEntry.getKey();
            Map<String, Object> value = (Map<String, Object>) schemaEntry.getValue();
            Map<String, Object> properties = (Map<String, Object>) value.get("properties");

            List<String> strings = (List<String>) value.get("required");
            if (strings == null) {
                strings = new ArrayList<>();
            }
            Set<String> required = new HashSet<>(strings);
            StringBuilder beanCode = new StringBuilder();
            beanCode.append("package ").append(basePackage).append(".vo;\n\n");

            beanCode.append("import com.fasterxml.jackson.annotation.JsonProperty;\n");
            beanCode.append("import lombok.Data;\n");
            beanCode.append("import java.util.List;\n");
            beanCode.append("import java.util.Map;\n");
            beanCode.append("import io.swagger.v3.oas.annotations.media.Schema;\n\n"); // 引入 @Schema 注解

            beanCode.append("@Data\n");
            String title = (String) value.get("title"); // 获取 title 属性
            String description = (String) value.get("description"); // 获取 description 属性
            if (description == null) {
                description = "";
            }
            description = description.replace("\n", "\\n").replace("\r", "");
            description = description.replace("\"", "'");
            if (StringUtils.isNotBlank(title)) {
                beanCode.append("@Schema(title = \"").append(title)
                        .append("\", description = \"").append(description).append("\")\n"); // 添加 @Schema 注解
            }
            className = className.replace(" ", "");
            beanCode.append("public class ").append(className).append(" {\n\n");

            String key = properties(properties, required, beanCode);
            if (key.isEmpty()) {
                continue;
            }

            beanCode.append("}\n\n");

            propertiesToBean.put(key, className);

            generatedClasses.put(className, beanCode.toString());
            // Write to file
            String fileName = className + ".java";
            try (FileWriter fileWriter = new FileWriter(path + fileName)) {
                fileWriter.write(beanCode.toString());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private static String mapToJavaType(String openApiType, Map<String, Object> details) {
        if (openApiType == null) {
            String ref = (String) details.get("$ref");
            if (ref != null) {
                return ref.substring(ref.lastIndexOf("/") + 1);
            } else {
                return "Map<String, Object>";
            }
        }
        switch (openApiType) {
            case "string":
                return "String";
            case "integer":
                return "Integer";
            case "number":
                return "Double";
            case "boolean":
                return "Boolean";
            case "object":
                String ref = (String) details.get("$ref");
                Map<String, Object> properties = (Map<String, Object>) details.get("properties");
                if (ref != null) {
                    return ref.substring(ref.lastIndexOf("/") + 1);
                } else if (properties != null && !properties.isEmpty()) {
                    return generateAnonymousClass(details);
                } else {
                    return "Map<String, Object>";
                }
            default:
                return "Object";
        }
    }

    private static String generateAnonymousClass(Map<String, Object> details) {
        String className = "AnonymousClass" + beanIndex++;

        StringBuilder beanCode = new StringBuilder();
        beanCode.append("package ").append(basePackage).append(".vo;\n\n");
        beanCode.append("import com.fasterxml.jackson.annotation.JsonProperty;\n");
        beanCode.append("import lombok.Data;\n");
        beanCode.append("import java.util.List;\n");
        beanCode.append("import java.util.Map;\n");
        beanCode.append("import io.swagger.v3.oas.annotations.media.Schema;\n\n"); // 引入 @Schema 注解

        beanCode.append("@Data\n");

        String title = (String) details.get("title"); // 获取 title 属性
        String description = (String) details.get("description"); // 获取 description 属性
        if (description == null) {
            description = "";
        }
        description = description.replace("\n", "\\n").replace("\r", "");
        description = description.replace("\"", "'");
        if (StringUtils.isNotBlank(title)) {
            beanCode.append("@Schema(title = \"").append(title)
                    .append("\", description = \"").append(description).append("\")\n"); // 添加 @Schema 注解
        }
        beanCode.append("public class ").append(className).append(" {\n\n");

        List<String> strings = (List<String>) details.get("required");
        if (strings == null) {
            strings = new ArrayList<>();
        }
        Set<String> required = new HashSet<>(strings);
        Map<String, Object> properties = (Map<String, Object>) details.get("properties");
        String key = properties(properties, required, beanCode);

        if (key.isEmpty()) {
            return "";
        }

        beanCode.append("}\n\n");

        if (propertiesToBean.containsKey(key)) {
            return propertiesToBean.get(key);
        }

        propertiesToBean.put(key, className);
        beanToProps.put(className, key);

        generatedClasses.put(className, className);
        // Write to file
        String fileName = className + ".java";
        try (FileWriter fileWriter = new FileWriter(path + fileName)) {
            fileWriter.write(beanCode.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }

        return className;
    }

    private static String properties(Map<String, Object> properties, Set<String> required, StringBuilder beanCode) {
        if (properties == null || properties.isEmpty()) {
            return "";
        }
        StringBuilder propertiesKey = new StringBuilder();
        for (Map.Entry<String, Object> propertyEntry : properties.entrySet()) {
            String propertyName = propertyEntry.getKey();
            boolean needProperty = false;
            String camelCasePropertyName = propertyName;

            if (propertyName.equals("package")) {
                camelCasePropertyName = "pack";
                needProperty = true;
            }

            if (propertyName.contains("_")) {
                if (propertyName.startsWith("_")) {
                    camelCasePropertyName = propertyName.substring(1);
                }
                camelCasePropertyName = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, camelCasePropertyName);
                needProperty = true;
            }
            Map<String, Object> propertyDetails = (Map<String, Object>) propertyEntry.getValue();
            String propertyType = (String) propertyDetails.get("type");
            String title = (String) propertyDetails.get("title"); // 获取 title 属性
            String description = (String) propertyDetails.get("description"); // 获取 description 属性
            if (description == null) {
                description = "";
            }
            description = description.replace("\n", "\\n").replace("\r", "");
            description = description.replace("\"", "'");
            String javaType;
            if ("array".equals(propertyType)) {
                Map<String, Object> items = (Map<String, Object>) propertyDetails.get("items");
                String itemsType = (String) items.get("type");
                javaType = mapToJavaType(itemsType, items);
                javaType = "List<" + javaType + ">";

            } else {
                javaType = mapToJavaType(propertyType, propertyDetails);
            }
            if (needProperty) {
                beanCode.append("    @JsonProperty(\"").append(propertyName).append("\")\n");
            }
            if (StringUtils.isNotBlank(title) || required.contains(propertyName)) {
                beanCode.append("    @Schema(title = \"").append(title)
                        .append("\", description = \"").append(description);
                if (required.contains(propertyName)) {
                    beanCode.append("\", requiredMode = Schema.RequiredMode.REQUIRED)\n"); // 添加 @Schema 注解
                } else {
                    beanCode.append("\")\n"); // 添加 @Schema 注解
                }
            }
            beanCode.append("    private ").append(javaType).append(" ").append(camelCasePropertyName).append(";\n\n");
            if (javaType.startsWith("AnonymousClass")) {
                propertiesKey.append(propertyName).append(beanToProps.get(javaType));
            } else {
                propertiesKey.append(propertyName).append(javaType);
            }
        }
        return propertiesKey.toString();
    }

    public static void generateSpringController(Map<String, Object> obj) {
        // Extract paths
        Map<String, Object> paths = (Map<String, Object>) obj.get("paths");
        if (paths == null) {
            System.out.println("No paths found in the YML file.");
            return;
        }

        Map<String, StringBuilder> controllersByTag = new HashMap<>();

        Map<String, String> tagMap = new HashMap<>();

        Map<String, String> tagRelMap = new HashMap<>();

        for (Map.Entry<String, Object> entry : paths.entrySet()) {
            String path = entry.getKey();
            Map<String, Object> methods = (Map<String, Object>) entry.getValue();

            for (Map.Entry<String, Object> methodEntry : methods.entrySet()) {
                String httpMethod = methodEntry.getKey();
                Map<String, Object> methodDetails = (Map<String, Object>) methodEntry.getValue();

                String operationId = path.substring(path.lastIndexOf("/") + 1).replaceAll("[^a-zA-Z0-9]", "");
                if (operationId.isEmpty()) {
                    operationId = "operation" + (int) (Math.random() * 1000);
                }
                List<String> tags = (List<String>) methodDetails.get("tags");
                Object parameters = methodDetails.get("parameters");
                Map<String, Object> requestBody = (Map<String, Object>) methodDetails.get("requestBody");
                Map<String, Object> responses = (Map<String, Object>) methodDetails.get("responses");

                // Determine the controller class based on tags
                String controllerClassName;
                if (tags != null && !tags.isEmpty()) {
                    String firstTag = tags.getFirst();
                    String tagClassName = tagMap.computeIfAbsent(firstTag, k -> "Api" + apiIndex++);
                    controllerClassName = tagClassName + "Api";
                    tagRelMap.put(controllerClassName, firstTag);
                } else {
                    controllerClassName = "ApiController";
                }

                StringBuilder controllerCode = controllersByTag.computeIfAbsent(controllerClassName, k -> {
                    String tag = tagRelMap.get(controllerClassName);
                    StringBuilder code = new StringBuilder();
                    code.append("package ").append(basePackage).append(".api;\n\n");
                    code.append("import org.springframework.web.bind.annotation.*;\n");
                    code.append("import ").append(basePackage).append(".vo.*;\n\n");
                    code.append("import io.swagger.v3.oas.annotations.Operation;\n");
                    code.append("import io.swagger.v3.oas.annotations.tags.Tag;\n\n");
                    code.append("@RestController\n");
                    code.append("@RequestMapping(\"" + basePath + "\")\n");
                    code.append("@Tag(name = \"").append(tag).append("\")\n");
                    code.append("public class ").append(controllerClassName).append(" {\n\n");
                    return code;
                });

                // Generate method signature

                String hm = httpMethod.substring(0, 1).toUpperCase() + httpMethod.substring(1);
                for (String s : replacePath) {
                    path = path.replace(s, "/");
                }
                controllerCode.append("    @").append(hm).append("Mapping(\"").append(path).append("\")\n");

                String description = (String) methodDetails.get("description");
                if (description == null) {
                    description = "";
                }
                description = description.replace("\n", "\\n").replace("\r", "");
                description = description.replace("\"", "'");
                description = description.replace("\n", "\\n").replace("\r", "");
                String summary = (String) methodDetails.get("summary");
                if (summary == null) {
                    summary = "";
                }
                controllerCode.append("    @Operation(summary = \"").append(summary).append("\", description = \"").append(description).append("\")\n");
                controllerCode.append("    public ");

                // Determine the return type
                if (responses != null && responses.containsKey("200")) {
                    Map<String, Object> responseDetails = (Map<String, Object>) responses.get("200");
                    Map<String, Object> content = (Map<String, Object>) responseDetails.get("content");
                    if (content != null && content.containsKey("application/json")) {
                        Map<String, Object> schema = (Map<String, Object>) ((Map<String, Object>) content.get("application/json")).get("schema");
                        String ref = (String) schema.get("$ref");
                        if (ref != null) {
                            String returnType = ref.substring(ref.lastIndexOf("/") + 1);
                            controllerCode.append(returnType).append(" ");
                        } else {
                            String returnType = generateAnonymousClass(schema);
                            controllerCode.append(returnType).append(" ");
                        }
                    } else {
                        controllerCode.append("String ");
                    }
                } else {
                    controllerCode.append("String ");
                }

                controllerCode.append(operationId).append("(");

                // Handle parameters
                if (parameters != null) {
                    for (Map<String, Object> param : (List<Map<String, Object>>) parameters) {
                        String paramName = (String) param.get("name");
                        Map<String, Object> o = (Map<String, Object>) param.get("schema");
                        String paramType = mapToJavaType((String) o.get("type"), param);
                        String paramIn = (String) param.get("in");

                        if ("path".equals(paramIn)) {
                            controllerCode.append("@PathVariable ").append(paramType).append(" ").append(paramName).append(", ");
                        } else if ("query".equals(paramIn)) {
                            controllerCode.append("@RequestParam ").append(paramType).append(" ").append(paramName).append(", ");
                        }
                    }
                }

                // Handle request body
                if (requestBody != null) {
                    Map<String, Object> content = (Map<String, Object>) ((Map<String, Object>) requestBody.get("content")).get("application/json");
                    if (content != null) {
                        Map<String, Object> schema = (Map<String, Object>) content.get("schema");
                        String ref = (String) schema.get("$ref");
                        if (ref != null) {
                            String requestType = ref.substring(ref.lastIndexOf("/") + 1);
                            String typeName = requestType.substring(0, 1).toLowerCase() + requestType.substring(1);
                            controllerCode.append("@RequestBody ").append(requestType).append(" ").append(typeName).append(", ");
                        } else {
                            String requestType = generateAnonymousClass(schema);
                            if (!requestType.isEmpty()) {
                                String typeName = requestType.substring(0, 1).toLowerCase() + requestType.substring(1);
                                controllerCode.append("@RequestBody ").append(requestType).append(" ").append(typeName).append(", ");
                            }
                        }
                    }
                }

                // Remove trailing comma and space
                if (controllerCode.charAt(controllerCode.length() - 2) == ',') {
                    controllerCode.delete(controllerCode.length() - 2, controllerCode.length());
                }

                controllerCode.append(") {\n");
                controllerCode.append("        return null;\n");
                controllerCode.append("    }\n\n");
            }
        }

        // Close all controller classes and write to files
        for (Map.Entry<String, StringBuilder> entry : controllersByTag.entrySet()) {
            String controllerClassName = entry.getKey();
            StringBuilder controllerCode = entry.getValue();
            controllerCode.append("}\n");

            // Write to file
            String fileName = controllerClassName + ".java";
            try (FileWriter fileWriter = new FileWriter(toPath + fileName)) {
                fileWriter.write(controllerCode.toString());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

}

