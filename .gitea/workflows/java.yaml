name: Gitea Actions Demo
run-name: Maven Build
on: [ push ]

jobs:
  build:
    runs-on: DEV
    container:
      image: cicd/java:latest
      volumes:
        - /data/cache/maven:/m2/repository
        - /data/compose:/data/compose
    steps:
      - name: Check out repository code
        uses: https://gitea.com/actions/checkout@v4
      - name: Build with Maven
        run: mvn clean package --settings=settings.xml -DskipTests -Dmaven.repo.local=/m2/repository
      - name: Login to Docker Registry
        run: docker login -u=${{vars.IMAGE_USERNAME}} -p=${{vars.IMAGE_PASSWORD}} ${{vars.IMAGE_URL}}
      - name: Build Docker Image
        run: docker build -t ${{vars.IMAGE_URL}}/dnhhsj/java-app:${{ gitea.ref_name }} .
      - name: Push Docker Image
        run: docker push ${{vars.IMAGE_URL}}/dnhhsj/java-app:${{ gitea.ref_name }}
      - name: Notify
        run: curl -X GET https://api.day.app/GA5J7BZLFpnjz4WsWHSNaF/server%20${{ gitea.ref_name }}%20build%20success