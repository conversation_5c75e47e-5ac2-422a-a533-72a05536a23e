openapi: 3.0.1
info:
  title: 花花世界
  description: ''
  version: 1.0.0
tags:
  - name: 商家小程序
  - name: 商家小程序/登录
  - name: 商家小程序/用户
  - name: 商家小程序/优惠券
  - name: 商家小程序/其他
  - name: 商家小程序/三轮车
  - name: 商家小程序/三轮车/违规处罚
  - name: 商家小程序/工作流
  - name: 商家小程序/消息
  - name: 商家小程序/订单
  - name: 商家小程序/文章
  - name: 商家小程序/轮播
  - name: 后台管理
  - name: 后台管理/登录
  - name: 后台管理/文章管理
  - name: 后台管理/商铺管理
  - name: 后台管理/字典管理
  - name: 后台管理/用户管理
  - name: 后台管理/媒体管理
  - name: 后台管理/横幅管理
  - name: 后台管理/投诉建议
  - name: 后台管理/其他
  - name: 后台管理/停车
  - name: 后台管理/工作流
  - name: 后台管理/三轮车
  - name: 后台管理/消息
  - name: 后台管理/经纪人
  - name: 后台管理/设置
  - name: 后台管理/拍卖
  - name: 后台管理/订单
  - name: 后台管理/优惠券
  - name: 后台管理/艺术品
  - name: 后台管理/商家用户
  - name: 停车场
paths:
  /merchant/loginByWechat:
    post:
      summary: 微信自动登录
      deprecated: false
      description: |-
        用户打开小程序执行一次
        如果没找到openId绑定的用户，返回{token:null,userInfo:null}
      tags:
        - 商家小程序/登录
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                  description: wx.login获取到的code
                  title: 微信code
              required:
                - code
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                        title: token
                      expiredAt:
                        type: integer
                        title: 过期时间
                        description: token过期时间
                      userInfo: &ref_0
                        $ref: '#/components/schemas/TUser'
                    title: 返回数据
                    required:
                      - token
                      - userInfo
                      - expiredAt
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/loginByMobileCode:
    post:
      summary: 手机验证码登录(开发用)
      deprecated: false
      description: ''
      tags:
        - 商家小程序/登录
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                mobile:
                  type: string
                  title: 手机号
                smsCode:
                  type: string
                  title: 验证码
              required:
                - mobile
                - smsCode
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                      expiredAt:
                        type: string
                        title: 过期时间
                      userInfo: *ref_0
                    title: 返回数据
                    required:
                      - token
                      - expiredAt
                      - userInfo
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/loginByWechatBoundMobile:
    post:
      summary: 微信绑定手机号登陆
      deprecated: false
      description: 如果没找到手机号绑定的用户，返回{token:null,userInfo:null}
      tags:
        - 商家小程序/登录
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                  title: 微信登录的code
                phoneNumberCode:
                  type: string
                  title: 绑定手机号的code
              required:
                - code
                - phoneNumberCode
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                        title: 凭证
                      expiredAt:
                        type: integer
                        title: 过期时间
                      userInfo: &ref_1
                        $ref: '#/components/schemas/TMerchantUser'
                    title: 返回数据
                    required:
                      - token
                      - userInfo
                      - expiredAt
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/myMobileBindByWechat:
    post:
      summary: 微信绑定手机号(废弃)
      deprecated: false
      description: ''
      tags:
        - 商家小程序/用户
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                phoneNumberCode:
                  type: string
                  title: 微信code
                  description: 使用微信getphonenumber获取到的code
                code:
                  type: string
                  title: 微信登录code
                  description: 使用wx.login获取到的code
              required:
                - phoneNumberCode
                - code
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                      expiredAt:
                        type: integer
                      userInfo: *ref_0
                    title: 返回数据
                    required:
                      - token
                      - expiredAt
                      - userInfo
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/myUserInfoGet:
    post:
      summary: 获取自己用户信息
      deprecated: false
      description: ''
      tags:
        - 商家小程序/用户
      parameters: [ ]
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_0
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/myUserInfoUpdate:
    post:
      summary: 修改自己用户信息
      deprecated: false
      description: ''
      tags:
        - 商家小程序/用户
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                nickname:
                  type: string
                  title: 昵称
                realname:
                  type: string
                  title: 真名
                avatar:
                  type: string
                  title: 头像
                gender:
                  type: string
                  title: 性别
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_0
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/userRealnameVerify:
    post:
      summary: 身份证实名认证
      deprecated: false
      description: |-
        身份证需要调取拍照，可以上传拍照的缩略图  
        返回的userInfo中realname字段为最新实名认证的姓名，可用于判断是否实名认证
      tags:
        - 商家小程序/用户
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                realname:
                  type: string
                  title: 真实姓名
                idCardNum:
                  type: string
                  title: 身份证号
                idCardUrl1:
                  type: string
                  title: 身份证人像面
                idCardUrl2:
                  type: string
                  title: 身份证国徽面
              required:
                - realname
                - idCardNum
                - idCardUrl1
                - idCardUrl2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      userInfo: *ref_1
                    title: 返回数据
                    required:
                      - userInfo
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/dictGet:
    post:
      summary: 获取单个字典
      deprecated: false
      description: ''
      tags:
        - 商家小程序/其他
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  title: 名称
                key:
                  type: string
                  title: 类型
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        label:
                          type: string
                        value:
                          type: string
                        status:
                          type: boolean
                      required:
                        - label
                        - value
                        - status
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/dictGetMap:
    post:
      summary: 获取字典Map
      deprecated: false
      description: 可以传入tags获取相关字典Map, Map的key是字典的key, Map的value是字典的options
      tags:
        - 商家小程序/其他
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tags:
                  type: array
                  items:
                    type: string
                key:
                  type: array
                  items:
                    type: string
                isOr:
                  type: boolean
                  description: 条件之间是否是或
                  title: 条件连接
              required:
                - key
                - isOr
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties: { }
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/upload:
    post:
      summary: 上传文件
      deprecated: false
      description: ''
      tags:
        - 商家小程序/其他
      parameters: [ ]
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  format: binary
                  type: string
                  example: ''
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/systemVar:
    post:
      summary: 获取系统变量
      deprecated: false
      description: ''
      tags:
        - 商家小程序/其他
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties: { }
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    title: 返回数据
                    $ref: '#/components/schemas/TSystemVar'
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/testMq:
    post:
      summary: 测试队列(开发用)
      deprecated: false
      description: ''
      tags:
        - 商家小程序/其他
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                data:
                  type: object
                  properties: { }
              required:
                - name
                - data
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/couponTplPageList:
    post:
      summary: 商家优惠券模板分页列表
      deprecated: false
      description: |-
        ```javascript
        //filter
        [
            {type: 'custom', field: 'useable',  value: true}, // 可生成优惠券的母版
            {type: 'custom', field: 'useable',  value: false}, // 不可生成优惠券的母版
        ]
        ```
      tags:
        - 商家小程序/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: &ref_2
              $ref: '#/components/schemas/TPageListReq'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: &ref_19
                          $ref: '#/components/schemas/TCouponTpl'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/couponTplQrCode:
    post:
      summary: 商家优惠券模板二维码
      deprecated: false
      description: 根据返回的code生成二维码，生成可使用uqrcodejs
      tags:
        - 商家小程序/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                couponTplId:
                  type: string
                  title: 优惠券模板ID
              required:
                - couponTplId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      qrCode:
                        type: string
                        title: 动态code
                    title: 返回数据
                    required:
                      - qrCode
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/couponBlankPageList:
    post:
      summary: 商家空白券记录分页列表
      deprecated: false
      description: ''
      tags:
        - 商家小程序/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: &ref_20
                          $ref: '#/components/schemas/TCouponBlank'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/couponListInCouponBlank:
    post:
      summary: 商家空白券记录包含优惠券
      deprecated: false
      description: ''
      tags:
        - 商家小程序/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                couponBlankId:
                  type: string
                  title: 空白券记录ID
              required:
                - couponBlankId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                        title:
                          type: string
                          title: 标题
                        couponCode:
                          type: string
                          title: 优惠券编码
                        useSceneType:
                          type: string
                          description: Coupon_useSceneType
                          title: 使用场景
                        type:
                          type: string
                          description: Coupon_type “0”小时券,”1”金额券 "2"折扣券
                          title: 类型
                        parValue:
                          type: number
                          title: 面值
                          description: 小时券,单位分钟，金额券,单位分 折扣券 0表示免费10表示1折85表示85折90表示9折
                        endAt:
                          type: string
                          title: 截至时间
                        couponTplId:
                          type: string
                          title: 优惠券模板id
                        couponTplObj: &ref_23
                          title: 优惠券模板
                          $ref: '#/components/schemas/TLcCouponTpl'
                        userId:
                          type: string
                          title: 用户id
                        userObj: *ref_0
                        orderId:
                          type: string
                          title: 订单id
                        orderObj: &ref_13
                          title: 订单
                          $ref: '#/components/schemas/TLcOrder'
                        plate:
                          type: string
                          title: 车牌号
                        usedAt:
                          type: string
                          title: 使用时间
                          description: 存在此项表示已使用
                        sourceType:
                          type: string
                          description: Coupon_sourceType
                          title: 来源类型
                        sourceId:
                          type: string
                          title: 来源ID
                        sourceModel:
                          type: string
                          title: 来源模型
                          description: Coupon_sourceModel
                        seqNum:
                          type: number
                          title: 序号
                          description: 和二维码上的序号一致
                      required:
                        - seqNum
                        - _id
                        - title
                        - couponCode
                        - useSceneType
                        - type
                        - parValue
                        - endAt
                        - couponTplId
                        - couponTplObj
                        - userId
                        - userObj
                        - orderId
                        - orderObj
                        - plate
                        - usedAt
                        - sourceType
                        - sourceId
                        - sourceModel
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/tricycleIllegalCreate:
    post:
      summary: 三轮车违规情况录入
      deprecated: false
      description: ''
      tags:
        - 商家小程序/三轮车/违规处罚
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tricycleId:
                  type: string
                  title: 三轮车ID
                ruleId:
                  type: string
                  title: 违规规则ID
                images:
                  type: array
                  items:
                    type: string
                  title: 图片
              required:
                - tricycleId
                - ruleId
                - images
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: &ref_3
                    title: 返回数据
                    $ref: '#/components/schemas/TTricycleIllegal'
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/tricycleIllegalRuleList:
    post:
      summary: 三轮车违规规则列表
      deprecated: false
      description: ''
      tags:
        - 商家小程序/三轮车/违规处罚
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties: { }
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: array
                    items: &ref_15
                      $ref: '#/components/schemas/TTricycleIllegalRule'
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/tricycleIllegalHandle:
    post:
      summary: 三轮车违规处理
      deprecated: false
      description: ''
      tags:
        - 商家小程序/三轮车/违规处罚
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tricycleIllegalId:
                  type: string
                handleStatus:
                  type: string
                  description: 1-取消 2-拖走
                  title: 处理状态
              required:
                - tricycleIllegalId
                - handleStatus
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/tricycleIllegalFinePay:
    post:
      summary: 三轮车违规缴纳罚款
      deprecated: false
      description: ''
      tags:
        - 商家小程序/三轮车/违规处罚
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tricycleIllegalId:
                  type: string
                  title: 违规记录ID
                fake:
                  type: boolean
                  title: 模拟支付
              required:
                - tricycleIllegalId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: &ref_4
                    title: 返回数据
                    $ref: '#/components/schemas/TWxPayParams'
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/tricycleIllegalPageList:
    post:
      summary: 三轮车违规记录列表
      deprecated: false
      description: |-
        ```js
        filter: [
            {type: 'custom', field: 'uiHandleStatus', value: 'xxx'}, // xxx可以是 已警告/待处理/已处理
        ]

        ```
      tags:
        - 商家小程序/三轮车/违规处罚
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      warnedTotal:
                        type: integer
                        title: 已警告数量
                      waitHandleTotal:
                        type: integer
                        title: 待处理数量
                      list:
                        type: array
                        items: *ref_3
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                      - warnedTotal
                      - waitHandleTotal
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/tricycleIllegalPass:
    post:
      summary: 三轮车违规出库放行
      deprecated: false
      description: ''
      tags:
        - 商家小程序/三轮车/违规处罚
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tricycleIllegalId:
                  type: string
                  title: 三轮车违规记录ID
              required:
                - tricycleIllegalId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      success:
                        type: boolean
                    title: 返回数据
                    required:
                      - success
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/tricycleIllegalGet:
    post:
      summary: 获取违规处罚详情
      deprecated: false
      description: |-
        _id和qrCode传其中一个 
        qrCode是取回三轮车
      tags:
        - 商家小程序/三轮车/违规处罚
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                qrCode:
                  type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/tricycleDepositeRefundApply:
    post:
      summary: 三轮车退保证金申请
      deprecated: false
      description: ''
      tags:
        - 商家小程序/三轮车/违规处罚
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tricycleId:
                  type: string
                  title: 三轮车ID
              required:
                - tricycleId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/tricycleCheckApply:
    post:
      summary: 三轮车审核申请
      deprecated: false
      description: ''
      tags:
        - 商家小程序/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                images:
                  type: array
                  items:
                    type: string
                  title: 图片
                tricycleId:
                  type: string
                  title: 三轮车ID
                  description: 创建不需要，重新审核需要
              required:
                - images
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: &ref_5
                    $ref: '#/components/schemas/TTricycle'
                    title: 三轮车对象
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/tricycleDepositePay:
    post:
      summary: 三轮车保证金付款
      deprecated: false
      description: ''
      tags:
        - 商家小程序/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tricycleId:
                  type: string
                  title: 三轮车ID
                fake:
                  type: boolean
                  title: 是否虚拟支付
              required:
                - tricycleId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_4
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/tricycleGet:
    post:
      summary: 三轮车详情获取
      deprecated: false
      description: _id和qrCode传其中一个
      tags:
        - 商家小程序/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                qrCode:
                  type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/tricyclePageList:
    post:
      summary: 三轮车分页列表
      deprecated: false
      description: ''
      tags:
        - 商家小程序/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: *ref_5
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/tricycleQrCode:
    post:
      summary: 三轮车二维码参数
      deprecated: false
      description: ''
      tags:
        - 商家小程序/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tricycleId:
                  type: string
                  title: 三轮车ID
              required:
                - tricycleId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      qrCode:
                        type: string
                        title: 二维码内容
                      numCode:
                        type: string
                        title: 三轮车编号
                    title: 返回数据
                    required:
                      - qrCode
                      - numCode
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/tricycleCheckCancel:
    post:
      summary: 三轮车取消审核
      deprecated: false
      description: ''
      tags:
        - 商家小程序/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tricycleId:
                  type: string
                  title: 三轮车ID
              required:
                - tricycleId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/workflowPageList:
    post:
      summary: 商家工作流分页列表
      deprecated: false
      description: |-
        ```js
        "filter": [
          {"type": "custom", "field": "doStatus", "value": "todo"} //待办
         {"type": "custom", "field": "doStatus", "value": "done"} //已办
        ]
        ```
      tags:
        - 商家小程序/工作流
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: &ref_14
                          $ref: '#/components/schemas/TWorkflow'
                          title: 工作流对象
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/messagePageList:
    post:
      summary: 商家消息分页列表
      deprecated: false
      description: ''
      tags:
        - 商家小程序/消息
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema: &ref_16
                $ref: '#/components/schemas/TPageListRes'
                required:
                  - data
          headers: { }
      security:
        - bearer: [ ]
  /merchant/orderPageList:
    post:
      summary: 订单分页列表
      deprecated: false
      description: |-
        ```js
        "filter":  [
            {"type": "custom", "field": "type", "value": "xxx"} //  xxx可以是保证金/账单
        ]
        ```
      tags:
        - 商家小程序/订单
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: &ref_18
                          title: 我的保证金订单
                          $ref: '#/components/schemas/TOrder'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/articlePageList:
    post:
      summary: 文章分页列表
      deprecated: false
      description: ''
      tags:
        - 商家小程序/文章
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                pageNum:
                  type: integer
                pageSize:
                  type: integer
                filter:
                  type: array
                  items:
                    type: object
                    properties:
                      field:
                        type: string
                      type:
                        type: string
                      value:
                        type: string
                    required:
                      - field
                      - type
                      - value
                  minItems: 0
                  maxItems: 0
                sort:
                  type: array
                  items:
                    type: object
                    properties:
                      field:
                        type: string
                      order:
                        type: string
                    required:
                      - field
                      - order
                  minItems: 0
                  maxItems: 0
              required:
                - pageNum
                - pageSize
                - filter
                - sort
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: &ref_6
                          $ref: '#/components/schemas/TArticle'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/articleGetById:
    post:
      summary: 文章详情
      deprecated: false
      description: ''
      tags:
        - 商家小程序/文章
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                title:
                  type: string
                  title: 按标题查询
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_6
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /merchant/bannerList:
    post:
      summary: 轮播列表
      deprecated: false
      description: ''
      tags:
        - 商家小程序/轮播
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                place:
                  type: string
                  title: 位置
                  description: 展示位置
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        _id:
                          type: string
                        type:
                          type: string
                          title: 类型
                          description: 1-图片；2-视频
                          enum:
                            - '1'
                            - '2'
                        place:
                          type: string
                          title: 位置
                          description: 1-首页顶部
                          enum:
                            - '1'
                        url:
                          type: string
                          title: URL
                          description: 资源地址
                        linkType:
                          type: string
                          title: 跳转方式
                          description: 1-小程序；2-H5
                          enum:
                            - '1'
                            - '2'
                        linkUrl:
                          type: string
                          title: 跳转地址
                      required:
                        - _id
                        - type
                        - place
                        - url
                        - linkType
                        - linkUrl
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/myUserInfoGet:
    post:
      summary: 获取登录用户信息
      deprecated: false
      description: ''
      tags:
        - 后台管理/登录
      parameters: [ ]
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      userInfo: &ref_7
                        $ref: '#/components/schemas/TAdminUser'
                    title: 返回数据
                    required:
                      - userInfo
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/loginByPwd:
    post:
      summary: 密码登录
      deprecated: false
      description: ''
      tags:
        - 后台管理/登录
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                  title: 用户名
                password:
                  type: string
                  title: 密码
              required:
                - username
                - password
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                      expiredAt:
                        type: integer
                        title: 时间戳
                      userInfo: *ref_7
                    title: 返回数据
                    required:
                      - token
                      - expiredAt
                      - userInfo
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/captcha:
    post:
      summary: 验证码
      deprecated: false
      description: ''
      tags:
        - 后台管理/登录
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties: { }
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/articlePageList:
    post:
      summary: 文章分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/文章管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: *ref_6
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/articleGetById:
    post:
      summary: 文章详情
      deprecated: false
      description: ''
      tags:
        - 后台管理/文章管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_6
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/articleDeleteById:
    post:
      summary: 文章删除
      deprecated: false
      description: ''
      tags:
        - 后台管理/文章管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      success:
                        type: boolean
                    title: 返回数据
                    required:
                      - success
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/articleAllTags:
    post:
      summary: 文章所有标签
      deprecated: false
      description: ''
      tags:
        - 后台管理/文章管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties: { }
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: array
                    items:
                      type: string
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/articleCreateOrUpdate:
    post:
      summary: 文章创建或修改
      deprecated: false
      description: ''
      tags:
        - 后台管理/文章管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                title:
                  type: string
                  title: 标题
                cover:
                  type: string
                  title: 封面
                intro:
                  type: string
                  title: 简介
                webViewUrl:
                  type: string
                  title: 跳转链接
                  description: 此项不为空，则文章详情页将跳转到该链接
                content:
                  type: string
                  title: 内容
                categoryId:
                  type: array
                  items:
                    type: string
                  title: 分类
                tags:
                  type: array
                  items:
                    type: string
                  title: 标签
                viewCount:
                  type: integer
                  title: 查看数
                favouriteCount:
                  type: string
                  title: 收藏数
                createdById:
                  type: string
                  title: 创建人
                createdByObj: &ref_24
                  $ref: '#/components/schemas/TSimpleUserInfo'
                createdAt:
                  type: string
                  title: 创建时间
                isRecommend:
                  type: boolean
                  title: 是否热点推荐
              required:
                - title
                - cover
                - intro
                - webViewUrl
                - content
                - categoryId
                - tags
                - viewCount
                - favouriteCount
                - createdById
                - createdByObj
                - createdAt
                - isRecommend
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_6
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/shopGetById:
    post:
      summary: 商铺详情
      deprecated: false
      description: ''
      tags:
        - 后台管理/商铺管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: &ref_8
                    title: 返回数据
                    $ref: '#/components/schemas/TShop'
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/shopPageList:
    post:
      summary: 商铺分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/商铺管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: *ref_8
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/shopCreateOrUpdate:
    post:
      summary: 商铺创建或修改
      deprecated: false
      description: ''
      tags:
        - 后台管理/商铺管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                  title: ID
                cellNo:
                  type: string
                  title: 商铺编码
                title:
                  type: string
                  title: 标题
                cover:
                  type: string
                  title: 封面
                videos:
                  type: array
                  items:
                    type: string
                  title: 视频
                images:
                  type: array
                  items:
                    type: string
                  title: 图片
                houseType:
                  type: array
                  items:
                    type: string
                  title: 户型
                tags:
                  type: array
                  items:
                    type: string
                  title: 标签
                payment:
                  type: string
                  title: 付款方式
                orientation:
                  type: string
                  title: 朝向
                floor:
                  type: integer
                  title: 楼层
                decoration:
                  type: string
                  title: 装修
                cellId:
                  type: integer
                  title: 单元ID
                cellName:
                  type: string
                  title: 单元名称
                cellProperty:
                  type: string
                  title: 房产性质
                cellArea:
                  type: number
                  title: 套内面积
                buildingArea:
                  type: number
                  title: 建筑面积
                cellStatus:
                  type: string
                  title: 单元状态
                contractStartDate:
                  type: string
                  title: 合同开始日期
                contractEndDate:
                  type: string
                  title: 合同结束日期
                contractNo:
                  type: string
                  title: 合同编号
                businessType:
                  type: array
                  items:
                    type: string
                  title: 经营类型
                cellRemark:
                  type: string
                  title: 单元备注
                attachmentNames:
                  type: string
                  title: 附件名称
                attachmentPaths:
                  type: string
                  title: 附件路径
                costName:
                  type: string
                  title: 费用名称
                unitPrice:
                  type: string
                  title: 单价
                billingCycle:
                  type: string
                  title: 付款方式
                chargeAmount:
                  type: string
                  title: 费用金额
                tenantId:
                  type: string
                  title: 租户Id
                tenantName:
                  type: string
                  title: 租户名称
                tenantPhone:
                  type: string
                  title: 租户电话
                contactPerson:
                  type: string
                  title: 联系人
                contactPhone:
                  type: string
                  title: 联系人电话
                tenantType:
                  type: string
                  title: 租户类型
                  description: 1-个人；2-公司
                tenantIdCard:
                  type: string
                  title: 租户身份证号
                tenantAddress:
                  type: string
                  title: 租户地址
                businessLicense:
                  type: string
                  title: 营业执照号
                tenantRemark:
                  type: string
                  title: 租户备注
                onShelf:
                  type: boolean
                  title: 是否上架
                auctionObj: &ref_17
                  $ref: '#/components/schemas/TAuction'
                region:
                  type: array
                  items:
                    type: string
                  title: 区域
                onShelfAt:
                  type: string
                  title: 上架时间
                viewCount:
                  type: integer
                  title: 浏览量
                favouriteCount:
                  type: string
                  title: 收藏量
                brokerCalledCount:
                  type: string
                  title: 通话量
                recommend:
                  type: number
                  title: 推荐值
                  description: 大于零表示推荐
                overallRank:
                  type: integer
                  title: 总排名
                inAreaRank:
                  type: integer
                  title: 同馆排名
              required:
                - cellNo
                - title
                - cover
                - videos
                - images
                - houseType
                - tags
                - payment
                - orientation
                - floor
                - decoration
                - cellId
                - cellName
                - cellProperty
                - cellArea
                - buildingArea
                - cellStatus
                - contractStartDate
                - contractEndDate
                - contractNo
                - businessType
                - onShelf
                - auctionObj
                - region
                - onShelfAt
                - favouriteCount
                - brokerCalledCount
                - recommend
                - overallRank
                - inAreaRank
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_8
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/shopAllTags:
    post:
      summary: 商铺所有标签
      deprecated: false
      description: ''
      tags:
        - 后台管理/商铺管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties: { }
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: array
                    items:
                      type: string
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /:
    post:
      summary: 关注列表
      deprecated: false
      description: 根据用户信息，查看当前关注的铺位，若为登陆需要先登陆
      tags:
        - 后台管理/商铺管理
      parameters: [ ]
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/shopRank:
    post:
      summary: 商铺重建排名
      deprecated: false
      description: ''
      tags:
        - 后台管理/商铺管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties: { }
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: &ref_9
                    title: 返回数据
                    $ref: '#/components/schemas/TSuccess'
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/shopSyncConflictPageList:
    post:
      summary: 商铺同步冲突分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/商铺管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/TShopSyncConflict'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/shopSyncConflictHandle:
    post:
      summary: 商铺同步冲突处理
      deprecated: false
      description: ''
      tags:
        - 后台管理/商铺管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                rightValue:
                  type: string
                  title: 正确的值
              required:
                - _id
                - rightValue
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/dictPageList:
    post:
      summary: 字典分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/字典管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: &ref_10
                          $ref: '#/components/schemas/TDict'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/dictCreateOrUpdate:
    post:
      summary: 字典创建或更新
      deprecated: false
      description: ''
      tags:
        - 后台管理/字典管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                key:
                  type: string
                  title: 键名
                name:
                  type: string
                  title: 名称
                description:
                  type: string
                  title: 描述
                options:
                  type: array
                  items:
                    type: object
                    properties:
                      label:
                        type: string
                      value:
                        type: string
                      status:
                        type: boolean
                    required:
                      - label
                      - value
                status:
                  type: boolean
                  description: 状态
              required:
                - key
                - name
                - description
                - options
                - status
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_10
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/dictGet:
    post:
      summary: 获取字典选项
      deprecated: false
      description: 根据name或key获取字典options
      tags:
        - 后台管理/字典管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                key:
                  type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: array
                    items:
                      type: string
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/dictGetMap:
    post:
      summary: 批量获取字典选项
      deprecated: false
      description: |-
        根据key数组，或tags数组，获取字典Map对象  
        Map对象的key是字典的key，value是字典的options
      tags:
        - 后台管理/字典管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                key:
                  type: array
                  items:
                    type: string
                tags:
                  type: array
                  items:
                    type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties: { }
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/dictAllTags:
    post:
      summary: 字典所有标签
      deprecated: false
      description: 获取字典表内字典的所有标签，用作筛选、设置标签的选项
      tags:
        - 后台管理/字典管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties: { }
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: array
                    items:
                      type: string
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/dictDeleteById:
    post:
      summary: 删除字典
      deprecated: false
      description: ''
      tags:
        - 后台管理/字典管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      success:
                        type: boolean
                    title: 返回数据
                    required:
                      - success
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/userPageList:
    post:
      summary: 用户分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/用户管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: *ref_0
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/userBatchSetBussinessType:
    post:
      summary: 上传用户商家类型
      deprecated: false
      description: ''
      tags:
        - 后台管理/用户管理
      parameters: [ ]
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  format: binary
                  type: string
                  example: file://C:\Users\<USER>\Desktop\用户商家类型模板.xlsx
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/userRealInfoGet:
    post:
      summary: 用户实名认证信息
      deprecated: false
      description: ''
      tags:
        - 后台管理/用户管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                merchantUserId:
                  type: string
              required:
                - merchantUserId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    title: 返回数据
                    $ref: '#/components/schemas/TUserRealInfo'
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/mediaPageList:
    post:
      summary: 媒体分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/媒体管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: &ref_11
                          $ref: '#/components/schemas/TMedia'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/mediaUpdate:
    post:
      summary: 媒体信息更新
      deprecated: false
      description: ''
      tags:
        - 后台管理/媒体管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                alt:
                  type: string
                  title: 占位文字
                desc:
                  type: string
                  title: 描述
                size:
                  type: string
                  title: byte大小
                width:
                  type: string
                  title: 宽度
                height:
                  type: string
                  title: 高度
                duration:
                  type: string
                  title: 视频时长
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_11
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/mediaDeleteById:
    post:
      summary: 媒体删除
      deprecated: false
      description: ''
      tags:
        - 后台管理/媒体管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      success:
                        type: boolean
                    title: 返回数据
                    required:
                      - success
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/bannerPageList:
    post:
      summary: 横幅分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/横幅管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: &ref_12
                          $ref: '#/components/schemas/TBanner'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/bannerCreateOrUpdate:
    post:
      summary: 横幅添加或修改
      deprecated: false
      description: ''
      tags:
        - 后台管理/横幅管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                type:
                  type: string
                  title: 类型
                  description: 1-图片；2-视频
                place:
                  type: string
                  title: 位置
                  description: 1-首页顶部
                url:
                  type: string
                  title: URL
                  description: 资源地址
                linkType:
                  type: string
                  title: 跳转方式
                  description: 1-小程序；2-H5
                linkUrl:
                  type: string
                  title: 跳转地址
                startAt:
                  type: string
                  title: 开始时间
                endAt:
                  type: string
                  title: 结束时间
                status:
                  type: boolean
                  title: 是否启用
              required:
                - type
                - place
                - url
                - linkType
                - linkUrl
                - startAt
                - endAt
                - status
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_12
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/bannerDeleteById:
    post:
      summary: 横幅删除
      deprecated: false
      description: ''
      tags:
        - 后台管理/横幅管理
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      success:
                        type: boolean
                    title: 返回数据
                    required:
                      - success
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/upload:
    post:
      summary: 上传
      deprecated: false
      description: ''
      tags:
        - 后台管理/其他
      parameters:
        - name: keepName
          in: query
          description: 是否保持文件名
          required: false
          schema:
            type: string
        - name: saveMedia
          in: query
          description: 是否保存到media
          required: false
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  format: binary
                  type: string
                  example: ''
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      url:
                        type: string
                    title: 返回数据
                    required:
                      - url
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/playground:
    post:
      summary: 测试
      deprecated: false
      description: ''
      tags:
        - 后台管理/其他
      parameters: [ ]
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/lcParkPageList:
    post:
      summary: 停车场库分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/停车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/TLcPark'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/lcOrderPageList:
    post:
      summary: 停车订单分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/停车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: *ref_13
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/lcOrderGetById:
    post:
      summary: 停车订单详情
      deprecated: false
      description: ''
      tags:
        - 后台管理/停车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_13
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/workflowPageList:
    post:
      summary: 后台工作流分页列表
      deprecated: false
      description: |-
        ```js
        "filter": [
          {"type": "custom", "field": "doStatus", "value": "todo"} //待办
         {"type": "custom", "field": "doStatus", "value": "done"} //已办
        ]
        ```
      tags:
        - 后台管理/工作流
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: *ref_14
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/workflowGetById:
    post:
      summary: 工作流详情
      deprecated: false
      description: ''
      tags:
        - 后台管理/工作流
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_14
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/tricyclePageList:
    post:
      summary: 三轮车分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: *ref_5
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/tricycleCheck:
    post:
      summary: 三轮车审核
      deprecated: false
      description: ''
      tags:
        - 后台管理/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tricycleId:
                  type: string
                  title: 三轮车ID
                checkStatus:
                  type: string
                  title: 审核状态
                checkRemark:
                  type: string
                  title: 审核备注
                checkRejectReason:
                  type: string
                  title: 拒绝理由
              required:
                - tricycleId
                - checkStatus
                - checkRemark
                - checkRejectReason
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/tricycleGetById:
    post:
      summary: 三轮车详情
      deprecated: false
      description: ''
      tags:
        - 后台管理/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_5
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/tricycleIllegalRulePageList:
    post:
      summary: 三轮车违规规则分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: *ref_15
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/tricycleIllegalRuleDeleteById:
    post:
      summary: 三轮车违规规则删除
      deprecated: false
      description: ''
      tags:
        - 后台管理/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/tricycleIllegalRuleCreateOrUpdate:
    post:
      summary: 三轮车违规规则新增或更新
      deprecated: false
      description: ''
      tags:
        - 后台管理/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                name:
                  type: string
                  title: 规则名称
                punishDelayMinutes:
                  type: integer
                  title: 延时处罚分钟数
                fineCent:
                  type: integer
                  title: 罚款金额
                  description: 单位分
                needTow:
                  type: boolean
                  title: 是否需要拖走
                  description: 拖走的多一个出库步骤
                status:
                  type: boolean
                  title: 是否启用
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_15
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/tricycleIllegalPageList:
    post:
      summary: 三轮车违规记录分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: *ref_3
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/tricycleIllegalGetById:
    post:
      summary: 三轮车违规记录详情
      deprecated: false
      description: ''
      tags:
        - 后台管理/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_3
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - traceId
                  - data
          headers: { }
      security:
        - bearer: [ ]
  /admin/tricycleDepositeRefund:
    post:
      summary: 三轮车保证金退款
      deprecated: false
      description: ''
      tags:
        - 后台管理/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tricycleId:
                  type: string
                  title: 三轮车ID
              required:
                - tricycleId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/tricycleQrCode:
    post:
      summary: 三轮车二维码
      deprecated: false
      description: ''
      tags:
        - 后台管理/三轮车
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tricycleId:
                  type: string
              required:
                - tricycleId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      qrCode:
                        type: string
                        title: 二维码
                      numCode:
                        type: string
                        title: 三路车编号
                    title: TTricycleQrCode
                    required:
                      - qrCode
                      - numCode
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/messagePageList:
    post:
      summary: 后台消息分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/消息
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema: *ref_16
          headers: { }
      security:
        - bearer: [ ]
  /admin/brokerCreateOrUpdate:
    post:
      summary: 经纪人创建或更新
      deprecated: false
      description: ''
      tags:
        - 后台管理/经纪人
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                name:
                  type: string
                  title: 姓名
                avatar:
                  type: string
                  title: 头像
                mobile:
                  type: string
                  title: 手机号
                jobTitle:
                  type: string
                  title: 头衔
                jobPosition:
                  type: string
                  title: 职位
                pastVolumeCount:
                  type: integer
                  title: 历史成交量
                winPercent:
                  type: string
                  title: 战胜比例
                  description: 每天跑个任务，排序之后更新，
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/brokerPageList:
    post:
      summary: 经纪人分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/经纪人
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/TBroker'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/brokerDeleteById:
    post:
      summary: 经济人删除
      deprecated: false
      description: ''
      tags:
        - 后台管理/经纪人
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                  title: 经纪人Id
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/settingMap:
    post:
      summary: 所有设置的Map
      deprecated: false
      description: ''
      tags:
        - 后台管理/设置
      parameters: [ ]
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties: { }
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/settingCreateOrUpdate:
    post:
      summary: 设置新增或更新
      deprecated: false
      description: ''
      tags:
        - 后台管理/设置
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                key:
                  type: string
                  title: 键名
                value:
                  type: string
                  title: 键值
                parentKey:
                  type: string
                  title: 父键名
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/feedbackUpdate:
    post:
      summary: 投诉建议处理
      deprecated: false
      description: ''
      tags:
        - 后台管理/投诉建议
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                remark:
                  type: string
                  title: 备注
                handled:
                  type: boolean
                  title: 是否处理
              required:
                - remark
                - handled
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/feedbackPageList:
    post:
      summary: 投诉建议分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/投诉建议
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/TFeedback'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/auctionCreateOrUpdate:
    post:
      summary: 拍卖新增或修改
      deprecated: false
      description: ''
      tags:
        - 后台管理/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  title: 标题
                targetId:
                  type: string
                  title: 拍卖物品id
                targetModel:
                  type: string
                  description: Auction_targetModel 商铺;摊位;广告位
                  title: 拍卖物品类型
                depositCent:
                  type: number
                  title: 保证金
                  description: 单位分
                bidStartCent:
                  type: number
                  title: 起拍价
                  description: 单位分
                bidIncCent:
                  type: number
                  title: 竞价幅度
                  description: 单位分
                startAt:
                  type: string
                  title: 开始时间
                freeCycleMins:
                  type: string
                  title: 自由竞价周期
                  description: Auction_bidCycle分钟数字符串
                delayCycleMins:
                  type: string
                  description: Auction_delayCycle分钟数字符串
                  title: 延时竞价周期
              required:
                - title
                - targetModel
                - targetId
                - depositCent
                - bidStartCent
                - bidIncCent
                - startAt
                - freeCycleMins
                - delayCycleMins
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/auctionPageList:
    post:
      summary: 拍卖分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: *ref_17
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/auctionDeleteById:
    post:
      summary: 拍卖删除
      deprecated: false
      description: ''
      tags:
        - 后台管理/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/auctionGet:
    post:
      summary: 拍卖详情
      deprecated: false
      description: ''
      tags:
        - 后台管理/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                targetId:
                  type: string
                  title: 拍卖物品id
                targetModel:
                  type: string
                  description: Auction_targetModel 商铺;摊位;广告位
                  title: 拍卖物品类型
                endAt:
                  type: 'null'
                  title: 结束时间
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_17
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/auctionBidRelatList:
    post:
      summary: 拍卖出价(关联)列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                auctionId:
                  type: string
                  title: 拍卖ID
              required:
                - auctionId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/TAuctionBid'
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/auctionRelatList:
    post:
      summary: 拍卖(关联)列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                targetId:
                  type: string
                targetModel:
                  type: string
              required:
                - targetId
                - targetModel
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: array
                    items: *ref_17
                    title: 返回数据
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/auctionTailBreak:
    post:
      summary: 拍卖尾款违约
      deprecated: false
      description: ''
      tags:
        - 后台管理/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                tailBreakRemark:
                  type: string
                  title: 违约备注
              required:
                - _id
                - tailBreakRemark
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/auctionDelivered:
    post:
      summary: 拍卖交割
      deprecated: false
      description: ''
      tags:
        - 后台管理/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                deliveredRemark:
                  type: string
                  title: 交割备注
                deliveredFiles:
                  type: array
                  items:
                    type: string
                  title: 交割附件
              required:
                - _id
                - deliveredRemark
                - deliveredFiles
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/auctionManualStop:
    post:
      summary: 拍卖手动终止
      deprecated: false
      description: ''
      tags:
        - 后台管理/拍卖
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                manualStopReason:
                  type: string
                  title: 手动中止理由
              required:
                - _id
                - manualStopReason
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/orderRelatList:
    post:
      summary: 订单(关联)列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/订单
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  title: 类型
                entityId:
                  type: string
                  title: 商品id
                entityModel:
                  type: string
                  title: 商品模型
                couponDiscountCent:
                  type: number
                  title: 优惠券抵扣金额
                  description: 单位分
                pointDiscountCent:
                  type: number
                  title: 积分抵扣金额
                  description: 单位分
                orderMoneyCent:
                  type: number
                  title: 订单金额
                  description: 单位分，应付金额
                paidMoneyCent:
                  type: number
                  title: 实付金额
                  description: 单位分
                payStatus:
                  type: string
                  title: 支付状态
                  description: Order_payStatus 未支付；已支付；已退款；部分退款；已关闭
                paidAt:
                  type: string
                  title: 付款时间
                closedAt:
                  type: string
                  title: 关闭时间
                closedReason:
                  type: string
                  title: 关闭原因
                remark:
                  type: string
                  title: 备注
                createdAt:
                  type: string
                  title: 创建时间
                refundList:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        title: 退款交易单号
                      moneyCent:
                        type: string
                        title: 退款金额
                      feeCent:
                        type: string
                        title: 退款手续费
                      trxid:
                        type: string
                        title: 通联退款流水号
                      operatedAt:
                        type: string
                        title: 退款操作时间
                      refundedAt:
                        type: string
                        title: 退款成功时间
                      reason:
                        type: string
                        title: 退款原因
                    required:
                      - id
                      - moneyCent
                      - feeCent
                      - trxid
                      - operatedAt
                      - refundedAt
                      - reason
                  title: 退款列表
              required:
                - type
                - entityId
                - entityModel
                - couponDiscountCent
                - pointDiscountCent
                - orderMoneyCent
                - paidMoneyCent
                - payStatus
                - paidAt
                - closedAt
                - closedReason
                - remark
                - createdAt
                - refundList
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/orderRefundById:
    post:
      summary: 订单退款
      deprecated: false
      description: ''
      tags:
        - 后台管理/订单
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                reason:
                  type: string
                  title: 原因
                moneyCent:
                  type: number
                  title: 金额
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/orderPageList:
    post:
      summary: 订单分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/订单
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: *ref_18
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/couponTplCreateOrUpdate:
    post:
      summary: 优惠券模板新增或更新
      deprecated: false
      description: ''
      tags:
        - 后台管理/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                title:
                  type: string
                  title: 标题
                receiveTitle:
                  type: string
                  title: 领取标题
                useSceneType:
                  type: string
                  title: 使用场景
                  description: Coupon_useSceneType
                type:
                  type: string
                  title: 类型
                  description: Coupon_type, “0”小时券,”1”金额券 "2"折扣券
                parValue:
                  type: integer
                  title: 面值
                  description: 小时券,单位分钟，金额券,单位分 折扣券 0表示免费10表示1折85表示85折90表示9折
                endAtType:
                  type: string
                  title: 过期类型
                  description: Coupon_endAtType
                relativeEndAt:
                  type: integer
                  title: 相对过期
                  description: 单位分钟
                absoluteEndAt:
                  type: string
                  title: 绝对过期
                publishCount:
                  type: integer
                  title: 总发布数量
                pusherIds:
                  type: string
                  title: 发券者
                status:
                  type: boolean
                  title: 启用状态
              required:
                - pusherIds
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_19
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/couponTplPageList:
    post:
      summary: 优惠券模板分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: *ref_19
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/couponPageList:
    post:
      summary: 优惠券分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/TCoupon'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/couponTplGetById:
    post:
      summary: 优惠券模板详情
      deprecated: false
      description: ''
      tags:
        - 后台管理/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_19
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/couponTplDeleteById:
    post:
      summary: 优惠券模板删除
      deprecated: false
      description: ''
      tags:
        - 后台管理/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/couponCreateByTpl:
    post:
      summary: 优惠券发放给用户
      deprecated: false
      description: ''
      tags:
        - 后台管理/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                couponTplId:
                  type: string
                  title: 优惠券模板Id
                userIds:
                  type: array
                  items:
                    type: string
                  title: 用户ID
              required:
                - couponTplId
                - userIds
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/couponBlankCreate:
    post:
      summary: 空白优惠券创建
      deprecated: false
      description: ''
      tags:
        - 后台管理/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                couponTplId:
                  type: string
                count:
                  type: string
                merchantUserId:
                  type: string
              required:
                - couponTplId
                - count
                - merchantUserId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/couponBlankPageList:
    post:
      summary: 空白券生成记录分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: *ref_20
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/couponBlankDeleteById:
    post:
      summary: 空白券生成记录删除
      deprecated: false
      description: ''
      tags:
        - 后台管理/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/couponBlankUpdate:
    post:
      summary: 空白券生成记录更新
      deprecated: false
      description: ''
      tags:
        - 后台管理/优惠券
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
                downloadUrl:
                  type: string
                  title: 下载地址
              required:
                - _id
                - downloadUrl
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/artworkPageList:
    post:
      summary: 艺术品分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/艺术品
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: &ref_21
                          $ref: '#/components/schemas/TArtwork'
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/artworkGetById:
    post:
      summary: 艺术品详情
      deprecated: false
      description: ''
      tags:
        - 后台管理/艺术品
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_21
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/artworkCreateOrUpdate:
    post:
      summary: 艺术品新增或更新
      deprecated: false
      description: ''
      tags:
        - 后台管理/艺术品
      parameters: [ ]
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_21
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/artworkDeleteById:
    post:
      summary: 艺术品删除
      deprecated: false
      description: ''
      tags:
        - 后台管理/艺术品
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                _id:
                  type: string
              required:
                - _id
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_9
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/merchantUserPageList:
    post:
      summary: 商家用户分页列表
      deprecated: false
      description: ''
      tags:
        - 后台管理/商家用户
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema: *ref_2
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: object
                    properties:
                      total:
                        type: number
                        title: 总数
                      list:
                        type: array
                        items: *ref_1
                        title: 列表
                    title: 返回数据
                    required:
                      - total
                      - list
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/merchantUserUpdate:
    post:
      summary: 商家用户修改
      deprecated: false
      description: ''
      tags:
        - 后台管理/商家用户
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                mobile:
                  type: string
                  title: 手机号
                roles:
                  type: array
                  items:
                    type: string
                  title: 角色
              required:
                - mobile
                - roles
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data: *ref_1
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/merchantUserCreateByMobiles:
    post:
      summary: 商家用户批量录入
      deprecated: false
      description: ''
      tags:
        - 后台管理/商家用户
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                mobiles:
                  type: array
                  items:
                    type: string
                  title: 手机号列表
                roles:
                  type: array
                  items:
                    type: string
                  title: 角色
              required:
                - mobiles
                - roles
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    type: string
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /admin/merchantUserRealInfoGet:
    post:
      summary: 商家实名认证信息
      deprecated: false
      description: ''
      tags:
        - 后台管理/商家用户
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                merchantUserId:
                  type: string
              required:
                - merchantUserId
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 错误码
                    description: 大于0表示错误
                  msg:
                    type: string
                    title: 错误信息
                  data:
                    title: 返回数据
                    $ref: '#/components/schemas/TMerchantUserRealInfo'
                  traceId:
                    type: string
                    title: 追溯ID
                    description: 方便在日志里定位的ID
                required:
                  - code
                  - msg
                  - data
                  - traceId
          headers: { }
      security:
        - bearer: [ ]
  /park/heartBeat:
    post:
      summary: 心跳接口
      deprecated: false
      description: ''
      tags:
        - 停车场
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                bookInParkCount:
                  type: string
                  title: 场库在场预约数
                parkName:
                  type: string
                  title: 场库名称
                freeSpaceCount:
                  type: string
                  title: 场库空车位数
                areaList:
                  type: array
                  items:
                    type: object
                    properties:
                      lastSpaceCount:
                        type: integer
                      bookInParkCount:
                        type: integer
                      areaId:
                        type: integer
                      areaName:
                        type: string
                      spaceCount:
                        type: integer
                      bookSpaceCount:
                        type: integer
                  title: 区域属性
                deviceList:
                  type: array
                  items:
                    type: object
                    properties:
                      lastTime:
                        type: string
                      deviceTypeId:
                        type: integer
                      ip:
                        type: string
                      name:
                        type: string
                      passageId:
                        type: integer
                      state:
                        type: integer
                      deviceId:
                        type: string
                    required:
                      - lastTime
                      - deviceTypeId
                      - ip
                      - name
                      - passageId
                      - state
                      - deviceId
                  title: 设备信息
                state:
                  type: integer
                parkNumber:
                  type: string
                  title: 场库编号
                  description: 每个场库的唯一编号(蓝卡云分配)
                spaceCount:
                  type: string
                  title: 场库总车位数
                bookSpaceCount:
                  type: string
                  title: 场库可预约数
              required:
                - bookInParkCount
                - parkName
                - freeSpaceCount
                - areaList
                - deviceList
                - state
                - parkNumber
                - spaceCount
                - bookSpaceCount
            example:
              bookInParkCount: '0'
              parkName: 北京总部展厅
              freeSpaceCount: '48'
              areaList:
                - lastSpaceCount: 48
                  bookInParkCount: 0
                  areaId: 1
                  areaName: 外层停车场
                  spaceCount: 100
                  bookSpaceCount: 0
              deviceList:
                - lastTime: '2022-11-30 10:39:01'
                  deviceTypeId: 1
                  ip: ************
                  name: 西-入1-一体机
                  passageId: 1
                  state: 1
                  deviceId: '1201491022'
                - lastTime: '2022-11-30 10:39:01'
                  deviceTypeId: 1
                  ip: ************
                  name: 东-出1-一体机
                  passageId: 3
                  state: 1
                  deviceId: '1201490591'
                - lastTime: '2022-11-30 10:39:01'
                  deviceTypeId: 1
                  ip: ************
                  name: 东-出2-识别机
                  passageId: 4
                  state: 1
                  deviceId: '1203500109'
                - lastTime: '2022-11-30 10:39:01'
                  deviceTypeId: 1
                  ip: ************
                  name: 西-入2-识别机
                  passageId: 2
                  state: 1
                  deviceId: '1203500014'
                - lastTime: '2022-11-10 17:03:07'
                  deviceTypeId: 12
                  ip: ************
                  name: 东-出1-一体机
                  passageId: 3
                  state: 2
                  deviceId: '2011631114'
                - lastTime: '2022-12-23 13:20:07'
                  deviceTypeId: 12
                  ip: ************
                  name: 西-入1-一体机
                  passageId: 1
                  state: 1
                  deviceId: '1203631017'
              state: 0
              parkNumber: '164'
              spaceCount: '100'
              bookSpaceCount: '0'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema: &ref_22
                $ref: '#/components/schemas/BlueCardResp'
          headers: { }
      security: [ ]
  /park/carIn:
    post:
      summary: 实时发送入场记录
      deprecated: false
      description: 场库实时发送最新的入场记录(每次发送1-10条入场记录)，若3次发送均失败，那么每5分钟发送一次此入场记录,直至发送成功或者车辆已出场为止。
      tags:
        - 停车场
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                size:
                  type: integer
                datas:
                  type: array
                  items:
                    type: object
                    properties:
                      orderId:
                        type: string
                        title: 入场记录编号
                      plate:
                        type: string
                        title: 车牌
                        description: 无牌车取""
                      ticketCode:
                        type: string
                        title: 无牌车票号
                        description: 牌车入场时从出票机取票的票号,唯一标识,有牌车时此字段填""
                      plateColor:
                        type: string
                        title: 车牌颜色
                      inTime:
                        type: string
                        title: 入场时间
                      inChannel:
                        type: string
                        title: 入场通道名称
                      inImage:
                        type: string
                        title: 入场图片名
                      visitReason:
                        type: string
                        title: 访问事由
                      openGateMode:
                        type: string
                        title: 放行类型
                        description: 自动抬杆/手动放行,无信息时填“”
                      matchMode:
                        type: string
                        title: 匹配模式
                        description: 全字匹配、近似匹配、人工匹配、人工纠正,无信息时填“”
                      idCard:
                        type: string
                        title: 证件号码
                      confidence:
                        type: string
                        title: 车牌识别可信度
                      carType:
                        type: string
                        description: 临时车/固定车/预约车
                        title: 车类型
                      userInfo:
                        type: object
                        properties:
                          idCard:
                            type: string
                            description: 证件号码
                          userName:
                            type: string
                            description: 车主姓名
                          phone:
                            type: string
                            description: 联系电话
                          address:
                            type: string
                            description: 地址
                        required:
                          - address
                          - phone
                          - idCard
                          - userName
                      placeInfo:
                        type: array
                        items:
                          type: object
                          properties:
                            areaId:
                              type: string
                              description: 区域id
                            areaName:
                              type: string
                              description: 区域名称
                            placeNumber:
                              type: string
                              description: 车位编号
                            memo:
                              type: string
                              description: 备注信息
                          required:
                            - areaId
                            - areaName
                            - placeNumber
                            - memo
                      barriorOpen:
                        type: string
                        title: 是否开闸
                        description: “开闸”“未开闸”
                      costTime:
                        type: string
                        title: 开闸耗时
                        description: 压地感到抬杆时间
                      spaceCount:
                        type: string
                        title: 空位数
                      imageList:
                        type: array
                        items:
                          type: string
                        title: 其他联动设备图片信息
                      ImageName:
                        type: string
                        title: 图片名
                      modifyMemo:
                        type: object
                        properties:
                          new_id:
                            type: string
                            title: 新的订单ID
                          old_id:
                            type: string
                            title: 旧的订单ID
                          new_plate:
                            type: string
                            title: 新的车牌
                          old_plate:
                            type: string
                            title: 旧的车牌
                        title: 修改车牌信息
                        description: 车牌修改信息，默认无此项
                        required:
                          - new_id
                          - old_id
                          - new_plate
                          - old_plate
                    required:
                      - imageList
                      - ImageName
                parkNumber:
                  type: string
              required:
                - size
                - datas
                - parkNumber
            example:
              size: 1
              datas:
                - userInfo:
                    address: xx街xx号
                    phone: '13300000000'
                    idCard: '123456123456123456'
                    userName: 猫猫
                  orderId: J22345_93958765
                  idCard: ''
                  confidence: '100'
                  inChannel: 西-入1-一体机
                  plate: 京J22345
                  inImage: 2022122311_20221223113925_7080680940_def.jpg
                  spaceCount: '50'
                  plateColor: 蓝牌
                  visitReason: 访问原因
                  inTime: '2022-12-23 11:39:25'
                  carType: 临时车
                  barriorOpen: 开闸
                  openGateMode: 自动抬杆
                  costTime: '0'
                  placeInfo: [ ]
                  ticketCode: ''
                  matchMode: 全字匹配
              parkNumber: '164'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema: *ref_22
          headers: { }
      security: [ ]
  /park/carOut:
    post:
      summary: 实时发送出场记录
      deprecated: false
      description: >+
        场库按出场时间倒序把未发送的出场记录发送到第三方云，(每次发送1-10条)。若3次发送均失败，那么每5分钟发送一次此出场记录,直至成功为止。  

        Charge=onLineCharge+offLineCharge+profitChargeTotal  

        profitChargeTotal=onLineProfitChargeValue+offLineProfitChargeValue+onLineProfitTimeValue
        +offLineProfitTimeValue  

      tags:
        - 停车场
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                size:
                  type: integer
                datas:
                  type: array
                  items:
                    type: object
                    properties:
                      orderId:
                        type: string
                      operatorId:
                        type: string
                        title: 操作员Id
                      operatorName:
                        type: string
                        title: 操作员姓名
                      invoiceNo:
                        type: string
                        title: 发票号码
                      carInfo:
                        type: object
                        properties:
                          plate:
                            type: string
                            title: 车牌
                          ticketCode:
                            type: string
                            title: 无牌车票号
                          plateColor:
                            type: string
                            title: 车牌颜色
                          carType:
                            type: string
                            title: 车类型
                          confidence:
                            type: string
                            title: 车牌识别可信度
                        required:
                          - carType
                          - confidence
                          - plate
                          - ticketCode
                          - plateColor
                      userInfo:
                        type: object
                        properties:
                          address:
                            type: string
                            title: 地址
                          phone:
                            type: string
                            title: 手机号
                          idCard:
                            type: string
                            title: 证件号
                          userName:
                            type: string
                            title: 姓名
                        required:
                          - address
                          - phone
                          - idCard
                          - userName
                        title: 车主信息
                      passInfo:
                        type: object
                        properties:
                          inTime:
                            type: string
                            title: 入场时间
                          outTime:
                            type: string
                            title: 出场时间
                          inImage:
                            type: string
                            title: 入场图片名
                          outImage:
                            type: string
                            title: 出场图片名
                          inChannel:
                            type: string
                            title: 入口通道名称
                          outChannel:
                            type: string
                            title: 出口通道名称
                          openGateMode:
                            type: string
                            title: 抬杆模式
                            description: 自动抬杆/手动放行
                          matchMode:
                            type: string
                            title: 匹配模式
                            description: 全字匹配、近似匹配、人工匹配、人工纠正
                        required:
                          - inTime
                          - openGateMode
                          - outImage
                          - inChannel
                          - inImage
                          - outTime
                          - outChannel
                          - matchMode
                        title: 通行信息
                      chargeStatistics:
                        type: object
                        properties:
                          charge:
                            type: string
                            title: 总停车费
                          onLineCharge:
                            type: string
                            title: 线上总收费
                          offLineCharge:
                            type: string
                            title: 线下总收费
                          profitChargeTotal:
                            type: string
                            title: 线上线下金额和时间优惠累计抵扣值
                          onLineProfitChargeNum:
                            type: string
                            title: 线上累计优惠金额总面值
                          onLineProfitChargeValue:
                            type: string
                            title: 线上累计优惠金额总抵扣值
                          offLineProfitChargeNum:
                            type: string
                            title: 线下累计优惠金额总面值
                          offLineProfitChargeValue:
                            type: string
                            title: 线下累计优惠金额总抵扣值
                          onLineProfitTimeNum:
                            type: string
                            title: 线上累计优惠时间
                          onLineProfitTimeValue:
                            type: string
                            title: 线上累计优惠时间总抵扣值
                          offLineProfitTimeNum:
                            type: string
                            title: 线下累计优惠时间
                          offLineProfitTimeValue:
                            type: string
                            title: 线下累计优惠时间总抵扣值
                        required:
                          - onLineProfitChargeValue
                          - onLineProfitTimeValue
                          - charge
                          - offLineProfitTimeValue
                          - offLineProfitTimeNum
                          - onLineProfitTimeNum
                          - onLineCharge
                          - offLineProfitChargeNum
                          - profitChargeTotal
                          - onLineProfitChargeNum
                          - offLineProfitChargeValue
                          - offLineCharge
                        title: 收费统计信息
                      profitList:
                        type: array
                        items:
                          type: object
                          properties:
                            profitCode:
                              type: string
                              title: 优惠码
                            getTime:
                              type: string
                              title: 优惠下发时间
                            profitTime:
                              type: string
                              title: 优惠时间
                            profitCharge:
                              type: string
                              title: 优惠金额面值
                            profitChargeValue:
                              type: string
                              title: 生效金额
                            memo:
                              type: string
                              title: 备注
                            shopName:
                              type: string
                              title: 商户名称
                          required:
                            - profitCode
                            - getTime
                            - profitTime
                            - profitCharge
                            - profitChargeValue
                            - memo
                            - shopName
                        title: 优惠明细
                      placeInfo:
                        type: array
                        items:
                          type: object
                          properties:
                            areaId:
                              type: string
                            areaName:
                              type: string
                            placeNumber:
                              type: string
                            memo:
                              type: string
                          required:
                            - areaId
                            - areaName
                            - placeNumber
                            - memo
                      costTime:
                        type: string
                        title: 开闸耗时
                      spaceCount:
                        type: string
                        title: 空位数
                      chargeList:
                        type: array
                        items:
                          type: object
                          properties:
                            payNo:
                              type: string
                              title: 支付订单号
                            getTime:
                              type: string
                              title: 结算时间
                            payCharge:
                              type: string
                              title: 支付金额
                            payKind:
                              type: string
                              title: 支付类型
                            payChannel:
                              type: string
                              title: 支付渠道
                            memo:
                              type: string
                              title: 备注
                            transactionId:
                              type: string
                              description: 每笔交易生成唯一流水号（支付结果下发）
                              title: 线上交易流水号
                          required:
                            - transactionId
                        title: 收费明细
                parkNumber:
                  type: string
              required:
                - size
                - datas
                - parkNumber
            example:
              size: 1
              datas:
                - userInfo:
                    address: ''
                    phone: '12345678901'
                    idCard: '12345678901'
                    userName: A车组
                  orderId: B907A3_93959867
                  chargeStatistics:
                    onLineProfitChargeValue: '0.0'
                    onLineProfitTimeValue: '0.0'
                    charge: '0.01'
                    offLineProfitTimeValue: '0'
                    offLineProfitTimeNum: '0'
                    onLineProfitTimeNum: '0.0'
                    onLineCharge: '0.0'
                    offLineProfitChargeNum: '0'
                    profitChargeTotal: '0.0'
                    onLineProfitChargeNum: '0.0'
                    offLineProfitChargeValue: '0'
                    offLineCharge: '0.01'
                  passInfo:
                    inTime: '2022-12-23 11:57:47'
                    openGateMode: 手动放行
                    outImage: 1201490591_20221223115915_0000000001.jpg
                    inChannel: 西-入1-一体机
                    inImage: 1201491022_20221223115747_0000000002.jpg
                    outTime: '2022-12-23 11:59:15'
                    outChannel: 东-出1-一体机
                    matchMode: 全字匹配
                  operatorName: ''
                  profitList: [ ]
                  spaceCount: '49'
                  chargeList:
                    - payKind: 现金
                      payNo: s164_3_B907A3_93959955
                      getTime: '2022-12-23 11:59:15'
                      memo: '1'
                      payChannel: 出口
                      payCharge: '0.01'
                  costTime: '0'
                  placeInfo: [ ]
                  carInfo:
                    carType: 全时段
                    confidence: '90'
                    plate: 苏B907A3
                    ticketCode: ''
                    plateColor: 蓝
                  invoiceNo: ''
                  operatorId: '-4'
              parkNumber: '164'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema: *ref_22
          headers: { }
      security: [ ]
  /park/passageWayUpsert:
    post:
      summary: 通道信息上传
      deprecated: false
      description: 场库将停车场信息发送给接口。注意:只有通道信息有变化才会调用此接口.
      tags:
        - 停车场
      parameters: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                parkNumber:
                  type: string
                  title: 停车场编号
                size:
                  type: integer
                  title: datas数据条数
                datas:
                  type: array
                  items:
                    type: object
                    properties:
                      passagewayId:
                        type: string
                        title: '通道Id     '
                      passagewayName:
                        type: string
                        title: 通道名称
                      targetAreaId:
                        type: string
                        title: '目标区域Id     '
                      sourceAreaId:
                        type: string
                        title: 源区域Id
                      deviceId:
                        type: string
                        title: 设备号
                      matchClassId:
                        type: string
                        title: 匹配级别id
                      openBarriorTypeId:
                        type: string
                        title: 开闸类型
                    required:
                      - targetAreaId
                      - passagewayName
                      - sourceAreaId
                      - passagewayId
                      - deviceId
                      - matchClassId
                      - openBarriorTypeId
              required:
                - size
                - datas
                - parkNumber
            example:
              size: 4
              datas:
                - targetAreaId: '1'
                  passagewayName: 西-入1-一体机
                  sourceAreaId: '0'
                  passagewayId: '1'
                - targetAreaId: '1'
                  passagewayName: 西-入2-识别机
                  sourceAreaId: '0'
                  passagewayId: '2'
                - targetAreaId: '0'
                  passagewayName: 东-出1-一体机
                  sourceAreaId: '1'
                  passagewayId: '3'
                - targetAreaId: '0'
                  passagewayName: 东-出2-识别机
                  sourceAreaId: '1'
                  passagewayId: '4'
              parkNumber: '164'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema: *ref_22
          headers: { }
      security: [ ]
components:
  schemas:
    TPageListReq:
      type: object
      properties:
        pageNum:
          type: integer
        pageSize:
          type: integer
        filter:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
              type:
                type: string
              value:
                type: string
            required:
              - field
              - type
              - value
          minItems: 0
          maxItems: 0
        sort:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
              order:
                type: string
            required:
              - field
              - order
          minItems: 0
          maxItems: 0
      title: 分页请求
      required:
        - pageNum
        - pageSize
        - filter
        - sort
    TPageListRes:
      title: 分页响应
      type: object
      properties:
        code:
          type: integer
          title: 错误码
          description: 大于0表示错误
        msg:
          type: string
          title: 错误信息
        data:
          type: object
          properties:
            total:
              type: number
              title: 总数
            list:
              type: array
              items:
                type: string
              title: 列表
          title: 返回数据
          required:
            - total
            - list
        traceId:
          type: string
          title: 追溯ID
          description: 方便在日志里定位的ID
      required:
        - code
        - msg
        - data
        - traceId
    HttpRes:
      type: object
      properties:
        code:
          type: integer
          title: 错误码
          description: 大于0表示错误
        msg:
          type: string
          title: 错误信息
        data:
          type: string
        traceId:
          type: string
          title: 追溯ID
          description: 方便在日志里定位的ID
      required:
        - code
        - msg
        - data
        - traceId
      title: 默认响应
    TSuccess:
      type: object
      properties:
        success:
          type: boolean
          title: 操作结果
          description: 固定为true
      title: 操作成功
      required:
        - success
    TLcCouponTpl:
      type: object
      properties:
        _id:
          type: string
        type:
          type: string
          title: 类型
          description: LcCoupon_type, “0”小时券,”1”金额券 "2"折扣券
        parValue:
          type: integer
          title: 面值
          description: 小时券,单位分钟，金额券,单位分 折扣券 0表示免费10表示1折85表示85折90表示9折
        endAtType:
          type: string
          title: 过期类型
          description: LcCoupon_endAtType
        relativeEndAt:
          type: integer
          title: 相对过期
          description: 单位分钟
        absoluteEndAt:
          type: string
          title: 绝对过期
        publishCount:
          type: integer
          title: 总发布数量
        receivedCount:
          type: integer
          title: 已领取数量
        usedCount:
          type: integer
          title: 已使用数量
        status:
          type: boolean
          title: 启用状态
        title:
          type: string
          title: 标题
      title: 停车优惠券模板（废弃）
      required:
        - _id
        - type
        - parValue
        - endAtType
        - relativeEndAt
        - absoluteEndAt
        - publishCount
        - receivedCount
        - usedCount
        - status
        - title
    TLcOrder:
      type: object
      properties:
        _id:
          type: string
        orderId:
          type: string
          title: 入场记录编号
        plate:
          type: string
          title: 车牌
        ticketCode:
          type: string
          title: 无牌车票号
        plateColor:
          type: string
          title: 车牌颜色
        inTime:
          type: string
          title: 入场时间
        inChannel:
          type: string
          title: 入场通道名称
        visitReason:
          type: string
          title: 访问事由
        openGateMode:
          type: string
          title: 放行类型
          description: 自动抬杆/手动放行,无信息时填''
        matchMode:
          type: string
          description: 全字匹配、近似匹配、人工匹配、人工纠正,无信息时填''
          title: 匹配模式
        confidence:
          type: number
          title: 车牌识别可信度
        carType:
          type: string
          title: 车辆类型
        userInfo_idCard:
          type: string
          title: 证件号码
        userInfo_userName:
          type: string
          title: 车主姓名
        userInfo_phone:
          type: string
          title: 联系电话
        userInfo_address:
          type: string
          title: 地址
        placeInfo:
          type: array
          items:
            type: object
            properties: { }
          title: 车位信息
          description: 暂时用不到
        barriorOpen:
          type: string
          description: “开闸”“未开闸”
          title: 是否开闸
        inCostTime:
          type: number
          title: 入场开闸耗时
          description: 压地感到抬杆时间
        spaceCount:
          type: number
          title: 空位数
        imageList:
          type: array
          items:
            type: string
          title: 其他联动设备图片信息
          description: 暂时用不到
        imageName:
          type: string
          title: 图片名
          description: 暂时用不到
        modifyList:
          type: array
          items:
            type: object
            properties:
              oldOrderId:
                type: string
                title: 旧订单id
              newOrderId:
                type: string
                title: 新订单id
              oldPlate:
                type: string
                title: 旧车牌
              newPlate:
                type: string
                title: 新车牌
              modifyTime:
                type: string
                title: 修改时间
            required:
              - oldOrderId
              - newOrderId
              - oldPlate
              - newPlate
              - modifyTime
          title: 手动修改记录
        lcParkObj:
          type: string
          title: 场库
        lcParkId:
          type: string
        parkNumber:
          type: string
          title: 场库编号
        operatorId:
          type: string
          title: 操作员Id
        operatorName:
          type: string
          title: 操作员姓名
        invoiceNo:
          type: string
          title: 发票号码
        outTime:
          type: string
          title: 出场时间
        outImage:
          type: string
          title: 出场图片名
        outChannel:
          type: string
          title: 出口通道名称
        charge:
          type: number
          title: 总停车费
          description: 原价
        offLineCharge:
          type: number
          title: 线下总收费
        offLineProfitChargeNum:
          type: number
          title: 线下累计优惠金额总面值
        offLineProfitChargeValue:
          type: number
          title: 线下累计优惠金额总抵扣值
        offLineProfitTimeNum:
          type: number
          title: 线下累计优惠时间
        offLineProfitTimeValue:
          type: number
          title: 线下累计优惠时间总抵扣值
        onLineCharge:
          type: number
          title: 线上总收费
          description: 实付价
        onLineProfitChargeNum:
          type: number
          title: 线上累计优惠金额总面值
        onLineProfitChargeValue:
          type: number
          title: 线上累计优惠金额总抵扣值
        onLineProfitTimeNum:
          type: number
          title: 线上累计优惠时间
        onLineProfitTimeValue:
          type: number
          title: 线上累计优惠时间总抵扣值
        profitChargeTotal:
          type: number
          title: 线上线下金额和时间优惠累计抵扣值
          description: 抵扣金额
        outCostTime:
          type: number
          title: 出场开闸耗时
        profitList:
          type: array
          items:
            type: object
            properties: { }
          title: 优惠明细
        chargeList:
          type: array
          items:
            $ref: '#/components/schemas/TLcOrderCharge'
          title: 收费明细
      title: 停车场订单
      required:
        - orderId
        - _id
        - plate
        - ticketCode
        - plateColor
        - inTime
        - inChannel
        - visitReason
        - openGateMode
        - matchMode
        - carType
        - userInfo_idCard
        - userInfo_userName
        - userInfo_phone
        - userInfo_address
        - placeInfo
        - barriorOpen
        - inCostTime
        - spaceCount
        - imageList
        - imageName
        - modifyList
        - lcParkObj
        - parkNumber
        - operatorId
        - operatorName
        - invoiceNo
        - outTime
        - outImage
        - offLineCharge
        - outChannel
        - charge
        - offLineProfitTimeNum
        - offLineProfitChargeNum
        - offLineProfitChargeValue
        - onLineCharge
        - offLineProfitTimeValue
        - onLineProfitChargeNum
        - onLineProfitChargeValue
        - profitChargeTotal
        - onLineProfitTimeValue
        - onLineProfitTimeNum
        - outCostTime
        - profitList
        - chargeList
        - lcParkId
    TLcOrderCharge:
      type: object
      properties:
        payNo:
          type: string
          title: 支付订单号
        payCharge:
          type: string
          title: 支付金额
        payKind:
          type: string
          title: 支付类型
        payChannel:
          type: string
          title: 支付渠道
        transactionId:
          type: string
          description: 每笔交易生成唯一流水号（支付结果下发）
          title: 线上交易流水号
        getTime:
          type: string
          title: 结算时间
        memo:
          type: string
          title: 备注
        lcOrderId:
          type: string
        lcOrderObj: *ref_13
        orderId:
          type: string
          title: 订单号
        createdAt:
          type: string
      title: 停车订单支付
      required:
        - payNo
        - payCharge
        - payKind
        - payChannel
        - transactionId
        - getTime
        - memo
        - lcOrderObj
        - orderId
        - createdAt
        - lcOrderId
    TLcOrderProfit:
      type: object
      properties:
        profitCode:
          type: string
          title: 优惠码
        profitTime:
          type: string
          title: 优惠时间
        shopName:
          type: string
          title: 商户名称
        profitCharge:
          type: string
          title: 优惠金额面值
        profitChargeValue:
          type: string
          title: 生效金额
        getTime:
          type: string
          title: 优惠下发时间
        memo:
          type: string
          title: 备注
        lcOrderId:
          type: string
        lcOrderObj: *ref_13
        orderId:
          type: string
          title: 订单号
        createdAt:
          type: string
      title: 停车订单优惠
      required:
        - profitCode
        - profitTime
        - shopName
        - profitCharge
        - profitChargeValue
        - getTime
        - memo
        - orderId
        - createdAt
        - lcOrderId
        - lcOrderObj
    TUser:
      type: object
      properties:
        _id:
          type: string
        nickname:
          type: string
          title: 昵称
        realname:
          type: string
          title: 真名
        mobile:
          type: string
          title: 手机号
        avatar:
          type: string
          title: 头像
        wxOpenId:
          type: string
          title: WxOpenId
        lastLoginAt:
          type: string
          title: 最后登录时间
        lastLoginIp:
          type: string
          title: 最后登录Ip
        gender:
          type: string
          title: 性别
          description: User_gender
        favCount:
          type: integer
          title: 收藏个数
        businessType:
          type: string
          description: User_businessType
          title: 商家类型
      title: 用户信息
      required:
        - _id
        - nickname
        - realname
        - mobile
        - avatar
        - wxOpenId
        - lastLoginAt
        - lastLoginIp
        - gender
        - favCount
        - businessType
    TLcPark:
      type: object
      properties:
        _id:
          type: string
        parkNumber:
          type: string
          title: 场库编号
        parkName:
          type: string
          title: 场库名称
        spaceCount:
          type: boolean
          title: 场库总车位数
        freeSpaceCount:
          type: integer
          title: 场库空车位数
        bookSpaceCount:
          type: integer
          title: 场库可预约数
        bookInParkCount:
          type: integer
          title: 场库在场预约数
      title: 停车场
      required:
        - _id
        - parkNumber
        - parkName
        - spaceCount
        - freeSpaceCount
        - bookSpaceCount
        - bookInParkCount
    TAuction:
      type: object
      properties:
        _id:
          type: string
        title:
          type: string
          title: 标题
        targetId:
          type: string
          title: 拍卖物品id
        targetObj:
          type: object
          properties: { }
          title: 拍卖物品对象
        targetModel:
          type: string
          description: Auction_targetModel 商铺;摊位;广告位
          title: 拍卖物品类型
        depositCent:
          type: number
          title: 保证金
          description: 单位分
        bidStartCent:
          type: number
          title: 起拍价
          description: 单位分
        bidIncCent:
          type: number
          title: 竞价幅度
          description: 单位分
        startAt:
          type: string
          title: 开始时间
        freeCycleMins:
          type: string
          title: 自由竞价周期
          description: Auction_bidCycle分钟数字符串
        delayCycleMins:
          type: string
          description: Auction_delayCycle分钟数字符串
          title: 延时竞价周期
        endAt:
          type: string
          title: 结束时间
          description: 存在此值表示拍卖结束
        status:
          type: boolean
          title: 启用状态
          description: 是否启用
        result:
          type: string
          title: 拍卖结果
          description: Auction_result 正常结束;流拍;提前终止
        dealUserId:
          type: string
          title: 买受人id
        dealUserObj: *ref_0
        dealCent:
          type: number
          title: 成交价格
          description: '单位分 '
        viewCount:
          type: number
          title: 浏览次数
        createdAt:
          type: string
          title: 创建时间
        manualStopAt:
          type: string
          title: 手动终止时间
        manualStopReason:
          type: string
          title: 手动终止理由
        myDepositOrderObj: *ref_18
        lastBidCent:
          type: number
          title: 最后出价
          description: 单位分
        tailPaidAt:
          type: string
          title: 尾款支付时间
          description: 小程序仅买受人可见
        tailBreakDefaultAt:
          type: string
          title: 尾款违约默认时间
          description: 小程序仅买受人可见
        tailBreakAt:
          type: string
          title: 尾款违约时间
          description: 小程序仅买受人可见
        tailBreakRemark:
          type: string
          title: 尾款违约备注
          description: 小程序不可见
        deliveredAt:
          type: string
          title: 交割时间
          description: 小程序仅买受人可见
        deliveredRemark:
          type: string
          description: 小程序不可见
          title: 交割备注
        deliveredFiles:
          type: array
          items:
            type: string
          title: 交割附件
          description: 小程序不可见
        runStatusText:
          type: string
          title: 运行状态
          description: 小程序特定接口才有，待开拍/竞价中/已结束(买受人细分为：尾款违约/已交割/待交割/待付尾款)
        contentObj:
          type: object
          properties:
            bulletin:
              type: string
              title: 拍卖公告
            thingsToKnow:
              type: string
              title: 竞买须知
          required:
            - bulletin
            - thingsToKnow
      title: 拍卖
      required:
        - _id
        - title
        - targetObj
        - targetModel
        - targetId
        - depositCent
        - bidStartCent
        - bidIncCent
        - startAt
        - freeCycleMins
        - delayCycleMins
        - endAt
        - status
        - result
        - dealUserId
        - dealUserObj
        - dealCent
        - viewCount
        - createdAt
        - manualStopAt
        - manualStopReason
        - myDepositOrderObj
        - lastBidCent
        - runStatusText
        - tailPaidAt
        - tailBreakDefaultAt
        - tailBreakAt
        - tailBreakRemark
        - deliveredRemark
        - deliveredAt
        - deliveredFiles
        - contentObj
    TOrder:
      type: object
      properties:
        _id:
          type: string
        type:
          type: string
          title: 类型
        entityId:
          type: string
          title: 商品id
        entityModel:
          type: string
          title: 商品模型
        entityObj:
          type: object
          properties: { }
          title: 商品
        userId:
          type: string
          title: 用户Id
        userObj: *ref_0
        originalMoneyCent:
          type: number
          title: 原价金额
          description: 单位分
        couponDiscountCent:
          type: number
          title: 优惠券抵扣金额
          description: 单位分
        pointDiscountCent:
          type: number
          title: 积分抵扣金额
          description: 单位分
        orderMoneyCent:
          type: number
          title: 订单金额
          description: 单位分，应付金额
        paidMoneyCent:
          type: number
          title: 实付金额
          description: 单位分
        payStatus:
          type: string
          title: 支付状态
          description: Order_payStatus 未支付；已支付；已退款；部分退款；已关闭
        paidAt:
          type: string
          title: 付款时间
        closedAt:
          type: string
          title: 关闭时间
        closedReason:
          type: string
          title: 关闭原因
        remark:
          type: string
          title: 备注
        createdAt:
          type: string
          title: 创建时间
        refundList:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                title: 退款交易单号
              moneyCent:
                type: string
                title: 退款金额
              feeCent:
                type: string
                title: 退款手续费
              trxid:
                type: string
                title: 通联退款流水号
              operatedAt:
                type: string
                title: 退款操作时间
              refundedAt:
                type: string
                title: 退款成功时间
              reason:
                type: string
                title: 退款原因
            required:
              - id
              - moneyCent
              - feeCent
              - trxid
              - operatedAt
              - refundedAt
              - reason
          title: 退款列表
      title: 通用订单
      required:
        - _id
        - entityId
        - entityObj
        - userId
        - userObj
        - orderMoneyCent
        - paidMoneyCent
        - payStatus
        - paidAt
        - closedAt
        - closedReason
        - remark
        - createdAt
        - refundList
        - originalMoneyCent
        - pointDiscountCent
        - couponDiscountCent
        - entityModel
        - type
    TAuctionBid:
      type: object
      properties:
        _id:
          type: string
        shortId:
          type: string
          title: 竞买号
        auctionId:
          type: string
        auctionObj: *ref_17
        userId:
          type: string
        userObj: *ref_0
        bidCent:
          type: number
          title: ' 出价金额'
          description: 单位分
        createdAt:
          type: string
      title: 拍卖出价
      required:
        - _id
        - auctionId
        - auctionObj
        - userId
        - userObj
        - bidCent
        - createdAt
        - shortId
    TAdminUser:
      type: object
      properties:
        _id:
          type: string
        username:
          type: string
          title: 用户名
        nickname:
          type: string
          title: 昵称
      title: 后台用户
      required:
        - _id
        - username
        - nickname
    TMerchantUser:
      type: object
      properties:
        _id:
          type: string
        nickname:
          type: string
          title: 昵称
        realname:
          type: string
          title: 真名
        mobile:
          type: string
          title: 手机号
        avatar:
          type: string
          title: 头像
        wxOpenId:
          type: string
          title: WxOpenId
        lastLoginAt:
          type: string
          title: 最后登录时间
        lastLoginIp:
          type: string
          title: 最后登录Ip
        gender:
          type: string
          title: 性别
          description: User_gender
        roles:
          type: array
          items:
            type: string
          title: 角色
          description: Merchant_role
      title: 商家用户信息
      required:
        - _id
        - nickname
        - realname
        - mobile
        - avatar
        - wxOpenId
        - lastLoginAt
        - lastLoginIp
        - gender
    TMerchantUserRealInfo:
      type: object
      properties:
        _id:
          type: string
        realname:
          type: string
          title: 姓名
        idCardNum:
          type: string
          title: 身份证号
        idCardUrl1:
          type: string
          title: 人像面
        idCardUrl2:
          type: string
          title: 国徽面
        merchantUserId:
          type: string
          title: 用户Id
      title: 商家实名认证信息
      required:
        - _id
        - realname
        - idCardNum
        - idCardUrl1
        - idCardUrl2
        - merchantUserId
    TUserRealInfo:
      type: object
      properties:
        _id:
          type: string
        realname:
          type: string
          title: 姓名
        idCardNum:
          type: string
          title: 身份证号
        idCardUrl1:
          type: string
          title: 人像面
        idCardUrl2:
          type: string
          title: 国徽面
        userId:
          type: string
          title: 用户Id
      title: 用户实名认证信息
      required:
        - _id
        - realname
        - idCardNum
        - idCardUrl1
        - idCardUrl2
        - userId
    TCoupon:
      type: object
      properties:
        _id:
          type: string
        title:
          type: string
          title: 标题
        couponCode:
          type: string
          title: 优惠券编码
        useSceneType:
          type: string
          description: Coupon_useSceneType
          title: 使用场景
        type:
          type: string
          description: Coupon_type “0”小时券,”1”金额券 "2"折扣券
          title: 类型
        parValue:
          type: number
          title: 面值
          description: 小时券,单位分钟，金额券,单位分 折扣券 0表示免费10表示1折85表示85折90表示9折
        endAt:
          type: string
          title: 截至时间
        couponTplId:
          type: string
          title: 优惠券模板id
        couponTplObj: *ref_23
        userId:
          type: string
          title: 用户id
        userObj: *ref_0
        orderId:
          type: string
          title: 订单id
        orderObj: *ref_13
        plate:
          type: string
          title: 车牌号
        usedAt:
          type: string
          title: 使用时间
          description: 存在此项表示已使用
        sourceType:
          type: string
          description: Coupon_sourceType
          title: 来源类型
        sourceId:
          type: string
          title: 来源ID
        sourceModel:
          type: string
          title: 来源模型
          description: Coupon_sourceModel
      title: 优惠券
      required:
        - _id
        - couponCode
        - type
        - parValue
        - endAt
        - couponTplObj
        - userObj
        - orderObj
        - orderId
        - plate
        - usedAt
        - couponTplId
        - userId
        - useSceneType
        - sourceType
        - sourceId
        - sourceModel
        - title
    TCouponTpl:
      type: object
      properties:
        _id:
          type: string
        useSceneType:
          type: string
          title: 使用场景
          description: Coupon_useSceneType
        type:
          type: string
          title: 类型
          description: Coupon_type, “0”小时券,”1”金额券 "2"折扣券
        parValue:
          type: integer
          title: 面值
          description: 小时券,单位分钟，金额券,单位分 折扣券 0表示免费10表示1折85表示85折90表示9折
        endAtType:
          type: string
          title: 过期类型
          description: Coupon_endAtType
        relativeEndAt:
          type: integer
          title: 相对过期
          description: 单位分钟
        absoluteEndAt:
          type: string
          title: 绝对过期
        publishCount:
          type: integer
          title: 总发布数量
        receivedCount:
          type: integer
          title: 已领取数量
        usedCount:
          type: integer
          title: 已使用数量
        status:
          type: boolean
          title: 启用状态
        title:
          type: string
          title: 标题
        receiveTitle:
          type: string
          title: 领取标题
        pusherIds:
          type: array
          items:
            type: string
          title: 发券者
      title: 优惠券模板
      required:
        - _id
        - type
        - parValue
        - endAtType
        - relativeEndAt
        - absoluteEndAt
        - publishCount
        - receivedCount
        - usedCount
        - status
        - title
        - useSceneType
        - receiveTitle
        - pusherIds
    TCouponBlank:
      type: object
      properties:
        _id:
          type: string
        couponTplId:
          type: string
          title: 模板ID
        couponTplObj: *ref_19
        merchantUserId:
          type: string
          title: 商家用户
        adminUserId:
          type: string
          title: 后台用户
        count:
          type: string
          title: 生成个数
        couponCodes:
          type: string
          title: 生成codes
        downloadUrl:
          type: string
          title: 下载链接
        createdAt:
          type: string
      required:
        - _id
        - couponTplId
        - merchantUserId
        - adminUserId
        - count
        - couponCodes
        - downloadUrl
        - createdAt
        - couponTplObj
      title: 空白优惠券记录
    TTricycle:
      type: object
      properties:
        _id:
          type: string
        numCode:
          type: string
          title: 车辆编号
        images:
          type: array
          items:
            type: string
          title: 图片
        merchantUserId:
          type: string
          title: 商家ID
        merchantUserObj: *ref_1
        depositBalanceCent:
          type: integer
          title: 保证金余额
          description: 单位分
        depositRefundedAt:
          type: string
          title: 保证金退款时间
        checkStatus:
          type: string
          description: Tricycle_checkStatus
          title: 审核状态
        checkList:
          type: array
          items:
            type: object
            properties:
              _id:
                type: string
                title: ID
              images:
                type: array
                items:
                  type: string
                title: 提交时的图片
              appliedAt:
                type: string
                title: 申请时间
              checkedAt:
                type: string
                title: 审核时间
              checkedBy:
                type: string
                title: 审核人
              checkStatus:
                type: string
                title: 审核状态
              checkRemark:
                type: string
                title: 审核备注
              tricycleId:
                type: string
                title: 三轮车ID
            required:
              - _id
              - images
              - appliedAt
              - checkedAt
              - checkedBy
              - checkStatus
              - checkRemark
              - tricycleId
        runningIllegalObj:
          type: string
          title: 当前未结案的违规记录
      title: 三轮车
      required:
        - _id
        - numCode
        - images
        - merchantUserId
        - depositBalanceCent
        - checkStatus
        - merchantUserObj
        - checkList
        - runningIllegalObj
        - depositRefundedAt
    TTricycleCheck:
      type: object
      properties:
        _id:
          type: string
          title: ID
        images:
          type: array
          items:
            type: string
          title: 提交时的图片
        appliedAt:
          type: string
          title: 申请时间
        checkedAt:
          type: string
          title: 审核时间
        checkedBy:
          type: string
          title: 审核人
        checkStatus:
          type: string
          title: 审核状态
        checkRemark:
          type: string
          title: 审核备注
        tricycleId:
          type: string
          title: 三轮车ID
      title: 三轮车审核记录
      required:
        - _id
        - appliedAt
        - checkedAt
        - checkedBy
        - checkStatus
        - checkRemark
        - tricycleId
        - images
    TTricycleIllegalRule:
      type: object
      properties:
        _id:
          type: string
        name:
          type: string
          title: 规则名称
        punishDelayMinutes:
          type: integer
          title: 延时处罚分钟数
        fineCent:
          type: integer
          title: 罚款金额
          description: 单位分
        needTow:
          type: boolean
          title: 是否需要拖走
          description: 拖走的多一个出库步骤
        status:
          type: boolean
          title: 是否启用
      required:
        - _id
        - name
        - punishDelayMinutes
        - fineCent
        - status
        - needTow
      title: 三轮车违规规则
    TTricycleIllegal:
      type: object
      properties:
        _id:
          type: string
        images:
          type: string
          title: 违规照片
          description: 违规时拍的照片
        tricycleId:
          type: string
          title: 三轮车ID
        tricycleObj: *ref_5
        tricycleNumCode:
          type: string
          title: 三轮车编号
        tricycleMasterId:
          type: string
          title: 三轮车车主ID
        tricycleMasterObj: *ref_1
        handleStatus:
          type: string
          title: 处理状态
          description: TricycleIllegal_HandleStatus
        createdById:
          type: string
          title: 创建者ID
        createdByObj: *ref_1
        createdAt:
          type: string
          title: 创建事件
        ruleId:
          type: string
          title: 规则ID
        ruleName:
          type: string
          title: 规则名称
        fineCent:
          type: integer
          title: 罚款金额
          description: 单位分
        punishDelayMinutes:
          type: integer
          title: 延时处罚的分钟数
        canCancelEndAt:
          type: string
          title: 可被取消的最后时间
        needTow:
          type: boolean
          title: 是否需要扣留
        canceledAt:
          type: string
          title: 取消时间
        canceledById:
          type: string
          title: 取消者ID
        canceledByObj: *ref_1
        punishedAt:
          type: string
          title: 处罚时间
          description: 执行拖走操作的人
        punishedById:
          type: string
          title: 处罚者ID
        punishedByObj: *ref_1
        passedAt:
          type: string
          title: 取回放行时间
        passedById:
          type: string
          title: 取回放行者ID
        passedByObj: *ref_1
        finePaidAt:
          type: string
          title: 罚金支付时间
        fineOrderId:
          type: string
          title: 罚金订单ID
        fineOrderObj: *ref_18
        workflowId:
          type: string
          title: 工作流ID
        workflowObj: *ref_14
        isEnd:
          type: boolean
          title: 是否结案
          description: 结案=被取消或支付完罚金
        endAt:
          type: string
          title: 结案时间
      title: 三轮车违规记录
      required:
        - _id
        - tricycleId
        - tricycleObj
        - tricycleNumCode
        - tricycleMasterId
        - tricycleMasterObj
        - handleStatus
        - createdById
        - createdAt
        - createdByObj
        - ruleId
        - ruleName
        - fineCent
        - punishDelayMinutes
        - canCancelEndAt
        - canceledAt
        - canceledById
        - canceledByObj
        - punishedAt
        - punishedById
        - punishedByObj
        - finePaidAt
        - workflowId
        - workflowObj
        - isEnd
        - endAt
        - images
        - fineOrderId
        - fineOrderObj
        - needTow
        - passedAt
        - passedByObj
        - passedById
    TWorkflow:
      type: object
      properties:
        _id:
          type: string
        type:
          type: string
          title: 工作流类型
          description: Workflow_type
        entityObj:
          type: object
          properties: { }
          title: 实体对象
        entityId:
          type: string
          title: 实体ID
        entityModel:
          type: string
          title: 实体模型
        todoStep:
          type: string
          title: 待办步骤
        todoAt:
          type: string
          title: 当前步骤待办开始时间
        todoStepData:
          type: object
          properties: { }
          title: 当前步骤附加数据
        todoUserModelIds:
          type: array
          items:
            type: string
          title: 待办者列表
        todoCheckId:
          type: string
          title: 待办审批ID
        todoCheckModel:
          type: string
          title: 待办审批模型
        todoCheckObj:
          type: object
          properties: { }
          title: 待办审批对象
        doneUserModelIds:
          type: array
          items:
            type: string
          title: 已办者列表
        doneStepList:
          type: array
          items:
            type: object
            properties:
              step:
                type: string
                title: 步骤名称
              resultText:
                type: array
                items:
                  type: string
                title: 完成结果
              resultData:
                type: object
                properties: { }
                title: 完成结果数据
              doneAt:
                type: string
                title: 完成时间
              doneUserModelIds:
                type: string
                title: 完成用户
              doneCheckId:
                type: string
                title: 完成审批ID
              doneCheckModel:
                type: string
                title: 完成审批模型
              doneHandlerModelIds:
                type: array
                items:
                  type: string
                title: 完成执行人
            required:
              - step
              - resultText
              - doneAt
              - doneUserModelIds
              - doneCheckId
              - doneCheckModel
              - doneHandlerModelIds
              - resultData
        isEnd:
          type: boolean
          title: 是否结束
        updatedAt:
          type: string
        createdAt:
          type: string
      required:
        - _id
        - type
        - todoUserModelIds
        - doneUserModelIds
        - entityId
        - entityModel
        - isEnd
        - createdAt
        - updatedAt
        - todoCheckId
        - todoCheckModel
        - entityObj
        - todoStep
        - todoAt
        - doneStepList
        - todoStepData
        - todoCheckObj
      title: 工作流
    TShop:
      type: object
      properties:
        _id:
          type: string
          title: ID
        cellNo:
          type: string
          title: 商铺编码
        title:
          type: string
          title: 标题
        cover:
          type: string
          title: 封面
        videos:
          type: array
          items:
            type: string
          title: 视频
        images:
          type: array
          items:
            type: string
          title: 图片
        houseType:
          type: array
          items:
            type: string
          title: 户型
        tags:
          type: array
          items:
            type: string
          title: 标签
        payment:
          type: string
          title: 付款方式
        orientation:
          type: string
          title: 朝向
        floor:
          type: integer
          title: 楼层
        decoration:
          type: string
          title: 装修
        cellId:
          type: integer
          title: 单元ID
        cellName:
          type: string
          title: 单元名称
        cellProperty:
          type: string
          title: 房产性质
        cellArea:
          type: number
          title: 套内面积
        buildingArea:
          type: number
          title: 建筑面积
        cellStatus:
          type: string
          title: 单元状态
        contractStartDate:
          type: string
          title: 合同开始日期
        contractEndDate:
          type: string
          title: 合同结束日期
        contractNo:
          type: string
          title: 合同编号
        businessType:
          type: array
          items:
            type: string
          title: 经营类型
        cellRemark:
          type: string
          title: 单元备注
        attachmentNames:
          type: string
          title: 附件名称
        attachmentPaths:
          type: string
          title: 附件路径
        costName:
          type: string
          title: 费用名称
        unitPrice:
          type: string
          title: 单价
        billingCycle:
          type: string
          title: 付款方式
        chargeAmount:
          type: string
          title: 费用金额
        tenantId:
          type: string
          title: 租户Id
        tenantName:
          type: string
          title: 租户名称
        tenantPhone:
          type: string
          title: 租户电话
        contactPerson:
          type: string
          title: 联系人
        contactPhone:
          type: string
          title: 联系人电话
        tenantType:
          type: string
          title: 租户类型
          description: 1-个人；2-公司
        tenantIdCard:
          type: string
          title: 租户身份证号
        tenantAddress:
          type: string
          title: 租户地址
        businessLicense:
          type: string
          title: 营业执照号
        tenantRemark:
          type: string
          title: 租户备注
        onShelf:
          type: boolean
          title: 是否上架
        auctionObj: *ref_17
        region:
          type: array
          items:
            type: string
          title: 区域
        onShelfAt:
          type: string
          title: 上架时间
        viewCount:
          type: integer
          title: 浏览量
        favouriteCount:
          type: string
          title: 收藏量
        brokerCalledCount:
          type: string
          title: 通话量
        recommend:
          type: number
          title: 推荐值
          description: 大于零表示推荐
        overallRank:
          type: integer
          title: 总排名
        inAreaRank:
          type: integer
          title: 同馆排名
      required:
        - cellId
        - cellNo
        - cellName
        - cellProperty
        - cellArea
        - buildingArea
        - cellStatus
        - contractStartDate
        - contractEndDate
        - contractNo
        - businessType
        - videos
        - images
        - houseType
        - tags
        - title
        - payment
        - orientation
        - floor
        - decoration
        - _id
        - cover
        - onShelf
        - region
        - onShelfAt
        - favouriteCount
        - brokerCalledCount
        - recommend
        - overallRank
        - inAreaRank
        - auctionObj
      title: 商铺
    TShopSyncConflict:
      type: object
      properties:
        _id:
          type: string
        cellNo:
          type: string
          title: 编号
        conflictField:
          type: string
          title: 冲突字段
        remoteValue:
          type: string
        localValue:
          type: string
        rightValue:
          type: string
        nowValue:
          type: string
          title: 当前商铺表值
        handled:
          type: boolean
          title: 是否已处理
        handledAt:
          type: string
          title: 处理时间
      required:
        - _id
        - cellNo
        - conflictField
        - remoteValue
        - localValue
        - rightValue
        - handled
        - handledAt
        - nowValue
      title: 商铺同步冲突
    TDict:
      type: object
      properties:
        _id:
          type: string
        key:
          type: string
          title: 键名
        name:
          type: string
          title: 名称
        description:
          type: string
          title: 描述
        options:
          type: array
          items:
            type: object
            properties:
              label:
                type: string
              value:
                type: string
              status:
                type: boolean
            required:
              - label
              - value
        status:
          type: boolean
          description: 状态
      title: 字典
      required:
        - _id
        - name
        - options
        - status
        - description
        - key
    TArticle:
      type: object
      properties:
        _id:
          type: string
        title:
          type: string
          title: 标题
        cover:
          type: string
          title: 封面
        intro:
          type: string
          title: 简介
        webViewUrl:
          type: string
          title: 跳转链接
          description: 此项不为空，则文章详情页将跳转到该链接
        content:
          type: string
          title: 内容
        categoryId:
          type: array
          items:
            type: string
          title: 分类
        tags:
          type: array
          items:
            type: string
          title: 标签
        viewCount:
          type: integer
          title: 查看数
        favouriteCount:
          type: string
          title: 收藏数
        createdById:
          type: string
          title: 创建人
        createdByObj: *ref_24
        createdAt:
          type: string
          title: 创建时间
        isRecommend:
          type: boolean
          title: 是否热点推荐
      title: 文章
      required:
        - _id
        - title
        - cover
        - intro
        - content
        - tags
        - createdAt
        - createdById
        - categoryId
        - viewCount
        - favouriteCount
        - createdByObj
        - isRecommend
        - webViewUrl
    TSimpleUserInfo:
      type: object
      properties:
        _id:
          type: string
        nickname:
          type: string
          title: 昵称
        avatar:
          type: string
          title: 头像
      title: 简单用户信息
      required:
        - _id
        - nickname
        - avatar
    TBanner:
      type: object
      properties:
        _id:
          type: string
        type:
          type: string
          title: 类型
          description: 1-图片；2-视频
        place:
          type: string
          title: 位置
          description: 1-首页顶部
        url:
          type: string
          title: URL
          description: 资源地址
        linkType:
          type: string
          title: 跳转方式
          description: 1-小程序；2-H5
        linkUrl:
          type: string
          title: 跳转地址
        startAt:
          type: string
          title: 开始时间
        endAt:
          type: string
          title: 结束时间
        status:
          type: boolean
          title: 是否启用
        createdAt:
          type: string
          title: 创建时间
        updatedAt:
          type: string
          title: 修改时间
        deletedAt:
          type: string
          title: 软删除时间
      title: 横幅
      required:
        - _id
        - type
        - place
        - url
        - linkType
        - linkUrl
        - startAt
        - endAt
        - status
        - createdAt
        - updatedAt
        - deletedAt
    TMedia:
      type: object
      properties:
        _id:
          type: string
        name:
          type: string
          title: 名称
        alt:
          type: string
          title: 占位文字
        desc:
          type: string
          title: 描述
        url:
          type: string
          title: 资源地址
        type:
          type: string
          title: 类型
          description: video,image
        size:
          type: string
          title: byte大小
        width:
          type: string
          title: 宽度
        height:
          type: string
          title: 高度
        duration:
          type: string
          title: 视频时长
        createdAt:
          type: string
          title: 创建时间
      title: 媒体
      required:
        - _id
        - name
        - alt
        - desc
        - url
        - type
        - size
        - width
        - height
        - duration
        - createdAt
    TBroker:
      type: object
      properties:
        _id:
          type: string
        name:
          type: string
          title: 姓名
        avatar:
          type: string
          title: 头像
        mobile:
          type: string
          title: 手机号
        jobTitle:
          type: string
          title: 头衔
        jobPosition:
          type: string
          title: 职位
        pastVolumeCount:
          type: integer
          title: 历史成交量
        winPercent:
          type: string
          title: 战胜比例
          description: 每天跑个任务，排序之后更新，
      title: 经纪人
      required:
        - _id
        - name
        - jobTitle
        - avatar
        - mobile
        - winPercent
        - jobPosition
        - pastVolumeCount
    BlueCardResp:
      type: object
      properties:
        status:
          type: string
        errorCode:
          type: string
          title: 失败原因
        data:
          type: object
          properties:
            timeStamp:
              type: string
              title: 时间戳
          title: 数据
      required:
        - status
      title: 蓝卡响应
    TFeedback:
      type: object
      properties:
        _id:
          type: string
        type:
          type: string
          description: Feedback_type
          title: 类型
        content:
          type: string
          title: 内容
        images:
          type: array
          items:
            type: string
          title: 图片
        mobile:
          type: string
          title: 用户输入手机号
        userId:
          type: string
          title: 用户id
        userObj:
          anyOf:
            - type: string
              title: 用户id
            - *ref_0
          title: 用户
          description: 用户id或用户信息
        remark:
          type: string
          title: 备注
        handledAt:
          type: string
          title: 处理时间
        createdAt:
          type: string
          title: 创建时间
      title: 投诉建议
      required:
        - _id
        - content
        - type
        - images
        - userObj
        - createdAt
        - remark
        - handledAt
        - userId
        - mobile
    TSystemVar:
      type: object
      properties:
        serviceTel:
          type: string
          title: 服务电话
        auctionTailCycleDays:
          type: number
          title: 拍卖尾款支付周期
          description: 单位天
        homeNavIcons:
          type: array
          items:
            type: object
            properties:
              icon:
                type: string
              label:
                type: string
              url:
                type: string
              appid:
                type: string
            required:
              - icon
              - label
              - url
              - appid
              - 01J8RVBQMJK93KB3WGP1N6HA8Q
          title: 首页图标导航
        artworkServiceTel:
          type: string
          title: 艺术展品服务电话
      title: 用户小程序系统变量
      required:
        - serviceTel
        - auctionTailCycleDays
        - homeNavIcons
        - artworkServiceTel
    TWxPayParams:
      type: object
      properties:
        paid:
          type: boolean
          title: 是否已支付
          description: 一般是0元订单，自动支付，不再调用微信支付
        appid:
          description: 微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致
          type: string
          title: AppId
        noncestr:
          type: string
          title: 随机字符串
        package:
          type: string
          title: 固定值
        partnerid:
          type: string
          title: 微信支付商户号
        prepayid:
          type: string
          title: 统一下单订单号
        timestamp:
          description: 单位：秒
          type: integer
          title: 时间戳
        sign:
          description: 这里用的 MD5/RSA 签名
          type: string
          title: 签名
      required:
        - appid
        - noncestr
        - package
        - partnerid
        - prepayid
        - timestamp
        - sign
      title: 微信支付参数
    TArtwork:
      type: object
      properties:
        _id:
          type: string
        title:
          type: string
          title: 标题
        artist:
          type: string
          title: 艺术家
        images:
          type: array
          items:
            type: object
            properties:
              url:
                type: string
                title: 地址
              width:
                type: number
                title: 宽
              height:
                type: number
                title: 高
            required:
              - url
              - width
              - height
          title: 图片
        categoryId:
          type: array
          items:
            type: string
          title: 分类
          description: 字典 artworkCategory
        content:
          type: string
          title: 详细介绍
        viewCount:
          type: string
          title: 浏览量
        status:
          type: boolean
          title: 是否发布
        createdAt:
          type: string
          title: 创建时间
        size:
          type: string
          title: 尺寸
      title: 艺术品
      required:
        - _id
        - images
        - title
        - artist
        - categoryId
        - content
        - viewCount
        - status
        - createdAt
        - size
  securitySchemes:
    bearer:
      type: http
      scheme: bearer
servers: [ ]
