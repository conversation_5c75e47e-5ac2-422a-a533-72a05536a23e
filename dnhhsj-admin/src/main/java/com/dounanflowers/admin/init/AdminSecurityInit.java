package com.dounanflowers.admin.init;

import com.dounanflowers.security.utils.SecurityHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AdminSecurityInit implements ApplicationListener<ApplicationStartedEvent> {

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        log.info("init admin security config");
        SecurityHolder.createContext("admin");
        SecurityHolder.createContext("shop");
    }

}
