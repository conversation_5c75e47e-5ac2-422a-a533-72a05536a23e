package com.dounanflowers.admin.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ParkOrderChargeBo {

    @Schema(title = "支付订单号")
    private String payNo;

    @Schema(title = "结算时间")
    private String getTime;

    @Schema(title = "支付金额")
    private String payCharge;

    @Schema(title = "支付类型")
    private String payKind;

    @Schema(title = "支付渠道")
    private String payChannel;

    @Schema(title = "备注")
    private String memo;

    @Schema(title = "线上交易流水号", description = "每笔交易生成唯一流水号（支付结果下发）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String transactionId;

}

