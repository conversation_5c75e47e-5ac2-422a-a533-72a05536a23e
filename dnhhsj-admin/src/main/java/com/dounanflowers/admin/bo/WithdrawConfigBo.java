package com.dounanflowers.admin.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "提现配置BO")
public class WithdrawConfigBo {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "账户姓名")
    private String name;

    @Schema(description = "开户行信息")
    private String bank;

    @Schema(description = "支行信息")
    private String branch;

    @Schema(description = "账号")
    private String number;
}
