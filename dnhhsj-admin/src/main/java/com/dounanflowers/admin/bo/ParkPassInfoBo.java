package com.dounanflowers.admin.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "通行信息")
public class ParkPassInfoBo {

    @Schema(title = "入场时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String inTime;

    @Schema(title = "出场时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String outTime;

    @Schema(title = "入场图片名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String inImage;

    @Schema(title = "出场图片名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String outImage;

    @Schema(title = "入口通道名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String inChannel;

    @Schema(title = "出口通道名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String outChannel;

    @Schema(title = "抬杆模式", description = "自动抬杆/手动放行", requiredMode = Schema.RequiredMode.REQUIRED)
    private String openGateMode;

    @Schema(title = "匹配模式", description = "全字匹配、近似匹配、人工匹配、人工纠正", requiredMode = Schema.RequiredMode.REQUIRED)
    private String matchMode;

}

