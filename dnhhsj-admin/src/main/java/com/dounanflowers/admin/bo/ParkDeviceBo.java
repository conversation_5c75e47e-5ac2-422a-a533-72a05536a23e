package com.dounanflowers.admin.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ParkDeviceBo {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private String lastTime;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer deviceTypeId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private String ip;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer passageId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer state;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer deviceId;

}

