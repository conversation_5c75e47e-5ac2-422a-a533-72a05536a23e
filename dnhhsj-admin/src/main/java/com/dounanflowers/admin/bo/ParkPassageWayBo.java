package com.dounanflowers.admin.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ParkPassageWayBo {

    @Schema(title = "通道Id")
    private String passagewayId;

    @Schema(title = "通道名称")
    private String passagewayName;

    @Schema(title = "目标区域Id")
    private String targetAreaId;

    @Schema(title = "源区域Id")
    private String sourceAreaId;

    @Schema(title = "设备号")
    private String deviceId;

    @Schema(title = "匹配级别id")
    private String matchClassId;

    @Schema(title = "开闸类型")
    private String openBarriorTypeId;


}

