package com.dounanflowers.admin.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ParkOrderProfitBo {

    @Schema(title = "优惠码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String profitCode;

    @Schema(title = "优惠下发时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String getTime;

    @Schema(title = "优惠时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String profitTime;

    @Schema(title = "优惠金额面值", requiredMode = Schema.RequiredMode.REQUIRED)
    private String profitCharge;

    @Schema(title = "生效金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private String profitChargeValue;

    @Schema(title = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    private String memo;

    @Schema(title = "商户名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String shopName;

}

