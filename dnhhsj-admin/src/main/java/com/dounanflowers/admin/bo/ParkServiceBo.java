package com.dounanflowers.admin.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "园区服务")
public class ParkServiceBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "价格")
    private Integer price;

    @Schema(description = "价格")
    private Integer priceMax;

    @Schema(description = "可预约天数")
    private Integer days;

    @Schema(description = "服务时长（分钟）")
    private Integer minutes;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "价格类型")
    private String priceType;

    @Schema(description = "图片")
    private List<String> images;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "待办数量")
    private int todoCount;

    private List<ParkServiceTimesBo> times;

    @Schema(description = "时间段")
    private List<ParkServiceTimeSlotBo> timeSlots;

}
