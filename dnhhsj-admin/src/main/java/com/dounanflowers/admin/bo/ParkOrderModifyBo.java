package com.dounanflowers.admin.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "修改车牌信息", description = "车牌修改信息，默认无此项")
public class ParkOrderModifyBo {

    @JsonProperty("new_id")
    @Schema(title = "新的订单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String newId;

    @JsonProperty("old_id")
    @Schema(title = "旧的订单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String oldId;

    @JsonProperty("new_plate")
    @Schema(title = "新的车牌", requiredMode = Schema.RequiredMode.REQUIRED)
    private String newPlate;

    @JsonProperty("old_plate")
    @Schema(title = "旧的车牌", requiredMode = Schema.RequiredMode.REQUIRED)
    private String oldPlate;

}

