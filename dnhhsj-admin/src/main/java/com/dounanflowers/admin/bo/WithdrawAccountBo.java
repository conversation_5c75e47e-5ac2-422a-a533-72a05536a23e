package com.dounanflowers.admin.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import com.dounanflowers.common.bo.AdminUserBo;

@Data
@Schema(description = "提现账户BO")
public class WithdrawAccountBo {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户信息")
    private AdminUserBo userObj;

    @Schema(description = "可用余额（分）")
    private Integer availableAmount;

    @Schema(description = "冻结余额（分）")
    private Integer frozenAmount;

    @Schema(description = "总余额（分）")
    private Integer totalAmount;

    @Schema(description = "累计提现金额（分）")
    private Integer totalWithdrawAmount;

    @Schema(description = "累计收入金额（分）")
    private Integer totalIncomeAmount;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "备注")
    private String remark;
}
