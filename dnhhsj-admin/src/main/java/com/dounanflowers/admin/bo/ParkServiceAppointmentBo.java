package com.dounanflowers.admin.bo;

import com.dounanflowers.common.bo.AdminUserBo;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "园区服务预约")
public class ParkServiceAppointmentBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(description = "服务ID")
    private Long serviceId;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "服务信息")
    private ParkServiceBo serviceObj;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户信息")
    private AdminUserBo userObj;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "审核人ID")
    private Long checkUserId;

    @Schema(description = "审核人信息")
    private AdminUserBo checkUserObj;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

}
