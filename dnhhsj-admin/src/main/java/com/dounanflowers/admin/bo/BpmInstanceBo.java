package com.dounanflowers.admin.bo;

import com.dounanflowers.bpm.enums.StatusEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
public class BpmInstanceBo {

    @JsonProperty("_id")
    private Long id;

    private Long processId;

    private BpmProcessBo process;

    private Long userId;

    private Long submitUserId;

    private StatusEnum status;

    private Long batchId;

    private Long currentNodeId;

    private Object data;

    private Long outerId;

    private String outerType;

    private LocalDateTime endAt;

    private List<BpmNodeInstanceBo> nodes;

    private LocalDateTime createdAt;

}
