package com.dounanflowers.admin.bo;

import java.time.LocalDateTime;

import com.dounanflowers.common.bo.AdminUserBo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "提现账户记录BO")
public class WithdrawAccountRecordBo {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户信息")
    private AdminUserBo userObj;

    @Schema(description = "关联记录ID")
    private Long relatedId;

    @Schema(description = "关联记录")
    private Object relatedObj;

    @Schema(description = "记录类型")
    private String type;

    @Schema(description = "资金方向")
    private String direction;

    @Schema(description = "金额（分）")
    private Integer amount;

    private Integer fee;

    @Schema(description = "变动前余额（分）")
    private Integer beforeBalance;

    @Schema(description = "变动后余额（分）")
    private Integer afterBalance;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
}
