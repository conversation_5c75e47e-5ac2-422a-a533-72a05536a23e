package com.dounanflowers.admin.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ParkCarInfoBo {

    @Schema(title = "车牌")
    private String plate;

    @Schema(title = "无牌车票号")
    private String ticketCode;

    @Schema(title = "车牌颜色")
    private String plateColor;

    @Schema(title = "车类型")
    private String carType;

    @Schema(title = "车牌识别可信度")
    private String confidence;

}

