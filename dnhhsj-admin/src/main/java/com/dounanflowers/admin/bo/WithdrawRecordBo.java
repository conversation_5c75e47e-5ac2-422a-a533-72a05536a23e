package com.dounanflowers.admin.bo;

import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.enums.WithdrawStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "提现记录BO")
public class WithdrawRecordBo {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户信息")
    private AdminUserBo userObj;

    @Schema(description = "提现金额（分）")
    private Integer amount;

    private Integer fee;

    @Schema(description = "账户姓名")
    private String name;

    @Schema(description = "开户行信息")
    private String bank;

    @Schema(description = "支行信息")
    private String branch;

    @Schema(description = "账号")
    private String number;

    @Schema(description = "状态")
    private WithdrawStatusEnum status;

    @Schema(description = "审核时间")
    private LocalDateTime checkedAt;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "审核人ID")
    private Long checkAdminUserId;

    @Schema(description = "审核人信息")
    private AdminUserBo checkAdminUserObj;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "附件")
    private List<String> files;
    
}
