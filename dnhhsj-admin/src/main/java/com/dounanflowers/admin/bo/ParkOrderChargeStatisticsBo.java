package com.dounanflowers.admin.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "收费统计信息")
public class ParkOrderChargeStatisticsBo {

    @Schema(title = "总停车费", requiredMode = Schema.RequiredMode.REQUIRED)
    private String charge;

    @Schema(title = "线上总收费", requiredMode = Schema.RequiredMode.REQUIRED)
    private String onLineCharge;

    @Schema(title = "线下总收费", requiredMode = Schema.RequiredMode.REQUIRED)
    private String offLineCharge;

    @Schema(title = "线上线下金额和时间优惠累计抵扣值", requiredMode = Schema.RequiredMode.REQUIRED)
    private String profitChargeTotal;

    @Schema(title = "线上累计优惠金额总面值", requiredMode = Schema.RequiredMode.REQUIRED)
    private String onLineProfitChargeNum;

    @Schema(title = "线上累计优惠金额总抵扣值", requiredMode = Schema.RequiredMode.REQUIRED)
    private String onLineProfitChargeValue;

    @Schema(title = "线下累计优惠金额总面值", requiredMode = Schema.RequiredMode.REQUIRED)
    private String offLineProfitChargeNum;

    @Schema(title = "线下累计优惠金额总抵扣值", requiredMode = Schema.RequiredMode.REQUIRED)
    private String offLineProfitChargeValue;

    @Schema(title = "线上累计优惠时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String onLineProfitTimeNum;

    @Schema(title = "线上累计优惠时间总抵扣值", requiredMode = Schema.RequiredMode.REQUIRED)
    private String onLineProfitTimeValue;

    @Schema(title = "线下累计优惠时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String offLineProfitTimeNum;

    @Schema(title = "线下累计优惠时间总抵扣值", requiredMode = Schema.RequiredMode.REQUIRED)
    private String offLineProfitTimeValue;

}

