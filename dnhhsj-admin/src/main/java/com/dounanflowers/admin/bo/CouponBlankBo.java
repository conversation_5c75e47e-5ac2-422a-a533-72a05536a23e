package com.dounanflowers.admin.bo;

import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.bo.CouponTplBo;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(title = "空白优惠券记录")
public class CouponBlankBo {

    @JsonProperty("_id")
    private Long id;

    @Schema(title = "模板ID")
    private Long couponTplId;

    private CouponTplBo couponTplObj;

    @Schema(title = "商家用户")
    @JsonProperty("merchantUserId")
    private Long belongUserId;

    @JsonProperty("merchantUserObj")
    private AdminUserBo belongUser;

    @Schema(title = "生成个数")
    private Integer count;

    @Schema(title = "生成codes")
    private List<String> couponCodes;

    @Schema(title = "下载链接")
    private String downloadUrl;

    private LocalDateTime createdAt;

}

