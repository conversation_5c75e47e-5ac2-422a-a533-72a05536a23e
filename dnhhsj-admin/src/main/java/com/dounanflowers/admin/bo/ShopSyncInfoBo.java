package com.dounanflowers.admin.bo;

import com.dounanflowers.admin.jsontype.LocalDateTimeDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ShopSyncInfoBo {
    private Integer cellId; // 单元ID
    private String cellNo; // 单元编号
    private String cellName; // 单元名称
    private String cellProperty; // 房产性质
    private Double cellArea; // 套内面积
    private Double buildingArea; // 建筑面积
    private String cellStatus; // 单元状态

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime contractStartDate; // 合同开始日期

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime contractEndDate; // 合同结束日期
    private String contractNo; // 合同编号
    private String businessType; // 经营类型
    private String cellRemark; // 单元备注
    private String attachmentNames; // 附件名称
    private String attachmentPaths; // 附件路径
    private String costName; // 费用名称
    private Double unitPrice; // 单价
    private String billingCycle; // 付款方式
    private Double chargeAmount; // 费用金额
    private String tenantId; // 租户ID
    private String tenanName; // 租户名称
    private String tenanPhone; // 租户电话
    private String contactPerson; // 租户名字
    private String contactPhone; // 租户电话
    private String tenantType; // 1-个人；2-公司
    private String tenantIdCard; // 租户身份证号
    private String tenanAddress; // 租户地址
    private String businessLicense; // 营业执照号
    private String tenanRemark; // 租户备注
}
