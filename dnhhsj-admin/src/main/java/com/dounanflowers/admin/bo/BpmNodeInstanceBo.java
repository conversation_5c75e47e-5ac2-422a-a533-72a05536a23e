package com.dounanflowers.admin.bo;

import com.dounanflowers.common.bo.AdminUserBo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class BpmNodeInstanceBo {

    private Long id;

    private Long instanceId;

    private String status;

    private String type;

    private String role;

    private String roleValue;

    private Long nodeId;

    private String nodeName;

    private Long stepId;

    private Long prevId;

    private Long nextId;

    private String remark;

    private Object data;

    private LocalDateTime operatedAt;

    private Long operatedUserId;

    private AdminUserBo operatedUserObj;

    private Long operatedActionId;

    private String operatedActionName;

    private String operatedReason;

    private LocalDateTime createdAt;

}
