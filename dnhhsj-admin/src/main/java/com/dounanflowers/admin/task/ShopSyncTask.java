package com.dounanflowers.admin.task;

import com.dounanflowers.admin.bo.ShopSyncInfoBo;
import com.dounanflowers.common.entity.Shop;
import com.dounanflowers.common.entity.ShopSyncConflict;
import com.dounanflowers.common.manager.ShopManager;
import com.dounanflowers.common.repo.ShopSyncConflictRepo;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.DateUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.google.common.collect.Lists;
import com.lambdaworks.crypto.SCrypt;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
@RequiredArgsConstructor
public class ShopSyncTask {

    final private ShopManager shopManager;
    final private ShopSyncConflictRepo shopSyncConflictRepo;

    @Scheduled(
            // initialDelay = 500,
            cron = "0 0 2 * * ?" // 每天凌晨2点执行
    )
    public void syncShop() throws NoSuchPaddingException, NoSuchAlgorithmException {
        System.out.println("同步店铺信息:开始");
        String textFileName = "tmp/shopSync_" + DateUtils.formatDateTime(LocalDateTime.now(), "YYYYMMDD") + ".txt";
        String jsonFileName = "tmp/shopSync_" + DateUtils.formatDateTime(LocalDateTime.now(), "YYYYMMDD") + ".json";
        Path textFilePath = Paths.get(textFileName);
        Path jsonFilePath = Paths.get(jsonFileName);
        String jsonContent = "";


        if (Files.exists(jsonFilePath)) {
            System.out.println("从本地加载json");
            try {
                jsonContent = new String(Files.readAllBytes(jsonFilePath));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } else {
            String textContent = "";
            if (Files.exists(textFilePath)) {
                System.out.println("从本地加载加密txt");
                try {
                    textContent = new String(Files.readAllBytes(textFilePath));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            } else {
                System.out.println("从远程加载加密txt");
                String jsonUrl = "http://36.138.249.45:3000/dnhhsj/cell-info";
                try {
                    System.out.println("加载远程店铺信息");
                    HttpClient httpClient = HttpClient.newBuilder().build();
                    HttpRequest request = HttpRequest.newBuilder()
                            .uri(URI.create(jsonUrl))
                            .build();
                    HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
                    String baseTextContent = response.body();
                    Map<String, Object> map = JsonUtils.toMap(baseTextContent);

                    textContent = (String) map.get("data");
                    Files.write(textFilePath, textContent.getBytes());
                } catch (Exception e) {
                    System.out.println("从远程加载加密txt:失败");
                    throw new RuntimeException(e);
                }
            }

            try {
                System.out.println("从加密txt解密json");
                String password = "dnhhsj-key";
                byte[] salt = new byte[0]; // No salt in this case
                int cost = 16384; // CPU/memory cost parameter
                int blockSize = 8; // Block size parameter
                int parallelization = 1; // Parallelization parameter
                int keyLength = 16; // 16 bytes for AES-128

                byte[] keyBytes = SCrypt.scrypt(password.getBytes(StandardCharsets.UTF_8), salt, cost, blockSize, parallelization, keyLength);
                SecretKeySpec key = new SecretKeySpec(keyBytes, "AES");

                Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
                cipher.init(Cipher.DECRYPT_MODE, key);
                byte[] encryptedBytes = hexStringToByteArray(textContent);
                byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
                jsonContent = new String(decryptedBytes, StandardCharsets.UTF_8);

                Files.write(jsonFilePath, jsonContent.getBytes());
            } catch (Exception e) {
                System.out.println("从加密txt解密json:失败");
                throw new RuntimeException(e);
            }
        }

        System.out.println("json转对象");

        List<ShopSyncInfoBo> shopSyncInfoBos = JsonUtils.toList(jsonContent, ShopSyncInfoBo.class);

        int total = shopSyncInfoBos.size();
        for (int i = 0; i < total; i++) {
            System.out.println("处理第" + i + "个店铺, 共" + total + "个");
            ShopSyncInfoBo shopSyncInfoBo = shopSyncInfoBos.get(i);
            String realBusinessType = businessTypeMap.get(shopSyncInfoBo.getBusinessType());
            ArrayList<String> businessType = Lists.newArrayList();
            if (realBusinessType != null) {
                businessType.add(realBusinessType);
            }


            Shop shop = shopManager.fetchByCellNo(shopSyncInfoBo.getCellNo());
            if (shop == null) {
                System.out.println("新店铺创建:" + shopSyncInfoBo.getCellNo());
                shop = new Shop();
                BeanUtils.copyProperties(shopSyncInfoBo, shop);
                shop.setBusinessType(businessType);
                shop.setEditedAt(LocalDateTime.now());
                shopManager.save(shop);
                continue;
            }

            System.out.println("比较店铺:" + shopSyncInfoBo.getCellNo());

            ArrayList<String> fields = Lists.newArrayList("cellId",
                    "cellNo",
                    "cellName",
                    "cellProperty",
                    "cellArea",
                    "buildingArea",
                    "cellStatus",
                    "contractStartDate",
                    "contractEndDate",
                    "contractNo",
                    "businessType",
                    "cellRemark",
                    "attachmentNames",
                    "attachmentPaths",
                    "costName",
                    "unitPrice",
                    "billingCycle",
                    "chargeAmount",
                    "tenantId",
                    "tenanName",
                    "tenanPhone",
                    "contactPerson",
                    "contactPhone",
                    "tenantType",
                    "tenantIdCard",
                    "tenanAddress",
                    "businessLicense",
                    "tenanRemark");
            Map<String, Object> shopSyncInfoBoMap = BeanUtils.toMap(shopSyncInfoBo);
            shopSyncInfoBoMap.put("businessType", businessType);
            Map<String, Object> shopMap = BeanUtils.toMap(shop);

            boolean needSaveShop = false;

            for (String field : fields) {
                Object shopSyncInfoField = shopSyncInfoBoMap.get(field);
                Object shopField = shopMap.get(field) instanceof Enum<?> ? ((Enum<?>) shopMap.get(field)).ordinal() : shopMap.get(field);

                if (field.equals("contractStartDate")) {
                    System.out.println("比较字段" + field);
                    //                    System.out.println(shopSyncInfoField.getClass());
                    //                    System.out.println(shopField.getClass());
                }

                int compRes = compareField(shopSyncInfoField, shopField);
                if (field.equals("contractStartDate")) {
                    System.out.println("结果" + compRes);
                }
                if (compRes == -1) {
                    // 如果字段冲突，且新数据可以覆盖旧数据，则更新
                    needSaveShop = true;
                    BeanUtils.copyProperty(shop, field, shopSyncInfoField);
                } else if (compRes == 0) {

                    // 查出同NO同字段同值冲突
                    Map<String, Object> sameConflictItemQueryMap = new HashMap<>();
                    sameConflictItemQueryMap.put("cell_no", shopSyncInfoBo.getCellNo());
                    sameConflictItemQueryMap.put("conflict_field", field);
                    sameConflictItemQueryMap.put("remote_value", JsonUtils.toJson(shopSyncInfoField));
                    ShopSyncConflict sameShopSyncConflict = shopSyncConflictRepo.selectOneByMap(sameConflictItemQueryMap);
                    if (sameShopSyncConflict != null) {
                        System.out.println("冲突字段" + field + "相同远程值之前已记录");
                        continue;
                    }

                    // 查出同NO同字段未处理冲突
                    Map<String, Object> waitHandleConflictItemQueryMap = new HashMap<>();
                    waitHandleConflictItemQueryMap.put("cell_no", shopSyncInfoBo.getCellNo());
                    waitHandleConflictItemQueryMap.put("conflict_field", field);
                    waitHandleConflictItemQueryMap.put("handled", IsEnum.FALSE);
                    ShopSyncConflict waitHandleConflictItem = shopSyncConflictRepo.selectOneByMap(waitHandleConflictItemQueryMap);
                    if (waitHandleConflictItem == null) {
                        // 如果不存在
                        ShopSyncConflict shopSyncConflict = new ShopSyncConflict();
                        shopSyncConflict.setCellNo(shopSyncInfoBo.getCellNo());
                        shopSyncConflict.setConflictField(field);
                        shopSyncConflict.setRemoteValue(JsonUtils.toJson(shopSyncInfoField));
                        shopSyncConflict.setLocalValue(JsonUtils.toJson(shopField));
                        shopSyncConflict.setHandled(IsEnum.FALSE);
                        shopSyncConflictRepo.insert(shopSyncConflict);
                    } else {
                        // 如果存在，但是值不同，更新
                        waitHandleConflictItem.setRemoteValue(JsonUtils.toJson(shopSyncInfoField));
                        waitHandleConflictItem.setLocalValue(JsonUtils.toJson(shopField));
                        waitHandleConflictItem.setHandled(IsEnum.FALSE);
                        shopSyncConflictRepo.update(waitHandleConflictItem);
                    }
                }

            }

            if (needSaveShop) {
                shopManager.save(shop);
            }
        }


    }

    private final HashMap<String, String> businessTypeMap = new HashMap<>() {{
        put("1", "005");
        put("2", "005");
        put("3", "005");
        put("4", "006");
        put("5", "002");
        put("6", "007");
        put("7", "008");
        put("8", "005");
        put("9", "005");
        put("10", "004");
        put("11", "005");
        put("12", "005");
        put("13", "005");
        put("14", "001");
        put("15", "002");
        put("16", "005");
        put("17", "005");
        put("18", "001");
        put("19", "003");
        put("20", "001");
        put("21", "005");
        put("22", "005");
        put("23", "005");
        put("24", "005");
        put("25", "002");
        put("26", "005");
        put("27", "002");
        put("28", "005");
        put("29", "008");
        put("30", "005");
        put("31", "004");
        put("32", "001");
        put("33", "004");
        put("34", "005");
        put("35", "004");
        put("36", "001");
        put("37", "003");
        put("38", "009");
        put("39", "006");
        put("40", "005");
        put("41", "005");
        put("42", "005");
        put("43", "001");
        put("44", "004");
        put("45", "004");
        put("46", "005");
        put("47", "005");
        put("48", "001");
        put("49", "001");
        put("50", "001");
        put("53", "005");
        put("54", "004");
        put("55", "004");
        put("56", "004");
        put("57", "008");
        put("59", "005");
    }};


    private byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    private static int compareField(Object shopSyncInfoField, Object shopField) {

        if (shopSyncInfoField == shopField) {
            return 1;
        }
        if (isEmpty(shopSyncInfoField) && isEmpty(shopField)) {
            return 1;
        }
        if ((shopSyncInfoField instanceof String || shopSyncInfoField instanceof Number) && (shopField instanceof String || shopField instanceof Number)) {
            String shopSyncInfoFieldString = shopSyncInfoField.toString();
            String shopFieldString = shopField.toString();
            return shopSyncInfoFieldString.equals(shopFieldString) ? 1 : 0;
        }
        if ((shopSyncInfoField instanceof LocalDateTime) || (shopField instanceof LocalDateTime)) {
            if (shopSyncInfoField == null || shopField == null) {
                return 0;
            }
            assert shopSyncInfoField instanceof LocalDateTime;
            LocalDateTime shopSyncInfoFieldDate = (LocalDateTime) shopSyncInfoField;
            LocalDateTime shopFieldDate = (LocalDateTime) shopField;
            return shopFieldDate.isEqual(shopSyncInfoFieldDate) ? 1 : 0;
        }

        if (shopSyncInfoField instanceof List) {
            if (!(shopField instanceof List<?> shopFieldList)) {
                return 0;
            }
            if (shopFieldList.isEmpty()) {
                return -1;
            }
            boolean shopFieldIsBig = ((List<?>) shopSyncInfoField).stream().allMatch(shopFieldList::contains);
            boolean shopSyncInfoFieldIsBig = shopFieldList.stream().allMatch(item -> ((List<?>) shopSyncInfoField).contains(item));
            if (shopFieldIsBig && shopSyncInfoFieldIsBig) {
                return 1;
            } else if (shopFieldIsBig) {
                return 0;
            } else if (shopSyncInfoFieldIsBig) {
                return -1;
            }
            return 0;
        }
        if (shopSyncInfoField != null && !isEmpty(shopSyncInfoField) && isEmpty(shopField)
        ) {
            return -1;
        }
        return 0;
    }

    public static boolean isEmpty(Object obj) {
        return switch (obj) {
            case null -> true;
            case String s -> s.isEmpty();
            case List list -> list.isEmpty();
            default -> false;
        };
    }
}
