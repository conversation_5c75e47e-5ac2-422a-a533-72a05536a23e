package com.dounanflowers.admin.task;

import com.dounanflowers.common.entity.LcPark;
import com.dounanflowers.common.entity.LcParkCount;
import com.dounanflowers.common.repo.LcParkCountRepo;
import com.dounanflowers.common.repo.LcParkRepo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class LcParkCountTask {

    @Value("${isProd:false}")
    private Boolean isProd;

    private final LcParkRepo lcParkRepo;
    private final LcParkCountRepo lcParkCountRepo;

    @Scheduled(
            //            initialDelay = 500
            cron = "0 */1 * * * ?" // 每分钟执行
    )
    public void lcParkCount() {
        System.out.println("lcParkCount task");
        if (!isProd) {
            return;
        }
        System.out.println("lcParkCount task start");
        int minutes = Math.round((float) System.currentTimeMillis() / 1000 / 60);
        List<LcPark> lcParks = lcParkRepo.selectAll();
        for (LcPark park : lcParks) {
            LcParkCount lcParkCount = new LcParkCount();
            lcParkCount.setLcParkId(park.getId());
            lcParkCount.setInParkCount(park.getSpaceCount() - park.getFreeSpaceCount());
            lcParkCount.setSpaceCount(park.getSpaceCount());
            lcParkCount.setFreeSpaceCount(park.getFreeSpaceCount());
            lcParkCount.setMinutes(minutes);
            lcParkCountRepo.insertSelective(lcParkCount);
        }
        System.out.println("lcParkCount task end");
    }
}
