package com.dounanflowers.admin.task;

import com.dounanflowers.admin.service.AdminTricycleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class TricyclePayTimeoutTask {

    private final AdminTricycleService adminTricycleService;

    @Scheduled(cron = "0 */10 * * * ?")
    public void timeout() {
        adminTricycleService.updateTimeout();
    }
}
