package com.dounanflowers.admin.task;

import com.dounanflowers.admin.service.AdminVehicleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class VehicleRemindTask {

    private final AdminVehicleService adminVehicleService;

    @Scheduled(cron = "0 0 8 * * ?")
    // @Scheduled(cron = "0 0 * * * ?")
    public void remind() {
        adminVehicleService.remindVehicle();
    }
}
