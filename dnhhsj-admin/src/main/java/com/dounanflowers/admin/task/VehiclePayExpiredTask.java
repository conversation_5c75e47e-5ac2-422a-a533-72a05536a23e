package com.dounanflowers.admin.task;

import com.dounanflowers.admin.service.AdminVehicleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class VehiclePayExpiredTask {

    private final AdminVehicleService adminVehicleService;

    @Scheduled(cron = "0 */10 * * * ?")
    public void expired() {
        adminVehicleService.updateExpired();
    }
}
