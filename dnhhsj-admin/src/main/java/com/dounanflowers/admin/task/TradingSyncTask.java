package com.dounanflowers.admin.task;

import com.dounanflowers.admin.service.AdminBiService;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

@Component
@RequiredArgsConstructor
public class TradingSyncTask {

    private final AdminBiService adminBiService;

    @Scheduled(cron = "0 0 1 * * ?")
    public void task() {
        adminBiService.syncOnlineTradingCountByDay(LocalDate.now().minusDays(1));
    }
}
