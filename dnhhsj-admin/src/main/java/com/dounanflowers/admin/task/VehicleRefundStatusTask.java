package com.dounanflowers.admin.task;

import com.dounanflowers.common.entity.Vehicle;
import com.dounanflowers.common.enums.AdminMenuEventEnum;
import com.dounanflowers.common.enums.VehicleRefundStatusEnum;
import com.dounanflowers.common.service.AdminMenuService;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.row.DbChain;
import com.mybatisflex.core.update.UpdateChain;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@Slf4j
@RequiredArgsConstructor
public class VehicleRefundStatusTask {

    private final AdminMenuService adminMenuService;

    @Scheduled( cron = "0 */1 * * * ?")
    public void remind() {
        long count = QueryChain.of(Vehicle.class).where(Vehicle::getRefundStatus).eq(VehicleRefundStatusEnum.WAIT_CHECK).and(Vehicle::getMonthlyCardExpireAt).lt(LocalDateTime.now()).count();
        if(count == 0L) return;
        UpdateChain.of(Vehicle.class)
                .set(Vehicle::getRefundStatus, VehicleRefundStatusEnum.REJECTED)
                .set(Vehicle::getRefundProcessedAt, LocalDateTime.now())
                .set(Vehicle::getRefundRemark, "月卡失效自动拒绝")
                .where(Vehicle::getRefundStatus).eq(VehicleRefundStatusEnum.WAIT_CHECK).and(Vehicle::getMonthlyCardExpireAt).lt(LocalDateTime.now())
                .update();
        adminMenuService.emitCount(AdminMenuEventEnum.VEHICLE_REFUND_CHECK);
    }
}
