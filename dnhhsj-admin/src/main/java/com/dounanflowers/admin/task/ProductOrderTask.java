package com.dounanflowers.admin.task;

import com.dounanflowers.admin.service.AdminProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ProductOrderTask {

    private final AdminProductService adminProductService;

    @Scheduled(fixedDelay = 300000)
    public void cancelOrder() {
        adminProductService.cancelOrderByExpired();
    }

}
