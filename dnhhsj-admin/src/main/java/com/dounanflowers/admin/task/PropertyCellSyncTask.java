package com.dounanflowers.admin.task;

import com.dounanflowers.common.service.PropertyCellService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class PropertyCellSyncTask {
    final private PropertyCellService propertyCellService;

    @Scheduled(
//            initialDelay = 1000,
            cron = "0 0 3 * * ?" // 每天凌晨3点执行
    )
    public void syncPropertyCell() {
        try {
            propertyCellService.syncAllPropertyCell();
        } catch (Exception e) {
            log.error("同步物业单元失败", e);
        }
    }
}
