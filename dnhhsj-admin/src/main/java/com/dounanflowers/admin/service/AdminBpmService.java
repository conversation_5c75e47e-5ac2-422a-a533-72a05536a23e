package com.dounanflowers.admin.service;

import com.dounanflowers.admin.bo.BpmActionBo;
import com.dounanflowers.admin.bo.BpmInstanceBo;
import com.dounanflowers.admin.bo.BpmNodeInstanceBo;
import com.dounanflowers.admin.bo.BpmProcessBo;
import com.dounanflowers.admin.dto.AdminBpmHandleDto;
import com.dounanflowers.bpm.dto.ActionContext;
import com.dounanflowers.bpm.dto.BpmHandleDto;
import com.dounanflowers.bpm.dto.HandleContext;
import com.dounanflowers.bpm.entity.*;
import com.dounanflowers.bpm.enums.NodeRoleEnum;
import com.dounanflowers.bpm.enums.NodeStatusEnum;
import com.dounanflowers.bpm.handler.ConditionHandler;
import com.dounanflowers.bpm.manager.BpmManager;
import com.dounanflowers.bpm.service.BpmService;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.entity.AdminUser;
import com.dounanflowers.common.entity.TricycleIllegal;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.common.manager.TricycleManager;
import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.framework.utils.SpringUtils;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import com.google.common.collect.Lists;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AdminBpmService {

    private final BpmService bpmService;

    private final BpmManager bpmManager;

    private final TricycleManager tricycleManager;

    private final AdminUserManager adminUserManager;

    public Object doAction(AdminBpmHandleDto dto) {
        BpmHandleDto enter = BeanUtils.copy(dto, BpmHandleDto.class);
        BpmAction bpmAction = bpmManager.fetchActionById(dto.getActionId());
        if (bpmAction == null) {
            return null;
        }
        enter.setActionType(bpmAction.getType().name());
        enter.setActionCode(bpmAction.getCode());
        enter.setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId());
        return bpmService.doAction(enter);
    }

    public Page<BpmInstanceBo> pageList(PageRequest dto) {
        List<PageFilter> newFilters = Lists.newArrayList();
        SessionInfo<Long, AdminUserBo> session = SecurityHolder.session();
        Long userId = session.getUserId();
        List<String> roles = session.getRoles();
        if (dto.getFilter() != null) {
            for (PageFilter filter : dto.getFilter()) {
                if ("custom".equals(filter.getType()) && "outerType".equals(filter.getField())) {
                    String[] values = filter.getValue().toString().split(":");
                    Long tricycleId = Long.parseLong(values[1]);
                    if ("tricycle".equals(values[0])) {
                        List<TricycleIllegal> tricycleIllegals = tricycleManager.fetchIllegalByTricycleId(tricycleId);
                        PageFilter newFilter = new PageFilter();
                        newFilter.setField("outerId");
                        newFilter.setType("in");
                        List<Long> list = tricycleIllegals.stream().map(BaseEntity::getId).collect(Collectors.toList());
                        list.add(tricycleId);
                        newFilter.setValue(list);
                        newFilters.add(newFilter);
                    }
                } else if ("custom".equals(filter.getType()) && "doStatus".equals(filter.getField())) {
                    if ("done".equals(filter.getValue())) {
                        QueryWrapper subWrapper = QueryWrapper.create().select("instance_id").from(BpmNodeInstance.class).eq(BpmNodeInstance::getOperatedUserId, userId);
                        filter.setType("sub");
                        filter.setField("id");
                        dto.addSubQuery("id", w -> w.in("id", subWrapper));
                    } else if ("todo".equals(filter.getValue())) {
                        QueryWrapper subWrapper = QueryWrapper.create().select("instance_id").from(BpmNodeInstance.class)
                                .eq(BpmNodeInstance::getStatus, NodeStatusEnum.WAITING)
                                .and(w -> w.eq(BpmNodeInstance::getRole, NodeRoleEnum.SELF).eq(BpmInstance::getUserId, userId)
                                                .or(or -> or.eq(BpmNodeInstance::getRole, NodeRoleEnum.USER).eq(BpmNodeInstance::getRoleValue, userId + ""), true)
                                                .or(or -> or.eq(BpmNodeInstance::getRole, NodeRoleEnum.ROLE).in(BpmNodeInstance::getRoleValue, roles), true)
                                        , true);
                        filter.setType("sub");
                        filter.setField("id");
                        dto.addSubQuery("id", w -> w.in("id", subWrapper));
                    }
                }
            }
        }
        dto.getFilter().addAll(newFilters);
        Page<BpmInstance> page = bpmManager.pageList(dto);
        List<Long> instanceIds = page.getList().stream().map(BaseEntity::getId).toList();
        List<Long> processIds = page.getList().stream().map(BpmInstance::getProcessId).distinct().toList();
        List<BpmProcess> bpmProcesses = bpmManager.fetchProcessByIds(processIds);
        Map<Long, BpmProcess> processMap = bpmProcesses.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        List<BpmNodeInstance> nodeInstances = bpmManager.fetchNodeInstancesByInstanceIds(instanceIds);
        List<Long> nodeIds = nodeInstances.stream().map(BpmNodeInstance::getNodeId).distinct().toList();
        List<BpmNode> bpmNodes = bpmManager.fetchNodeByIds(nodeIds);
        Map<Long, List<BpmNodeInstance>> nodeInstanceMap = nodeInstances.stream().collect(Collectors.groupingBy(BpmNodeInstance::getInstanceId));
        Map<Long, BpmNode> nodeMap = bpmNodes.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        return page.convert(v -> {
            BpmInstanceBo one = BeanUtils.copy(v, BpmInstanceBo.class);
            List<BpmNodeInstance> nodeList = nodeInstanceMap.get(v.getId());
            if (processMap.get(v.getProcessId()) != null) {
                one.setProcess(BeanUtils.copy(processMap.get(v.getProcessId()), BpmProcessBo.class));
            }
            nodeList.sort(Comparator.comparing(BaseEntity::getCreatedAt));
            one.setData(JsonUtils.toMap(v.getData()));
            one.setNodes(nodeList.stream().map(node -> {
                BpmNodeInstanceBo copy = BeanUtils.copy(node, BpmNodeInstanceBo.class);
                copy.setNodeName(nodeMap.get(node.getNodeId()).getName());
                if (node.getData() != null) {
                    copy.setData(JsonUtils.toMap(node.getData()));
                }
                return copy;
            }).toList());
            return one;
        });
    }

    public List<BpmActionBo> getInstanceActions(Long id) {
        SessionInfo<Long, AdminUserBo> session = SecurityHolder.session();
        Long userId = session.getUserId();
        List<String> roles = session.getRoles();
        BpmInstance instance = bpmManager.fetchInstance(id);
        BpmNodeInstance nodeInstance = bpmManager.fetchNodeInstance(instance.getCurrentNodeId());
        BpmNode node = bpmManager.fetchNode(nodeInstance.getNodeId());
        BpmProcess process = bpmManager.fetchProcess(instance.getProcessId());
        List<BpmAction> actions = bpmManager.fetchActionsByNode(nodeInstance.getNodeId());
        List<ConditionHandler> conditionHandlers = SpringUtils.getBeans(ConditionHandler.class);
        Map<String, ConditionHandler> conditionMap = conditionHandlers.stream().collect(Collectors.toMap(ConditionHandler::code, v -> v));
        List<BpmActionBo> list = new ArrayList<>();
        for (BpmAction v : actions) {
            boolean hasRole = switch (nodeInstance.getRole()) {
                case SYSTEM -> 0L == userId;
                case SELF -> Objects.equals(userId, instance.getUserId());
                case USER -> Objects.equals(userId, Long.parseLong(nodeInstance.getRoleValue()));
                case ROLE -> roles.contains(nodeInstance.getRoleValue());
            };
            if (!hasRole) {
                continue;
            }
            if (!StringUtils.isBlank(v.getCode()) && conditionMap.containsKey(v.getCode())) {
                ConditionHandler conditionHandler = conditionMap.get(v.getCode());
                HandleContext hc = new HandleContext();
                hc.setInstance(instance);
                hc.setNodeInstance(nodeInstance);
                hc.setNode(node);
                hc.setProcess(process);
                ActionContext ctx = new ActionContext(hc);
                ctx.setAction(v);
                if (conditionHandler.condition(ctx)) {
                    list.add(BeanUtils.copy(v, BpmActionBo.class));
                }
            } else {
                list.add(BeanUtils.copy(v, BpmActionBo.class));
            }
        }
        return list;
    }

    public BpmInstanceBo getInstanceById(Long id) {
        BpmInstance instance = bpmManager.fetchInstance(id);
        BpmProcess process = bpmManager.fetchProcess(instance.getProcessId());
        List<BpmNodeInstance> nodeInstances = bpmManager.fetchNodeInstancesByInstanceIds(List.of(id));
        List<Long> nodeIds = nodeInstances.stream().map(BpmNodeInstance::getNodeId).toList();
        List<BpmNode> bpmNodes = bpmManager.fetchNodeByIds(nodeIds);
        Map<Long, BpmNode> nodeMap = bpmNodes.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        Map<Long, List<BpmNodeInstance>> nodeInstanceMap = nodeInstances.stream().collect(Collectors.groupingBy(BpmNodeInstance::getInstanceId));
        BpmInstanceBo one = BeanUtils.copy(instance, BpmInstanceBo.class);
        one.setProcess(BeanUtils.copy(process, BpmProcessBo.class));
        List<BpmNodeInstance> nodeList = nodeInstanceMap.get(id);
        List<Long> userIds = nodeList.stream().map(BpmNodeInstance::getOperatedUserId).distinct().toList();
        List<Long> actionIds = nodeList.stream().map(BpmNodeInstance::getOperatedActionId).distinct().toList();
        List<BpmAction> actions = bpmManager.fetchActionByIds(actionIds);
        Map<Long, BpmAction> actionMap = actions.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        List<AdminUser> users = adminUserManager.fetchByIds(userIds);
        Map<Long, AdminUser> userMap = users.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        nodeList.sort(Comparator.comparing(BaseEntity::getCreatedAt));
        one.setData(JsonUtils.toMap(instance.getData()));
        one.setNodes(nodeList.stream().map(node -> {
            BpmNodeInstanceBo copy = BeanUtils.copy(node, BpmNodeInstanceBo.class);
            copy.setNodeName(nodeMap.get(node.getNodeId()).getName());
            if (node.getData() != null) {
                copy.setData(JsonUtils.toMap(node.getData()));
            }
            if (userMap.get(node.getOperatedUserId()) != null) {
                copy.setOperatedUserObj(BeanUtils.copy(userMap.get(node.getOperatedUserId()), AdminUserBo.class));
            }
            if (actionMap.get(node.getOperatedActionId()) != null) {
                copy.setOperatedActionName(actionMap.get(node.getOperatedActionId()).getName());
            }
            return copy;
        }).toList());
        return one;
    }

    public BpmInstanceBo getInstanceByNodeInstanceId(Long nodeInstanceId) {
        BpmNodeInstance nodeInstance = bpmManager.fetchNodeInstance(nodeInstanceId);
        return getInstanceById(nodeInstance.getInstanceId());
    }

}
