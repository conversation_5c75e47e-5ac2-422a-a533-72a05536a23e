package com.dounanflowers.admin.service;

import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.CheckOrderPageDto;
import com.dounanflowers.common.dto.OrderRefundDto;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.AccountRecordTypeEnum;
import com.dounanflowers.common.enums.BillTypeEnum;
import com.dounanflowers.common.enums.OrderStatusEnum;
import com.dounanflowers.common.enums.ProductStatusEnum;
import com.dounanflowers.common.manager.*;
import com.dounanflowers.common.service.CommonProductService;
import com.dounanflowers.common.service.FileService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.IdUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.utils.SecurityHolder;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AdminProductService {

    private final CommonProductService commonProductService;
    private final ProductManager productManager;
    private final StoreManager storeManager;
    private final FileService fileService;
    private final ClientUserManager clientUserManager;
    private final ShoppingCartManager shoppingCartManager;
    private final WithdrawManager withdrawManager;

    public Page<ProductOrderBo> orderPageList(PageRequest dto) {
        dto.addFilter(new PageFilter().setField("merge").setType("eq").setValue(0));
        Page<ProductOrder> orderPage = productManager.getOrderPage(dto);
        List<Long> userIds = orderPage.getList().stream().map(ProductOrder::getUserId).toList();
        List<ClientUser> clientUsers = clientUserManager.fetchByIds(userIds);
        Map<Long, ClientUser> clientUserMap = clientUsers.stream().collect(Collectors.toMap(ClientUser::getId, v -> v));
        List<Long> storeIds = orderPage.getList().stream().map(ProductOrder::getStoreId).distinct().toList();
        List<Store> stores = storeManager.fetchByIds(storeIds);
        Map<Long, Store> storeMap = stores.stream().collect(Collectors.toMap(Store::getId, v -> v));
        return orderPage.convert(order -> {
            ProductOrderBo one = BeanUtils.copy(order, ProductOrderBo.class);
            Store store = storeMap.get(order.getStoreId());
            if (store != null) {
                one.setStoreObj(BeanUtils.copy(store, StoreBo.class));
            }
            ClientUser user = clientUserMap.get(order.getUserId());
            if (user != null) {
                one.setUserObj(BeanUtils.copy(user, ClientUserBo.class));
            }
            commonProductService.setOrderItems(one, order.getItems());
            return one;
        });
    }

    public ProductOrderBo getOrderDetail(Long id) {
        return commonProductService.getOrderDetail(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void markOrderAsCompleted(Long id) {
        ProductOrder order = productManager.getOrderWithItems(id);
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        if (order.getStatus() != OrderStatusEnum.PAID) {
            throw new BaseException("订单状态不正确");
        }
        order.setStatus(OrderStatusEnum.COMPLETED);
        productManager.updateOrder(order);
    }

    @Transactional(rollbackFor = Exception.class)
    public void refundOrder(OrderRefundDto dto) {
        ProductOrder order = productManager.getOrderWithItems(dto.getId());
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        if (order.getStatus() != OrderStatusEnum.PAID) {
            throw new BaseException("订单状态不正确");
        }
        order.setStatus(OrderStatusEnum.REFUNDING);
        order.setRefundReason(dto.getReason());
        order.setRefundTime(LocalDateTime.now());
        productManager.updateOrder(order);
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(Long id) {
        commonProductService.cancelOrder(id);
    }

    public Page<ProductBo> productPageList(PageRequest dto) {
        Page<Product> list = productManager.productPageList(dto);
        List<Store> stores = storeManager.fetchByIds(list.getList().stream().map(Product::getStoreId).toList());
        Map<Long, Store> storeMap = stores.stream().collect(Collectors.toMap(Store::getId, v -> v));
        return list.convert(v -> {
            ProductBo one = BeanUtils.copy(v, ProductBo.class);
            one.setImages(v.getImages());
            one.setStoreObj(BeanUtils.copy(storeMap.get(v.getStoreId()), StoreBo.class));
            return one;
        });
    }

    public ProductBo getProductDetail(Long id) {
        Product product = productManager.fetchProductByIdWithRelation(id);
        if (product == null) {
            throw new BaseException("商品不存在");
        }
        ProductBo copy = BeanUtils.copy(product, ProductBo.class);
        copy.setImages(product.getImages());
        return copy;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveProduct(ProductBo dto) {
        Product product;
        Store store = storeManager.fetchByIdWithRelation(dto.getStoreId());
        if (store == null) {
            throw new BaseException("商铺不存在");
        }
        if (dto.getId() != null) {
            product = productManager.fetchProductByIdWithRelation(dto.getId());
            if (product == null) {
                throw new BaseException("商品不存在");
            }
        } else {
            product = new Product();
            product.setType(store.getType());
            product.setTotalReviews(0);
            product.setSoldCount(0);
            product.setRate(0);
        }
        BeanUtils.copyProperties(dto, product);
        if (product.getStock() > 0 && product.getStatus() == ProductStatusEnum.SOLD_OUT) {
            product.setStatus(ProductStatusEnum.PUBLISHED);
        } else if (product.getStock() == 0 && product.getStatus() == ProductStatusEnum.PUBLISHED) {
            product.setStatus(ProductStatusEnum.SOLD_OUT);
        }
        productManager.saveProduct(product);
        fileService.updateFileLink(product.getId(), dto.getImages());
        List<Product> products = productManager.fetchProductByStoreId(product.getStoreId());
        if (!products.isEmpty()) {
            Integer minPrice = products.stream().mapToInt(Product::getPrice).min().getAsInt();
            store.setMinPrice(minPrice);
            storeManager.save(store);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteProduct(Long id) {
        Product product = productManager.fetchProductByIdWithRelation(id);
        if (product == null) {
            throw new BaseException("商品不存在");
        }
        productManager.deleteProduct(id);
        List<Product> products = productManager.fetchProductByStoreId(product.getStoreId());
        if (!products.isEmpty()) {
            Integer minPrice = products.stream().mapToInt(Product::getPrice).min().getAsInt();
            Store store = storeManager.fetchById(product.getStoreId());
            if (store == null) {
                throw new BaseException("商铺不存在");
            }
            store.setMinPrice(minPrice);
            storeManager.save(store);
        }
        shoppingCartManager.deleteByProductId(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateProductStock(Long id, Integer stock) {
        Product product = productManager.fetchProductByIdWithRelation(id);
        if (product == null) {
            throw new BaseException("商品不存在");
        }
        if (stock < 0) {
            throw new BaseException("库存不能小于0");
        }
        product.setStock(stock);
        productManager.saveProduct(product);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateProductStatus(Long id, String status) {
        Product product = productManager.fetchProductByIdWithRelation(id);
        if (product == null) {
            throw new BaseException("商品不存在");
        }
        try {
            ProductStatusEnum statusEnum = ProductStatusEnum.valueOf(status);
            product.setStatus(statusEnum);
            productManager.saveProduct(product);
        } catch (IllegalArgumentException e) {
            throw new BaseException("无效的商品状态");
        }
    }

//    public void handleProductBill(Bill bill) {
//        if (bill.getType() != BillTypeEnum.PRODUCT_ORDER) {
//            return;
//        }
//        ProductOrder order = productManager.fetchOrderById(bill.getEntityId());
//        if (order.getMerge().isTrue()) {
//            List<ProductOrder> orders = productManager.fetchOrderByParentId(order.getId());
//            if (orders.isEmpty()) {
//                return;
//            }
//            for (ProductOrder sub : orders) {
//                Store store = storeManager.fetchById(sub.getStoreId());
//                sub.setStatus(OrderStatusEnum.PAID);
//                sub.setPayTime(LocalDateTime.now());
//                List<Product> products = productManager.fetchProductByIdsWithRelation(
//                        sub.getItems().stream().map(ProductOrderItem::getProductId).distinct().toList());
//                Map<Long, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, v -> v));
//                for (ProductOrderItem item : sub.getItems()) {
//                    item.setCode(IdUtils.nextId().toString());
//                    item.setSnapshot(JsonUtils.toJson(productMap.get(item.getProductId())));
//                }
//                productManager.updateOrder(sub);
//                if (store.getAdminUserId() != null) {
//                    Product product = productMap.get(sub.getItems().get(0).getProductId());
//                    String title = product.getTitle();
//                    if (sub.getItems().size() > 1) {
//                        title = "等" + sub.getItems().size() + "件商品";
//                    }
//                    int fee = sub.getRealAmount() * store.getFeeRate() / 1000;
//                    int amount = sub.getRealAmount() - fee;
//                    withdrawManager.updateAccountIncome(store.getAdminUserId(),
//                            amount, fee, sub.getId(),
//                            AccountRecordTypeEnum.PRODUCT_ORDER_INCOME, title);
//                }
//            }
//        }
//        order.setStatus(OrderStatusEnum.PAID);
//        order.setPayTime(LocalDateTime.now());
//        if (CollectionUtils.isNotEmpty(order.getItems())) {
//            List<Product> products = productManager.fetchProductByIdsWithRelation(
//                    order.getItems().stream().map(ProductOrderItem::getProductId).distinct().toList());
//            Map<Long, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, v -> v));
//            for (ProductOrderItem item : order.getItems()) {
//                item.setCode(IdUtils.nextId().toString());
//                item.setSnapshot(JsonUtils.toJson(productMap.get(item.getProductId())));
//                Product product = productMap.get(item.getProductId());
//
//                if (product.getValidDays() > 0) {
//                    LocalDateTime itemExpireTime = LocalDateTime.now().plusDays(product.getValidDays());
//                    if(itemExpireTime.isAfter(product.getValidTo())) {
//                        itemExpireTime = product.getValidTo();
//                    }
//                    item.setExpireTime(itemExpireTime);
//                }
//
//            }
//            Store store = storeManager.fetchById(order.getStoreId());
//            if (store.getAdminUserId() != null) {
//                Product product = productMap.get(order.getItems().get(0).getProductId());
//                String title = product.getTitle();
//                if (order.getItems().size() > 1) {
//                    title = "等" + order.getItems().size() + "件商品";
//                }
//                int fee = order.getRealAmount() * store.getFeeRate() / 1000;
//                int amount = order.getRealAmount() - fee;
//                withdrawManager.updateAccountIncome(store.getAdminUserId(),
//                        amount, fee, order.getId(),
//                        AccountRecordTypeEnum.PRODUCT_ORDER_INCOME, title);
//            }
//        }
//        productManager.updateOrder(order);
//        clientUserManager.addPoints(order.getUserId(), order.getRealAmount() / 100, "购物赠送积分");
//
//    }

    @Transactional(rollbackFor = Exception.class)
    public void checkOrder(String code, Long userId) {

        ProductOrderItem productOrderItem = productManager.fetchOrderItemByCode(code);
        if (productOrderItem == null) {
            throw new BaseException("订单不存在");
        }
        if(productOrderItem.getUseTime() != null) {
            throw new BaseException("订单已使用");
        }

        ProductOrder order = productManager.getOrderWithItems(productOrderItem.getOrderId());
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        if (order.getStatus() != OrderStatusEnum.PAID) {
            throw new BaseException("当前订单状态无法核销");
        }
        Store store = storeManager.fetchByIdWithRelation(order.getStoreId());
        if (!userId.equals(store.getAdminUserId())) {
            if (store.getEmployees().stream().noneMatch(v -> userId.equals(v.getUserId()))) {
                throw new BaseException("无权限");
            }
        }

        productOrderItem.setUseTime(LocalDateTime.now());
        productOrderItem.setCheckAdminUserId(userId);
        productManager.saveProductOrderItem(productOrderItem);

        order = productManager.getOrderWithItems(productOrderItem.getOrderId());

        if (order.getItems().stream().allMatch(v -> v.getUseTime() != null)) {
            order.setStatus(OrderStatusEnum.PENDING_REVIEW);
        }
        productManager.updateOrder(order);

        if (store.getAdminUserId() != null) {
            Product product = productManager.fetchProductById(productOrderItem.getProductId());
            String title = product.getTitle();

            int fee = productOrderItem.getRealAmount() * store.getFeeRate() / 1000;
            int amount = productOrderItem.getRealAmount() - fee;
            withdrawManager.updateAccountIncome(store.getAdminUserId(),
                    amount, fee, productOrderItem.getId(),
                    AccountRecordTypeEnum.PRODUCT_ORDER_INCOME, title);
        }
    }

    public ProductOrderBo getOrderDetailByCode(String code) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        ProductOrderItem productOrderItem = productManager.fetchOrderItemByCode(code);
        if (productOrderItem == null) {
            throw new BaseException("订单不存在");
        }
        ProductOrder order = productManager.getOrderWithItems(productOrderItem.getOrderId());
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        Store store = storeManager.fetchByIdWithRelation(order.getStoreId());
        if (!userId.equals(store.getAdminUserId())) {
            if (store.getEmployees().stream().noneMatch(v -> userId.equals(v.getUserId()))) {
                throw new BaseException("无权限");
            }
        }
        ProductOrderBo orderDetail = getOrderDetail(productOrderItem.getOrderId());
        orderDetail.setItems(orderDetail.getItems().stream().filter(v -> Objects.equals(v.getCode(), code)).toList());
        return orderDetail;
    }

    public void cancelOrderByExpired() {
        List<ProductOrder> productOrders = productManager.fetchOrderByStatus(OrderStatusEnum.PENDING_PAYMENT);
        for (ProductOrder productOrder : productOrders) {
            if (productOrder.getExpireTime().isBefore(LocalDateTime.now())) {
                commonProductService.cancelOrder(productOrder.getId());
            }
        }
    }

    public CheckOrderPageBo checkOrderPageList(CheckOrderPageDto dto) {
        if (dto.getStartDate() != null && dto.getEndDate() != null) {
            dto.addFilter(new PageFilter().setField("createdAt").setType("range").setValue(Lists
                    .newArrayList(dto.getStartDate().atStartOfDay(), dto.getEndDate().plusDays(1).atStartOfDay())));
        }
        List<Long> storeIds = storeManager
                .fetchStoreIdsByUserId(SecurityHolder.<Long, ClientUserBo>session().getUserId());
        if (CollectionUtils.isEmpty(storeIds)) {
            return new CheckOrderPageBo();
        }
        if (dto.getStoreId() != null) {
            if (!storeIds.contains(dto.getStoreId())) {
                return new CheckOrderPageBo();
            }
            storeIds = Lists.newArrayList(dto.getStoreId());
        }
        Page<ProductOrderItem> productOrderItemPage = productManager.fetchOrderItemsPage(dto, storeIds);
        List<Long> clientUserIds = productOrderItemPage.getList().stream().map(ProductOrderItem::getOrder)
                .map(ProductOrder::getUserId).toList();
        List<ClientUser> clientUsers = clientUserManager.fetchByIds(clientUserIds);
        Map<Long, ClientUser> clientUserMap = clientUsers.stream().collect(Collectors.toMap(ClientUser::getId, v -> v));
        dto.addFilter(new PageFilter().setField("storeId").setType("in").setValue(storeIds));
        CheckOrderPageBo result = productManager.statsCheckOrderInfo(dto);
        result.setTotal(productOrderItemPage.getTotal());
        result.setList(productOrderItemPage.getList().stream().map(v -> {
            ProductOrderItemBo copy = BeanUtils.copy(v, ProductOrderItemBo.class);
            copy.setOrderObj(BeanUtils.copy(v.getOrder(), ProductOrderBo.class));
            ClientUser user = clientUserMap.get(v.getOrder().getUserId());
            if (user != null) {
                copy.setUserObj(BeanUtils.copy(user, ClientUserBo.class));
            }
            Product product = JsonUtils.toObject(v.getSnapshot(), Product.class);
            ProductBo p = BeanUtils.copy(product, ProductBo.class);
            p.setImages(product.getImages());
            copy.setProductObj(p);
            return copy;
        }).collect(Collectors.toList()));
        return result;
    }
}
