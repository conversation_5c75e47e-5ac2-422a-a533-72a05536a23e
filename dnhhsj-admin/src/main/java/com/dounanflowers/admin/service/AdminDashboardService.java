package com.dounanflowers.admin.service;

import com.dounanflowers.admin.bo.DashboardCountBo;
import com.dounanflowers.admin.bo.StatisticInfoBo;
import com.dounanflowers.admin.dto.DateRangeDto;
import com.dounanflowers.common.bo.LcParkCountBo;
import com.dounanflowers.common.entity.LcPark;
import com.dounanflowers.common.manager.LcManager;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.DateUtils;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.row.Db;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AdminDashboardService {

    private final LcManager lcManager;

    public StatisticInfoBo statisticInfo() {
        LocalDateTime todayStartTime = LocalDateTime.now()
                .withHour(0).withMinute(0).withSecond(0).withNano(0);
        long todayParkTotal = Db.selectCountByQuery("lc_order", QueryWrapper.create().ge("in_time", todayStartTime));
        LcPark lcPark = lcManager.fetchFistLcPark();
        int nowParkCount = lcPark == null ? 0 : lcPark.getSpaceCount() - lcPark.getFreeSpaceCount();
        long todayUserAdd = Db.selectCountByQuery("client_user", QueryWrapper.create().ge("created_at", todayStartTime));
        long userTotal = Db.selectCountByQuery("client_user", QueryWrapper.create());
        long todayMerchantUserAdd = Db.selectCountByQuery("admin_user", QueryWrapper.create().ge("created_at", todayStartTime));
        long merchantUserTotal = Db.selectCountByQuery("admin_user", QueryWrapper.create());
        long tricycleTotal = Db.selectCountByQuery("tricycle", QueryWrapper.create().eq("check_status", 1));
        long onShelfShopTotal = Db.selectCountByQuery("shop", QueryWrapper.create().eq("on_shelf", 1));

        StatisticInfoBo statisticInfoBo = new StatisticInfoBo();
        statisticInfoBo.setTodayParkTotal(Math.toIntExact(todayParkTotal));
        statisticInfoBo.setNowParkCount(nowParkCount);
        statisticInfoBo.setTodayUserAdd(Math.toIntExact(todayUserAdd));
        statisticInfoBo.setUserTotal(Math.toIntExact(userTotal));
        statisticInfoBo.setTodayMerchantUserAdd(Math.toIntExact(todayMerchantUserAdd));
        statisticInfoBo.setMerchantUserTotal(Math.toIntExact(merchantUserTotal));
        statisticInfoBo.setTricycleTotal(Math.toIntExact(tricycleTotal));
        statisticInfoBo.setOnShelfShopTotal(Math.toIntExact(onShelfShopTotal));

        return statisticInfoBo;
    }

    public List<LocalDateTime> dateRange(List<Long> range) {
        if (range.isEmpty()) {
            throw new BaseException("日期范围不能为空");
        }
        if (range.size() != 2) {
            throw new BaseException("日期范围必须为2个");
        }
        if (range.get(0) > range.get(1)) {
            throw new BaseException("日期范围不合法");
        }
        LocalDateTime start = DateUtils.parseLocalDateTime(Long.parseLong(range.getFirst().toString()));
        LocalDateTime end = DateUtils.parseLocalDateTime(Long.parseLong(range.getLast().toString()));
        return Arrays.asList(start, end);
    }

    public List<LcParkCountBo> lcParkCountList(DateRangeDto dto) {
        List<LocalDateTime> range = dateRange(dto.getDateRange());
        return lcManager.fetchLcParkCountList(range.getFirst(), range.getLast()).stream().map(l -> BeanUtils.copy(l, LcParkCountBo.class)).toList();
    }

    public List<DashboardCountBo> clientUser(DateRangeDto dto) {
        List<LocalDateTime> range = dateRange(dto.getDateRange());
        long total = Db.selectCountByQuery("client_user", QueryWrapper.create().lt("created_at", range.getFirst()));
        Map<String, Integer> collect = Db.selectListByQuery("client_user", QueryWrapper.create()
                        .select("TO_CHAR(created_at, 'YY-MM-DD') as date", "count(*) as add")
                        .ge("created_at", range.getFirst())
                        .le("created_at", range.getLast())
                        .groupBy("date")
                ).stream()
                .collect(Collectors.toMap(r -> r.getString("date"), r -> r.getInt("add")));

        return getDashboardCountBos(range, total, collect);
    }

    public List<DashboardCountBo> merchantUser(DateRangeDto dto) {
        List<LocalDateTime> range = dateRange(dto.getDateRange());
        long total = Db.selectCountByQuery("admin_user", QueryWrapper.create().lt("created_at", range.getFirst()));
        Map<String, Integer> collect = Db.selectListByQuery("admin_user", QueryWrapper.create()
                        .select("TO_CHAR(created_at, 'YY-MM-DD') as date", "count(*) as add")
                        .ge("created_at", range.getFirst())
                        .le("created_at", range.getLast())
                        .groupBy("date")
                ).stream()
                .collect(Collectors.toMap(r -> r.getString("date"), r -> r.getInt("add")));

        return getDashboardCountBos(range, total, collect);
    }


    public List<DashboardCountBo> tricycle(DateRangeDto dto) {
        List<LocalDateTime> range = dateRange(dto.getDateRange());
        long total = Db.selectCountByQuery("tricycle", QueryWrapper.create().lt("created_at", range.getFirst()));
        Map<String, Integer> collect = Db.selectListByQuery("tricycle", QueryWrapper.create()
                        .select("TO_CHAR(effective_at, 'YY-MM-DD') as date", "count(*) as add")
                        .ge("effective_at", range.getFirst())
                        .le("effective_at", range.getLast())
                        .eq("check_status", 1)
                        .groupBy("date")
                ).stream()
                .collect(Collectors.toMap(r -> r.getString("date"), r -> r.getInt("add")));

        return getDashboardCountBos(range, total, collect);
    }


    @NotNull
    private List<DashboardCountBo> getDashboardCountBos(List<LocalDateTime> range, long total, Map<String, Integer> collect) {
        List<DashboardCountBo> dashboardCountBos = new ArrayList<>();

        long until = range.getFirst().until(range.getLast(), ChronoUnit.DAYS);
        for (int i = 0; i < until; i++) {
            LocalDateTime localDateTime = range.getFirst().plusDays(i);
            String date = DateUtils.formatLocalDate(LocalDate.from(localDateTime), "yy-MM-dd");
            Integer add = collect.get(date);
            total += add == null ? 0 : add;
            DashboardCountBo dashboardCountBo = new DashboardCountBo();
            dashboardCountBo.setDate(date);
            dashboardCountBo.setAdd(add == null ? 0 : add);
            dashboardCountBo.setTotal(Math.toIntExact(total));
            dashboardCountBos.add(dashboardCountBo);
        }

        return dashboardCountBos;
    }

}
