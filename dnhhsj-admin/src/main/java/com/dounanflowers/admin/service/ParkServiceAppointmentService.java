package com.dounanflowers.admin.service;

import com.dounanflowers.admin.bo.ParkServiceAppointmentBo;
import com.dounanflowers.admin.bo.ParkServiceBo;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.dto.MsgSendByTemplateDto;
import com.dounanflowers.common.dto.ParkServiceAppointmentCreateDto;
import com.dounanflowers.common.dto.ParkServiceAppointmentStatusDto;
import com.dounanflowers.common.entity.AdminUser;
import com.dounanflowers.common.entity.ParkService;
import com.dounanflowers.common.entity.ParkServiceAppointment;
import com.dounanflowers.common.entity.ParkServiceTimes;
import com.dounanflowers.common.enums.AppointmentStatusEnum;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.common.manager.JobPostingManager;
import com.dounanflowers.common.manager.SystemManager;
import com.dounanflowers.common.service.MsgService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.DateUtils;
import com.dounanflowers.security.utils.SecurityHolder;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ParkServiceAppointmentService {

    private final JobPostingManager jobPostingManager;
    private final AdminUserManager adminUserManager;
    private final MsgService msgService;
    private final ParkServiceService parkServiceService;
    private final SystemManager systemManager;

    public ParkServiceAppointmentBo create(Long userId, ParkServiceAppointmentCreateDto dto) {
        ParkServiceAppointment appointment = BeanUtils.copy(dto, ParkServiceAppointment.class);
        ParkService service = jobPostingManager.fetchParkServiceById(dto.getServiceId());
        if (service == null) {
            throw new BaseException("服务不存在");
        }
        if (dto.getStartTime() == null) {
            throw new BaseException("开始时间不能为空");
        }

        // Check if the appointment time falls within any of the available time slots
        LocalTime appointmentTime = dto.getStartTime().toLocalTime();
        boolean isWithinTimeSlot = false;
        
        for (ParkServiceTimes timeSlot : service.getTimes()) {
            LocalTime slotStartTime = LocalTime.parse(timeSlot.getStartTime());
            LocalTime slotEndTime = LocalTime.parse(timeSlot.getEndTime());
            
            if (!appointmentTime.isBefore(slotStartTime) && !appointmentTime.isAfter(slotEndTime)) {
                isWithinTimeSlot = true;
                break;
            }
        }
        
        if (!isWithinTimeSlot) {
            throw new BaseException("预约时间不在服务时间范围内");
        }

        appointment.setUserId(userId);
        appointment.setStatus(AppointmentStatusEnum.PENDING);
        appointment.setEndTime(dto.getStartTime().plusMinutes(service.getMinutes()));
        jobPostingManager.saveAppointment(appointment);
        return toAppointmentBo(appointment);
    }

    public Page<ParkServiceAppointmentBo> pageList(PageRequest pageRequest) {
        Page<ParkServiceAppointment> page = jobPostingManager.pageAppointments(pageRequest);
        List<Long> userIds = page.getList().stream().map(ParkServiceAppointment::getUserId).collect(Collectors.toList());
        userIds.addAll(page.getList().stream().map(ParkServiceAppointment::getCheckUserId).toList());
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(userIds);
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));

        List<Long> serviceIds = page.getList().stream().map(ParkServiceAppointment::getServiceId).collect(Collectors.toList());
        List<ParkService> services = jobPostingManager.fetchParkServiceByIds(serviceIds);
        Map<Long, ParkService> serviceMap = services.stream().collect(Collectors.toMap(ParkService::getId, v -> v));

        return page.convert(v -> {
            ParkServiceAppointmentBo bo = BeanUtils.copy(v, ParkServiceAppointmentBo.class);
            AdminUser adminUser = adminUserMap.get(v.getUserId());
            if (adminUser != null) {
                bo.setUserObj(BeanUtils.copy(adminUser, AdminUserBo.class));
            }
            AdminUser checkUser = adminUserMap.get(v.getCheckUserId());
            if (checkUser != null) {
                bo.setCheckUserObj(BeanUtils.copy(checkUser, AdminUserBo.class));
            }
            ParkService service = serviceMap.get(v.getServiceId());
            if (service != null) {
                ParkServiceBo copy = BeanUtils.copy(service, ParkServiceBo.class);
                copy.setImages(service.getImages());
                bo.setServiceObj(copy);
            }
            return bo;
        });
    }

    public void updateStatus(ParkServiceAppointmentStatusDto dto) {
        ParkServiceAppointment appointment = BeanUtils.copy(dto, ParkServiceAppointment.class);
        if (appointment.getStatus() == AppointmentStatusEnum.ACCEPTED || appointment.getStatus() == AppointmentStatusEnum.REJECTED) {
            Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
            appointment.setCheckUserId(userId);
            if (appointment.getStatus() == AppointmentStatusEnum.ACCEPTED) {
                ParkServiceAppointment db = jobPostingManager.fetchAppointmentById(appointment.getId());
                Object remark = systemManager.fetchSettingMap().get("Job").get("jobServiceAppointmentSuccessNotice");
                if (remark == null) {
                    remark = "";
                }
                ParkServiceBo parkServiceBo = parkServiceService.get(appointment.getServiceId());
                Map<String, Object> ctx = new HashMap<>();
                ctx.put("name", parkServiceBo.getName());
                ctx.put("date", DateUtils.formatDateTime(db.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
                ctx.put("remark", remark);
                MsgSendByTemplateDto send = new MsgSendByTemplateDto()
                        .setTemplateCode("parkServiceApp")
                        .setContext(ctx)
                        .setOuterId(appointment.getId())
                        .setOuterType("parkServiceApp")
                        .setSendUserId(userId)
                        .setReceiveUserIds(Lists.newArrayList(db.getUserId()));
                msgService.sendMessageByTemplate(send);
            }
        }
        jobPostingManager.saveAppointment(appointment);
    }

    private ParkServiceAppointmentBo toAppointmentBo(ParkServiceAppointment entity) {
        if (entity == null) {
            return null;
        }
        ParkServiceAppointmentBo bo = BeanUtils.copy(entity, ParkServiceAppointmentBo.class);
        if (entity.getUserId() != null) {
            AdminUser adminUser = adminUserManager.fetchById(entity.getUserId());
            if (adminUser != null) {
                bo.setUserObj(BeanUtils.copy(adminUser, AdminUserBo.class));
            }
        }
        if (entity.getCheckUserId() != null) {
            AdminUser checkUser = adminUserManager.fetchById(entity.getCheckUserId());
            if (checkUser != null) {
                bo.setCheckUserObj(BeanUtils.copy(checkUser, AdminUserBo.class));
            }
        }
        if (entity.getServiceId() != null) {
            ParkService service = jobPostingManager.fetchParkServiceById(entity.getServiceId());
            if (service != null) {
                ParkServiceBo copy = BeanUtils.copy(service, ParkServiceBo.class);
                copy.setImages(service.getImages());
                bo.setServiceObj(copy);
            }
        }
        return bo;
    }

    public ParkServiceAppointmentBo get(Long id) {
        ParkServiceAppointment entity = jobPostingManager.fetchAppointmentById(id);
        if (entity == null) {
            throw new BaseException("用工预约不存在");
        }
        return toAppointmentBo(entity);
    }
}
