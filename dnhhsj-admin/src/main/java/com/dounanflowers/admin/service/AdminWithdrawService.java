package com.dounanflowers.admin.service;

import com.dounanflowers.admin.bo.WithdrawAccountBo;
import com.dounanflowers.admin.bo.WithdrawAccountRecordBo;
import com.dounanflowers.admin.bo.WithdrawConfigBo;
import com.dounanflowers.admin.bo.WithdrawRecordBo;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.dto.MsgSendByTemplateDto;
import com.dounanflowers.common.dto.WithdrawApproveDto;
import com.dounanflowers.common.dto.WithdrawCreateDto;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.common.manager.SystemManager;
import com.dounanflowers.common.manager.WithdrawManager;
import com.dounanflowers.common.service.FileService;
import com.dounanflowers.common.service.MsgService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.utils.SecurityHolder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AdminWithdrawService {

    private final WithdrawManager withdrawManager;

    private final AdminUserManager adminUserManager;

    private final SystemManager systemManager;

    private final FileService fileService;

    private final MsgService msgService;

    public WithdrawAccountBo getAccount(Long userId) {
        WithdrawAccount account = withdrawManager.getAccountByUserId(userId);
        WithdrawAccountBo bo = new WithdrawAccountBo();
        bo.setAvailableAmount(account.getAvailableAmount());
        bo.setFrozenAmount(account.getFrozenAmount());
        bo.setTotalAmount(account.getAvailableAmount() + account.getFrozenAmount());
        return bo;
    }

    public Page<WithdrawAccountBo> getAccountPage(PageRequest pageRequest) {
        Page<WithdrawAccount> page = withdrawManager.getAccountPage(pageRequest);
        List<Long> userIds = page.getList().stream().map(WithdrawAccount::getUserId).collect(Collectors.toList());
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(userIds);
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));
        return page.convert(account -> {
            WithdrawAccountBo bo = BeanUtils.copy(account, WithdrawAccountBo.class);
            bo.setTotalAmount(account.getAvailableAmount() + account.getFrozenAmount());
            AdminUser adminUser = adminUserMap.get(account.getUserId());
            if (adminUser != null) {
                bo.setUserObj(BeanUtils.copy(adminUser, AdminUserBo.class));
            }
            return bo;
        });
    }

    public WithdrawConfigBo getConfig(Long userId) {
        WithdrawConfig config = withdrawManager.getConfig(userId);
        if (config == null) {
            return null;
        }
        return BeanUtils.copy(config, WithdrawConfigBo.class);
    }

    public void saveConfig(WithdrawConfigBo configBo) {
        WithdrawConfig config = BeanUtils.copy(configBo, WithdrawConfig.class);
        withdrawManager.saveConfig(config);
    }

    public Page<WithdrawRecordBo> getRecordPage(PageRequest pageRequest) {
        Page<WithdrawRecord> page = withdrawManager.getRecordPage(pageRequest);
        List<Long> userIds = page.getList().stream().map(WithdrawRecord::getUserId).collect(Collectors.toList());
        userIds.addAll(page.getList().stream().map(WithdrawRecord::getCheckAdminUserId).collect(Collectors.toList()));
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(userIds);
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));
        return page.convert(record -> {
            WithdrawRecordBo bo = BeanUtils.copy(record, WithdrawRecordBo.class);
            bo.setFiles(record.getFiles());
            AdminUser adminUser = adminUserMap.get(record.getUserId());
            if (adminUser != null) {
                bo.setUserObj(BeanUtils.copy(adminUser, AdminUserBo.class));
            }
            adminUser = adminUserMap.get(record.getCheckAdminUserId());
            if (adminUser != null) {
                bo.setCheckAdminUserObj(BeanUtils.copy(adminUser, AdminUserBo.class));
            }
            return bo;
        });
    }

    public WithdrawRecordBo create(WithdrawCreateDto createDto) {
        Integer amount = createDto.getAmount();
        if (amount == null || amount <= 0) {
            throw new BaseException("提现金额不正确");
        }
        Map<String, Map<String, Object>> settingMap = systemManager.fetchSettingMap();
        Object o = settingMap.get("Merchant").get("withdrawFeeRate") == null ? 0 : settingMap.get("Merchant").get("withdrawFeeRate");
        int withdrawFeeRate = Integer.parseInt(o.toString());
        WithdrawRecord record = BeanUtils.copy(createDto, WithdrawRecord.class);
        record.setFee(amount * withdrawFeeRate / 10000);
        withdrawManager.createWithdrawRecord(record);

        // 如果需要保存为默认配置
        if (Boolean.TRUE.equals(createDto.getSaveAsConfig())) {
            WithdrawConfig config = withdrawManager.getConfig(createDto.getUserId());
            if (config == null) {
                config = new WithdrawConfig();
                config.setUserId(createDto.getUserId());
            }

            config.setName(createDto.getName())
                    .setBank(createDto.getBank())
                    .setBranch(createDto.getBranch())
                    .setNumber(createDto.getNumber());

            withdrawManager.saveConfig(config);
        }

        return BeanUtils.copy(record, WithdrawRecordBo.class);
    }

    public void approve(WithdrawApproveDto approveDto) {
        Long adminUserId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        WithdrawRecord record = withdrawManager.approveWithdraw(
                approveDto.getRecordId(),
                adminUserId,
                approveDto.getRemark());
        fileService.updateFileLink(approveDto.getRecordId(), "files", approveDto.getFiles());
        MsgSendByTemplateDto template = new MsgSendByTemplateDto();
        template.setSendUserId(adminUserId);
        Map<String, Object> context = JsonUtils.toMap(JsonUtils.toJson(record));
        context.put("statusName", "已通过");
        context.put("amount", record.getAmount() / 100.0);
        template.setContext(context);
        template.setOuterType("withdrawRecord");
        template.setOuterId(record.getId());
        template.setReceiveUserIds(List.of(record.getUserId()));
        template.setTemplateCode("withdraw");
        msgService.sendMessageByTemplate(template);
    }

    public void reject(WithdrawApproveDto approveDto) {
        Long adminUserId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        WithdrawRecord record = withdrawManager.rejectWithdraw(
                approveDto.getRecordId(),
                adminUserId,
                approveDto.getRemark());
        fileService.updateFileLink(approveDto.getRecordId(), "files", approveDto.getFiles());
        MsgSendByTemplateDto template = new MsgSendByTemplateDto();
        template.setSendUserId(adminUserId);
        Map<String, Object> context = JsonUtils.toMap(JsonUtils.toJson(record));
        context.put("statusName", "已拒绝");
        context.put("amount", record.getAmount() / 100.0);
        template.setContext(context);
        template.setOuterType("withdrawRecord");
        template.setOuterId(record.getId());
        template.setReceiveUserIds(List.of(record.getUserId()));
        template.setTemplateCode("withdraw");
        msgService.sendMessageByTemplate(template);
    }

    public Page<WithdrawAccountRecordBo> getAccountRecordPage(PageRequest pageRequest) {
        Page<WithdrawAccountRecord> page = withdrawManager.getAccountRecordPage(pageRequest);
        List<Long> userIds = page.getList().stream().map(WithdrawAccountRecord::getUserId).collect(Collectors.toList());
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(userIds);
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));
        List<Long> relatedIds = page.getList().stream().map(WithdrawAccountRecord::getRelatedId).collect(Collectors.toList());
        List<WithdrawRecord> relatedRecords = withdrawManager.fetchByIds(relatedIds);
        Map<Long, WithdrawRecord> relatedRecordMap = relatedRecords.stream().collect(Collectors.toMap(WithdrawRecord::getId, v -> v));
        return page.convert(record -> {
            WithdrawAccountRecordBo bo = BeanUtils.copy(record, WithdrawAccountRecordBo.class);
            AdminUser adminUser = adminUserMap.get(record.getUserId());
            if (adminUser != null) {
                bo.setUserObj(BeanUtils.copy(adminUser, AdminUserBo.class));
            }
            WithdrawRecord relatedRecord = relatedRecordMap.get(record.getRelatedId());
            if (relatedRecord != null) {
                bo.setRelatedObj(BeanUtils.copy(relatedRecord, WithdrawRecordBo.class));
            }
            return bo;
        });
    }
}
