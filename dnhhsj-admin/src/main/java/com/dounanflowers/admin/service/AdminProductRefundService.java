package com.dounanflowers.admin.service;

import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.ProductOrderForceRefundDto;
import com.dounanflowers.common.dto.ProductRefundHandleDto;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.OrderStatusEnum;
import com.dounanflowers.common.enums.RefundStatusEnum;
import com.dounanflowers.common.manager.*;
import com.dounanflowers.common.service.ProductRefundService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AdminProductRefundService {

    private final ProductRefundService refundService;
    private final ProductRefundManager refundManager;
    private final ProductManager productManager;
    private final StoreManager storeManager;
    private final ClientUserManager clientUserManager;
    private final BillManager billManager;

    public Page<ProductRefundApplyBo> pageList(PageRequest dto) {
        Page<ProductRefundApply> page = refundManager.pageList(dto);

        // 获取关联数据
        List<Long> userIds = page.getList().stream().map(ProductRefundApply::getUserId).toList();
        List<Long> productIds = page.getList().stream().map(ProductRefundApply::getProductId).toList();
        List<Long> storeIds = page.getList().stream().map(ProductRefundApply::getStoreId).toList();
        List<Long> orderIds = page.getList().stream().map(ProductRefundApply::getOrderId).toList();

        List<ClientUser> users = clientUserManager.fetchByIds(userIds);
        List<Product> products = productManager.fetchProductByIdsWithRelation(productIds);
        List<Store> stores = storeManager.fetchByIds(storeIds);
        List<ProductOrder> orders = productManager.fetchOrderByIds(orderIds);

        Map<Long, ClientUser> userMap = users.stream().collect(Collectors.toMap(ClientUser::getId, v -> v));
        Map<Long, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, v -> v));
        Map<Long, Store> storeMap = stores.stream().collect(Collectors.toMap(Store::getId, v -> v));
        Map<Long, ProductOrder> orderMap = orders.stream().collect(Collectors.toMap(ProductOrder::getId, v -> v));

        return page.convert(refund -> {
            ProductRefundApplyBo bo = BeanUtils.copy(refund, ProductRefundApplyBo.class);
            bo.setImages(refund.getImages());

            ClientUser user = userMap.get(refund.getUserId());
            if (user != null) {
                bo.setUserObj(BeanUtils.copy(user, ClientUserBo.class));
            }

            Product product = productMap.get(refund.getProductId());
            if (product != null) {
                bo.setProductObj(BeanUtils.copy(product, ProductBo.class));
            }

            Store store = storeMap.get(refund.getStoreId());
            if (store != null) {
                bo.setStoreObj(BeanUtils.copy(store, StoreBo.class));
            }

            ProductOrder order = orderMap.get(refund.getOrderId());
            if (order != null) {
                bo.setOrderObj(BeanUtils.copy(order, ProductOrderBo.class));
            }

            return bo;
        });
    }

    public ProductRefundApplyBo getById(Long id) {
        ProductRefundApply refund = refundManager.fetchById(id);
        if (refund == null) {
            throw new BaseException("退款申请不存在");
        }

        ProductRefundApplyBo bo = BeanUtils.copy(refund, ProductRefundApplyBo.class);
        bo.setImages(refund.getImages());

        // 填充关联数据
        ClientUser user = clientUserManager.fetchById(refund.getUserId());
        if (user != null) {
            bo.setUserObj(BeanUtils.copy(user, ClientUserBo.class));
        }

        Product product = productManager.fetchProductByIdWithRelation(refund.getProductId());
        if (product != null) {
            bo.setProductObj(BeanUtils.copy(product, ProductBo.class));
        }

        Store store = storeManager.fetchByIdWithRelation(refund.getStoreId());
        if (store != null) {
            bo.setStoreObj(BeanUtils.copy(store, StoreBo.class));
        }

        ProductOrder order = productManager.getOrderWithItems(refund.getOrderId());
        if (order != null) {
            bo.setOrderObj(BeanUtils.copy(order, ProductOrderBo.class));
        }

        return bo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleRefund(ProductRefundHandleDto dto) {
        ProductRefundApply refund = refundManager.fetchById(dto.getId());
        if (refund == null) {
            throw new BaseException("退款申请不存在");
        }

        if (refund.getStatus() != RefundStatusEnum.PENDING) {
            throw new BaseException("当前状态不可处理");
        }

        refundManager.updateStatus(dto.getId(), dto.getStatus(), dto.getHandleNote());
        if (dto.getStatus() == RefundStatusEnum.APPROVED) {
            refundService.handleRefund(refund);
        } else if (dto.getStatus() == RefundStatusEnum.REJECTED) {
            ProductOrder order = productManager.getOrderWithItems(refund.getOrderId());
            if (order == null) {
                throw new BaseException("订单不存在");
            }
            if (order.getStatus() != OrderStatusEnum.REFUNDING) {
                throw new BaseException("当前订单状态不可处理");
            }
            order.setStatus(OrderStatusEnum.PAID);
            productManager.updateOrder(order);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void forceRefund(ProductOrderForceRefundDto dto) {
        refundService.forceRefund(dto);
    }

}
