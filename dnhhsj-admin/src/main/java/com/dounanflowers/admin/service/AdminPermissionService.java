package com.dounanflowers.admin.service;

import com.dounanflowers.admin.bo.AdminPermissionBo;
import com.dounanflowers.admin.dto.AdminPermissionDto;
import com.dounanflowers.common.entity.AdminPermission;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.utils.BeanUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AdminPermissionService {

    private final AdminUserManager adminUserManager;

    public Page<AdminPermissionBo> pageList(PageRequest request) {
        Page<AdminPermission> page = adminUserManager.pagePermissions(request);
        return page.convert(v -> BeanUtils.copy(v, AdminPermissionBo.class));
    }

    public void save(AdminPermissionDto dto) {
        AdminPermission permission = BeanUtils.copy(dto, AdminPermission.class);
        if (dto.getId() == null) {
            // 新增时检查编码是否重复
            AdminPermission existingPermission = adminUserManager.getPermissionByCode(permission.getCode());
            if (existingPermission != null) {
                throw new IllegalArgumentException("权限编码已存在");
            }
        }
        adminUserManager.savePermission(permission);
    }

    public AdminPermissionBo detail(Long id) {
        AdminPermission permission = adminUserManager.getPermissionById(id);
        return BeanUtils.copy(permission, AdminPermissionBo.class);
    }

    public void delete(Long id) {
        AdminPermission permission = adminUserManager.getPermissionById(id);
        // 检查权限是否被使用
        if (adminUserManager.isPermissionInUse(permission.getCode())) {
            throw new IllegalArgumentException("权限已被使用，无法删除");
        }
        adminUserManager.deletePermission(id);
    }

    public List<AdminPermissionBo> search(String keyword) {
        return adminUserManager.searchPermissions(keyword)
                .stream()
                .map(v -> BeanUtils.copy(v, AdminPermissionBo.class))
                .collect(Collectors.toList());
    }

}
