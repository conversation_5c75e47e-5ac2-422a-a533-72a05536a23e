package com.dounanflowers.admin.service;

import com.dounanflowers.admin.bo.*;
import com.dounanflowers.admin.dto.ParkCarInDto;
import com.dounanflowers.admin.dto.ParkCarOutDto;
import com.dounanflowers.admin.dto.ParkHeartBeatDto;
import com.dounanflowers.admin.dto.ParkPassageWayUpsertDto;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.manager.ParkManager;
import com.dounanflowers.framework.bean.BlueCardResult;
import com.dounanflowers.framework.utils.BeanUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ParkService {
    private final ParkManager parkManager;

    public BlueCardResult heartBeat(ParkHeartBeatDto parkHeartBeatDto) {
        LcPark lcPark = parkManager.fetchLcParkByParkNumber(parkHeartBeatDto.getParkNumber());
        if (lcPark == null) {
            lcPark = new LcPark();
        }
        BeanUtils.copyProperties(parkHeartBeatDto, lcPark);
        parkManager.saveLcPark(lcPark);

        List<ParkAreaBo> areaListBoList = parkHeartBeatDto.getAreaList();
        if (areaListBoList != null) {
            List<LcArea> lcAreas = parkManager.fetchLcAreasByParkNumber(lcPark.getParkNumber());
            Map<String, LcArea> lcAreaMap = lcAreas.stream()
                    .collect(Collectors.toMap(LcArea::getAreaId, area -> area));
            for (ParkAreaBo parkAreaBo : areaListBoList) {
                LcArea lcArea = lcAreaMap.get(String.valueOf(parkAreaBo.getAreaId()));
                if (lcArea == null) {
                    lcArea = new LcArea();
                }
                BeanUtils.copyProperties(parkAreaBo, lcArea);
                lcArea.setLcParkId(lcPark.getId());
                lcArea.setParkNumber(lcPark.getParkNumber());
                parkManager.saveLcArea(lcArea);
            }
        }

        List<ParkDeviceBo> deviceList = parkHeartBeatDto.getDeviceList();
        if (deviceList != null) {
            List<LcDevice> lcDevices = parkManager.fetchLcDevicesByParkNumber(lcPark.getParkNumber());
            Map<Integer, LcDevice> lcDeviceMap = lcDevices.stream()
                    .collect(Collectors.toMap(LcDevice::getDeviceId, device -> device));
            for (ParkDeviceBo parkDeviceBo : deviceList) {
                LcDevice lcDevice = lcDeviceMap.get(parkDeviceBo.getDeviceId());
                if (lcDevice == null) {
                    lcDevice = new LcDevice();
                }
                BeanUtils.copyProperties(parkDeviceBo, lcDevice);
                lcDevice.setLcParkId(lcPark.getId());
                lcDevice.setParkNumber(lcPark.getParkNumber());
                parkManager.saveLcDevice(lcDevice);
            }
        }

        return BlueCardResult.success();
    }

    public BlueCardResult carIn(ParkCarInDto parkCarInDto) {
        LcPark lcPark = parkManager.fetchLcParkByParkNumber(parkCarInDto.getParkNumber());
        if (lcPark == null) {
            return BlueCardResult.fail("停车场不存在");
        }
        List<ParkCarInDto.ParkCarInDataDto> datas = parkCarInDto.getDatas();
        if (datas != null) {
            for (ParkCarInDto.ParkCarInDataDto data : datas) {
                // 如果有修改记录，则更新
                if (data.getModifyMemo() != null && data.getModifyMemo().getOldId() != null) {
                    String oldId = data.getModifyMemo().getOldId();
                    String newId = data.getModifyMemo().getNewId();
                    String oldPlate = data.getModifyMemo().getOldPlate();
                    String newPlate = data.getModifyMemo().getNewPlate();
                    LcOrder lcOrder = parkManager.fetchLcOrderByBcOrderId(oldId);
                    if (lcOrder != null) {
                        lcOrder.setBcOrderId(newId);
                        lcOrder.setPlate(newPlate);
                        parkManager.saveLcOrder(lcOrder);

                        LcOrderModify lcOrderModify = new LcOrderModify();
                        lcOrderModify.setLcOrderId(lcOrder.getId());
                        lcOrderModify.setOldBcOrderId(oldId);
                        lcOrderModify.setOldPlate(oldPlate);
                        lcOrderModify.setNewBcOrderId(newId);
                        lcOrderModify.setNewPlate(newPlate);
                        lcOrderModify.setModifyTime(LocalDateTime.now());
                        parkManager.saveLcOrderModify(lcOrderModify);
                    }
                }

                LcOrder lcOrder = parkManager.fetchLcOrderByBcOrderId(data.getOrderId());
                if (lcOrder == null) {
                    lcOrder = new LcOrder();
                }
                //                lcOrder.setBcOrderId(data.getOrderId());
                //                lcOrder.setPlate(data.getPlate());
                //                lcOrder.setTicketCode(data.getTicketCode());
                //                lcOrder.setPlateColor(data.getPlateColor());
                //                lcOrder.setInTime(LocalDateTime.parse(data.getInTime()));
                //                lcOrder.setInChannel(data.getInChannel());
                //                lcOrder.setInImage(data.getInImage());
                //                lcOrder.setVisitReason(data.getVisitReason());
                //                lcOrder.setOpenGateMode(data.getOpenGateMode());
                //                lcOrder.setMatchMode(data.getMatchMode());
                //                lcOrder.setConfidence(data.getConfidence());
                //                lcOrder.setCarType(data.getCarType());
                BeanUtils.copyProperties(data, lcOrder);
                lcOrder.setBcOrderId(data.getOrderId());
                lcOrder.setInTime(LocalDateTime.parse(data.getInTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                lcOrder.setUserInfoIdCard(data.getUserInfo().getIdCard());
                lcOrder.setUserInfoUserName(data.getUserInfo().getUserName());
                lcOrder.setUserInfoPhone(data.getUserInfo().getPhone());
                lcOrder.setUserInfoAddress(data.getUserInfo().getAddress());
                List<ParkPlaceInfoBo> placeList = data.getPlaceInfo();

                if (placeList != null) {
                    ParkPlaceInfoBo first = placeList.getFirst();
                    if (first != null) {
                        LcArea lcArea = parkManager.fetchLcAreasByAreaId(first.getAreaId());
                        if (lcArea != null) {
                            lcOrder.setLcAreaId(lcArea.getId());
                        }
                    }
                }


                lcOrder.setBarriorOpen(data.getBarriorOpen());
                lcOrder.setInCostTime(Integer.valueOf(data.getCostTime()));
                lcOrder.setSpaceCount(Integer.valueOf(data.getSpaceCount()));

                List<String> imageList = data.getImageList();
                if (imageList != null) {
                    lcOrder.setImageList(String.join(",", imageList));
                }

                lcOrder.setImageName(data.getImageName());
                lcOrder.setLcParkId(lcPark.getId());
                lcOrder.setParkNumber(lcPark.getParkNumber());
                parkManager.saveLcOrder(lcOrder);

            }
        }


        return BlueCardResult.success();
    }

    public BlueCardResult carOut(ParkCarOutDto parkCarOutDto) {
        if (parkCarOutDto.getDatas() != null) {
            for (ParkCarOutDto.ParkCarOutDataDto data : parkCarOutDto.getDatas()) {
                LcOrder lcOrder = parkManager.fetchLcOrderByBcOrderId(data.getOrderId());
                if (lcOrder != null) {
                    lcOrder.setOperatorId(data.getOperatorId());
                    lcOrder.setOperatorName(data.getOperatorName());
                    lcOrder.setInvoiceNo(data.getInvoiceNo());

                    ParkPassInfoBo passInfo = data.getPassInfo();
                    if (passInfo != null) {
                        lcOrder.setOutTime(LocalDateTime.parse(passInfo.getOutTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        lcOrder.setOutImage(passInfo.getOutImage());
                        lcOrder.setOutChannel(passInfo.getOutChannel());
                    }

                    ParkOrderChargeStatisticsBo chargeStatistics = data.getChargeStatistics();
                    if (chargeStatistics != null) {
                        BeanUtils.copyProperties(chargeStatistics, lcOrder);
                    }

                    lcOrder.setOutCostTime(data.getCostTime());

                    parkManager.saveLcOrder(lcOrder);

                    parkManager.deleteLcOrderChargeByLcOrderId(lcOrder.getId());
                    List<ParkOrderChargeBo> chargeList = data.getChargeList();
                    if (chargeList != null) {
                        for (ParkOrderChargeBo charge : chargeList) {
                            LcOrderCharge lcOrderCharge = BeanUtils.copy(charge, LcOrderCharge.class);
                            lcOrderCharge.setLcOrderId(lcOrder.getId());
                            lcOrderCharge.setGetTime(LocalDateTime.parse(charge.getGetTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                            parkManager.saveLcOrderCharge(lcOrderCharge);
                        }
                    }

                    parkManager.deleteLcOrderProfitByLcOrderId(lcOrder.getId());
                    List<ParkOrderProfitBo> profitList = data.getProfitList();
                    if (profitList != null) {
                        for (ParkOrderProfitBo profit : profitList) {
                            LcOrderProfit lcOrderProfit = BeanUtils.copy(profit, LcOrderProfit.class);
                            lcOrderProfit.setLcOrderId(lcOrder.getId());
                            lcOrderProfit.setGetTime(LocalDateTime.parse(profit.getGetTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                            parkManager.saveLcOrderProfit(lcOrderProfit);
                        }

                    }
                }

            }
        }
        return BlueCardResult.success();
    }

    public BlueCardResult passageWayUpsert(ParkPassageWayUpsertDto parkPassageWayUpsertDto) {
        List<ParkPassageWayBo> datas = parkPassageWayUpsertDto.getDatas();
        LcPark lcPark = parkManager.fetchLcParkByParkNumber(parkPassageWayUpsertDto.getParkNumber());
        if (lcPark == null) {
            return BlueCardResult.fail("停车场不存在");
        }
        if (datas != null) {
            for (ParkPassageWayBo data : datas) {

                LcPassageWay lcPassageWay = parkManager.fetchLcPassageWayByPassagewayId(data.getPassagewayId());
                if (lcPassageWay == null) {
                    lcPassageWay = new LcPassageWay();
                }
                BeanUtils.copyProperties(data, lcPassageWay);
                lcPassageWay.setLcParkId(lcPark.getId());

                parkManager.saveLcPassageWay(lcPassageWay);
            }
        }
        return BlueCardResult.success();
    }


}
