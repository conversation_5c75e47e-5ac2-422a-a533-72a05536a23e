package com.dounanflowers.admin.service;

import com.dounanflowers.admin.bo.AdminRoleBo;
import com.dounanflowers.admin.dto.AdminRoleDto;
import com.dounanflowers.common.entity.AdminRole;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.utils.BeanUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AdminRoleService {

    private final AdminUserManager adminUserManager;

    public Page<AdminRoleBo> pageList(PageRequest request) {
        Page<AdminRole> page = adminUserManager.pageRoles(request);
        return page.convert(v -> BeanUtils.copy(v, AdminRoleBo.class));
    }

    public void save(AdminRoleDto dto) {
        AdminRole role = BeanUtils.copy(dto, AdminRole.class);
        if (dto.getId() == null) {
            // 新增时检查编码是否重复
            AdminRole existingRole = adminUserManager.getRoleByCode(role.getCode());
            if (existingRole != null) {
                throw new IllegalArgumentException("角色编码已存在");
            }
        }
        adminUserManager.saveRole(role, dto.getPermissions());
    }

    public AdminRoleBo detail(Long id) {
        AdminRole role = adminUserManager.getRoleById(id);
        if (role == null) {
            throw new IllegalArgumentException("角色不存在");
        }
        return BeanUtils.copy(role, AdminRoleBo.class);
    }

    public void delete(Long id) {
        // 检查角色是否被使用
        if (adminUserManager.isRoleInUse(id)) {
            throw new IllegalArgumentException("角色已被使用，无法删除");
        }
        adminUserManager.deleteRole(id);
    }

    public List<String> getRolePermissions(Long roleId) {
        AdminRole role = adminUserManager.getRoleById(roleId);
        if (role == null) {
            throw new IllegalArgumentException("角色不存在");
        }
        return adminUserManager.listPermissionCodesByRoleId(role.getCode());
    }

}
