package com.dounanflowers.admin.service;

import com.dounanflowers.admin.bo.CouponBlankBo;
import com.dounanflowers.admin.bo.CouponBlankDetailBo;
import com.dounanflowers.admin.bo.CouponBlankQrcodeBo;
import com.dounanflowers.common.bo.CouponTplBo;
import com.dounanflowers.admin.dto.CouponBlankCreateDto;
import com.dounanflowers.admin.dto.CouponCreateByTplDto;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.bo.CouponBo;
import com.dounanflowers.common.bo.CouponQrCodeInfoBo;
import com.dounanflowers.common.dto.CouponReceiveDto;
import com.dounanflowers.common.entity.AdminUser;
import com.dounanflowers.common.entity.Coupon;
import com.dounanflowers.common.entity.CouponBlank;
import com.dounanflowers.common.entity.CouponTpl;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.common.manager.CouponManager;
import com.dounanflowers.common.service.CouponService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.utils.SecurityHolder;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
public class AdminCouponService {

    private final CouponService couponService;

    private final CouponManager couponManager;

    private final AdminUserManager adminUserManager;

    public CouponTplBo getTplById(Long id) {
        CouponTpl tpl = couponManager.fetchCouponTplById(id);
        CouponTplBo copy = BeanUtils.copy(tpl, CouponTplBo.class);
        if (tpl.getPusherIds() != null) {
            List<AdminUser> adminUsers = adminUserManager.fetchByIds(tpl.getPusherIds());
            copy.setPusherList(BeanUtils.copyList(adminUsers, AdminUserBo.class));
            copy.setPusherIds(tpl.getPusherIds());
        }
        return copy;
    }

    public Boolean deleteTplById(Long id) {
        return couponManager.deleteCouponTplById(id);
    }

    public Page<CouponTplBo> pageTplList(PageRequest pageRequest) {
        Page<CouponTpl> page = couponManager.pageCouponTplList(pageRequest);
        Set<Long> collect = page.getList().stream().map(CouponTpl::getPusherIds).flatMap(Collection::stream).collect(Collectors.toSet());
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(Lists.newArrayList(collect));
        Map<Long, AdminUser> collect1 = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));
        return page.convert(tpl -> {
            CouponTplBo one = BeanUtils.copy(tpl, CouponTplBo.class);
            one.setPusherIds(tpl.getPusherIds());
            List<AdminUserBo> list = Lists.newArrayList();
            for (Long pusherId : one.getPusherIds()) {
                AdminUser adminUser = collect1.get(pusherId);
                if (adminUser != null) {
                    list.add(BeanUtils.copy(adminUser, AdminUserBo.class));
                }
            }
            one.setPusherList(list);
            return one;
        });
    }

    public CouponTplBo saveTpl(CouponTplBo dto) {
        if (dto.getId() != null) {
            CouponTpl db = couponManager.fetchCouponTplById(dto.getId());
            if (db.getReceivedCount() > dto.getPublishCount()) {
                throw new BaseException("优惠券母版数量不足");
            }
            if(db.getIsStackable() != IsEnum.of(dto.getIsStackable())) {
                couponManager.updateCouponStackableByTplId(db.getId(), IsEnum.of(dto.getIsStackable()));
            }
        }
        CouponTpl tpl = BeanUtils.copy(dto, CouponTpl.class);
        tpl.setPusherIds(dto.getPusherIds());
        couponManager.saveCouponTpl(tpl);
        return BeanUtils.copy(tpl, CouponTplBo.class);
    }

    @Transactional(rollbackFor = Exception.class)
    public CouponBlankDetailBo createBlank(CouponBlankCreateDto dto) {
        List<String> codes = couponService.receiveCoupon(new CouponReceiveDto().setCouponTplId(dto.getCouponTplId()).setBlankCount(dto.getCount()).setSourceUserId(dto.getUserId()));
        CouponBlank blank = new CouponBlank();
        blank.setCouponCodes(codes);
        blank.setBelongUserId(dto.getUserId());
        blank.setCount(dto.getCount());
        blank.setCouponTplId(dto.getCouponTplId());
        couponManager.saveCouponBlank(blank);
        CouponBlankDetailBo ret = new CouponBlankDetailBo();
        ret.setCouponBlank(BeanUtils.copy(blank, CouponBlankBo.class));
        ret.setQrCodes(codes.stream().map(code -> {
            CouponQrCodeInfoBo qrcodeBo = new CouponQrCodeInfoBo().setType("blank").setCode(code);
            String qrcode = Base64.getUrlEncoder().encodeToString(JsonUtils.toJson(qrcodeBo).getBytes());
            return "https://minicode.dounanflowers.com#/my/coupon/receive?code=" + qrcode;
        }).toList());
        return ret;
    }

    public Page<CouponBlankBo> blankPage(PageRequest dto) {
        Page<CouponBlank> couponBlankPage = couponManager.pageCouponBlankList(dto);
        List<Long> tplIds = couponBlankPage.getList().stream().map(CouponBlank::getCouponTplId).toList();
        List<CouponTpl> couponTplList = couponManager.fetchCouponTplListByIds(tplIds);
        Map<Long, CouponTpl> tplMap = couponTplList.stream().collect(Collectors.toMap(CouponTpl::getId, v -> v));
        List<Long> userIds = couponBlankPage.getList().stream().map(CouponBlank::getBelongUserId).toList();
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(userIds);
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));
        return couponBlankPage.convert(v -> {
            CouponBlankBo bo = BeanUtils.copy(v, CouponBlankBo.class);
            bo.setCouponCodes(v.getCouponCodes());
            bo.setCouponTplObj(BeanUtils.copy(tplMap.get(v.getCouponTplId()), CouponTplBo.class));
            if (v.getBelongUser() != null) {
                bo.setBelongUser(BeanUtils.copy(v.getBelongUser(), AdminUserBo.class));
            } else if(adminUserMap.containsKey(v.getBelongUserId())) {
                bo.setBelongUser(BeanUtils.copy(adminUserMap.get(v.getBelongUserId()), AdminUserBo.class));
            }
            return bo;
        });
    }

    public Boolean deleteBlank(Long id) {
        return couponManager.deleteCouponBlankById(id);
    }

    public CouponBlankBo saveBlank(CouponBlankBo dto) {
        CouponBlank blank = BeanUtils.copy(dto, CouponBlank.class);
        blank.setCouponCodes(dto.getCouponCodes());
        couponManager.saveCouponBlank(blank);
        return BeanUtils.copy(blank, CouponBlankBo.class);
    }

    public CouponBo saveCoupon(CouponBo dto) {
        Coupon coupon = BeanUtils.copy(dto, Coupon.class);
        if (coupon.getIsInstantGen() == null) {
            coupon.setIsInstantGen(IsEnum.FALSE);
        }
        couponManager.saveCoupon(coupon);
        return BeanUtils.copy(coupon, CouponBo.class);
    }

    public Boolean createCouponByTpl(CouponCreateByTplDto dto) {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        couponService.receiveCoupon(new CouponReceiveDto().setCouponTplId(dto.getCouponTplId()).setUserIds(dto.getUserIds()).setSourceUserId(userId));
        return true;
    }

    public Boolean updateBlank(CouponBlankBo dto) {
        CouponBlank blank = BeanUtils.copy(dto, CouponBlank.class);
        blank.setCouponCodes(dto.getCouponCodes());
        couponManager.saveCouponBlank(blank);
        return true;
    }

    public List<CouponBo> couponListInCouponBlank(Long id) {
        CouponBlank blank = couponManager.fetchCouponBlankById(id);
        if (blank == null || CollectionUtils.isEmpty(blank.getCouponCodes())) {
            return Lists.newArrayList();
        }
        List<Coupon> coupons = couponManager.fetchCouponListByCodes(blank.getCouponCodes());
        return BeanUtils.copyList(coupons, CouponBo.class);
    }

    public CouponBlankQrcodeBo getTplQrCode(Long id) {
        CouponTpl couponTpl = couponManager.fetchAvailableCouponTpl(id, 1);
        Long currentUserId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        if (!couponTpl.getPusherIds().contains(currentUserId)) {
            throw new BaseException("没有此优惠券发券权限");
        }
        String couponCode;
        Coupon coupon = couponManager.fetchLastBlankCoupon(couponTpl.getId(), currentUserId);
        if (coupon != null) {
            couponCode = coupon.getCode();
        } else {
            CouponReceiveDto dto = new CouponReceiveDto().setCouponTplId(couponTpl.getId()).setSourceUserId(currentUserId).setBlankCount(1).setInstantGen(true);
            couponCode = couponService.receiveCoupon(dto).getFirst();
        }
        if (couponCode == null) {
            throw new BaseException("生成优惠券失败");
        }
        CouponQrCodeInfoBo qrcode = new CouponQrCodeInfoBo().setCode(couponCode).setType("blank");
        String code = Base64.getUrlEncoder().encodeToString(JsonUtils.toJson(qrcode).getBytes());
        code = "https://minicode.dounanflowers.com#/my/coupon/receive?code=" + code;
        return new CouponBlankQrcodeBo().setQrCode(code);
    }

    public CouponBlankDetailBo getBlankInfo(Long id) {
        CouponBlank blank = couponManager.fetchCouponBlankById(id);
        if (blank == null) {
            throw new BaseException("优惠券不存在");
        }
        CouponBlankDetailBo ret = new CouponBlankDetailBo();
        ret.setCouponBlank(BeanUtils.copy(blank, CouponBlankBo.class));
        ret.setQrCodes(blank.getCouponCodes().stream().map(code -> {
            CouponQrCodeInfoBo qrcodeBo = new CouponQrCodeInfoBo().setType("blank").setCode(code);
            String qrcode = Base64.getUrlEncoder().encodeToString(JsonUtils.toJson(qrcodeBo).getBytes());
            return "https://minicode.dounanflowers.com#/my/coupon/receive?code=" + qrcode;
        }).toList());
        return ret;
    }

    public CouponTplBo getTplByCode(String code) {
        CouponTpl tpl = couponManager.fetchCouponTplByCode(code);
        if (tpl == null) {
            throw new BaseException("优惠券不存在");
        }
        CouponTplBo copy = BeanUtils.copy(tpl, CouponTplBo.class);
        if (tpl.getPusherIds() != null) {
            List<AdminUser> adminUsers = adminUserManager.fetchByIds(tpl.getPusherIds());
            copy.setPusherList(BeanUtils.copyList(adminUsers, AdminUserBo.class));
            copy.setPusherIds(tpl.getPusherIds());
        }
        return copy;
    }
}
