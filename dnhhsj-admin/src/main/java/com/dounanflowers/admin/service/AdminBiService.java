package com.dounanflowers.admin.service;

import com.dounanflowers.admin.bo.BiTradingCountBo;
import com.dounanflowers.admin.dto.BiPeopleReportDto;
import com.dounanflowers.admin.dto.BiTradingCountDto;
import com.dounanflowers.common.entity.BiTradingCount;
import com.dounanflowers.common.enums.BiTradingCountTypeEnum;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.mysql.manager.BiManager;
import com.dounanflowers.third.bo.BiPeopleCountRegionInfoBo;
import com.dounanflowers.third.bo.TradingData;
import com.dounanflowers.third.service.BiPeopleCountService;
import com.dounanflowers.third.service.BiTradingService;
import com.mybatisflex.core.row.Row;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class AdminBiService {

    private final BiManager biManager;

    private final BiTradingService biTradingService;

    private final BiPeopleCountService biPeopleCountService;

    public List<Row> flowerPrice() {
        return biManager.flowerPrice();
    }

    public List<Row> truckCount() {
        return biManager.truckCount();
    }

    public BiTradingCountBo getLatestBiTradingCount(BiTradingCountDto dto) {
        BiTradingCount latestBiTradingCount = biManager.getLatestBiTradingCount(dto.getType());
        return BeanUtils.copy(latestBiTradingCount, BiTradingCountBo.class);
    }

    public void syncOnlineTradingCountByDay(LocalDate start) {
        LocalDate end = start.plusDays(1);
        // 删除可能存在的重复数据
        biManager.deleteBiTradingCountByDate(start, BiTradingCountTypeEnum.ONLINE);
        // 获取前一天数据
        BiTradingCount latestBiTradingCount = biManager.getBiTradingCountByDate(start.minusDays(1), BiTradingCountTypeEnum.ONLINE);
        // 获取当天数据
        TradingData data = biTradingService.getTradingDataByDate(start, end);
        BiTradingCount biTradingCount = new BiTradingCount();
        biTradingCount.setType(BiTradingCountTypeEnum.ONLINE);
        biTradingCount.setDate(start);
        biTradingCount.setOrderCount(data.getData().getCount());
        biTradingCount.setAmountCount(new BigDecimal(data.getData().getTotalPayment()).multiply(new BigDecimal(100)).intValue());
        if (latestBiTradingCount != null) {
            biTradingCount.setOrderCountTotal(latestBiTradingCount.getOrderCountTotal() + biTradingCount.getOrderCount());
            biTradingCount.setAmountCountTotal(latestBiTradingCount.getAmountCountTotal() + biTradingCount.getAmountCount());
        } else {
            biTradingCount.setOrderCountTotal(biTradingCount.getOrderCount());
            biTradingCount.setAmountCountTotal(biTradingCount.getAmountCount());
        }
        biManager.saveBiTradingCount(biTradingCount);
    }



    public Map<String, Object> biPeopleCountGetRegionInfo() {
        return biPeopleCountService.getRegionInfo();
    }

    public List<Map<String, Object>> biPeopleCountRealTimeReport(BiPeopleReportDto dto) {
        return biPeopleCountService.realTimeReport(dto.getTimeUnit(), dto.getStart(), dto.getEnd());
    }

    public Map<String, Object> biPeopleCountInAndOutReport(BiPeopleReportDto dto) {
        return biPeopleCountService.inAndOutReport(dto.getTimeUnit(), dto.getStart(), dto.getEnd());
    }

}
