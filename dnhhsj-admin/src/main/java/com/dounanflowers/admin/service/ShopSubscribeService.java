package com.dounanflowers.admin.service;

import com.dounanflowers.admin.dto.ShopSubscribeAddDto;
import com.dounanflowers.common.bo.ShopSubscribeBo;
import com.dounanflowers.common.entity.ShopSubscribe;
import com.dounanflowers.common.manager.ShopSubscribeManager;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ShopSubscribeService {

    private final ShopSubscribeManager shopSubscribeManager;

    public Page<ShopSubscribeBo> pageList(PageRequest dto) {
        Page<ShopSubscribe> shopSubscribePage = shopSubscribeManager.pageList(dto);
        return shopSubscribePage.convert(s -> {
            ShopSubscribeBo copy = BeanUtils.copy(s, ShopSubscribeBo.class);
            copy.setFilter(JsonUtils.toObject(s.getFilter(), Object.class));
            return copy;
        });
    }

    public ShopSubscribeBo add(ShopSubscribeAddDto dto, Long userId) {
        ShopSubscribe shopSubscribe = new ShopSubscribe();
        shopSubscribe.setTitle(dto.getTitle());
        shopSubscribe.setFilter(JsonUtils.toJson(dto.getFilter()));
        shopSubscribe.setAdminUserId(userId);
        shopSubscribeManager.save(shopSubscribe);
        ShopSubscribeBo copy = BeanUtils.copy(shopSubscribe, ShopSubscribeBo.class);
        copy.setFilter(JsonUtils.toObject(shopSubscribe.getFilter(), Object.class));
        return copy;
    }

    public void deleteById(Long id) {
        shopSubscribeManager.deleteById(id);
    }
}
