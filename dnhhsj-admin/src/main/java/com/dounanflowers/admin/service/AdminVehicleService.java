package com.dounanflowers.admin.service;

import com.dounanflowers.admin.bpm.enums.ProcessEnum;
import com.dounanflowers.admin.dto.FakePayDto;
import com.dounanflowers.admin.dto.VehicleApplyDto;
import com.dounanflowers.admin.dto.VehicleCheckDto;
import com.dounanflowers.admin.dto.VehiclePlateChangeApplyDto;
import com.dounanflowers.bpm.dto.BpmHandleDto;
import com.dounanflowers.bpm.dto.BpmSubmitDto;
import com.dounanflowers.bpm.entity.BpmInstance;
import com.dounanflowers.bpm.enums.ActionTypeEnum;
import com.dounanflowers.bpm.service.BpmService;
import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.BillRefundDto;
import com.dounanflowers.common.dto.MsgSendByTemplateDto;
import com.dounanflowers.common.dto.VehiclePlateChangeCheckDto;
import com.dounanflowers.common.dto.VehicleRefundCheckDto;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.*;
import com.dounanflowers.common.manager.*;
import com.dounanflowers.common.repo.VehicleMonthlyCardRepo;
import com.dounanflowers.common.service.*;
import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.DateUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.ThirdPartyHolder;
import com.dounanflowers.third.bo.ParkWhiteBo;
import com.dounanflowers.third.bo.WxPayParamsBo;
import com.dounanflowers.third.dto.PayParamsDto;
import com.mybatisflex.core.update.UpdateChain;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AdminVehicleService {

    private final VehicleManager vehicleManager;

    private final FileService fileService;

    private final BpmService bpmService;

    private final AdminUserManager adminUserManager;

    private final CouponManager couponManager;

    private final BillManager billManager;

    private final CouponService couponService;

    private final MsgService msgService;
    private final LcService lcService;
    private final BillService billService;

    private final SystemManager systemManager;
    private final VehicleMonthlyCardRepo vehicleMonthlyCardRepo;
    private final UserCommonManager userCommonManager;

    /**
     * 商家提交申请
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public VehicleBo applyVehicle(VehicleApplyDto dto) {
        String numcode = dto.getNumCode();

        if (StringUtils.isBlank(numcode)) {
            throw new BaseException("请输入车牌号");
        }
        numcode = numcode.trim();
        Vehicle old = vehicleManager.getCheckingVehicleByNumber(numcode);
        if (old != null) {
            throw new BaseException("已存在审核流程中的机动车");
        }
        if (dto.getMonthlyCardId() == null) {
            throw new BaseException("请选择月卡");
        }
        VehicleMonthlyCard monthlyCard = vehicleManager.fetchMonthlyCardById(dto.getMonthlyCardId());
        if (monthlyCard == null) {
            throw new BaseException("请选择月卡");
        }
        SessionInfo<Long, AdminUserBo> session = SecurityHolder.session();
        Long userId = session.getUserId();
        AdminUser user = adminUserManager.fetchById(userId);
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        user.setRealname(dto.getRealname());
        adminUserManager.save(user);
        Vehicle vehicle = new Vehicle();
        if (dto.getCouponId() != null) {
            CouponTpl couponTpl = couponManager.fetchCouponTplById(dto.getCouponId());
            if (couponTpl == null) {
                throw new BaseException("优惠券不存在");
            }
            if(!couponTpl.getBoundVehicleMonthlyCardIds().contains((String.valueOf(monthlyCard.getId())))) {
                throw new BaseException("此优惠券不适用当前月卡");
            }
            Coupon coupon = couponService.receiveCouponToUser(couponTpl.getId(), userId, UserTypeEnum.ADMIN);
            if (coupon == null) {
                throw new BaseException("优惠券领取失败");
            }
            vehicle.setCouponId(coupon.getId());
            monthlyCard.setCouponTpl(couponTpl);
        }
        vehicle.setNumber(numcode);
        vehicle.setUserId(userId);
        vehicle.setMonthlyCardId(dto.getMonthlyCardId());
        vehicle.setCheckStatus(VehicleCheckStatusEnum.UNCHECKED);
        vehicleManager.save(vehicle);

        vehicle.setImages(dto.getImages());
        fileService.updateFileLink(vehicle.getId(), dto.getImages());
        startVehicleCheckProcess(vehicle, monthlyCard); // 启动审核流程
        VehicleBo copy = BeanUtils.copy(vehicle, VehicleBo.class);
        copy.setImages(dto.getImages());
        return copy;
    }

    /**
     * 启动审核流程
     * @param vehicle
     * @param monthlyCard
     */
    private void startVehicleCheckProcess(Vehicle vehicle, VehicleMonthlyCard monthlyCard) {
        if (vehicle.getCheckStatus() != VehicleCheckStatusEnum.UNCHECKED) {
            return;
        }
        BpmInstance exist = bpmService.getInstanceByOuter(ProcessEnum.VEHICLE_CHECK.getKey(), vehicle.getId());
        if (exist != null && exist.getEndAt() == null) {
            throw new BaseException("审核流程未结束");
        }
        BpmSubmitDto dto = new BpmSubmitDto()
                .setOuterType(ProcessEnum.VEHICLE_CHECK.getKey())
                .setOuterId(vehicle.getId())
                .setProcessCode(ProcessEnum.VEHICLE_CHECK.getKey())
                .setData(vehicle)
                .setUserId(vehicle.getUserId())
                .setSubmitUserId(vehicle.getUserId());
        BpmInstance instance = bpmService.submit(dto);
        bpmService.doAction(new BpmHandleDto()
                .setInstanceId(instance.getId())
                .setActionType(ActionTypeEnum.APPROVE.name())
                .setData(monthlyCard)
                .setUserId(vehicle.getUserId()));
    }

    /**
     * 再次申请审核
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public VehicleBo applyVehicleAgain(VehicleApplyDto dto) {
        Vehicle vehicle = vehicleManager.fetchVehicleById(dto.getId());
        if (vehicle == null) {
            throw new BaseException("未找到机动车");
        }
        vehicle.setImages(dto.getImages());
        fileService.updateFileLink(vehicle.getId(), dto.getImages());
        BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.VEHICLE_CHECK.getKey(), vehicle.getId());
        if (instance == null) {
            throw new BaseException("未找到流程实例");
        }
        bpmService.doAction(new BpmHandleDto()
                .setInstanceId(instance.getId())
                .setActionType(ActionTypeEnum.APPROVE.name())
                .setRemark(dto.getRemark())
                .setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId()));
        VehicleBo copy = BeanUtils.copy(vehicle, VehicleBo.class);
        copy.setImages(dto.getImages());
        return copy;
    }

    /**
     * 审批
     * @param dto
     * @return
     */
    public Boolean checkVehicle(VehicleCheckDto dto) {
        Vehicle vehicle = vehicleManager.fetchVehicleById(dto.getId());
        if (vehicle == null) {
            throw new BaseException("未找到机动车");
        }
        if (vehicle.getCheckStatus() != VehicleCheckStatusEnum.UNCHECKED) {
            throw new BaseException("机动车审核状态不是待审核");
        }
        VehicleCheckStatusEnum checkStatus = BaseEnum.ordinalOf(VehicleCheckStatusEnum.class, dto.getCheckStatus());
        if (checkStatus == null) {
            throw new BaseException("审核状态错误");
        }
        if (checkStatus == VehicleCheckStatusEnum.REJECTED && StringUtils.isBlank(dto.getCheckRejectReason())) {
            throw new BaseException("审核不通过时，必须填写拒绝原因");
        }
        BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.VEHICLE_CHECK.getKey(), vehicle.getId());
        if (instance == null) {
            throw new BaseException("未找到流程实例");
        }
        if (checkStatus == VehicleCheckStatusEnum.APPROVED) {
            bpmService.doAction(new BpmHandleDto()
                    .setInstanceId(instance.getId())
                    .setActionType(ActionTypeEnum.APPROVE.name())
                    .setRemark(dto.getCheckRemark())
                    .setForce(dto.isForce())
                    .setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId()));
        } else if (checkStatus == VehicleCheckStatusEnum.REJECTED) {
            bpmService.doAction(new BpmHandleDto()
                    .setInstanceId(instance.getId())
                    .setActionType(ActionTypeEnum.REJECT.name())
                    .setRemark(dto.getCheckRemark())
                    .setForce(dto.isForce())
                    .setReason(dto.getCheckRejectReason())
                    .setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId()));
        }
        return true;
    }

    /**
     * 录入车辆
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean entryVehicle(VehicleCheckDto dto) {
        Vehicle vehicle = vehicleManager.fetchVehicleById(dto.getId());
        if (vehicle == null) {
            throw new BaseException("未找到机动车");
        }
        if (vehicle.getCheckStatus() != VehicleCheckStatusEnum.APPROVED) {
            throw new BaseException("机动车审核状态不是通过");
        }
        BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.VEHICLE_CHECK.getKey(), vehicle.getId());
        if (instance == null) {
            throw new BaseException("未找到流程实例");
        }
        bpmService.doAction(new BpmHandleDto()
                .setInstanceId(instance.getId())
                .setActionType(ActionTypeEnum.APPROVE.name())
                .setRemark(dto.getCheckRemark())
                .setForce(true)
                .setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId()));
        return true;
    }

    public Page<VehicleBo> pageList(PageRequest pageRequest) {
        pageRequest.getFilter().add(new PageFilter().setField("show").setValue(IsEnum.TRUE).setType("eq"));
        Page<Vehicle> vehiclePage = vehicleManager.pageList(pageRequest);
        List<Long> userIds = vehiclePage.getList().stream().map(Vehicle::getUserId).toList();
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(userIds);
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));
        List<Long> couponIds = vehiclePage.getList().stream().map(Vehicle::getCouponId).toList();
        List<Coupon> coupons = couponManager.fetchByIds(couponIds);
        Map<Long, Coupon> couponMap = coupons.stream().collect(Collectors.toMap(Coupon::getId, v -> v));
        List<Long> tplIds = coupons.stream().map(Coupon::getCouponTplId).toList();
        List<CouponTpl> couponTpls = couponManager.fetchCouponTplListByIds(tplIds);
        Map<Long, CouponTpl> tplMap = couponTpls.stream().collect(Collectors.toMap(CouponTpl::getId, v -> v));
        List<VehicleMonthlyCard> vehicleMonthlyCards = vehicleManager.fetchMonthlyCardByIds(vehiclePage.getList().stream().map(Vehicle::getMonthlyCardId).toList());
        Map<Long, VehicleMonthlyCard> vehicleMonthlyCardMap = vehicleMonthlyCards.stream().collect(Collectors.toMap(VehicleMonthlyCard::getId, v -> v));
        Map<Long, VehiclePlateChange> vehiclePlateChangeMap = vehicleManager.fetchLastVehiclePlateChangeByVehicleIds(vehiclePage.getList().stream().map(Vehicle::getId).toList());
        return vehiclePage.convert(v -> {
            VehicleBo copy = BeanUtils.copy(v, VehicleBo.class);
            copy.setImages(v.getImages());
            AdminUser adminUser = adminUserMap.get(v.getUserId());
            if (adminUser != null) {
                copy.setUserObj(BeanUtils.copy(adminUser, AdminUserBo.class));
            }
            Coupon coupon = couponMap.get(v.getCouponId());
            if (coupon != null) {
                CouponTpl tpl = tplMap.get(coupon.getCouponTplId());
                copy.setCouponObj(BeanUtils.copy(coupon, CouponBo.class));
                if (tpl != null) {
                    copy.setCouponTplObj(BeanUtils.copy(tpl, CouponTplBo.class));
                }
            }
            VehicleMonthlyCard monthlyCard = vehicleMonthlyCardMap.get(v.getMonthlyCardId());
            if (monthlyCard != null) {
                copy.setMonthlyCardObj(BeanUtils.copy(monthlyCard, VehicleMonthlyCardBo.class));
            }
            VehiclePlateChange vehiclePlateChange = vehiclePlateChangeMap.get(v.getId());
            if (vehiclePlateChange != null) {
                copy.setLastVehiclePlateChangeObj(BeanUtils.copy(vehiclePlateChange, VehiclePlateChangeBo.class));
            }
            return copy;
        });
    }

    public VehicleBo getById(Long id) {
        Vehicle vehicle = vehicleManager.fetchVehicleById(id);
        if (vehicle == null) {
            return null;
        }
        VehicleBo copy = BeanUtils.copy(vehicle, VehicleBo.class);
        copy.setImages(vehicle.getImages());
        AdminUser adminUser = adminUserManager.fetchById(vehicle.getUserId());
        if (adminUser != null) {
            copy.setUserObj(BeanUtils.copy(adminUser, AdminUserBo.class));
        }
        return copy;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelCheckVehicle(Long id) {
        Vehicle vehicle = vehicleManager.fetchVehicleById(id);
        if (vehicle == null) {
            throw new BaseException("未找到机动车");
        }
        long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        if (userId != vehicle.getUserId()) {
            throw new BaseException("机动车不属于当前用户");
        }
        if (vehicle.getCheckStatus() == VehicleCheckStatusEnum.APPROVED) {
            throw new BaseException("机动车已审核通过");
        }
        if (vehicle.getCheckStatus() != VehicleCheckStatusEnum.UNCHECKED
                && vehicle.getCheckStatus() != VehicleCheckStatusEnum.PENDING_DEPOSIT) {
            throw new BaseException("机动车状态不允许取消审核");
        }
        BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.VEHICLE_CHECK.getKey(), vehicle.getId());
        if (instance == null) {
            vehicle.setCheckStatus(VehicleCheckStatusEnum.CANCELED);
            vehicleManager.save(vehicle);
            return true;
        }
        bpmService.doAction(new BpmHandleDto()
                .setInstanceId(instance.getId())
                .setActionType(ActionTypeEnum.CANCEL.name())
                .setRemark("取消审核")
                .setData(vehicle)
                .setUserId(userId));
        return true;
    }

    public List<VehicleBo> vehicleListByUser(Long userId) {
        List<Vehicle> vehicles = vehicleManager.vehicleListByUser(userId);
        return vehicles.stream().map(v -> {
            VehicleBo copy = BeanUtils.copy(v, VehicleBo.class);
            copy.setImages(v.getImages());
            return copy;
        }).toList();
    }

    /**
     * 生成付款参数
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public WxPayParamsBo vehicleBuy(FakePayDto dto) {
        Vehicle vehicle = vehicleManager.fetchVehicleById(dto.getId());

        if (vehicle == null) {
            throw new BaseException("未找到机动车");
        }
        if(vehicle.getCheckStatus() == VehicleCheckStatusEnum.EFFECTIVE) {
            throw new BaseException("已生效，可以尝试下拉刷新");
        }
        VehicleMonthlyCard monthlyCard = vehicleManager.getMonthlyCard(vehicle.getMonthlyCardId());
        if (monthlyCard == null) {
            throw new BaseException("月卡不存在");
        }

        // 创建订单
        VehicleMonthlyCardOrder order = new VehicleMonthlyCardOrder();
        order.setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId());
        order.setVehicleId(vehicle.getId());
        order.setMonthlyCardId(monthlyCard.getId());
        order.setOriginalPrice(monthlyCard.getPrice());

        // 计算实际价格
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        BigDecimal actualPrice = couponService.calculateActualPrice(
                BigDecimal.valueOf(monthlyCard.getPrice()),
                vehicle.getCouponId(),
                userId,
                CouponSceneEnum.VEHICLE);

        if (vehicle.getCouponId() != null) {
            order.setCouponId(vehicle.getCouponId());
        }

        order.setActualPrice(actualPrice.intValue());
        order.setStatus(BillPayStatusEnum.UNPAID); // 待支付

        vehicleManager.saveMonthlyCardOrder(order);

        // 创建账单
        Bill bill = new Bill();
        bill.setType(BillTypeEnum.VEHICLE_MONTHLY);
        bill.setEntityId(order.getId());
        bill.setUserId(userId);
        bill.setUserType(UserTypeEnum.ADMIN);
        bill.setUserModel("admin_user");
        bill.setOriginalMoneyCent(monthlyCard.getPrice());
        bill.setCouponDiscountCent(monthlyCard.getPrice() - order.getActualPrice());
        bill.setOrderMoneyCent(order.getActualPrice());
        bill.setPayStatus(BillPayStatusEnum.UNPAID);
        bill.setOtherData(JsonUtils.toJson(order));
        billManager.saveBill(bill);

        if (dto.isFake()) {
            bill.setPayStatus(BillPayStatusEnum.PAID);
            bill.setPaidAt(LocalDateTime.now());
            billManager.saveBill(bill);
            handleVehicleBill(bill);
            WxPayParamsBo wxPayParamsBo = new WxPayParamsBo();
            wxPayParamsBo.setPaid(true);
            return wxPayParamsBo;
        } else {
            AdminUserBo userInfo = SecurityHolder.<Long, AdminUserBo>session().getUserInfo();
            // 获取微信支付参数
            PayParamsDto params = new PayParamsDto();
            params.setTrxamt(bill.getOrderMoneyCent());
            params.setUnireqsn(String.valueOf(bill.getId()));
            params.setAcct(userInfo.getOpenId());
            params.setBody("机动车月卡订单");
            params.setRemark(userInfo.getMobile());
            return ThirdPartyHolder.allinpayService("shop").pay(params);
        }
    }

    /**
     * 付款完后的流程推进
     * @param bill
     */
    public void handleVehicleBill(Bill bill) {
        Long entityId = bill.getEntityId();
        VehicleMonthlyCardOrder order = vehicleManager.getMonthlyCardOrder(entityId);
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        Vehicle vehicle = vehicleManager.fetchVehicleById(order.getVehicleId());
        if (vehicle == null) {
            throw new BaseException("机动车不存在");
        }
        if (vehicle.getCouponId() != null) {
            Coupon coupon = couponManager.fetchCouponById(vehicle.getCouponId());
            if (coupon != null) {
                couponService.markCouponAsUsed(coupon.getId());
            }
        }
        BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.VEHICLE_CHECK.getKey(), vehicle.getId());
        bpmService.doAction(new BpmHandleDto()
                .setInstanceId(instance.getId())
                .setActionType(ActionTypeEnum.APPROVE.name())
                .setUserId(order.getUserId()));

    }

    public void updateExpired() {
        List<Vehicle> vehicles = vehicleManager.fetchVehiclesByStatus(VehicleCheckStatusEnum.EFFECTIVE);
        if (CollectionUtils.isEmpty(vehicles)) {
            return;
        }
        for (Vehicle vehicle : vehicles) {
            if (LocalDateTime.now().isAfter(vehicle.getMonthlyCardExpireAt())) {
                vehicle.setCheckStatus(VehicleCheckStatusEnum.EXPIRED);
                vehicleManager.save(vehicle);
            }
        }
    }

    /**
     * 续费提醒
     */
    public void remindVehicle() {
        List<Vehicle> vehicles = vehicleManager.fetchVehiclesByStatus(VehicleCheckStatusEnum.EFFECTIVE);
        if (CollectionUtils.isEmpty(vehicles)) {
            return;
        }
        List<Long> monthCardIds = vehicles.stream().map(Vehicle::getMonthlyCardId).toList();
        List<VehicleMonthlyCard> monthCards = vehicleManager.fetchMonthlyCardsByIds(monthCardIds);
        Map<Long, VehicleMonthlyCard> monthCardMap = monthCards.stream().collect(Collectors.toMap(VehicleMonthlyCard::getId, v -> v));
        for (Vehicle vehicle : vehicles) {
            VehicleMonthlyCard monthlyCard = monthCardMap.get(vehicle.getMonthlyCardId());
            if (monthlyCard == null) {
                continue;
            }
            if (LocalDateTime.now().toLocalDate().plusDays(monthlyCard.getRemindDays()).isEqual(vehicle.getMonthlyCardExpireAt().toLocalDate())) {
                MsgSendByTemplateDto template = new MsgSendByTemplateDto();
                template.setSendUserId(0L);
                Map<String, Object> context = new HashMap<>();
                context.put("number", vehicle.getNumber());
                context.put("date", DateUtils.formatDateTime(vehicle.getMonthlyCardExpireAt(), "yyyy-MM-dd HH:mm:ss"));
                context.put("content", monthlyCard.getName() + "剩余" + monthlyCard.getRemindDays() + "天到期，请您及时续费。");
                template.setContext(context);
                template.setOuterType("vehicle");
                template.setOuterId(vehicle.getId());
                template.setReceiveUserIds(List.of(vehicle.getUserId()));
                template.setTemplateCode("vehicleRemind");
                msgService.sendMessageByTemplate(template);
            }
        }
    }

    /**
     * 换牌申请
     * @param vehicleId
     * @param newPlate
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public WxPayParamsBo vehiclePlateChangeApply(Long vehicleId, String newPlate, Boolean isFake) {
        Vehicle vehicle = vehicleManager.fetchVehicleById(vehicleId);
        if (vehicle == null) {
            throw new BaseException("未找到机动车");
        }

        Vehicle checkingOrPaying = vehicleManager.fetchCheckingOrPaying(newPlate);
        if(checkingOrPaying != null) {
            throw new BaseException("新车牌存在正在审核或支付中的月卡申请");
        }
        ParkWhiteBo parkWhiteBo = lcService.whitelistQuery(newPlate);
        if (parkWhiteBo != null) {
            throw new BaseException("新车牌已有月卡");
        }
        Boolean needPay = vehicleManager.plateChangeNeedPay(vehicleId);
        AdminUserBo userInfo = SecurityHolder.<Long, AdminUserBo>session().getUserInfo();
        VehiclePlateChange vehiclePlateChange = new VehiclePlateChange();
        vehiclePlateChange.setUserId(userInfo.getId());
        vehiclePlateChange.setVehicleId(vehicleId);
        vehiclePlateChange.setOldPlate(vehicle.getNumber());
        vehiclePlateChange.setNewPlate(newPlate);

        if(!needPay) {
            vehiclePlateChange.setChangeStatus(VehiclePlateChangeStatusEnum.WAIT_CHANGE);
            vehicleManager.saveVehiclePlateChange(vehiclePlateChange);

            startVehiclePlateChangeProcess(vehicle, vehiclePlateChange);

            WxPayParamsBo wxPayParamsBo = new WxPayParamsBo();
            wxPayParamsBo.setPaid(true);
            return wxPayParamsBo;
        } else {
            Map<String, Map<String, Object>> stringMapMap = systemManager.fetchSettingMap();
            int needPayCent = Integer
                    .parseInt(stringMapMap.get("Merchant").getOrDefault("vehicleChangePlateFee", "0").toString());

            vehiclePlateChange.setChangeStatus(VehiclePlateChangeStatusEnum.WAIT_PAY);
            vehicleManager.saveVehiclePlateChange(vehiclePlateChange);
            Bill bill = new Bill();
            bill.setUserId(userInfo.getId());
            bill.setUserType(UserTypeEnum.ADMIN);
            bill.setType(BillTypeEnum.VEHICLE_PLATE_CHANGE);
            bill.setEntityId(vehiclePlateChange.getId());
            bill.setEntityModel("vehicle_plate_change");
            bill.setUserModel("admin_user");
            bill.setOriginalMoneyCent(needPayCent);
            bill.setCouponDiscountCent(0);
            bill.setOrderMoneyCent(needPayCent);
            bill.setPayStatus(BillPayStatusEnum.UNPAID);
            Map<String, Object> billOtherData = Map.of("vehicleId", String.valueOf(vehicleId));
            bill.setOtherData(JsonUtils.toJson(billOtherData));
            billManager.saveBill(bill);

            if (isFake || needPayCent == 0) {
                bill.setPayStatus(BillPayStatusEnum.PAID);
                bill.setPaidAt(LocalDateTime.now());
                bill.setPaidMoneyCent(needPayCent);
                billManager.saveBill(bill);
                handleVehiclePlateChangeBill(bill);
                WxPayParamsBo wxPayParamsBo = new WxPayParamsBo();
                wxPayParamsBo.setPaid(true);
                return wxPayParamsBo;
            } else {
                PayParamsDto payDto = new PayParamsDto();
                payDto.setTrxamt(bill.getOrderMoneyCent());
                payDto.setUnireqsn(String.valueOf(bill.getId()));
                payDto.setAcct(userInfo.getOpenId());
                payDto.setBody("支付更换车牌费");
                payDto.setRemark(userInfo.getMobile());
                return ThirdPartyHolder.allinpayService("shop").pay(payDto);
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public WxPayParamsBo vehiclePlateChangePay(Long vehiclePlateChangeId) {
        VehiclePlateChange vehiclePlateChange = vehicleManager.fetchVehiclePlateChangeById(vehiclePlateChangeId);
        if (vehiclePlateChange == null) {
            throw new BaseException("换牌申请不存在");
        }
        if (vehiclePlateChange.getChangeStatus() != VehiclePlateChangeStatusEnum.WAIT_PAY) {
            throw new BaseException("换牌申请不是待支付状态");
        }
        AdminUserBo userInfo = SecurityHolder.<Long, AdminUserBo>session().getUserInfo();
        Map<String, Map<String, Object>> stringMapMap = systemManager.fetchSettingMap();
        int needPayCent = Integer
                .parseInt(stringMapMap.get("Merchant").getOrDefault("vehicleChangePlateFee", "0").toString());
        Bill bill = new Bill();
        bill.setUserId(userInfo.getId());
        bill.setUserType(UserTypeEnum.ADMIN);
        bill.setType(BillTypeEnum.VEHICLE_PLATE_CHANGE);
        bill.setEntityId(vehiclePlateChange.getId());
        bill.setEntityModel("vehicle_plate_change");
        bill.setUserModel("admin_user");
        bill.setOriginalMoneyCent(needPayCent);
        bill.setCouponDiscountCent(0);
        bill.setOrderMoneyCent(needPayCent);
        bill.setPayStatus(BillPayStatusEnum.UNPAID);
        Map<String, Object> billOtherData = Map.of("vehicleId", String.valueOf(vehiclePlateChange.getVehicleId()));
        bill.setOtherData(JsonUtils.toJson(billOtherData));
        billManager.saveBill(bill);


        PayParamsDto payDto = new PayParamsDto();
        payDto.setTrxamt(bill.getOrderMoneyCent());
        payDto.setUnireqsn(String.valueOf(bill.getId()));
        payDto.setAcct(userInfo.getOpenId());
        payDto.setBody("支付更换车牌费");
        payDto.setRemark(userInfo.getMobile());
        return ThirdPartyHolder.allinpayService("shop").pay(payDto);

    }

    /**
     * 换牌付款完后的流程推进
     * @param bill
     */
    public void handleVehiclePlateChangeBill(Bill bill) {
        Long entityId = bill.getEntityId();
        VehiclePlateChange vehiclePlateChange = vehicleManager.fetchVehiclePlateChangeById(entityId);
        if (vehiclePlateChange == null) {
            throw new BaseException("换牌申请不存在");
        }
        Vehicle vehicle = vehicleManager.fetchVehicleById(vehiclePlateChange.getVehicleId());
        if (vehicle == null) {
            throw new BaseException("机动车不存在");
        }
        vehiclePlateChange.setChangeStatus(VehiclePlateChangeStatusEnum.WAIT_CHANGE);
        vehiclePlateChange.setBillId(bill.getId());
        vehicleManager.saveVehiclePlateChange(vehiclePlateChange);
        startVehiclePlateChangeProcess(vehicle, vehiclePlateChange);
    }

    /**
     * 启动换牌流程
     * @param vehicle 机动车
     * @param vehiclePlateChange 车牌更换
     */
    private void startVehiclePlateChangeProcess(Vehicle vehicle, VehiclePlateChange vehiclePlateChange) {
        if (vehiclePlateChange.getChangeStatus() != VehiclePlateChangeStatusEnum.WAIT_CHANGE) {
            return;
        }
        BpmInstance exist = bpmService.getInstanceByOuter(ProcessEnum.VEHICLE_PLATE_CHANGE.getKey(), vehicle.getId());
        if (exist != null && exist.getEndAt() == null) {
            throw new BaseException("号牌变更流程未结束");
        }
        BpmSubmitDto dto = new BpmSubmitDto()
                .setOuterType(ProcessEnum.VEHICLE_PLATE_CHANGE.getKey())
                .setOuterId(vehicle.getId())
                .setProcessCode(ProcessEnum.VEHICLE_PLATE_CHANGE.getKey())
                .setData(vehicle)
                .setUserId(vehicle.getUserId())
                .setSubmitUserId(vehicle.getUserId());
        BpmInstance instance = bpmService.submit(dto);
        bpmService.doAction(new BpmHandleDto()
                .setInstanceId(instance.getId())
                .setActionType(ActionTypeEnum.APPROVE.name())
                .setData(vehiclePlateChange)
                .setUserId(vehicle.getUserId()));
    }

//    System.out.println("================退款=================");
//            if(vehiclePlateChange.getBillId() != null) {
//        BillRefundDto billRefundDto = new BillRefundDto();
//        billRefundDto.setId(vehiclePlateChange.getBillId().toString());
//        billRefundDto.setReason("换牌失败，自动退款");
//        billService.refund(billRefundDto);
//    }

    public void vehiclePlateChangeRefund(VehiclePlateChange vehiclePlateChange) {
        if (vehiclePlateChange.getBillId() != null) {
            BillRefundDto billRefundDto = new BillRefundDto();
            billRefundDto.setId(vehiclePlateChange.getBillId().toString());
            billRefundDto.setReason("换牌失败，自动退款");
            billService.refund(billRefundDto);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean vehiclePlateChangeCancel(Long vehiclePlateChangeId) {
        VehiclePlateChange vehiclePlateChange = vehicleManager.fetchVehiclePlateChangeById(vehiclePlateChangeId);
        if (vehiclePlateChange == null) {
            throw new BaseException("换牌申请不存在");
        }
        if (vehiclePlateChange.getChangeStatus() == VehiclePlateChangeStatusEnum.WAIT_PAY) {
            vehiclePlateChange.setChangeStatus(VehiclePlateChangeStatusEnum.CANCELED);
            vehicleManager.saveVehiclePlateChange(vehiclePlateChange);
            return true;
        }
        if (vehiclePlateChange.getChangeStatus() != VehiclePlateChangeStatusEnum.WAIT_CHANGE) {
            throw new BaseException("换牌申请状态不允许取消");
        }

        vehiclePlateChangeRefund(vehiclePlateChange);

        BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.VEHICLE_PLATE_CHANGE.getKey(), vehiclePlateChange.getVehicleId());
        if (instance == null) {
            vehiclePlateChange.setChangeStatus(VehiclePlateChangeStatusEnum.CANCELED);
            vehicleManager.saveVehiclePlateChange(vehiclePlateChange);

            return true;
        }
        bpmService.doAction(new BpmHandleDto()
                .setInstanceId(instance.getId())
                .setActionType(ActionTypeEnum.CANCEL.name())
                .setRemark("取消审核")
                .setUserId(vehiclePlateChange.getUserId()));
        return true;

    }

    public Boolean vehiclePlateChangeCheck(VehiclePlateChangeCheckDto dto) {

        VehiclePlateChange vehiclePlateChange = vehicleManager.fetchVehiclePlateChangeById(dto.getVehiclePlateChangeId());

        if (vehiclePlateChange == null) {
            throw new BaseException("换牌申请不存在");
        }

        if(vehiclePlateChange.getChangeStatus() != VehiclePlateChangeStatusEnum.WAIT_CHANGE) {
            throw new BaseException("换牌申请状态不允许审核");
        }

        BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.VEHICLE_PLATE_CHANGE.getKey(), vehiclePlateChange.getVehicleId());
        if (instance == null) {
            throw new BaseException("未找到流程实例");
        }
        if (dto.getChangeStatus() == VehiclePlateChangeStatusEnum.DONE) {
            bpmService.doAction(new BpmHandleDto()
                    .setInstanceId(instance.getId())
                    .setActionType(ActionTypeEnum.APPROVE.name())
                    .setRemark(dto.getRemark())
                    .setForce(dto.isForce())
                    .setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId()));
        } else if (dto.getChangeStatus() == VehiclePlateChangeStatusEnum.REJECTED) {
            bpmService.doAction(new BpmHandleDto()
                    .setInstanceId(instance.getId())
                    .setActionType(ActionTypeEnum.REJECT.name())
                    .setRemark(dto.getRemark())
                    .setForce(dto.isForce())
                    .setReason(dto.getRejectReason())
                    .setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId()));
        }
        return true;
    }

    public VehicleBo getEffectiveVehicleByPlate(String plate) {
        Vehicle vehicle = vehicleManager.getEffectiveVehicleByPlate(plate);
        if (vehicle == null) {
            return null;
        }
        VehicleBo copy = BeanUtils.copy(vehicle, VehicleBo.class);
        copy.setImages(vehicle.getImages());
        AdminUser adminUser = adminUserManager.fetchById(vehicle.getUserId());
        if (adminUser != null) {
            copy.setUserObj(BeanUtils.copy(adminUser, AdminUserBo.class));
        }
        return copy;
    }

    public Boolean vehicleRefundApply(Long vehicleId) {
        Vehicle vehicle = vehicleManager.fetchVehicleById(vehicleId);
        if (vehicle == null) {
            throw new BaseException("未找到机动车");
        }
        if (vehicle.getCheckStatus() != VehicleCheckStatusEnum.EFFECTIVE) {
            throw new BaseException("机动车状态不允许退款");
        }
        if(vehicle.getRefundStatus() != VehicleRefundStatusEnum.NONE && vehicle.getRefundStatus() != VehicleRefundStatusEnum.REJECTED) {
            throw new BaseException("机动车已申请过退款");
        }
        vehicle.setRefundStatus(VehicleRefundStatusEnum.WAIT_CHECK);
        vehicleManager.save(vehicle);
        return true;
    }

    public Boolean vehicleRefundCheck(VehicleRefundCheckDto dto) {
        Vehicle vehicle = vehicleManager.fetchVehicleById(dto.getId());
        if (vehicle == null) {
            throw new BaseException("未找到机动车");
        }
        if(vehicle.getRefundStatus() != VehicleRefundStatusEnum.WAIT_CHECK) {
            throw new BaseException("机动车不是待退款");
        }
        if(dto.getRefundStatus() != VehicleRefundStatusEnum.REJECTED) {
            ParkWhiteBo parkWhiteBo = lcService.whitelistQuery(vehicle.getNumber().trim());
            if(parkWhiteBo != null) {
                throw new BaseException("机动车还在蓝卡白名单中");
            }
        }

        if (dto.getRefundStatus() == VehicleRefundStatusEnum.APPROVED) {
            Bill bill = vehicleMonthlyCardBill(dto.getId());
            if (bill == null) {
                throw new BaseException("未找到关联账单");
            }
            if (bill.getPayStatus() == BillPayStatusEnum.REFUNDED) {
                throw new BaseException("关联账单状态是已退款");
            }
            if (bill.getPayStatus() == BillPayStatusEnum.PART_REFUNDED) {
                throw new BaseException("关联账单状态是已部分退款");
            }
            if (bill.getPayStatus() != BillPayStatusEnum.PAID) {
                throw new BaseException("关联账单状态不是已付款");
            }
            BillRefundDto refundDto = new BillRefundDto();

            refundDto.setId(String.valueOf(bill.getId()));
            refundDto.setMoneyCent(bill.getOrderMoneyCent());
            refundDto.setReason("机动车月卡退款");
            refundDto.setRemark(dto.getRemark());
            billManager.billRefund(bill, refundDto);
        }

        if(dto.getRefundStatus() == VehicleRefundStatusEnum.APPROVED || dto.getRefundStatus() == VehicleRefundStatusEnum.MANUAL_APPROVED) {
            vehicle.setCheckStatus(VehicleCheckStatusEnum.REFUNDED);
        }
        vehicle.setRefundStatus(dto.getRefundStatus());
        vehicle.setRefundProcessedAt(LocalDateTime.now());
        Long adminUserId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        vehicle.setRefundProcessorId(adminUserId);
        vehicle.setRefundProcessedAt(LocalDateTime.now());
        vehicle.setRefundRejectReason(dto.getRejectReason() != null ? dto.getRejectReason() : "");
        vehicle.setRefundMoneyCent(dto.getRefundCent());
        vehicle.setRefundImages(JsonUtils.toJson(dto.getImages()));
        vehicle.setRefundRemark(dto.getRemark());
        vehicleManager.save(vehicle);

        MsgSendByTemplateDto template = new MsgSendByTemplateDto();
        template.setSendUserId(vehicle.getRefundProcessorId());
        Map<String, Object> context = JsonUtils.toMap(JsonUtils.toJson(vehicle));
        context.put("statusName", vehicle.getRefundStatus() == VehicleRefundStatusEnum.APPROVED ? "已同意" : (vehicle.getRefundStatus() == VehicleRefundStatusEnum.MANUAL_APPROVED ? "已手动同意" : "已拒绝"));
        context.put("rejectReason", io.micrometer.common.util.StringUtils.isBlank(vehicle.getRefundRejectReason()) ? "无" : vehicle.getRefundRejectReason());
        context.put("amount", vehicle.getRefundMoneyCent() / 100.0);
        context.put("time", vehicle.getRefundProcessedAt());
        template.setContext(context);
        template.setOuterType("vehicle");
        template.setOuterId(vehicle.getId());
        template.setReceiveUserIds(List.of(vehicle.getUserId()));
        template.setTemplateCode("vehicleRefund");
        msgService.sendMessageByTemplate(template);
        return true;
    }

    public Bill vehicleMonthlyCardBill(Long vehicleId) {
        VehicleMonthlyCardOrder monthlyCardOrder = vehicleManager.getMonthlyCardOrderByVehicleId(vehicleId);
        if (monthlyCardOrder == null) {
            throw new BaseException("未找到月卡订单");
        }
        return billManager.fetchByEntityId(monthlyCardOrder.getId());

    }

    public List<VehicleBo> sameRootHiddenVehicles(Long id) {
        Vehicle vehicle = vehicleManager.fetchVehicleById(id);
        if (vehicle == null) {
            throw new BaseException("未找到机动车");
        }
        List<Vehicle> vehicles = vehicleManager.sameRootHiddenVehicles(vehicle.getRootId());
        List<Long> userIds = vehicles.stream().map(Vehicle::getUserId).toList();
        Map<Long, UserObjBo> userObjBoMap = userCommonManager.fetchUserMapByUserIds(userIds);

        List<Long> couponIds = vehicles.stream().map(Vehicle::getCouponId).toList();
        List<Coupon> coupons = couponManager.fetchByIds(couponIds);
        Map<Long, Coupon> couponMap = coupons.stream().collect(Collectors.toMap(Coupon::getId, v -> v));
        List<Long> tplIds = coupons.stream().map(Coupon::getCouponTplId).toList();
        List<CouponTpl> couponTpls = couponManager.fetchCouponTplListByIds(tplIds);
        Map<Long, CouponTpl> tplMap = couponTpls.stream().collect(Collectors.toMap(CouponTpl::getId, v -> v));
        List<VehicleMonthlyCard> vehicleMonthlyCards = vehicleManager.fetchMonthlyCardByIds(vehicles.stream().map(Vehicle::getMonthlyCardId).toList());
        Map<Long, VehicleMonthlyCard> vehicleMonthlyCardMap = vehicleMonthlyCards.stream().collect(Collectors.toMap(VehicleMonthlyCard::getId, v -> v));

        return vehicles.stream().map(v -> {
            VehicleBo copy = BeanUtils.copy(v, VehicleBo.class);
            copy.setImages(v.getImages());

            if (userObjBoMap.get(v.getUserId()) != null) {
                copy.setUserObj(BeanUtils.copy(userObjBoMap.get(v.getUserId()), AdminUserBo.class));
            }

            Coupon coupon = couponMap.get(v.getCouponId());
            if (coupon != null) {
                CouponTpl tpl = tplMap.get(coupon.getCouponTplId());
                copy.setCouponObj(BeanUtils.copy(coupon, CouponBo.class));
                if (tpl != null) {
                    copy.setCouponTplObj(BeanUtils.copy(tpl, CouponTplBo.class));
                }
            }
            VehicleMonthlyCard monthlyCard = vehicleMonthlyCardMap.get(v.getMonthlyCardId());
            if (monthlyCard != null) {
                copy.setMonthlyCardObj(BeanUtils.copy(monthlyCard, VehicleMonthlyCardBo.class));
            }
            return copy;
        }).collect(Collectors.toList());
    }

}
