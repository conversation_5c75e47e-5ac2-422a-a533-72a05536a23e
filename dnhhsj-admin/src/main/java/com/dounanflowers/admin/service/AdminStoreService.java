package com.dounanflowers.admin.service;

import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.bo.ShopBo;
import com.dounanflowers.common.bo.StoreBo;
import com.dounanflowers.common.bo.StoreEmployeeBo;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.common.manager.ProductManager;
import com.dounanflowers.common.manager.ShopManager;
import com.dounanflowers.common.manager.StoreManager;
import com.dounanflowers.common.service.FileService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class AdminStoreService {

    private final StoreManager storeManager;

    private final ShopManager shopManager;

    private final FileService fileService;

    private final ProductManager productManager;

    private final AdminUserManager adminUserManager;

    @Transactional
    public StoreBo create(StoreBo dto) {
        Store exist = storeManager.fetchByCellNo(dto.getCellNo());
        if (exist != null) {
            throw new BaseException("商铺编号已存在");
        }

        Store store = BeanUtils.copy(dto, Store.class);
        Shop shop = shopManager.fetchByCellNo(dto.getCellNo());
        if (shop != null) {
            store.setShopId(shop.getId());
        }

        if (store.getMobile() != null) {
            AdminUser adminUser = adminUserManager.fetchByMobileOrCreate(store.getMobile(), store.getContact());
            if (adminUser != null) {
                store.setAdminUserId(adminUser.getId());
                storeManager.addUsersUnderwriter(List.of(adminUser.getId()));
            }
        }
        store.setStatus(IsEnum.TRUE);
        store.setTags(dto.getTags());
        if (dto.getEmployees() != null) {
            store.setEmployees(dto.getEmployees().stream().map(v -> BeanUtils.copy(v, StoreEmployeeR.class)).toList());
        }
        store.setSoldCount(0);
        store.setReviewCount(0);
        store.setRate(0);
        storeManager.save(store);
        fileService.updateFileLink(store.getId(), dto.getImages());
        StoreBo copy = BeanUtils.copy(store, StoreBo.class);
        if(shop != null) {
            copy.setShopObj(BeanUtils.copy(shop, ShopBo.class));
        }

        return copy;
    }

    @Transactional
    public StoreBo update(StoreBo dto) {
        Store store = storeManager.fetchByIdWithRelation(dto.getId());
        if (store == null) {
            throw new BaseException("商铺不存在");
        }
        Store update = BeanUtils.copy(dto, Store.class);
        store.setCellNo(update.getCellNo());
        store.setStatus(update.getStatus());
        store.setAddress(update.getAddress());
        store.setLocation(update.getLocation());
        store.setOpenAt(update.getOpenAt());
        store.setCloseAt(update.getCloseAt());
        store.setAdminUserId(update.getAdminUserId());
        store.setMobile(update.getMobile());
        store.setType(update.getType());
        store.setFeeRate(update.getFeeRate());

        if (store.getMobile() != null) {
            if(store.getAdminUserId() != null) {
                storeManager.deleteUsersUnderwriter(List.of(store.getAdminUserId()));
            }

            AdminUser adminUser = adminUserManager.fetchByMobileOrCreate(store.getMobile(), store.getContact());
            if (adminUser != null) {
                store.setAdminUserId(adminUser.getId());
                storeManager.addUsersUnderwriter(List.of(adminUser.getId()));
            } else {
                store.setAdminUserId(0L);
            }
        }
        store.setContact(update.getContact());
        store.setName(update.getName());
        store.setRecommend(update.getRecommend());
        store.setTags(dto.getTags());
        if (dto.getEmployees() != null) {
            store.setEmployees(dto.getEmployees().stream().map(v -> BeanUtils.copy(v, StoreEmployeeR.class)).toList());
        }
        storeManager.save(store);
        if(dto.getImages() !=null) {
            fileService.updateFileLink(store.getId(), dto.getImages());
        }
        return BeanUtils.copy(store, StoreBo.class);
    }

    @Transactional
    public void delete(Long id) {
        List<Product> products = productManager.fetchProductByStoreId(id);
        if (!products.isEmpty()) {
            throw new BaseException("商铺下存在商品");
        }
        Store store = storeManager.fetchByIdWithRelation(id);
        if (store == null) {
            throw new BaseException("商铺不存在");
        }
        storeManager.deleteById(id);
    }

    public StoreBo get(Long id) {
        Store store = storeManager.fetchByIdWithRelation(id);
        if (store == null) {
            throw new BaseException("商铺不存在");
        }
        StoreBo copy = BeanUtils.copy(store, StoreBo.class);
        Shop shop = shopManager.fetchByCellNo(store.getCellNo());
        if(shop != null) {
            copy.setShopObj(BeanUtils.copy(shop, ShopBo.class));
        }

        copy.setImages(store.getImages());
        copy.setTags(store.getTags());
        List<Long> list = store.getEmployees().stream().map(StoreEmployeeR::getUserId).toList();
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(list);
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));
        copy.setEmployees(store.getEmployees().stream().map(v -> {
            StoreEmployeeBo one = BeanUtils.copy(v, StoreEmployeeBo.class);
            AdminUser source = adminUserMap.get(v.getUserId());
            if (source != null) {
                one.setUserObj(BeanUtils.copy(source, AdminUserBo.class));
            }
            return one;
        }).toList());
        return copy;
    }

    public Page<StoreBo> page(PageRequest pageRequest) {
        Page<Store> list = storeManager.pageList(pageRequest);
        List<Long> shopIds = list.getList().stream().map(Store::getShopId).toList();
        List<Shop> shops = shopManager.fetchByIds(shopIds);
        Map<Long, Shop> shopMap = shops.stream().collect(Collectors.toMap(Shop::getId, v -> v));
        List<Long> adminUserIds = Stream.concat(
                list.getList().stream().map(Store::getEmployees).flatMap(List::stream).map(StoreEmployeeR::getUserId),
                list.getList().stream().map(Store::getAdminUserId)).toList();
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(adminUserIds);
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));
        return list.convert(v -> {
            StoreBo copy = BeanUtils.copy(v, StoreBo.class);
            copy.setImages(v.getImages());
            copy.setTags(v.getTags());
            Shop shop = shopMap.get(v.getShopId());
            if (shop != null) {
                copy.setShopObj(BeanUtils.copy(shop, ShopBo.class));
            }
            AdminUser adminUser = adminUserMap.get(v.getAdminUserId());
            if (adminUser != null) {
                copy.setAdminUserObj(BeanUtils.copy(adminUser, AdminUserBo.class));
            }
            copy.setEmployees(v.getEmployees().stream().map(v1 -> {
                StoreEmployeeBo one = BeanUtils.copy(v1, StoreEmployeeBo.class);
                AdminUser source = adminUserMap.get(v1.getUserId());
                if (source != null) {
                    one.setUserObj(BeanUtils.copy(source, AdminUserBo.class));
                }
                return one;
            }).toList());
            return copy;
        });
    }

    public StoreBo getByShopId(Long shopId) {
        Store store = storeManager.fetchByShopId(shopId);
        if (store == null) {
            throw new BaseException("商铺不存在");
        }
        Shop shop = shopManager.fetchByCellNo(store.getCellNo());
        if (shop == null) {
            throw new BaseException("商铺不存在");
        }
        StoreBo copy = BeanUtils.copy(store, StoreBo.class);
        copy.setShopObj(BeanUtils.copy(shop, ShopBo.class));
        return copy;
    }

    @Transactional
    public void updateStatus(Long id, IsEnum status) {
        Store store = storeManager.fetchById(id);
        if (store == null) {
            throw new BaseException("商铺不存在");
        }
        store.setStatus(status);
        storeManager.save(store);
    }

    public List<StoreBo> storeListByUserId(Long userId) {
        List<Store> list = storeManager.fetchStoreByUserId(userId);
        return list.stream().map(v -> {
            StoreBo copy = BeanUtils.copy(v, StoreBo.class);
            copy.setImages(v.getImages());
            copy.setTags(v.getTags());
            return copy;
        }).toList();
    }
}
