package com.dounanflowers.admin.service;

import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.bo.FavouriteBo;
import com.dounanflowers.common.bo.JobPostingBo;
import com.dounanflowers.common.bo.UserObjBo;
import com.dounanflowers.common.dto.JobPostingAuditDto;
import com.dounanflowers.common.dto.JobPostingRecommendDto;
import com.dounanflowers.common.entity.Favourite;
import com.dounanflowers.common.entity.JobPosting;
import com.dounanflowers.common.enums.AdminMenuEventEnum;
import com.dounanflowers.common.enums.FavouriteTypeEnum;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.common.manager.FavouriteManager;
import com.dounanflowers.common.manager.JobPostingManager;
import com.dounanflowers.common.manager.UserCommonManager;
import com.dounanflowers.common.repo.JobPostingRepo;
import com.dounanflowers.common.service.AdminMenuService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.bean.PageSort;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.security.utils.SecurityHolder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class JobPostingService {

    private final JobPostingManager jobPostingManager;

    private final AdminUserManager adminUserManager;

    private final FavouriteManager favouriteManager;
    private final UserCommonManager userCommonManager;
    private final JobPostingRepo jobPostingRepo;



    public JobPostingBo create(JobPostingBo dto) {
        JobPosting jobPosting = BeanUtils.copy(dto, JobPosting.class);
        jobPosting.setTags(dto.getTags());
        jobPosting.setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId());
        jobPosting = jobPostingManager.createJobPosting(jobPosting);
        JobPostingBo copy = BeanUtils.copy(jobPosting, JobPostingBo.class);
        copy.setTags(jobPosting.getTags());
        return copy;
    }

    public JobPostingBo update(JobPostingBo dto) {
        JobPosting jobPosting = BeanUtils.copy(dto, JobPosting.class);
        jobPosting.setTags(dto.getTags());
        jobPosting = jobPostingManager.updateJobPosting(jobPosting);
        JobPostingBo copy = BeanUtils.copy(jobPosting, JobPostingBo.class);
        copy.setTags(jobPosting.getTags());
        return copy;
    }

    public void delete(Long id) {
        jobPostingManager.deleteJobPosting(id);
    }

    public void audit(JobPostingAuditDto dto) {
        JobPosting jobPosting = BeanUtils.copy(dto, JobPosting.class);
        jobPosting.setCheckUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId());
        jobPostingManager.updateJobPosting(jobPosting);
    }

    public Page<JobPostingBo> pageList(PageRequest pageRequest) {
        if (pageRequest.getFilter() != null) {
            for (PageFilter filter : pageRequest.getFilter()) {
                if(filter.getType().equals("custom") && filter.getField().equals("orderByIds") && filter.getValue() != null){
                    List<Long> ids = ((List<String>) filter.getValue()).stream().map(Long::parseLong).toList();
                    if(!ids.isEmpty()) {
                        pageRequest.addSort(new PageSort().setConsumer(w -> {
                            w.orderBy("id in (" + ids.stream().map(String::valueOf).collect(Collectors.joining(",")) + ")", false);
                        }));
                    }

                }
            }
        }
        pageRequest.addSort(new PageSort().setConsumer(w -> {
            w.orderBy(JobPosting::getRecommend, false);
            w.orderBy(JobPosting::getCreatedAt, false);
        }));
        Page<JobPosting> page = jobPostingManager.pageJobPostings(pageRequest);
        List<Long> userIds = page.getList().stream().map(JobPosting::getUserId).collect(Collectors.toList());
        userIds.addAll(page.getList().stream().map(JobPosting::getCheckUserId).toList());
        Map<Long, UserObjBo> userMap = userCommonManager.fetchUserMapByUserIds(userIds);

        Map<Long, Integer> favouriteMap = favouriteManager.countByEntityIds(FavouriteTypeEnum.JOB, page.getList().stream().map(JobPosting::getId).toList());

        return page.convert(v -> {
            JobPostingBo bo = BeanUtils.copy(v, JobPostingBo.class);
            bo.setTags(v.getTags());

            if (userMap.get(v.getUserId()) != null) {
                bo.setUserObj(userMap.get(v.getUserId()));
            }

            if (userMap.get(v.getCheckUserId()) != null) {
                bo.setCheckUserObj(userMap.get(v.getCheckUserId()));
            }
            bo.setFavouriteCount(favouriteMap.get(v.getId()) != null ? favouriteMap.get(v.getId()) : 0);

            return bo;
        });
    }

    public Page<JobPostingBo> singlePageList(PageRequest pageRequest) {
        Page<JobPosting> page = jobPostingManager.pageJobPostings(pageRequest);
        List<Long> userIds = page.getList().stream().map(JobPosting::getUserId).collect(Collectors.toList());
        userIds.addAll(page.getList().stream().map(JobPosting::getCheckUserId).toList());
        Map<Long, UserObjBo> userMap = userCommonManager.fetchUserMapByUserIds(userIds);
        Map<Long, Integer> favouriteMap = favouriteManager.countByEntityIds(FavouriteTypeEnum.JOB, page.getList().stream().map(JobPosting::getId).toList());
        return page.convert(v -> {
            JobPostingBo bo = BeanUtils.copy(v, JobPostingBo.class);
            bo.setTags(v.getTags());
            if (userMap.get(v.getUserId()) != null) {
                bo.setUserObj(userMap.get(v.getUserId()));
            }
            if (userMap.get(v.getCheckUserId()) != null) {
                bo.setCheckUserObj(userMap.get(v.getCheckUserId()));
            }
            bo.setFavouriteCount(favouriteMap.get(v.getId()) != null ? favouriteMap.get(v.getId()) : 0);
            return bo;
        });
    }

    public void toggleShow(Long id) {
        JobPosting jobPosting = jobPostingManager.fetchJobPostingById(id);
        if (jobPosting == null) {
            return;
        }
        if (jobPosting.getShow() == null || jobPosting.getShow().isFalse()) {
            jobPosting.setShow(IsEnum.TRUE);
        } else {
            jobPosting.setShow(IsEnum.FALSE);
        }
        jobPostingManager.updateJobPosting(jobPosting);
    }

    public JobPostingBo recommend(JobPostingRecommendDto dto) {
        JobPosting jobPosting = BeanUtils.copy(dto, JobPosting.class);
        jobPostingManager.updateJobPosting(jobPosting);
        return BeanUtils.copy(jobPosting, JobPostingBo.class);
    }

    public Page<FavouriteBo> jobFavouritePage(PageRequest pageRequest) {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        AtomicReference<Long> jobPostId = new AtomicReference<>(0L);
       pageRequest.getFilter().forEach(filter -> {
           if(filter.getField().equals("entityId")) {
               jobPostId.set(Long.parseLong(filter.getValue().toString()));
           }
       });
        if(jobPostId.get() == 0L) {
            throw new BaseException("职位id不能为空");
        }

        JobPosting job = jobPostingManager.fetchJobPostingById(jobPostId.get());
        if(job == null) {
            throw new BaseException("职位不存在");
        }
        if(!job.getUserId().equals(userId)) {
            throw new BaseException("职位不属于当前用户");
        }

        Page<Favourite> page = favouriteManager.page(pageRequest);
        List<Long> userIds = page.getList().stream().map(Favourite::getUserId).collect(Collectors.toList());
        Map<Long, UserObjBo> userMap = userCommonManager.fetchUserMapByUserIds(userIds);
        return page.convert(v -> {
            FavouriteBo bo = BeanUtils.copy(v, FavouriteBo.class);
            if (userMap.get(v.getUserId()) != null) {
                bo.setUserObj(userMap.get(v.getUserId()));
            }
            return bo;
        });
    }
}
