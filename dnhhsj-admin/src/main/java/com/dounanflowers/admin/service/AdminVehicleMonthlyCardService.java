package com.dounanflowers.admin.service;

import com.dounanflowers.common.bo.VehicleMonthlyCardBo;
import com.dounanflowers.common.bo.VehicleMonthlyCardOrderBo;
import com.dounanflowers.common.dto.CreateMonthlyCardDTO;
import com.dounanflowers.common.entity.VehicleMonthlyCard;
import com.dounanflowers.common.entity.VehicleMonthlyCardOrder;
import com.dounanflowers.common.enums.CouponSceneEnum;
import com.dounanflowers.common.manager.VehicleManager;
import com.dounanflowers.common.service.CouponService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

@Service
@RequiredArgsConstructor
public class AdminVehicleMonthlyCardService {

    private final VehicleManager vehicleManager;
    private final CouponService couponService;

    @Transactional(rollbackFor = Exception.class)
    public void saveMonthlyCard(CreateMonthlyCardDTO dto) {
        VehicleMonthlyCard monthlyCard = new VehicleMonthlyCard();
        BeanUtils.copyProperties(dto, monthlyCard);
        vehicleManager.saveMonthlyCard(monthlyCard);
    }

    public Page<VehicleMonthlyCardBo> pageMonthlyCards(PageRequest pageRequest) {
        Page<VehicleMonthlyCard> page = vehicleManager.pageMonthlyCards(pageRequest);
        return page.convert(card -> {
            VehicleMonthlyCardBo bo = new VehicleMonthlyCardBo();
            BeanUtils.copyProperties(card, bo);
            return bo;
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteMonthlyCard(Long id) {
        VehicleMonthlyCard monthlyCard = vehicleManager.getMonthlyCard(id);
        if (monthlyCard == null) {
            throw new BaseException("月卡不存在");
        }
        vehicleManager.deleteMonthlyCard(monthlyCard);
    }

    public VehicleMonthlyCardBo getMonthlyCard(Long id) {
        VehicleMonthlyCard card = vehicleManager.getMonthlyCard(id);
        if (card == null) {
            throw new BaseException("月卡不存在");
        }
        VehicleMonthlyCardBo bo = new VehicleMonthlyCardBo();
        BeanUtils.copyProperties(card, bo);
        return bo;
    }

    public Page<VehicleMonthlyCardOrderBo> pageMonthlyCardOrders(PageRequest pageRequest) {
        Page<VehicleMonthlyCardOrder> page = vehicleManager.pageMonthlyCardOrders(pageRequest);
        return page.convert(order -> {
            VehicleMonthlyCardOrderBo bo = new VehicleMonthlyCardOrderBo();
            BeanUtils.copyProperties(order, bo);
            return bo;
        });
    }

    /**
     * 计算月卡价格
     *
     * @param monthlyCardId 月卡ID
     * @param couponId      优惠券模板ID
     * @return 计算后的价格
     */
    public BigDecimal calculatePrice(Long monthlyCardId, Long couponId) {
        VehicleMonthlyCard monthlyCard = vehicleManager.getMonthlyCard(monthlyCardId);
        if (monthlyCard == null) {
            throw new BaseException("月卡不存在");
        }

        return couponService.calculateActualPriceByTplId(
                BigDecimal.valueOf(monthlyCard.getPrice()),
                couponId,
                CouponSceneEnum.VEHICLE
        );
    }
}
