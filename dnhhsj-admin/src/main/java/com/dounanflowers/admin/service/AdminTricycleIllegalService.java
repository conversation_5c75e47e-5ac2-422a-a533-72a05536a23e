package com.dounanflowers.admin.service;

import com.dounanflowers.admin.bpm.enums.ProcessEnum;
import com.dounanflowers.admin.dto.TricycleIllegalFinePayDto;
import com.dounanflowers.admin.dto.TricycleIllegalHandleDto;
import com.dounanflowers.admin.dto.TricycleIllegalRecordDto;
import com.dounanflowers.bpm.dto.BpmHandleDto;
import com.dounanflowers.bpm.dto.BpmSubmitDto;
import com.dounanflowers.bpm.entity.BpmInstance;
import com.dounanflowers.bpm.enums.ActionTypeEnum;
import com.dounanflowers.bpm.service.BpmService;
import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.entity.AdminUser;
import com.dounanflowers.common.entity.Tricycle;
import com.dounanflowers.common.entity.TricycleIllegal;
import com.dounanflowers.common.entity.TricycleIllegalRule;
import com.dounanflowers.common.enums.TricycleIllegalStatsuEnum;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.common.manager.TricycleManager;
import com.dounanflowers.common.service.FileService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.IdUtils;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.bo.WxPayParamsBo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdminTricycleIllegalService {

    private final FileService fileService;

    private final TricycleManager tricycleManager;

    private final AdminUserManager adminUserManager;

    private final BpmService bpmService;

    public Page<TricycleIllegalRuleBo> pageIllegalRule(PageRequest pageRequest) {
        Page<TricycleIllegalRule> pageIllegalRule = tricycleManager.pageIllegalRule(pageRequest);
        return pageIllegalRule.convert(v -> BeanUtils.copy(v, TricycleIllegalRuleBo.class));
    }

    public Boolean deleteIllegalRule(Long id) {
        return tricycleManager.deleteIllegalRule(id);
    }

    public TricycleIllegalRuleBo saveIllegalRule(TricycleIllegalRuleBo tricycleIllegalRuleBo) {
        TricycleIllegalRule tricycleIllegalRule = BeanUtils.copy(tricycleIllegalRuleBo, TricycleIllegalRule.class);
        tricycleManager.saveIllegalRule(tricycleIllegalRule);
        return BeanUtils.copy(tricycleIllegalRule, TricycleIllegalRuleBo.class);
    }

    @Transactional(rollbackFor = Exception.class)
    public TricycleIllegalBo createIllegalRecord(TricycleIllegalRecordDto dto) {
        Long currentUserId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        Tricycle tricycle = tricycleManager.fetchTricycleById(dto.getTricycleId());
        if (tricycle == null) {
            throw new BaseException("三轮车未找到");
        }
        TricycleIllegalRule rule = tricycleManager.fetchIllegalRuleById(dto.getRuleId());
        if (rule == null) {
            throw new BaseException("违规规则未找到");
        }
        if (rule.getStatus().isFalse()) {
            throw new BaseException("违规规则未启用");
        }
        TricycleIllegal db = tricycleManager.fetchUnEndIllegalByTricycleAndRule(dto.getTricycleId(), dto.getRuleId());
        if (db != null) {
            throw new BaseException("已存在未结束的同规则违规记录");
        }
        Long id = IdUtils.nextId();
        List<String> images = dto.getImages();
        if (CollectionUtils.isNotEmpty(images)) {
            fileService.updateFileLink(id, images);
        }
        TricycleIllegal illegal = new TricycleIllegal();
        illegal.setId(id);
        illegal.setTricycleId(dto.getTricycleId())
                .setIsEnd(IsEnum.FALSE)
                .setUserId(tricycle.getUserId())
                .setTricycleNo(tricycle.getNumber())
                .setRuleId(dto.getRuleId())
                .setRuleId(dto.getRuleId())
                .setRuleName(rule.getTitle())
                .setFine(rule.getFine())
                .setNeedTow(rule.getNeedTow())
                .setPunishDelayMinute(rule.getPunishDelayMinute());
        tricycleManager.saveIllegal(illegal);
        BpmSubmitDto enter = new BpmSubmitDto()
                .setOuterType(ProcessEnum.TRICYCLE_ILLEGAL.getKey())
                .setOuterId(illegal.getId())
                .setProcessCode(ProcessEnum.TRICYCLE_ILLEGAL.getKey())
                .setData(illegal)
                .setUserId(tricycle.getUserId())
                .setSubmitUserId(currentUserId);
        BpmInstance instance = bpmService.submit(enter);
        bpmService.doAction(new BpmHandleDto()
                .setActionType(ActionTypeEnum.APPROVE.name())
                .setInstanceId(instance.getId())
                .setUserId(currentUserId));
        return BeanUtils.copy(illegal, TricycleIllegalBo.class);
    }

    public List<TricycleIllegalRuleBo> listIllegalRule() {
        List<TricycleIllegalRule> list = tricycleManager.listAvailableIllegalRule();
        return BeanUtils.copyList(list, TricycleIllegalRuleBo.class);
    }

    public TricycleIllegalPageBo pageIllegalRecord(PageRequest pageRequest) {
        Page<TricycleIllegal> pageIllegal = tricycleManager.pageIllegal(pageRequest);
        TricycleIllegalPageBo pageBo = new TricycleIllegalPageBo();
        pageBo.setTotal(pageIllegal.getTotal());
        List<TricycleIllegal> list = pageIllegal.getList();
        List<Long> tricycleIds = list.stream().map(TricycleIllegal::getTricycleId).toList();
        List<Tricycle> tricycles = tricycleManager.fetchTricycleListByIds(tricycleIds);
        List<Long> userIds = tricycles.stream().map(Tricycle::getUserId).toList();
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(userIds);
        Map<Long, Tricycle> tricycleMap = tricycles.stream().collect(Collectors.toMap(Tricycle::getId, v -> v));
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));
        pageBo.setList(list.stream().map(v -> {
            TricycleIllegalBo copy = BeanUtils.copy(v, TricycleIllegalBo.class);
            copy.setImages(v.getImages());
            Tricycle tricycle = tricycleMap.get(v.getTricycleId());
            if (tricycle != null) {
                copy.setTricycleObj(BeanUtils.copy(tricycle, TricycleBo.class));
                AdminUser adminUser = adminUserMap.get(tricycle.getUserId());
                if (adminUser != null) {
                    copy.setTricycleMasterObj(BeanUtils.copy(adminUser, AdminUserBo.class));
                }
            }
            return copy;
        }).toList());
        return pageBo;
    }

    public TricycleIllegalBo getIllegalRecordById(Long id) {
        TricycleIllegal illegal = tricycleManager.fetchIllegalById(id);
        if (illegal == null) {
            return null;
        }
        return BeanUtils.copy(illegal, TricycleIllegalBo.class);
    }

    public TricycleIllegalBo handleIllegalRecord(TricycleIllegalHandleDto dto) {
        Long currentUserId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        TricycleIllegal illegal = tricycleManager.fetchIllegalById(dto.getTricycleIllegalId());
        if (illegal == null) {
            throw new BaseException("违规记录未找到");
        }
        TricycleIllegalStatsuEnum status = BaseEnum.ordinalOf(TricycleIllegalStatsuEnum.class, dto.getHandleStatus());
        BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.TRICYCLE_ILLEGAL.getKey(), illegal.getId());
        if (instance == null) {
            throw new BaseException("未找到流程实例");
        }
        switch (status) {
            case CANCELED -> bpmService.doAction(new BpmHandleDto()
                    .setInstanceId(instance.getId())
                    .setActionType(ActionTypeEnum.CANCEL.name())
                    .setUserId(currentUserId));
            case PROCESSING -> bpmService.doAction(new BpmHandleDto()
                    .setInstanceId(instance.getId())
                    .setActionType(ActionTypeEnum.APPROVE.name())
                    .setUserId(currentUserId));
        }
        return BeanUtils.copy(illegal, TricycleIllegalBo.class);
    }

    public Boolean passIllegalRecord(Long id) {
        Long currentUserId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        TricycleIllegal illegal = tricycleManager.fetchIllegalById(id);
        if (illegal == null) {
            throw new BaseException("违规记录未找到");
        }
        BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.TRICYCLE_ILLEGAL.getKey(), illegal.getId());
        if (instance == null) {
            throw new BaseException("未找到流程实例");
        }
        bpmService.doAction(new BpmHandleDto()
                .setInstanceId(instance.getId())
                .setActionType(ActionTypeEnum.CANCEL.name())
                .setUserId(currentUserId));
        return true;
    }

    public WxPayParamsBo payFine(TricycleIllegalFinePayDto dto) {
        Long currentUserId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        TricycleIllegal illegal = tricycleManager.fetchIllegalById(dto.getTricycleIllegalId());
        if (illegal == null) {
            throw new BaseException("违规记录未找到");
        }
        if (!Objects.equals(illegal.getUserId(), SecurityHolder.<Long, AdminUserBo>session().getUserId())) {
            throw new BaseException("该违规记录不属于当前商家");
        }
        if (illegal.getStatus() != TricycleIllegalStatsuEnum.PROCESSING) {
            throw new BaseException("该违规记录不是已开罚单状态，无法缴纳罚款");
        }
        if (dto.isFake()) {
            paidIllegalFine(illegal, currentUserId);
        } else {
            // TODO 调起支付
        }
        return null;
    }

    public void paidIllegalFine(TricycleIllegal illegal, Long userId) {
        BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.TRICYCLE_ILLEGAL.getKey(), illegal.getId());
        if (instance == null) {
            throw new BaseException("未找到流程实例");
        }
        bpmService.doAction(new BpmHandleDto()
                .setInstanceId(instance.getId())
                .setActionType(ActionTypeEnum.APPROVE.name())
                .setUserId(userId));
    }


}
