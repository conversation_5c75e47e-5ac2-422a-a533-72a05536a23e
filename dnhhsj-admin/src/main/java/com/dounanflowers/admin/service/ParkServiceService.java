package com.dounanflowers.admin.service;

import com.dounanflowers.admin.bo.ParkServiceBo;
import com.dounanflowers.admin.bo.ParkServicePageBo;
import com.dounanflowers.admin.bo.ParkServiceTimeSlotBo;
import com.dounanflowers.admin.bo.ParkServiceTimesBo;
import com.dounanflowers.common.entity.ParkService;
import com.dounanflowers.common.entity.ParkServiceAppointment;
import com.dounanflowers.common.entity.ParkServiceTimes;
import com.dounanflowers.common.manager.JobPostingManager;
import com.dounanflowers.common.service.FileService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ParkServiceService {

    private final JobPostingManager jobPostingManager;

    private final FileService fileService;

    public ParkServiceBo create(ParkServiceBo dto) {
        ParkService parkService = BeanUtils.copy(dto, ParkService.class);
        if (dto.getDays() == null) {
            parkService.setDays(7);
        }
        parkService.setTimes(dto.getTimes().stream().map(v -> BeanUtils.copy(v, ParkServiceTimes.class)).collect(Collectors.toList()));
        parkService = jobPostingManager.createParkService(parkService);
        fileService.updateFileLink(parkService.getId(), dto.getImages());
        return toParkServiceBo(parkService);
    }

    public ParkServiceBo update(ParkServiceBo dto) {
        ParkService parkService = BeanUtils.copy(dto, ParkService.class);
        parkService.setTimes(dto.getTimes().stream().map(v -> BeanUtils.copy(v, ParkServiceTimes.class)).collect(Collectors.toList()));
        parkService = jobPostingManager.updateParkService(parkService);
        fileService.updateFileLink(parkService.getId(), dto.getImages());
        return toParkServiceBo(parkService);
    }

    public void delete(Long id) {
        jobPostingManager.deleteParkService(id);
    }

    public ParkServicePageBo pageList(PageRequest pageRequest) {
        Page<ParkService> page = jobPostingManager.pageParkServices(pageRequest);
        List<Long> ids = page.getList().stream().map(ParkService::getId).toList();
        List<ParkServiceAppointment> parkServiceAppointments = jobPostingManager.fetchTodoAppointmentsByServiceIds(ids);
        Map<Long, List<ParkServiceAppointment>> appointmentGroup = parkServiceAppointments.stream().collect(Collectors.groupingBy(ParkServiceAppointment::getServiceId, Collectors.toList()));
        Page<ParkServiceBo> convert = page.convert(v -> {
            ParkServiceBo bo = BeanUtils.copy(v, ParkServiceBo.class);
            bo.setImages(v.getImages());
            if (appointmentGroup.containsKey(v.getId())) {
                bo.setTodoCount(appointmentGroup.get(v.getId()).size());
            }
            if (v.getTimes() != null) {
                bo.setTimes(v.getTimes().stream().map(v1 -> BeanUtils.copy(v1, ParkServiceTimesBo.class)).collect(Collectors.toList()));
            }
            return bo;
        });
//        Long todoCount = jobPostingManager.fetchTodoAppointmentCount();
        ParkServicePageBo parkServicePageBo = new ParkServicePageBo();
        parkServicePageBo.setList(convert.getList());
        parkServicePageBo.setTotal(convert.getTotal());
//        parkServicePageBo.setTodoCount(todoCount.intValue());
        return parkServicePageBo;
    }

    private ParkServiceBo toParkServiceBo(ParkService entity) {
        if (entity == null) {
            return null;
        }
        ParkServiceBo bo = BeanUtils.copy(entity, ParkServiceBo.class);
        bo.setImages(entity.getImages());

        if (entity.getTimes() != null) {
            bo.setTimes(entity.getTimes().stream().map(v1 -> BeanUtils.copy(v1, ParkServiceTimesBo.class)).collect(Collectors.toList()));
        }
        return bo;
    }

    public ParkServiceBo get(Long id) {
        ParkService entity = jobPostingManager.fetchParkServiceById(id);
        if (entity == null) {
            throw new BaseException("服务不存在");
        }
        ParkServiceBo bo = toParkServiceBo(entity);
        List<ParkServiceAppointment> parkServiceAppointments = jobPostingManager.fetchTodoAppointmentsByServiceIds(Lists.newArrayList(id));

        // Generate time slots based on entity configuration
        LocalDateTime now = LocalDateTime.now();
        List<ParkServiceTimeSlotBo> timeSlots = new ArrayList<>();

        // Generate slots for configured days
        for (int day = 0; day < entity.getDays(); day++) {
            LocalDate date = now.toLocalDate().plusDays(day);

            // For each time slot in the service
            for (ParkServiceTimes timeSlot : entity.getTimes()) {
                LocalTime slotStartTime = LocalTime.parse(timeSlot.getStartTime());
                LocalTime slotEndTime = LocalTime.parse(timeSlot.getEndTime());
                Duration interval = Duration.ofMinutes(entity.getMinutes());

                LocalDateTime slotStart = LocalDateTime.of(date, slotStartTime);

                // Skip slots before current time for today
                if (day == 0) {
                    slotStart = LocalDateTime.of(date, now.toLocalTime())
                            .withMinute(0)
                            .withSecond(0)
                            .withNano(0)
                            .plusHours(1);
                    if (slotStart.toLocalTime().isAfter(slotEndTime)) {
                        continue;
                    }
                }

                while (!slotStart.toLocalTime().isAfter(slotEndTime)) {
                    LocalDateTime slotEnd = slotStart.plus(interval);

                    // Check if the slot overlaps with any existing appointment
                    boolean isAvailable = true;
                    for (ParkServiceAppointment appointment : parkServiceAppointments) {
                        if (slotStart.isEqual(appointment.getStartTime())
                                || (slotEnd.isAfter(appointment.getStartTime()) && slotStart.isBefore(appointment.getStartTime()))
                                || (slotEnd.isAfter(appointment.getEndTime()) && slotStart.isBefore(appointment.getEndTime()))) {
                            isAvailable = false;
                            break;
                        }
                    }

                    // Create time slot if it's in the future
                    if (slotStart.isAfter(now)) {
                        ParkServiceTimeSlotBo timeSlotBo = new ParkServiceTimeSlotBo();
                        timeSlotBo.setStartTime(slotStart);
                        timeSlotBo.setEndTime(slotEnd);
                        timeSlotBo.setDate(date);
                        timeSlotBo.setAvailable(isAvailable);
                        timeSlots.add(timeSlotBo);
                    }

                    slotStart = slotEnd;
                }
            }
        }

        bo.setTimeSlots(timeSlots);
        return bo;
    }
}
