package com.dounanflowers.admin.service;

import com.dounanflowers.admin.dto.AdminUserBatchCreateDto;
import com.dounanflowers.admin.dto.AdminUserDto;
import com.dounanflowers.admin.dto.AdminUserEditDto;
import com.dounanflowers.admin.dto.AdminUserUpdateDto;
import com.dounanflowers.common.dto.TwoFactorLoginDto;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.dto.UserRealVerifyDto;
import com.dounanflowers.common.entity.AdminPermission;
import com.dounanflowers.common.entity.AdminRole;
import com.dounanflowers.common.entity.AdminUser;
import com.dounanflowers.common.enums.GenderEnum;
import com.dounanflowers.common.enums.PermissionTypeEnum;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.common.manager.TricycleManager;
import com.dounanflowers.common.model.TricycleCount;
import com.dounanflowers.common.model.TricycleIllegalCount;
import com.dounanflowers.common.service.TwoFactorAuthService;
import com.dounanflowers.common.utils.IdCardUtils;
import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.service.AliyunOpenapiService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AdminUserService {

    private final AdminUserManager adminUserManager;

    private final TricycleManager tricycleManager;

    private final AliyunOpenapiService aliyunOpenapiService;

    private final TwoFactorAuthService twoFactorAuthService;

    public SessionInfo<Long, AdminUserBo> loginByUsername(String username, String password) {
        AdminUserBo user = fetchLoginByUsername(username);
        if (user == null) {
            throw new BaseException("登录失败");
        }
        String encryptPassword = SecurityHolder.context("admin").encryptPassword(password);
        if (!encryptPassword.equals(user.getPassword())) {
            throw new BaseException("登录失败");
        }
        if (!user.getIsEnabled()) {
            throw new BaseException("用户已被禁用");
        }
        return SecurityHolder.context("admin").login(user);
    }

    public AdminUser fetchByUsername(String username) {
        return adminUserManager.fetchByUsername(username);
    }

    public AdminUserBo fetchLoginByUsername(String username) {
        AdminUser user = adminUserManager.fetchByUsername(username);
        if (user == null) {
            throw new BaseException("登录失败");
        }
        return getUserBo(user, PermissionTypeEnum.ADMIN);
    }

    private AdminUserBo getUserBo(AdminUser user, PermissionTypeEnum type) {
        // 1. 基础用户信息转换
        AdminUserBo ret = AdminUserBo.from(user);

        // 2. 获取用户角色信息
        Long userId = user.getId();
        List<AdminRole> roles = adminUserManager.fetchRolesByUserIdAndType(userId, type);

        if (CollectionUtils.isNotEmpty(roles)) {
            ret.setHome(roles.getFirst().getHome());
        }

        // 3. 设置角色编码
        List<String> roleCodes = roles.stream()
                .map(AdminRole::getCode)
                .toList();
        ret.setRoles(roleCodes);

        // 4. 获取用户权限
        List<String> permissions;
        if (roleCodes.contains("admin")) {
            // 如果是admin角色，获取所有权限
            permissions = adminUserManager.listAllPermissions()
                    .stream()
                    .map(AdminPermission::getCode)
                    .distinct()
                    .toList();
        } else {
            // 其他角色只获取分配的权限
            permissions = adminUserManager.listPermissionsByRoles(roleCodes)
                    .stream()
                    .map(AdminPermission::getCode)
                    .distinct()
                    .toList();
        }
        ret.setPermissions(permissions);

        return ret;
    }

    public void changePassword(Long userId, String oldPassword, String newPassword) {
        AdminUser adminUser = adminUserManager.fetchById(userId);
        if (!adminUser.getPassword().equals(oldPassword)) {
            throw new BaseException("旧密码错误");
        }
        adminUser.setPassword(newPassword);
        adminUserManager.save(adminUser);
    }

    public AdminUser getUserByOpenId(String openId) {
        return adminUserManager.fetchUserByOpenId(openId);
    }

    public AdminUserBo loginByOpenId(String openId) {
        AdminUser user = adminUserManager.fetchUserByOpenId(openId);
        if (user == null) {
            throw new BaseException("登录失败");
        }
        return getUserBo(user, PermissionTypeEnum.SHOP);
    }

    public AdminUserBo loginByMobile(String mobile) {
        AdminUser user = adminUserManager.fetchByMobile(mobile);
        if (user == null) {
            throw new BaseException("登录失败");
        }
        return getUserBo(user, PermissionTypeEnum.SHOP);
    }

    public void save(AdminUser user) {
        adminUserManager.save(user);
    }

    public AdminUser getUserById(Long id) {
        return adminUserManager.fetchById(id);
    }

    public AdminUserBo addAdmin(AdminUserDto enter) {
        AdminUser exist = adminUserManager.fetchByUsername(enter.getUsername());
        if (exist != null) {
            throw new BaseException("已存在相同用户名用户");
        }
        exist = adminUserManager.fetchByMobile(enter.getMobile());
        if (exist != null) {
            throw new BaseException("已存在相同手机号用户");
        }
        AdminUser user = new AdminUser();
        user.setUsername(enter.getUsername());
        user.setMobile(enter.getMobile());
        if (StringUtils.isBlank(user.getPassword())) {
            user.setPassword(SecurityHolder.context().encryptPassword("dnhs@123"));
        } else {
            user.setPassword(SecurityHolder.context().encryptPassword(user.getPassword()));
        }
        adminUserManager.save(user);
        AdminUserBo from = AdminUserBo.from(user);
        return from.clearSensitive();
    }

    public AdminUser fetchByMobile(String phoneNumber) {
        return adminUserManager.fetchByMobile(phoneNumber);
    }

    public Page<AdminUserBo> pageList(PageRequest dto) {
        Page<AdminUser> page = adminUserManager.pageList(dto);
        List<Long> userIds = page.getList().stream().map(BaseEntity::getId).toList();
        List<TricycleCount> tricycleCounts = tricycleManager.fetchTricycleByUserIds(userIds);
        Map<Long, Long> tricycleMap = tricycleCounts.stream().collect(Collectors.toMap(TricycleCount::getUserId, TricycleCount::getCount));
        List<TricycleIllegalCount> tricycleIllegalCounts = tricycleManager.fetchUnEndTricycleIllegalByUserIds(userIds);
        Map<Long, Long> tricycleIllegalMap = tricycleIllegalCounts.stream().collect(Collectors.toMap(TricycleIllegalCount::getUserId, TricycleIllegalCount::getCount));
        return page.convert(v -> {
            AdminUserBo adminUserBo = AdminUserBo.from(v).clearSensitive();
            adminUserBo.setRoles(v.getRoles());
            Long tricycle = tricycleMap.get(v.getId());
            if (tricycle != null) {
                adminUserBo.setTricycleCount(tricycle.intValue());
            }
            Long illegal = tricycleIllegalMap.get(v.getId());
            if (illegal != null) {
                adminUserBo.setTricycleIllegalCount(illegal.intValue());
            }
            return adminUserBo;
        });
    }

    public AdminUserBo saveAdmin(AdminUserDto enter) {
        AdminUser user = adminUserManager.fetchById(enter.getId());
        adminUserManager.save(user);
        AdminUserBo from = AdminUserBo.from(user);
        return from.clearSensitive();
    }

    public AdminUserBo saveAdmin(AdminUserBo enter) {
        AdminUser user = BeanUtils.copy(enter, AdminUser.class);
        if (enter.getId() == null && enter.getPassword() == null) {
            throw new BaseException("密码不能为空");
        }
        if (enter.getPassword() != null) {
            user.setPassword(SecurityHolder.context().encryptPassword(enter.getPassword()));
        }
        user.setRoles(enter.getRoles());
        adminUserManager.save(user);
        return AdminUserBo.from(user).clearSensitive();
    }

    public void deleteAdmin(Long id) {
        AdminUser user = adminUserManager.fetchById(id);
        adminUserManager.delete(id);
        SecurityHolder.context("admin").kick(id);
        SecurityHolder.context("shop").kick(id);
        adminUserManager.deleteUserRole(id);
    }

    public AdminUserBo detail(Long id) {
        AdminUser user = adminUserManager.fetchById(id);
        if (user == null) {
            return null;
        }
        AdminUserBo from = AdminUserBo.from(user);
        return from.clearSensitive();
    }

    public AdminUser bindAccount(String phoneNumberCode, String openId) {
        return null;
    }


    public void realnameVerify(UserRealVerifyDto dto) {
        SessionInfo<Long, AdminUserBo> session = SecurityHolder.session();
        Long userId = session.getUserId();
        AdminUser user = adminUserManager.fetchById(userId);
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        if (dto.getFake() == null || !dto.getFake()) {
            aliyunOpenapiService.credentialVerify(dto.getRealname(), dto.getIdCardNum(), dto.getIdCardUrl1());
        } else {
            if (!IdCardUtils.verifyIdCard(dto.getIdCardNum())) {
                throw new BaseException("身份证格式不正确");
            }
            if (user.getFake().isFalse()) {
                throw new BaseException("当前用户无法模拟实名认证");
            }
        }
        user.setRealname(dto.getRealname());
        adminUserManager.updateUserCredential(userId, dto.getRealname(), dto.getIdCardNum(), dto.getIdCardUrl1(), dto.getIdCardUrl2());
        adminUserManager.save(user);
        session.getUserInfo().setRealname(user.getRealname());
        SecurityHolder.update(session);
    }

    public AdminUserBo updateSelfUserInfo(AdminUserEditDto dto) {
        SessionInfo<Long, AdminUserBo> session = SecurityHolder.session();
        Long userId = session.getUserId();
        AdminUser user = adminUserManager.fetchById(userId);
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        user.setNickname(dto.getNickname());
        user.setAvatar(dto.getAvatar());
        user.setGender(BaseEnum.ordinalOf(GenderEnum.class, dto.getGender()));
        adminUserManager.save(user);
        session.getUserInfo().setNickname(user.getNickname());
        session.getUserInfo().setAvatar(user.getAvatar());
        session.getUserInfo().setGender(user.getGender().ordinal() + "");
        SecurityHolder.update(session);
        return session.getUserInfo();
    }

    @Transactional(rollbackFor = Exception.class)
    public AdminUserBo updateUserInfo(AdminUserUpdateDto dto) {
        AdminUser user = adminUserManager.fetchById(dto.getId());
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        if ("admindnhhsj".equals(user.getUsername())) {
            throw new BaseException("系统管理员不允许修改");
        }
        user.setMobile(dto.getMobile());
        adminUserManager.save(user);
        adminUserManager.updateUserRole(user.getId(), dto.getRoles());
        return AdminUserBo.from(user);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean batchCreateUser(AdminUserBatchCreateDto dto) {
        if (CollectionUtils.isEmpty(dto.getMobiles())) {
            throw new BaseException("手机号不能为空");
        }
        for (String mobile : dto.getMobiles()) {
            AdminUser exist = adminUserManager.fetchByMobile(mobile);
            if (exist != null) {
                continue;
            }
            AdminUser user = new AdminUser();
            user.setMobile(mobile);
            user.setNickname(mobile);
            user.setPassword(SecurityHolder.context().encryptPassword("dnhs@123"));
            user.setUsername(mobile);
            adminUserManager.save(user);
            adminUserManager.updateUserRole(user.getId(), dto.getRoles());
        }
        return true;
    }

    public Boolean deleteUser(Long id) {
        AdminUser user = adminUserManager.fetchById(id);
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        if ("admindnhhsj".equals(user.getUsername())) {
            throw new BaseException("系统管理员不允许删除");
        }
        if (StringUtils.isNotBlank(user.getOpenId())) {
            throw new BaseException("用户已绑定微信，不允许删除");
        }
        adminUserManager.delete(id);
        SecurityHolder.context("admin").kick(id);
        SecurityHolder.context("shop").kick(id);
        return true;
    }

    public Boolean deleteUserShop(Long id) {
        AdminUser user = adminUserManager.fetchById(id);
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        if ("admindnhhsj".equals(user.getUsername())) {
            throw new BaseException("系统管理员不允许删除");
        }
        adminUserManager.delete(id);
        SecurityHolder.context("shop").kick(id);
        return true;
    }

    public Boolean changeUserEnabled(Long id, Boolean isEnabled) {
        AdminUser user = adminUserManager.fetchById(id);
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        if ("admindnhhsj".equals(user.getUsername())) {
            throw new BaseException("系统管理员不允许修改");
        }
        user.setIsEnabled(IsEnum.of(isEnabled));
        adminUserManager.save(user);
        SecurityHolder.context("admin").logoutByUserId(id);
        SecurityHolder.context("shop").logoutByUserId(id);
        return true;
    }

    public AdminUser fetchUserById(Long id) {
        return adminUserManager.fetchById(id);
    }

    public void unbindOpenid(Long userId) {
        AdminUser user = adminUserManager.fetchById(userId);
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        user.setOpenId("");
        adminUserManager.save(user);
        SecurityHolder.<Long,AdminUserBo>session().getUserInfo().setOpenId(null);
        SecurityHolder.update(SecurityHolder.<Long,AdminUserBo>session());
    }

    public void unbindMobile(Long userId) {
        AdminUser user = adminUserManager.fetchById(userId);
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        user.setMobile("");
        adminUserManager.save(user);
        SecurityHolder.<Long, AdminUserBo>session().getUserInfo().setMobile(null);
        SecurityHolder.update(SecurityHolder.<Long,AdminUserBo>session());
    }

    /**
     * Login using 2FA code
     *
     * @param dto The 2FA login DTO containing mobile and code
     * @return The session info
     */
    public SessionInfo<Long, AdminUserBo> loginBy2Fa(TwoFactorLoginDto dto) {
        // Verify the 2FA code
        if (!twoFactorAuthService.verifyCode(dto.getCode())) {
            throw new BaseException("2FA验证码无效");
        }

        // Find the user by mobile
        AdminUser user = adminUserManager.fetchByMobile(dto.getMobile());
        if (user == null) {
            throw new BaseException("用户不存在");
        }

        if (user.getIsEnabled().isFalse()) {
            throw new BaseException("用户已被禁用");
        }

        // Get the user BO with permissions
        AdminUserBo userBo = getUserBo(user, PermissionTypeEnum.ADMIN);

        // Login the user
        return SecurityHolder.context("shop").login(userBo);
    }
}
