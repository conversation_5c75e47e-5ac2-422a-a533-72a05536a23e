package com.dounanflowers.admin.service;

import cn.binarywang.wx.miniapp.api.WxMaUserService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.bo.ClientUserBo;
import com.dounanflowers.common.entity.AdminUser;
import com.dounanflowers.common.entity.ClientUser;
import com.dounanflowers.common.enums.GenderEnum;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.IpUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.ThirdPartyHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdminWxService {

    private final AdminUserService userService;

    @Transactional(rollbackFor = Exception.class)
    public SessionInfo<Long, AdminUserBo> login(String code, String phoneNumberCode) {
        String sessionKey = null;
        String openId = null;
        AdminUserBo bo;
        try {
            WxMaJscode2SessionResult result = getWxService().getSessionInfo(code);
            sessionKey = result.getSessionKey();
            openId = result.getOpenid();
        } catch (Exception e) {
            log.error("获取sessionKey失败:{}", e.getMessage(), e);
        }
        if (sessionKey == null || openId == null) {
            throw new BaseException("获取sessionKey失败");
        }
        AdminUser user = userService.getUserByOpenId(openId);
        if (user != null) {
            // 更新用户信息
            user.setLastLoginIp(IpUtils.getIpAddr());
            user.setLastLoginAt(LocalDateTime.now());
        } else if (StringUtils.isNotBlank(phoneNumberCode)) {
            try {
                WxMaPhoneNumberInfo phoneNoInfo = getWxService().getPhoneNoInfo(phoneNumberCode);
                String phoneNumber = phoneNoInfo.getPhoneNumber();
                if (phoneNumber == null) {
                    throw new BaseException("获取手机号失败");
                }
                user = userService.fetchByMobile(phoneNumber);
                if (user == null) {
                    user = new AdminUser();
                    user.setMobile(phoneNumber);
                    user.setNickname("用户" + phoneNumber.substring(7));
                } else if (StringUtils.isNotBlank(user.getOpenId())) {
                    throw new BaseException("手机号已被绑定");
                }
                user.setOpenId(openId);
                user.setLastLoginIp(IpUtils.getIpAddr());
                user.setIsEnabled(IsEnum.TRUE);
                user.setLastLoginAt(LocalDateTime.now());
            } catch (BaseException e) {
                throw e;
            } catch (Exception e) {
                log.error("获取手机号失败:{}", e.getMessage(), e);
                throw new BaseException("获取手机号失败");
            }
        } else {
            return new SessionInfo<>();
        }
        if (user.getIsEnabled().isFalse()) {
            throw new BaseException("用户已被禁用");
        }
        userService.save(user);
        bo = userService.loginByOpenId(openId);
        SessionInfo<Long, AdminUserBo> session = SecurityHolder.context("shop").login(bo);
        log.info("用户登录成功:{}", JsonUtils.toJson(session));
        return session;
    }

    public SessionInfo<Long, AdminUserBo> updatePhone(String code) {
        SessionInfo<Long, AdminUserBo> sessionInfo = SecurityHolder.session();
        AdminUserBo user = sessionInfo.getUserInfo();
        AdminUser dbUser = userService.getUserById(user.getId());
        String phoneNumber = null;
        try {
            WxMaPhoneNumberInfo phoneNoInfo = getWxService().getPhoneNoInfo(code);
            phoneNumber = phoneNoInfo.getPhoneNumber();
        } catch (Exception e) {
            log.error("获取手机号失败:{}", e.getMessage(), e);
        }
        if (phoneNumber == null) {
            throw new BaseException("获取手机号失败");
        }
        AdminUser u = userService.fetchByMobile(phoneNumber);
        if (u != null) {
            throw new BaseException("手机号已被绑定");
        }
        dbUser.setMobile(phoneNumber);
        userService.save(dbUser);
        user.setMobile(phoneNumber);
        SecurityHolder.update(sessionInfo);
        return sessionInfo;
    }

    private static WxMaUserService getWxService() {
        return ThirdPartyHolder.wxMaService("shop").getUserService();
    }

    public void logout(Long userId) {
        AdminUser user = userService.getUserById(userId);
        if (user != null && StringUtils.isNotBlank(user.getOpenId())) {
            user.setOpenId(user.getOpenId() + "_logout");
            userService.save(user);
        }
    }

    public SessionInfo<Long, AdminUserBo> loginByMobile(String phoneNumberCode) {
        AdminUser user;
        AdminUserBo bo;
        try {
            WxMaPhoneNumberInfo phoneNoInfo = getWxService().getPhoneNoInfo(phoneNumberCode);
            String phoneNumber = phoneNoInfo.getPhoneNumber();
            if (phoneNumber == null) {
                throw new BaseException("获取手机号失败");
            }
            AdminUser clientUser = userService.fetchByMobile(phoneNumber);
            if (clientUser != null) {
                user = clientUser;
                user.setLastLoginIp(IpUtils.getIpAddr());
                user.setLastLoginAt(LocalDateTime.now());
                userService.save(user);
            } else {
                // 创建用户
                user = new AdminUser();
                user.setUsername("mobile_" + phoneNumber);
                user.setLastLoginIp(IpUtils.getIpAddr());
                user.setLastLoginAt(LocalDateTime.now());
                user.setNickname("用户" + phoneNumber.substring(phoneNumber.length() - 4).toUpperCase());
                user.setAvatar("");
                user.setMobile(phoneNumber);
                user.setGender(GenderEnum.NONE);
                userService.save(user);
            }
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取手机号失败:{}", e.getMessage(), e);
            throw new BaseException("获取手机号失败");
        }
        userService.save(user);
        bo = userService.loginByMobile(user.getMobile());
        SessionInfo<Long, AdminUserBo> session = SecurityHolder.context("shop").login(bo);
        log.info("用户登录成功:{}", JsonUtils.toJson(session));
        return session;
    }

    public SessionInfo<Long, AdminUserBo> openId(String code) {
        String sessionKey = null;
        String openId = null;
        try {
            WxMaJscode2SessionResult result = getWxService().getSessionInfo(code);
            sessionKey = result.getSessionKey();
            openId = result.getOpenid();
        } catch (Exception e) {
            log.error("获取sessionKey失败:{}", e.getMessage(), e);
        }

        if (sessionKey == null || openId == null) {
            throw new BaseException("获取sessionKey失败");
        }
        SessionInfo<Long, AdminUserBo> session = SecurityHolder.session();
        session.getUserInfo().setOpenId(openId);
        SecurityHolder.update(session);
        return session;
    }

    public void bindWx(String code) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUserInfo();
        AdminUser adminUser = userService.fetchUserById(user.getId());
        String openId = null;
        String sessionKey = null;
        try {
            WxMaJscode2SessionResult result = getWxService().getSessionInfo(code);
            sessionKey = result.getSessionKey();
            openId = result.getOpenid();
        } catch (Exception e) {
            log.error("获取sessionKey失败:{}", e.getMessage(), e);
        }
        if (sessionKey == null || openId == null) {
            throw new BaseException("获取sessionKey失败");
        }
        adminUser.setOpenId(openId);
        userService.save(adminUser);
        SecurityHolder.<Long, AdminUserBo>session().getUserInfo().setOpenId(openId);
        SecurityHolder.update(SecurityHolder.<Long, AdminUserBo>session());
    }

}
