package com.dounanflowers.admin.service;

import com.dounanflowers.admin.bpm.enums.ProcessEnum;
import com.dounanflowers.admin.dto.FakePayDto;
import com.dounanflowers.admin.dto.ManualPayDto;
import com.dounanflowers.admin.dto.TricycleApplyDto;
import com.dounanflowers.admin.dto.TricycleCheckDto;
import com.dounanflowers.bpm.dto.BpmHandleDto;
import com.dounanflowers.bpm.dto.BpmSubmitDto;
import com.dounanflowers.bpm.entity.BpmInstance;
import com.dounanflowers.bpm.enums.ActionTypeEnum;
import com.dounanflowers.bpm.service.BpmService;
import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.CreateTricycleBatchDTO;
import com.dounanflowers.common.dto.EditWithPasswordDto;
import com.dounanflowers.common.entity.*;
import com.dounanflowers.common.enums.*;
import com.dounanflowers.common.manager.AdminUserManager;
import com.dounanflowers.common.manager.BillManager;
import com.dounanflowers.common.manager.SystemManager;
import com.dounanflowers.common.manager.TricycleManager;
import com.dounanflowers.common.model.TricycleStatusCount;
import com.dounanflowers.common.service.FileService;
import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.DateUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.ThirdPartyHolder;
import com.dounanflowers.third.bo.WxPayParamsBo;
import com.dounanflowers.third.dto.PayParamsDto;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AdminTricycleService {

    private final TricycleManager tricycleManager;

    private final SystemManager systemManager;

    private final FileService fileService;

    private final BpmService bpmService;

    private final AdminUserManager adminUserManager;

    private final BillManager billManager;

    @Transactional(rollbackFor = Exception.class)
    public TricycleBo applyTricycle(TricycleApplyDto dto) {
        SessionInfo<Long, AdminUserBo> session = SecurityHolder.session();
        Long userId = session.getUserId();
        AdminUser user = adminUserManager.fetchById(userId);
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        user.setRealname(dto.getRealname());
        adminUserManager.save(user);
        Tricycle tricycle = new Tricycle();
        tricycle.setUserId(userId);
        tricycle.setCheckStatus(TricycleCheckStatusEnum.PENDING_DEPOSIT);
        tricycle.setEffectiveAt(LocalDateTime.now());
        tricycleManager.save(tricycle);
        fileService.updateFileLink(tricycle.getId(), dto.getImages());
        TricycleBo copy = BeanUtils.copy(tricycle, TricycleBo.class);
        copy.setImages(dto.getImages());
        tricycle.setImages(dto.getImages());
        saveRecord(dto, tricycle);
        return copy;
    }

    private void saveRecord(TricycleApplyDto dto, Tricycle tricycle) {
        SessionInfo<Long, AdminUserBo> session = SecurityHolder.session();
        Long userId = session.getUserId();
        TricycleInfoRecord record = new TricycleInfoRecord();
        record.setTricycleId(tricycle.getId());
        record.setUserId(userId);
        record.setRealname(dto.getRealname());
        record.setMobile(dto.getMobile());
        record.setImages(JsonUtils.toJson(dto.getImages()));
        tricycleManager.saveTricycleInfoRecord(record);
    }

    @Transactional(rollbackFor = Exception.class)
    public TricycleBo applyTricycleAgain(TricycleApplyDto dto) {
        Tricycle tricycle = tricycleManager.fetchTricycleById(dto.getId());
        if (tricycle == null) {
            throw new BaseException("未找到三轮车");
        }
        if (tricycle.getBatchId() == null) {
            tricycle.setImages(dto.getImages());
            tricycle.setEffectiveAt(LocalDateTime.now());
            tricycleManager.save(tricycle);
            fileService.updateFileLink(tricycle.getId(), dto.getImages());
            BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.TRICYCLE_CHECK.getKey(), tricycle.getId());
            if (instance == null) {
                throw new BaseException("未找到流程实例");
            }
            bpmService.doAction(new BpmHandleDto()
                    .setInstanceId(instance.getId())
                    .setActionType(ActionTypeEnum.APPROVE.name())
                    .setRemark(dto.getRemark())
                    .setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId()));
        } else if (tricycle.getUserId() == null) {
            AdminUser adminUser;
            if (StringUtils.isBlank(dto.getMobile())) {
                adminUser = adminUserManager.fetchById(SecurityHolder.<Long, AdminUserBo>session().getUserId());
            } else {
                adminUser = adminUserManager.fetchByMobile(dto.getMobile());
            }
            if (adminUser == null) {
                throw new BaseException("用户不存在");
            }
            tricycle.setImages(dto.getImages());
            tricycle.setUserId(adminUser.getId());
            if (StringUtils.isNotBlank(dto.getRealname())) {
                adminUser.setRealname(dto.getRealname());
                adminUserManager.save(adminUser);
            }
            tricycle.setCheckStatus(TricycleCheckStatusEnum.PENDING_DEPOSIT);
            tricycle.setEffectiveAt(LocalDateTime.now());
            fileService.updateFileLink(tricycle.getId(), dto.getImages());
            tricycleManager.save(tricycle);
            TricycleBatch batch = tricycleManager.fetchBatchById(tricycle.getBatchId());
            if (batch == null) {
                throw new BaseException("未找到批次");
            }
            batch.setCompletedCount(batch.getCompletedCount() + 1);
            tricycleManager.updateBatch(batch);
        } else {
            throw new BaseException("三轮车号牌已被领取");
        }
        TricycleBo copy = BeanUtils.copy(tricycle, TricycleBo.class);
        copy.setImages(dto.getImages());
        tricycle.setImages(dto.getImages());
        saveRecord(dto, tricycle);
        return copy;
    }

    @Transactional(rollbackFor = Exception.class)
    public WxPayParamsBo depositPay(FakePayDto dto) {
        Map<String, Map<String, Object>> stringMapMap = systemManager.fetchSettingMap();
        int depositAmount = Integer
                .parseInt(stringMapMap.get("Merchant").getOrDefault("tricycleDepositCent", "0").toString());
        Tricycle tricycle = tricycleManager.fetchTricycleById(dto.getId());
        if (tricycle == null) {
            throw new BaseException("未找到三轮车");
        }
        int balance = depositAmount;
        if (tricycle.getCheckStatus() != TricycleCheckStatusEnum.PENDING_DEPOSIT) {
            balance += tricycle.getDeposit();
        }
        AdminUserBo userInfo = SecurityHolder.<Long, AdminUserBo>session().getUserInfo();
        Long userId = userInfo.getId();
        TricycleDeposit deposit = new TricycleDeposit();
        deposit.setTricycleId(tricycle.getId());
        deposit.setChange(depositAmount);
        deposit.setBalance(balance);
        deposit.setFake(IsEnum.of(dto.isFake()));
        deposit.setType(TricycleDepositTypeEnum.PAYMENT);
        tricycle.setDeposit(balance);
        tricycleManager.saveDeposit(deposit);
        if (dto.isFake()) {
            startTricycleCheckProcess(tricycle, deposit);
        } else {
            Bill bill = new Bill();
            bill.setType(BillTypeEnum.TRICYCLE_DEPOSIT);
            bill.setEntityId(deposit.getId());
            bill.setUserId(userId);
            bill.setUserType(UserTypeEnum.ADMIN);
            bill.setUserModel("admin_user");
            bill.setOriginalMoneyCent(depositAmount);
            bill.setCouponDiscountCent(0);
            bill.setOrderMoneyCent(depositAmount);
            bill.setPayStatus(BillPayStatusEnum.UNPAID);
            bill.setOtherData(JsonUtils.toJson(deposit));
            billManager.saveBill(bill);
            PayParamsDto params = new PayParamsDto();
            params.setTrxamt(bill.getOrderMoneyCent());
            params.setUnireqsn(String.valueOf(bill.getId()));
            params.setAcct(userInfo.getOpenId());
            params.setBody("支付保证金");
            params.setRemark(userInfo.getMobile());
            return ThirdPartyHolder.allinpayService("shop").pay(params);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public WxPayParamsBo makePay(FakePayDto dto) {
        Map<String, Map<String, Object>> stringMapMap = systemManager.fetchSettingMap();
        int depositAmount = Integer
                .parseInt(stringMapMap.get("Merchant").getOrDefault("qrcodeFeeCent", "0").toString());
        Tricycle tricycle = tricycleManager.fetchTricycleById(dto.getId());
        if (tricycle == null) {
            throw new BaseException("未找到三轮车");
        }
        AdminUserBo userInfo = SecurityHolder.<Long, AdminUserBo>session().getUserInfo();
        Long userId = userInfo.getId();
        tricycle.setDeposit(0);
        tricycleManager.save(tricycle);
        if (dto.isFake() || depositAmount == 0) {
            if (tricycle.getBatchId() == null) {
                startTricycleMakeProcess(tricycle);
            } else {
                tricycle.setCheckStatus(TricycleCheckStatusEnum.APPROVED);
                tricycleManager.save(tricycle);
            }
            return null;
        } else {
            Bill bill = new Bill();
            bill.setType(BillTypeEnum.TRICYCLE_MAKE);
            bill.setEntityId(tricycle.getId());
            bill.setUserId(userId);
            bill.setUserType(UserTypeEnum.ADMIN);
            bill.setUserModel("admin_user");
            bill.setOriginalMoneyCent(depositAmount);
            bill.setCouponDiscountCent(0);
            bill.setOrderMoneyCent(depositAmount);
            bill.setPayStatus(BillPayStatusEnum.UNPAID);
            bill.setOtherData(JsonUtils.toJson(tricycle));
            billManager.saveBill(bill);
            PayParamsDto params = new PayParamsDto();
            params.setTrxamt(bill.getOrderMoneyCent());
            params.setUnireqsn(String.valueOf(bill.getId()));
            params.setAcct(userInfo.getOpenId());
            params.setBody("三轮车号牌制作工本费");
            params.setRemark(userInfo.getMobile());
            return ThirdPartyHolder.allinpayService("shop").pay(params);
        }
    }

    public BillBo manualMakePay(ManualPayDto dto) {
        Tricycle tricycle = tricycleManager.fetchTricycleById(dto.getId());
        if (tricycle == null) {
            throw new BaseException("未找到三轮车");
        }
        Map<String, Map<String, Object>> stringMapMap = systemManager.fetchSettingMap();
        int depositAmount = Integer
                .parseInt(stringMapMap.get("Merchant").getOrDefault("qrcodeFeeCent", "0").toString());
        tricycle.setDeposit(0);
        Bill bill = new Bill();
        bill.setType(BillTypeEnum.TRICYCLE_MAKE);
        bill.setEntityId(tricycle.getId());
        bill.setUserId(tricycle.getUserId());
        bill.setUserType(UserTypeEnum.ADMIN);
        bill.setUserModel("admin_user");
        bill.setOriginalMoneyCent(depositAmount);
        bill.setCouponDiscountCent(0);
        bill.setOrderMoneyCent(depositAmount);
        bill.setPaidMoneyCent(dto.getRealAmount());
        bill.setPayStatus(BillPayStatusEnum.MANUAL);
        bill.setOtherData(JsonUtils.toJson(tricycle));
        bill.setRemark(dto.getRemark());
        bill.setPaidAt(LocalDateTime.now());
        billManager.saveBill(bill);
        fileService.updateFileLink(bill.getId(), "images", dto.getImages(), FileTypeEnum.IMAGE);
        if (tricycle.getBatchId() == null) {
            startTricycleMakeProcess(tricycle);
        } else {
            tricycle.setCheckStatus(TricycleCheckStatusEnum.APPROVED);
            tricycleManager.save(tricycle);
        }
        BillBo copy = BeanUtils.copy(bill, BillBo.class);
        copy.setImages(dto.getImages());
        return copy;
    }

    @Transactional(rollbackFor = Exception.class)
    public void handlerTricycleDeposit(Bill bill) {
        Long entityId = bill.getEntityId();
        TricycleDeposit deposit = tricycleManager.fetchDepositById(entityId);
        if (deposit == null) {
            throw new BaseException("押金记录不存在");
        }
        Tricycle tricycle = tricycleManager.fetchTricycleById(deposit.getTricycleId());
        if (tricycle == null) {
            throw new BaseException("三轮车不存在");
        }
        startTricycleCheckProcess(tricycle, deposit);
    }

    @Transactional(rollbackFor = Exception.class)
    public void handlerTricycleMake(Bill bill) {
        Long entityId = bill.getEntityId();
        Tricycle tricycle = tricycleManager.fetchTricycleById(entityId);
        if (tricycle == null) {
            throw new BaseException("三轮车不存在");
        }
        if (tricycle.getBatchId() == null) {
            startTricycleMakeProcess(tricycle);
        } else {
            tricycle.setCheckStatus(TricycleCheckStatusEnum.APPROVED);
            tricycleManager.save(tricycle);
        }
    }

    private void startTricycleCheckProcess(Tricycle tricycle, TricycleDeposit deposit) {
        String maxNo = tricycleManager.getMaxTricycleNo();
        if (maxNo == null) {
            maxNo = "000001";
        } else {
            maxNo = String.format("%06d", Integer.parseInt(maxNo) + 1);
        }
        tricycle.setNumber(maxNo);
        int balance = deposit.getChange();
        if (tricycle.getCheckStatus() != TricycleCheckStatusEnum.PENDING_DEPOSIT) {
            balance += tricycle.getDeposit();
        }
        tricycle.setDeposit(balance);
        tricycleManager.save(tricycle);
        if (tricycle.getCheckStatus() != TricycleCheckStatusEnum.PENDING_DEPOSIT) {
            return;
        }
        BpmInstance exist = bpmService.getInstanceByOuter(ProcessEnum.TRICYCLE_CHECK.getKey(), tricycle.getId());
        if (exist != null && exist.getEndAt() == null) {
            throw new BaseException("三轮车审核流程未结束");
        }
        BpmSubmitDto dto = new BpmSubmitDto()
                .setOuterType(ProcessEnum.TRICYCLE_CHECK.getKey())
                .setOuterId(tricycle.getId())
                .setProcessCode(ProcessEnum.TRICYCLE_CHECK.getKey())
                .setData(tricycle)
                .setUserId(tricycle.getUserId())
                .setSubmitUserId(tricycle.getUserId());
        BpmInstance instance = bpmService.submit(dto);
        bpmService.doAction(new BpmHandleDto()
                .setInstanceId(instance.getId())
                .setActionType(ActionTypeEnum.APPROVE.name())
                .setData(deposit)
                .setUserId(tricycle.getUserId()));
    }

    private void startTricycleMakeProcess(Tricycle tricycle) {
        String maxNo = tricycleManager.getMaxTricycleNo();
        if (maxNo == null) {
            maxNo = "000001";
        } else {
            maxNo = String.format("%06d", Integer.parseInt(maxNo) + 1);
        }
        tricycle.setNumber(maxNo);
        tricycleManager.save(tricycle);
        if (tricycle.getCheckStatus() != TricycleCheckStatusEnum.PENDING_DEPOSIT) {
            return;
        }
        BpmInstance exist = bpmService.getInstanceByOuter(ProcessEnum.TRICYCLE_MAKE.getKey(), tricycle.getId());
        if (exist != null && exist.getEndAt() == null) {
            throw new BaseException("三轮车审核流程未结束");
        }
        BpmSubmitDto dto = new BpmSubmitDto()
                .setOuterType(ProcessEnum.TRICYCLE_MAKE.getKey())
                .setOuterId(tricycle.getId())
                .setProcessCode(ProcessEnum.TRICYCLE_MAKE.getKey())
                .setData(tricycle)
                .setUserId(tricycle.getUserId())
                .setSubmitUserId(tricycle.getUserId());
        BpmInstance instance = bpmService.submit(dto);
        bpmService.doAction(new BpmHandleDto()
                .setInstanceId(instance.getId())
                .setActionType(ActionTypeEnum.APPROVE.name())
                .setData(tricycle)
                .setUserId(tricycle.getUserId()));
    }

    public Boolean checkTricycle(TricycleCheckDto dto) {
        Tricycle tricycle = tricycleManager.fetchTricycleById(dto.getTricycleId());
        if (tricycle == null) {
            throw new BaseException("未找到三轮车");
        }
        if (tricycle.getDeposit() <= 0) {
            throw new BaseException("三轮车保证金不足");
        }
        if (tricycle.getCheckStatus() != TricycleCheckStatusEnum.UNCHECKED) {
            throw new BaseException("三轮车审核状态不是待审核");
        }
        TricycleCheckStatusEnum checkStatus = BaseEnum.ordinalOf(TricycleCheckStatusEnum.class, dto.getCheckStatus());
        if (checkStatus == null) {
            throw new BaseException("审核状态错误");
        }
        if (checkStatus == TricycleCheckStatusEnum.REJECTED && StringUtils.isBlank(dto.getCheckRejectReason())) {
            throw new BaseException("审核不通过时，必须填写拒绝原因");
        }
        BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.TRICYCLE_CHECK.getKey(), tricycle.getId());
        if (instance == null) {
            throw new BaseException("未找到流程实例");
        }
        if (checkStatus == TricycleCheckStatusEnum.APPROVED) {
            bpmService.doAction(new BpmHandleDto()
                    .setInstanceId(instance.getId())
                    .setActionType(ActionTypeEnum.APPROVE.name())
                    .setRemark(dto.getCheckRemark())
                    .setForce(dto.isForce())
                    .setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId()));
        } else if (checkStatus == TricycleCheckStatusEnum.REJECTED) {
            bpmService.doAction(new BpmHandleDto()
                    .setInstanceId(instance.getId())
                    .setActionType(ActionTypeEnum.REJECT.name())
                    .setRemark(dto.getCheckRemark())
                    .setForce(dto.isForce())
                    .setReason(dto.getCheckRejectReason())
                    .setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId()));
        }
        return true;
    }

    public TricyclePageBo pageList(PageRequest pageRequest) {
        Page<Tricycle> tricyclePage = tricycleManager.pageList(pageRequest);
        List<Long> userIds = tricyclePage.getList().stream().map(Tricycle::getUserId).toList();
        List<Long> batchIds = tricyclePage.getList().stream().map(Tricycle::getBatchId).toList();
        List<TricycleBatch> tricycleBatches = tricycleManager.fetchBatchByIds(batchIds);
        Map<Long, TricycleBatch> batchMap = tricycleBatches.stream()
                .collect(Collectors.toMap(TricycleBatch::getId, v -> v));
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(userIds);
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));

        List<Long> ids = tricyclePage.getList().stream().map(Tricycle::getId).toList();
        Map<Long, Integer> longIntegerMap = tricycleManager.countTricycleInfoRecordByTricycleIds(ids);


        Page<TricycleBo> convert = tricyclePage.convert(v -> {
            TricycleBo copy = BeanUtils.copy(v, TricycleBo.class);
            copy.setImages(v.getImages());
            AdminUser adminUser = adminUserMap.get(v.getUserId());
            if (adminUser != null) {
                copy.setUserObj(BeanUtils.copy(adminUser, AdminUserBo.class));
            }
            if (v.getBatchId() != null) {
                TricycleBatch tricycleBatch = batchMap.get(v.getBatchId());
                if (tricycleBatch != null) {
                    copy.setBatchObj(BeanUtils.copy(tricycleBatch, TricycleBatchBo.class));
                }
            }
            copy.setInfoRecordCount(longIntegerMap.getOrDefault(v.getId(), 0));
            return copy;
        });
        List<TricycleStatusCount> counts = tricycleManager.countTricycleByStatus();
        Map<TricycleCheckStatusEnum, Integer> statusMap = counts.stream()
                .collect(Collectors.toMap(TricycleStatusCount::getCheckStatus, TricycleStatusCount::getCount));
        TricyclePageBo ret = new TricyclePageBo();
        ret.setTotal(convert.getTotal());
        ret.setList(convert.getList());
        ret.setWaitCheckCount(statusMap.getOrDefault(TricycleCheckStatusEnum.UNCHECKED, 0));
        ret.setWaitPayDepositCount(statusMap.getOrDefault(TricycleCheckStatusEnum.PENDING_DEPOSIT, 0));
        ret.setNormalDepositCount(statusMap.getOrDefault(TricycleCheckStatusEnum.APPROVED, 0));
        ret.setUnclaimedCount(statusMap.getOrDefault(TricycleCheckStatusEnum.UNCLAIMED, 0));
        ret.setWaitingMakeCount(statusMap.getOrDefault(TricycleCheckStatusEnum.PENDING_MAKE, 0));
        ret.setMakingCount(statusMap.getOrDefault(TricycleCheckStatusEnum.MAKING, 0));
        return ret;
    }

    public Page<TricycleDepositBo> tricycleDepositPage(PageRequest dto) {
        Page<TricycleDeposit> page = tricycleManager.tricycleDepositPage(dto);
        return page.convert(v -> BeanUtils.copy(v, TricycleDepositBo.class));
    }

    public TricycleBo getById(Long id) {
        Tricycle tricycle = tricycleManager.fetchTricycleById(id);
        if (tricycle == null) {
            return null;
        }
        TricycleBo copy = BeanUtils.copy(tricycle, TricycleBo.class);
        copy.setImages(tricycle.getImages());
        if (tricycle.getUserId() != null) {
            AdminUser adminUser = adminUserManager.fetchById(tricycle.getUserId());
            if (adminUser != null) {
                copy.setUserObj(BeanUtils.copy(adminUser, AdminUserBo.class));
            }
        }
        return copy;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelCheckTricycle(Long id) {
        Tricycle tricycle = tricycleManager.fetchTricycleById(id);
        if (tricycle == null) {
            throw new BaseException("未找到三轮车");
        }
        long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        if (userId != tricycle.getUserId()) {
            throw new BaseException("三轮车不属于当前用户");
        }
        if (tricycle.getCheckStatus() == TricycleCheckStatusEnum.APPROVED) {
            throw new BaseException("三轮车已审核通过");
        }
        if (tricycle.getCheckStatus() != TricycleCheckStatusEnum.UNCHECKED
                && tricycle.getCheckStatus() != TricycleCheckStatusEnum.PENDING_DEPOSIT) {
            throw new BaseException("三轮车状态不允许取消审核");
        }
        BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.TRICYCLE_CHECK.getKey(), tricycle.getId());
        if (instance == null) {
            tricycle.setCheckStatus(TricycleCheckStatusEnum.CANCELED);
            tricycleManager.save(tricycle);
            return true;
        }
        bpmService.doAction(new BpmHandleDto()
                .setInstanceId(instance.getId())
                .setActionType(ActionTypeEnum.CANCEL.name())
                .setRemark("取消审核")
                .setData(tricycle)
                .setUserId(userId));
        submitRefundProcess(tricycle, userId);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean depositRefund(Long id) {
        Tricycle tricycle = tricycleManager.fetchTricycleById(id);
        if (tricycle == null) {
            throw new BaseException("未找到三轮车");
        }
        BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.TRICYCLE_REFUND.getKey(), tricycle.getId());
        if (instance == null) {
            throw new BaseException("未找到流程实例");
        }
        bpmService.doAction(new BpmHandleDto()
                .setInstanceId(instance.getId())
                .setActionType(ActionTypeEnum.APPROVE.name())
                .setData(tricycle)
                .setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId()));
        return true;
    }

    private void submitRefundProcess(Tricycle tricycle, Long userId) {
        BpmSubmitDto dto = new BpmSubmitDto()
                .setOuterType(ProcessEnum.TRICYCLE_REFUND.getKey())
                .setOuterId(tricycle.getId())
                .setProcessCode(ProcessEnum.TRICYCLE_REFUND.getKey())
                .setData(tricycle)
                .setUserId(userId)
                .setSubmitUserId(userId);
        BpmInstance instance = bpmService.submit(dto);
        bpmService.doAction(new BpmHandleDto()
                .setInstanceId(instance.getId())
                .setActionType(ActionTypeEnum.APPROVE.name())
                .setData(tricycle)
                .setUserId(userId));
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean depositRefundApply(Long id) {
        Tricycle tricycle = tricycleManager.fetchTricycleById(id);
        if (tricycle == null) {
            throw new BaseException("未找到三轮车");
        }
        if (tricycle.getCheckStatus() != TricycleCheckStatusEnum.APPROVED) {
            throw new BaseException("三轮车当前状态不允许申请退款");
        }
        submitRefundProcess(tricycle, SecurityHolder.<Long, AdminUserBo>session().getUserId());
        return true;
    }

    public List<TricycleBo> tricycleListByUser(Long userId) {
        List<Tricycle> tricycles = tricycleManager.tricycleListByUser(userId);
        List<Long> ids = tricycles.stream().map(BaseEntity::getId).toList();
        List<TricycleIllegal> tricycleIllegals = tricycleManager.fetchUnEndIllegalByTricycleIds(ids);
        Map<Long, List<TricycleIllegal>> illegalMap = tricycleIllegals.stream()
                .collect(Collectors.groupingBy(TricycleIllegal::getTricycleId));
        return tricycles.stream().map(v -> {
            TricycleBo copy = BeanUtils.copy(v, TricycleBo.class);
            copy.setImages(v.getImages());
            List<TricycleIllegal> tricycleIllegals1 = illegalMap.get(v.getId());
            if (tricycleIllegals1 != null) {
                copy.setRunningIllegalList(BeanUtils.copyList(tricycleIllegals1, TricycleIllegalBo.class));
            }
            return copy;
        }).toList();
    }

    public TricycleQrcodeBo qrcode(Long id) {
        Tricycle tricycle = tricycleManager.fetchTricycleById(id);
        if (tricycle == null) {
            throw new BaseException("未找到三轮车");
        }
        TricycleQrcodeBo qrcodeBo = new TricycleQrcodeBo();
        qrcodeBo.setNumCode(tricycle.getNumber());
        qrcodeBo.setQrCode("https://shopminicode.dounanflowers.com" + "?target=tricycle" + "&id="
                + URLEncoder.encode(tricycle.getId().toString(), StandardCharsets.UTF_8));
        return qrcodeBo;
    }

    public List<TricycleBo> tricycleDepositWarnList() {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        Map<String, Map<String, Object>> stringMapMap = systemManager.fetchSettingMap();
        int depositWarnAmount = Integer
                .parseInt(stringMapMap.get("Merchant").getOrDefault("tricycleDepositWarnCent", "0").toString());
        List<Tricycle> list = tricycleManager.tricycleDepositLowList(userId, depositWarnAmount);
        return list.stream().map(v -> {
            TricycleBo copy = BeanUtils.copy(v, TricycleBo.class);
            copy.setImages(v.getImages());
            return copy;
        }).toList();
    }

    public Boolean makeSend(TricycleApplyDto dto) {
        Tricycle tricycle = tricycleManager.fetchTricycleById(dto.getId());
        if (tricycle == null) {
            throw new BaseException("未找到三轮车");
        }
        if (tricycle.getBatchId() == null) {
            BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.TRICYCLE_MAKE.getKey(), tricycle.getId());
            if (instance == null) {
                throw new BaseException("未找到流程实例");
            }
            bpmService.doAction(new BpmHandleDto()
                    .setForce(true)
                    .setInstanceId(instance.getId())
                    .setActionType(ActionTypeEnum.APPROVE.name())
                    .setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId()));
        } else {
            applyTricycleAgain(dto);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public TricycleBatchBo createBatch(CreateTricycleBatchDTO dto) {
        // 创建批次记录
        TricycleBatch batch = new TricycleBatch()
                .setName(dto.getName())
                .setDescription(dto.getDescription())
                .setCount(dto.getCount())
                .setCompletedCount(0)
                .setType(TricycleBatchTypeEnum.CREATE)
                .setRemark(dto.getRemark());
        tricycleManager.saveBatch(batch);

        // 批量创建三轮车
        List<Tricycle> tricycles = new ArrayList<>();
        String maxNo = tricycleManager.getMaxTricycleNo();
        int startNo = maxNo == null ? 1 : Integer.parseInt(maxNo) + 1;

        for (int i = 0; i < dto.getCount(); i++) {
            String number = String.format("%06d", startNo + i);
            Tricycle tricycle = new Tricycle()
                    .setNumber(number)
                    .setCheckStatus(TricycleCheckStatusEnum.UNCLAIMED)
                    .setDeposit(0)
                    .setBatchId(batch.getId());
            tricycles.add(tricycle);
        }
        tricycleManager.insertBatch(tricycles);
        return BeanUtils.copy(batch, TricycleBatchBo.class);
    }

    public Page<TricycleBatchBo> tricycleBatchPage(PageRequest dto) {
        return tricycleManager.tricycleBatchPage(dto).convert(v -> BeanUtils.copy(v, TricycleBatchBo.class));
    }

    public TricycleBatchBo getBatchById(Long id) {
        TricycleBatch batch = tricycleManager.fetchBatchById(id);
        if (batch == null) {
            throw new BaseException("未找到批次");
        }
        TricycleBatchBo copy = BeanUtils.copy(batch, TricycleBatchBo.class);
        List<Tricycle> tricycles = null;

        if (batch.getType() == TricycleBatchTypeEnum.MAKE) {
            BatchMakeBo batchInfo = JsonUtils.toObject(batch.getRemark(), BatchMakeBo.class);
            tricycles = tricycleManager.fetchTricycleByIds(batchInfo.getSuccess());
        } else if (batch.getType() == TricycleBatchTypeEnum.CREATE) {
            tricycles = tricycleManager.fetchTricycleByBatchId(id);
        }
        if (tricycles == null) {
            return copy;
        }
        List<Long> userIds = tricycles.stream().map(Tricycle::getUserId).toList();
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(userIds);
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));
        copy.setTricycles(tricycles.stream().map(v -> {
            TricycleBo one = BeanUtils.copy(v, TricycleBo.class);
            one.setImages(v.getImages());
            AdminUser adminUser = adminUserMap.get(v.getUserId());
            if (adminUser != null) {
                one.setUserObj(BeanUtils.copy(adminUser, AdminUserBo.class));
            }
            return one;
        }).toList());
        return copy;
    }

    @Transactional(rollbackFor = Exception.class)
    public TricycleBatchBo makeBatch(List<Long> ids) {
        List<Tricycle> tricyclesInPendingMake = tricycleManager
                .fetchTricyclesByStatusAndIds(TricycleCheckStatusEnum.PENDING_MAKE, ids);
        if (CollectionUtils.isEmpty(tricyclesInPendingMake)) {
            throw new BaseException("没有待制作的三轮车");
        }
        List<BpmInstance> instances = bpmService.getInstanceByOuterIds(ProcessEnum.TRICYCLE_MAKE.getKey(),
                tricyclesInPendingMake.stream().map(Tricycle::getId).toList());
        Map<Long, BpmInstance> instanceMap = instances.stream()
                .collect(Collectors.toMap(BpmInstance::getOuterId, v -> v));
        List<Tricycle> submits = Lists.newArrayList();
        for (Tricycle tricycle : tricyclesInPendingMake) {
            BpmInstance instance = instanceMap.get(tricycle.getId());
            if (instance == null) {
                continue;
            }
            bpmService.doAction(new BpmHandleDto()
                    .setForce(true)
                    .setInstanceId(instance.getId())
                    .setActionType(ActionTypeEnum.APPROVE.name())
                    .setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId()));
            submits.add(tricycle);
        }
        TricycleBatch batch = new TricycleBatch();
        batch.setName("批量制作" + DateUtils.formatDateTime(LocalDateTime.now(), "yyyyMMddHHmmss"));
        batch.setCount(ids.size());
        batch.setCompletedCount(submits.size());
        batch.setDescription("批量制作三轮车二维码，总共：" + ids.size() + ",成功：" + submits.size());
        List<Long> submitIds = submits.stream().map(Tricycle::getId).toList();
        BatchMakeBo batchMakeBo = new BatchMakeBo();
        batchMakeBo.setSubmit(ids);
        batchMakeBo.setSuccess(submitIds);
        batchMakeBo.setFail(ids.stream().filter(v -> !submitIds.contains(v)).toList());
        batch.setRemark(JsonUtils.toJson(batchMakeBo));
        batch.setType(TricycleBatchTypeEnum.MAKE);
        tricycleManager.saveBatch(batch);
        return BeanUtils.copy(batch, TricycleBatchBo.class);
    }

    public TricycleBatchBo doneBatch(List<Long> ids) {
        List<Tricycle> tricyclesInPendingMake = tricycleManager
                .fetchTricyclesByStatusAndIds(TricycleCheckStatusEnum.MAKING, ids);
        if (CollectionUtils.isEmpty(tricyclesInPendingMake)) {
            throw new BaseException("没有制作中的三轮车");
        }
        List<BpmInstance> instances = bpmService.getInstanceByOuterIds(ProcessEnum.TRICYCLE_MAKE.getKey(),
                tricyclesInPendingMake.stream().map(Tricycle::getId).toList());
        Map<Long, BpmInstance> instanceMap = instances.stream()
                .collect(Collectors.toMap(BpmInstance::getOuterId, v -> v));
        List<Tricycle> submits = Lists.newArrayList();
        for (Tricycle tricycle : tricyclesInPendingMake) {
            BpmInstance instance = instanceMap.get(tricycle.getId());
            if (instance == null) {
                continue;
            }
            bpmService.doAction(new BpmHandleDto()
                    .setForce(true)
                    .setInstanceId(instance.getId())
                    .setActionType(ActionTypeEnum.APPROVE.name())
                    .setUserId(SecurityHolder.<Long, AdminUserBo>session().getUserId()));
            submits.add(tricycle);
        }
        TricycleBatch batch = new TricycleBatch();
        batch.setName("批量完成" + DateUtils.formatDateTime(LocalDateTime.now(), "yyyyMMddHHmmss"));
        batch.setCount(ids.size());
        batch.setCompletedCount(submits.size());
        batch.setDescription("批量完成三轮车二维码，总共：" + ids.size() + ",成功：" + submits.size());
        List<Long> submitIds = submits.stream().map(Tricycle::getId).toList();
        BatchMakeBo batchMakeBo = new BatchMakeBo();
        batchMakeBo.setSubmit(ids);
        batchMakeBo.setSuccess(submitIds);
        batchMakeBo.setFail(ids.stream().filter(v -> !submitIds.contains(v)).toList());
        batch.setRemark(JsonUtils.toJson(batchMakeBo));
        batch.setType(TricycleBatchTypeEnum.DONE);
        tricycleManager.saveBatch(batch);
        return BeanUtils.copy(batch, TricycleBatchBo.class);
    }

    public Boolean deleteTricycle(EditWithPasswordDto dto) {
        // TODO: TOTP校验密码是否正确
        if ("dnhhsj@123".equals(dto.getPassword())) {
            throw new BaseException("密码不正确");
        }
        Tricycle tricycle = tricycleManager.fetchTricycleById(dto.getId());
        if (tricycle == null) {
            throw new BaseException("未找到三轮车");
        }
        BpmInstance instance = bpmService.getInstanceByOuter(ProcessEnum.TRICYCLE_MAKE.getKey(), tricycle.getId());
        if (instance != null && instance.getEndAt() == null) {
            bpmService.stopProcess(instance.getId());
        }
        tricycleManager.deleteTricycle(dto.getId());
        return true;
    }

    public Boolean updateTricycle(TricycleApplyDto dto) {
        Tricycle tricycle = tricycleManager.fetchTricycleById(dto.getId());
        if (tricycle == null) {
            throw new BaseException("未找到三轮车");
        }
        tricycle.setImages(dto.getImages());
        fileService.updateFileLink(tricycle.getId(), dto.getImages());
        return true;
    }

    public void updateTimeout() {
        List<Tricycle> tricycles = tricycleManager.fetchTricyclesByStatus(TricycleCheckStatusEnum.PENDING_DEPOSIT);
        if (CollectionUtils.isEmpty(tricycles)) {
            return;
        }
        String timeout = systemManager.fetchSettingMap().get("Merchant")
                .getOrDefault("tricycleMakeFeePayExpireHours", "1").toString();
        int hours = Integer.parseInt(timeout);
        for (Tricycle tricycle : tricycles) {
            LocalDateTime deadline = tricycle.getEffectiveAt().plusHours(hours);
            if (LocalDateTime.now().isAfter(deadline)) {
                if (tricycle.getBatchId() == null) {
                    tricycle.setCheckStatus(TricycleCheckStatusEnum.TIMEOUT);
                    tricycle.setEffectiveAt(null);
                    tricycleManager.updateTricycleWithNull(tricycle);
                } else {
                    releaseBatchTricycle(tricycle);
                }
            }
        }
    }

    public void releaseBatchTricycle(Tricycle tricycle) {
        tricycle.setUserId(null);
        tricycle.setCheckStatus(TricycleCheckStatusEnum.UNCLAIMED);
        tricycle.setEffectiveAt(null);
        tricycleManager.updateTricycleWithNull(tricycle);
        TricycleBatch batch = tricycleManager.fetchBatchById(tricycle.getBatchId());
        batch.setCompletedCount(batch.getCompletedCount() - 1);
        tricycleManager.saveBatch(batch);
        fileService.updateFileLink(tricycle.getId(), Lists.newArrayList());
    }

    public Page<TricycleInfoRecordBo> tricycleRecordPage(PageRequest dto) {
        Page<TricycleInfoRecord> page = tricycleManager.tricycleInfoRecordPage(dto);
        List<Long> userIds = page.getList().stream().map(TricycleInfoRecord::getUserId).toList();
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(userIds);
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(AdminUser::getId, v -> v));
        return page.convert(v -> {
            TricycleInfoRecordBo copy = BeanUtils.copy(v, TricycleInfoRecordBo.class);
            AdminUser adminUser = adminUserMap.get(v.getUserId());
            if (adminUser != null) {
                copy.setUserObj(BeanUtils.copy(adminUser, AdminUserBo.class));
            }
            return copy;
        });
    }

}
