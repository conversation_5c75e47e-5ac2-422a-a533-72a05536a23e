package com.dounanflowers.admin.queue;

import com.dounanflowers.admin.service.AdminProductService;
import com.dounanflowers.admin.service.AdminTricycleService;
import com.dounanflowers.admin.service.AdminVehicleService;
import com.dounanflowers.common.constant.MqConstant;
import com.dounanflowers.common.entity.Bill;
import com.dounanflowers.common.enums.BillTypeEnum;
import com.dounanflowers.common.service.CommonProductService;
import com.dounanflowers.common.service.EnergyService;
import com.dounanflowers.common.service.LcService;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.JsonUtils;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
@RequiredArgsConstructor
public class BillPaidQueue {

    private final AdminTricycleService adminTricycleService;
    private final LcService lcService;
    private final CommonProductService commonProductService;
    private final AdminVehicleService adminVehicleService;
    private final EnergyService energyService;

    @RabbitListener(queues = MqConstant.BILL_QUEUE)
    @RabbitHandler
    public void handler(Message message, Channel channel) throws IOException {
        log.info("mq消费者获取消息详情：---------------{}", message.toString());
        try {
            String msg = new String(message.getBody());
            Bill bill = JsonUtils.toObject(msg, Bill.class);
            if(bill.getType() != null) {
                switch (bill.getType()) {
                    case PARK -> lcService.handlePaid(bill);
                    case TRICYCLE_DEPOSIT -> adminTricycleService.handlerTricycleDeposit(bill);
                    case TRICYCLE_MAKE -> adminTricycleService.handlerTricycleMake(bill);
                    case TRICYCLE_ILLEGAL_FINE -> handlerTricycleIllegalFine(bill);
                    case PRODUCT_ORDER -> commonProductService.handleProductBill(bill);
                    case VEHICLE_MONTHLY -> adminVehicleService.handleVehicleBill(bill);
                    case VEHICLE_PLATE_CHANGE -> adminVehicleService.handleVehiclePlateChangeBill(bill);
                    case ENERGY_CELL_PREPAY -> energyService.handleEnergyCellPaid(bill);
                }
            } else {
                System.out.println("bill.getType()为null");
            }

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (BaseException e) {
            log.error("消息处理错误{}", e.getMessage(), e);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("mq消费者处理消息异常", e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
        }
    }

    private void handlerTricycleIllegalFine(Bill bill) {

    }


}
