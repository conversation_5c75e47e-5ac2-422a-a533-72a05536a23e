package com.dounanflowers.admin.websocket;

import com.dounanflowers.common.bo.WsMenuCountBo;
import com.dounanflowers.common.bo.WsMessageBo;
import com.dounanflowers.common.enums.AdminMenuEventEnum;
import com.dounanflowers.common.service.AdminMenuService;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.framework.utils.SpringUtils;
import com.dounanflowers.security.utils.SecurityHolder;
import jakarta.websocket.*;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * WebSocket服务器端点
 */
@ServerEndpoint("/admin/ws")
@Component
@Slf4j
public class WebSocketServer {
    
    /**
     * 当前在线连接数
     */
    private static final AtomicInteger ONLINE_COUNT = new AtomicInteger(0);
    
    /**
     * 用于存放所有在线客户端
     */
    private static final Map<String, Session> CLIENTS = new ConcurrentHashMap<>();

    private static final Map<String, List<String>> CLIENTS_PERMISSION = new ConcurrentHashMap<>();

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session) {

        String token = session.getRequestParameterMap().get("token").getFirst();
        if(token == null) {
            close(session);
            return;
        }



        SecurityHolder.init(token);
        List<String> permissions = SecurityHolder.session().getPermissions();
        if(permissions.isEmpty()) {
            close(session);
            return;
        }



        CLIENTS.put(session.getId(), session);
        CLIENTS_PERMISSION.put(session.getId(), permissions);
        int count = ONLINE_COUNT.incrementAndGet();
        log.info("有新连接加入：{}，当前在线人数为：{}", session.getId(), count);
        for (String permission : permissions) {
            Arrays.stream(AdminMenuEventEnum.values()).forEach(v -> {
                if(Objects.equals(v.getApi(), permission)) {
                    AdminMenuService adminMenuService = SpringUtils.getBean(AdminMenuService.class);
                    adminMenuService.emitCount(v);
                }
            });
        }
    }
    
    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose(Session session) {
        // 从集合中移除
        CLIENTS.remove(session.getId());
        // 在线数减1
        int count = ONLINE_COUNT.decrementAndGet();
        log.info("有一连接关闭：{}，当前在线人数为：{}", session.getId(), count);
    }
    
    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("服务端收到客户端[{}]的消息:{}", session.getId(), message);
        // 这里可以处理来自客户端的消息，如有需要
    }
    
    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("发生错误", error);
        try {
            session.close();
        } catch (IOException e) {
            log.error("关闭session异常", e);
        }
    }


    public static void broadcastAdminMenuMessage(WsMessageBo<WsMenuCountBo> message) {
        String messageJson = JsonUtils.toJson(message);
        for (Map.Entry<String, Session> sessionEntry : CLIENTS.entrySet()) {
            List<String> permissions = CLIENTS_PERMISSION.get(sessionEntry.getKey());
            if(permissions.contains(message.getData().getApi())) {
                Session session = sessionEntry.getValue();
                try {
                    if (session.isOpen()) {
                        // 使用同步方式发送消息，避免并发发送导致的TEXT_FULL_WRITING错误
                        synchronized (session) {
                            session.getBasicRemote().sendText(messageJson);
                        }
                    }
                } catch (IOException e) {
                    log.error("广播消息异常", e);
                }
            }
        }
    }


    public static void close(Session session) {
        try {
            session.close();
        } catch (IOException e) {
            log.error("无token,断开连接", e);
        }
    }
    
    /**
     * 发送消息到指定客户端
     * @param sessionId 客户端会话ID
     * @param message 消息内容
     */
    public static void sendMessage(String sessionId, Object message) {
        Session session = CLIENTS.get(sessionId);
        if (session != null && session.isOpen()) {
            try {
                String messageJson = JsonUtils.toJson(message);
                // 使用同步方式发送消息，避免并发发送导致的TEXT_FULL_WRITING错误
                synchronized (session) {
                    session.getBasicRemote().sendText(messageJson);
                }
            } catch (IOException e) {
                log.error("发送消息异常", e);
            }
        } else {
            log.warn("客户端[{}]不在线", sessionId);
        }
    }
}
