package com.dounanflowers.admin.bpm.handle;

import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.bpm.handler.ConditionHandler;
import com.dounanflowers.common.entity.TricycleIllegal;
import org.springframework.stereotype.Component;

@Component
public class TricycleIllegalHasTowHandle extends ConditionHandler<TricycleIllegal, TricycleIllegal> {

    @Override
    protected boolean condition(EventContext ctx, TricycleIllegal model, TricycleIllegal data) {
        return model.getNeedTow().isTrue();
    }

    @Override
    public String code() {
        return "illegalNeedTow";
    }

}
