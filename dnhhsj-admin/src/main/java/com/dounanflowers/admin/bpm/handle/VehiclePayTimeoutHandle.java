package com.dounanflowers.admin.bpm.handle;

import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.bpm.handler.event.TimeoutEventHandler;
import com.dounanflowers.common.entity.Vehicle;
import com.dounanflowers.common.entity.VehicleMonthlyCard;
import com.dounanflowers.common.manager.SystemManager;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class VehiclePayTimeoutHandle extends TimeoutEventHandler<Vehicle, VehicleMonthlyCard> {

    @Resource
    private SystemManager systemManager;

    @Override
    protected LocalDateTime timeoutAt(EventContext ctx, Vehicle model, VehicleMonthlyCard data) {
        String timeout = systemManager.fetchSettingMap().get("Merchant")
                .getOrDefault("vehicleMonthlyPayExpireHours", "1").toString();
        int hours = Integer.parseInt(timeout);
        return model.getUpdatedAt().plusHours(hours);
    }

    @Override
    public String code() {
        return "vehicleTimeout";
    }

}
