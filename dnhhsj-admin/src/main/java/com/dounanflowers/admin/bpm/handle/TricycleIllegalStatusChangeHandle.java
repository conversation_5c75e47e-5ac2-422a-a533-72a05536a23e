package com.dounanflowers.admin.bpm.handle;

import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.bpm.handler.EventHandler;
import com.dounanflowers.common.entity.TricycleIllegal;
import com.dounanflowers.common.enums.TricycleIllegalStatsuEnum;
import com.dounanflowers.common.manager.TricycleManager;
import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.framework.exception.BaseException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TricycleIllegalStatusChangeHandle extends EventHandler<TricycleIllegal, TricycleIllegal> {

    private final TricycleManager tricycleManager;

    @Override
    protected Object handle(EventContext ctx, TricycleIllegal model, TricycleIllegal data) {
        TricycleIllegal illegal = tricycleManager.fetchIllegalById(model.getId());
        String params = ctx.getEvent().getParams();
        if (params == null) {
            throw new BaseException("未设置状态");
        }
        String[] split = params.split(",");
        if (split.length == 0) {
            throw new BaseException("未设置状态");
        }
        TricycleIllegalStatsuEnum status;
        TricycleIllegalStatsuEnum preStatus = null;
        if (split.length == 1) {
            status = BaseEnum.ordinalOf(TricycleIllegalStatsuEnum.class, params);
        } else {
            status = BaseEnum.ordinalOf(TricycleIllegalStatsuEnum.class, split[0]);
            preStatus = BaseEnum.ordinalOf(TricycleIllegalStatsuEnum.class, split[1]);
        }
        if (preStatus != null && illegal.getStatus() != preStatus) {
            throw new BaseException("状态不匹配");
        }
        illegal.setStatus(status);
        tricycleManager.saveIllegal(illegal);
        return illegal;
    }

    @Override
    public String code() {
        return "illegalStatusChange";
    }

}
