package com.dounanflowers.admin.bpm.handle;

import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.bpm.handler.ConditionHandler;
import com.dounanflowers.common.entity.Vehicle;
import com.dounanflowers.common.entity.VehicleMonthlyCard;
import com.dounanflowers.common.entity.VehicleMonthlyCardOrder;
import com.dounanflowers.common.enums.BillPayStatusEnum;
import com.dounanflowers.common.manager.VehicleManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class VehicleNeedPayMonthlyHandle extends ConditionHandler<Vehicle, VehicleMonthlyCard> {

    private final VehicleManager vehicleManager;

    @Override
    protected boolean condition(EventContext ctx, Vehicle model, VehicleMonthlyCard data) {
        VehicleMonthlyCardOrder monthlyCardOrder = vehicleManager.getMonthlyCardOrder(data.getId());
        if (monthlyCardOrder == null) {
            return false;
        }
        return monthlyCardOrder.getStatus() == BillPayStatusEnum.PAID;
    }

    @Override
    public String code() {
        return "needPayMonthly";
    }

}
