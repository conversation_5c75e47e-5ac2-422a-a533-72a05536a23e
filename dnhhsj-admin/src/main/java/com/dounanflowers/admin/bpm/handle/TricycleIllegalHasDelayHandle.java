package com.dounanflowers.admin.bpm.handle;

import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.bpm.handler.EventHandler;
import com.dounanflowers.common.entity.TricycleIllegal;
import org.springframework.stereotype.Component;

@Component
public class TricycleIllegalHasDelayHandle extends EventHandler<TricycleIllegal, TricycleIllegal> {

    @Override
    protected Object handle(EventContext ctx, TricycleIllegal model, TricycleIllegal data) {
        return data;
    }

    @Override
    protected boolean condition(EventContext ctx, TricycleIllegal model, TricycleIllegal data) {
        return model.getPunishDelayMinute() > 0;
    }

    @Override
    public String code() {
        return "illegalHasDelay";
    }

}
