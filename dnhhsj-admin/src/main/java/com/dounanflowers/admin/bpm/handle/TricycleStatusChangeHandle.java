package com.dounanflowers.admin.bpm.handle;

import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.bpm.handler.EventHandler;
import com.dounanflowers.common.entity.Tricycle;
import com.dounanflowers.common.enums.TricycleCheckStatusEnum;
import com.dounanflowers.common.manager.TricycleManager;
import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.framework.exception.BaseException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@RequiredArgsConstructor
public class TricycleStatusChangeHandle extends EventHandler<Tricycle, Tricycle> {

    private final TricycleManager tricycleManager;

    @Override
    protected Object handle(EventContext ctx, Tricycle model, Tricycle data) {
        Tricycle tricycle = tricycleManager.fetchTricycleById(model.getId());
        String params = ctx.getEvent().getParams();
        if (params == null) {
            throw new BaseException("未设置状态");
        }
        String[] split = params.split(",");
        if (split.length == 0) {
            throw new BaseException("未设置状态");
        }
        TricycleCheckStatusEnum status;
        TricycleCheckStatusEnum preStatus = null;
        if (split.length == 1) {
            status = BaseEnum.ordinalOf(TricycleCheckStatusEnum.class, params);
        } else {
            status = BaseEnum.ordinalOf(TricycleCheckStatusEnum.class, split[0]);
            preStatus = BaseEnum.ordinalOf(TricycleCheckStatusEnum.class, split[1]);
        }
        if (preStatus != null && tricycle.getCheckStatus() != preStatus) {
            throw new BaseException("状态不匹配");
        }
        tricycle.setCheckStatus(status);
        if (status == TricycleCheckStatusEnum.APPROVED) {
            tricycle.setEffectiveAt(LocalDateTime.now());
        }
        tricycleManager.save(tricycle);
        model.setCheckStatus(status);
        return tricycle;
    }

    @Override
    public String code() {
        return "tricycleStatusChange";
    }

}
