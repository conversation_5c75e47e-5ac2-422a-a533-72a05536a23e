package com.dounanflowers.admin.bpm.handle;

import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.bpm.handler.ConditionHandler;
import com.dounanflowers.common.entity.TricycleIllegal;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TricycleIllegalNotNeedTowHandle extends ConditionHandler<TricycleIllegal, TricycleIllegal> {

    @Override
    protected boolean condition(EventContext ctx, TricycleIllegal model, TricycleIllegal data) {
        return model.getNeedTow().isFalse();
    }

    @Override
    public String code() {
        return "illegalNotNeedTow";
    }

}
