package com.dounanflowers.admin.bpm.handle;

import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.bpm.handler.event.TimeoutEventHandler;
import com.dounanflowers.common.entity.TricycleIllegal;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class TricycleIllegalTimeoutHandle extends TimeoutEventHandler<TricycleIllegal, TricycleIllegal> {

    @Override
    protected LocalDateTime timeoutAt(EventContext ctx, TricycleIllegal model, TricycleIllegal data) {
        return ctx.getNodeInstance().getCreatedAt().plusMinutes(model.getPunishDelayMinute());
    }

    @Override
    public String code() {
        return "illegalTimeout";
    }

}
