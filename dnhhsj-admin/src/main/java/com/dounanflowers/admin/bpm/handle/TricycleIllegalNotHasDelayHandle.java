package com.dounanflowers.admin.bpm.handle;

import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.bpm.handler.EventHandler;
import com.dounanflowers.common.entity.TricycleIllegal;
import com.dounanflowers.common.enums.TricycleIllegalStatsuEnum;
import com.dounanflowers.common.manager.TricycleManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@RequiredArgsConstructor
public class TricycleIllegalNotHasDelayHandle extends EventHandler<TricycleIllegal, TricycleIllegal> {

    private final TricycleManager tricycleManager;

    @Override
    protected Object handle(EventContext ctx, TricycleIllegal model, TricycleIllegal data) {
        model.setPunishedAt(LocalDateTime.now())
                .setPunishedUserId(ctx.getUserId())
                .setStatus(TricycleIllegalStatsuEnum.PROCESSING);
        tricycleManager.saveIllegal(model);
        return data;
    }

    @Override
    protected boolean condition(EventContext ctx, TricycleIllegal model, TricycleIllegal data) {
        return model.getPunishDelayMinute() == 0;
    }

    @Override
    public String code() {
        return "illegalNotHasDelay";
    }

}
