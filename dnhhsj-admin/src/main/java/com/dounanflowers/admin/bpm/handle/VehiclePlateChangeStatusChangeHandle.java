package com.dounanflowers.admin.bpm.handle;

import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.bpm.handler.EventHandler;
import com.dounanflowers.common.dto.BillRefundDto;
import com.dounanflowers.common.entity.Vehicle;
import com.dounanflowers.common.entity.VehiclePlateChange;
import com.dounanflowers.common.enums.VehiclePlateChangeStatusEnum;
import com.dounanflowers.common.manager.VehicleManager;
import com.dounanflowers.common.service.BillService;
import com.dounanflowers.common.service.LcService;
import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.third.bo.ParkWhiteBo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@RequiredArgsConstructor
public class VehiclePlateChangeStatusChangeHandle extends EventHandler<Vehicle, VehiclePlateChange> {
    private final VehicleManager vehicleManager;
    private final LcService lcService;
    private final BillService billService;

    @Override
    protected Object handle(EventContext ctx, Vehicle model, VehiclePlateChange data) {
        System.out.println("VehiclePlateChangeStatusChangeHandle");
        Vehicle vehicle = vehicleManager.fetchVehicleById(model.getId());
//        VehiclePlateChange vehiclePlateChange = vehicleManager.fetchVehiclePlateChangeById(data.getId());
        String params = ctx.getEvent().getParams();
        if (params == null) {
            throw new BaseException("未设置状态");
        }
        String[] split = params.split(",");
        if (split.length == 0) {
            throw new BaseException("未设置状态");
        }
        VehiclePlateChangeStatusEnum status;
        VehiclePlateChangeStatusEnum preStatus = null;
        if (split.length == 1) {
            status = BaseEnum.ordinalOf(VehiclePlateChangeStatusEnum.class, params);
        } else {
            status = BaseEnum.ordinalOf(VehiclePlateChangeStatusEnum.class, split[0]);
            preStatus = BaseEnum.ordinalOf(VehiclePlateChangeStatusEnum.class, split[1]);
        }
        if (preStatus != null && data.getChangeStatus() != preStatus) {
            throw new BaseException("状态不匹配");
        }
        if(status == VehiclePlateChangeStatusEnum.DONE) {
            // 检查停车场系统是否已经有新的车牌， 如果没有就报错，如果有修改车的车牌号
            ParkWhiteBo parkWhiteBo = lcService.whitelistQuery(data.getNewPlate());
            if(parkWhiteBo == null) {
                throw new BaseException("未查询到蓝卡有新车牌月卡，请检查是否修改车牌成功");
            }
            vehicle.setNumber(data.getNewPlate());
            vehicleManager.save(vehicle);
            data.setChangedAt(LocalDateTime.now());
        } else if(status == VehiclePlateChangeStatusEnum.REJECTED) {
            // 检查是否需要退款，如果需要，就退款
            System.out.println("================退款=================");
            if(data.getBillId() != null) {
                BillRefundDto billRefundDto = new BillRefundDto();
                billRefundDto.setId(data.getBillId().toString());
                billRefundDto.setReason("换牌失败，自动退款");
                billService.refund(billRefundDto);
            }
            data.setRejectReason(ctx.getReason());
        }
        data.setChangeStatus(status);
        vehicleManager.saveVehiclePlateChange(data);
        return data;

    }

    @Override
    public String code() {
        return "vehiclePlateChangeStatusChange";
    }
}
