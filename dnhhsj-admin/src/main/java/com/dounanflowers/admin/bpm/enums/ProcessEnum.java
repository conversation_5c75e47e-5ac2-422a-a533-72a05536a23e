package com.dounanflowers.admin.bpm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ProcessEnum {
    TRICYCLE_CHECK("tricycleCheck", "三轮车审核"),
    TRICYCLE_REFUND("tricycleRefund", "三轮车退款"),
    TRICYCLE_ILLEGAL("tricycleIllegal", "三轮车违章"),
    TRICYCLE_MAKE("tricycleMake", "三轮车号牌制作"),
    VEHICLE_CHECK("vehicleCheck", "机动车审核"), // 和数据库public.bpm_process的code一致
    VEHICLE_PLATE_CHANGE("vehiclePlateChange", "机动车号牌变更"), // 和数据库public.bpm_process的code一致
    ;

    private final String key;

    private final String title;
}
