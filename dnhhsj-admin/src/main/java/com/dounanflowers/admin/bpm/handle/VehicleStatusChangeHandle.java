package com.dounanflowers.admin.bpm.handle;

import com.dounanflowers.admin.bo.VehicleLcWhitelistBo;
import com.dounanflowers.bpm.dto.EventContext;
import com.dounanflowers.bpm.handler.EventHandler;
import com.dounanflowers.common.bo.VehicleBo;
import com.dounanflowers.common.dto.MsgSendByTemplateDto;
import com.dounanflowers.common.entity.Tricycle;
import com.dounanflowers.common.entity.Vehicle;
import com.dounanflowers.common.entity.VehicleMonthlyCard;
import com.dounanflowers.common.enums.TricycleCheckStatusEnum;
import com.dounanflowers.common.enums.VehicleCheckStatusEnum;
import com.dounanflowers.common.enums.VehicleRefundStatusEnum;
import com.dounanflowers.common.manager.VehicleManager;
import com.dounanflowers.common.service.LcService;
import com.dounanflowers.common.service.MsgService;
import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.third.bo.ParkWhiteBo;
import com.rabbitmq.tools.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class VehicleStatusChangeHandle extends EventHandler<Vehicle, VehicleMonthlyCard> {

    private final VehicleManager vehicleManager;
    private final LcService lcService;
    private final MsgService msgService;

    @Override
    protected Object handle(EventContext ctx, Vehicle model, VehicleMonthlyCard data) {
        Vehicle db = vehicleManager.fetchVehicleById(model.getId());
        String params = ctx.getEvent().getParams();
        if (params == null) {
            throw new BaseException("未设置状态");
        }
        String[] split = params.split(",");
        if (split.length == 0) {
            throw new BaseException("未设置状态");
        }
        VehicleCheckStatusEnum status;
        VehicleCheckStatusEnum preStatus = null;
        if (split.length == 1) {
            status = BaseEnum.ordinalOf(VehicleCheckStatusEnum.class, params);
        } else {
            status = BaseEnum.ordinalOf(VehicleCheckStatusEnum.class, split[0]);
            preStatus = BaseEnum.ordinalOf(VehicleCheckStatusEnum.class, split[1]);
        }
        if (preStatus != null && db.getCheckStatus() != preStatus) {
            throw new BaseException("状态不匹配");
        }
        db.setCheckStatus(status);
        if (status == VehicleCheckStatusEnum.APPROVED || status == VehicleCheckStatusEnum.REJECTED || status == VehicleCheckStatusEnum.PENDING_DEPOSIT) {
            VehicleLcWhitelistBo vehicleLcWhitelistBo = new VehicleLcWhitelistBo();
            ParkWhiteBo parkWhiteBo = lcService.whitelistQuery(model.getNumber());
            if(parkWhiteBo != null) {
                vehicleLcWhitelistBo.setLcWhite(parkWhiteBo);
            }
            Vehicle vehicle = vehicleManager.getEffectiveVehicleByPlate(model.getNumber());
            if(vehicle != null) {
                vehicleLcWhitelistBo.setVehicle(BeanUtils.copy(vehicle, VehicleBo.class));
            }
            db.setCheckRejectReason(ctx.getReason());
            db.setCheckRemark(ctx.getRemark());
            db.setBeforeLcWhite(JsonUtils.toJson(vehicleLcWhitelistBo));
            model.setCheckRejectReason(ctx.getReason() == null ? "" : ctx.getReason());
            model.setCheckRemark(ctx.getRemark() == null ? "" : ctx.getRemark());
            model.setBeforeLcWhite(JsonUtils.toJson(vehicleLcWhitelistBo));
        }

        if (status == VehicleCheckStatusEnum.EFFECTIVE) {
            List<LocalDateTime> localDateTimes = lcService.addWhitelist(model.getNumber(), data.getDuration(), model.getUserId());
            db.setMonthlyCardStartAt(localDateTimes.get(0));
            db.setMonthlyCardExpireAt(localDateTimes.get(1));
            vehicleManager.hideOtherSamePlate(db);
//            db.setMonthlyCardStartAt(LocalDateTime.now());
//            Integer duration = data.getDuration();
//            LocalDateTime endAt = LocalDateTime.now().plusDays(duration);
//            db.setMonthlyCardExpireAt(endAt);
        }
        vehicleManager.save(db);

        model.setCheckStatus(status);


        if (status == VehicleCheckStatusEnum.REJECTED) {
            MsgSendByTemplateDto template = new MsgSendByTemplateDto();
            template.setSendUserId(0L);
            Map<String, Object> context = JsonUtils.toMap(JsonUtils.toJson(db));
            template.setContext(context);
            template.setOuterType("vehicle");
            template.setOuterId(db.getId());
            template.setReceiveUserIds(List.of(db.getUserId()));
            template.setTemplateCode("vehicleReject");
            msgService.sendMessageByTemplate(template);
        }

        return data;
    }

    @Override
    public String code() {
        return "vehicleStatusChange";
    }

}
