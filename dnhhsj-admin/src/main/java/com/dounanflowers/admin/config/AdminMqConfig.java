package com.dounanflowers.admin.config;

import com.dounanflowers.common.constant.MqConstant;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AdminMqConfig {

    @Bean
    public Queue billQueue() {
        return new Queue(MqConstant.BILL_QUEUE, true, false, false);
    }

    @Bean
    public DirectExchange billExchange() {
        return new DirectExchange(MqConstant.BILL_EXCHANGE, true, false);
    }

    @Bean
    public Binding billBinding() {
        return BindingBuilder.bind(billQueue()).to(billExchange()).with(MqConstant.BILL_ROUTING_KEY);
    }

}
