package com.dounanflowers.admin.api;

import com.dounanflowers.admin.dto.ShopSubscribeAddDto;
import com.dounanflowers.admin.service.ShopSubscribeService;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.bo.ShopSubscribeBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.dto.HandleStatusBo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin")
@Tag(name = "小程序/商铺/订阅")
@RequiredArgsConstructor
@Authorized({"admin", "shop"})
public class AdminShopSubscribeApi {

    private final ShopSubscribeService shopSubscribeService;

    @PostMapping("/shopSubscribePageList")
    @Operation(summary = "商铺订阅分页列表")
    public Page<ShopSubscribeBo> shopSubscribePageList(@RequestBody PageRequest pageRequest) {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        PageFilter filter1 = new PageFilter();
        filter1.setField("adminUserId");
        filter1.setType("eq");
        filter1.setValue(userId);
        pageRequest.addFilter(filter1);
        return shopSubscribeService.pageList(pageRequest);
    }

    @PostMapping("/shopSubscribeAdd")
    @Operation(summary = "商铺订阅新增")
    public ShopSubscribeBo shopSubscribeAdd(@RequestBody ShopSubscribeAddDto dto) {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        return shopSubscribeService.add(dto, userId);
    }

    @PostMapping("/shopSubscribeDeleteById")
    @Operation(summary = "商铺订阅删除")
    public HandleStatusBo shopSubscribeDeleteById(@RequestBody EditIdDto dto) {
        shopSubscribeService.deleteById(dto.getId());
        return HandleStatusBo.success();
    }

}
