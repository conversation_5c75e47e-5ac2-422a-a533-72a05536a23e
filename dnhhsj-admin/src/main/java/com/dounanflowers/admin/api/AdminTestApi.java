package com.dounanflowers.admin.api;

import com.dounanflowers.common.enums.AdminMenuEventEnum;
import com.dounanflowers.common.service.AdminMenuService;
import com.dounanflowers.security.annotation.Authorized;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * WebSocket测试接口
 */
@Tag(name = "后台管理/测试")
@RestController
@RequestMapping("/admin/test")
@RequiredArgsConstructor
public class AdminTestApi {

    private final AdminMenuService menuService;
    /**
     * 测试发送菜单消息
     */
    @Operation(summary = "测试发送消息")
    @PostMapping("/menu-message")
    @Authorized({"admin"})
    public boolean testSendFavouriteMessage() {
        menuService.emitCount(AdminMenuEventEnum.JOB_AUDIT);
        return true;
    }
}
