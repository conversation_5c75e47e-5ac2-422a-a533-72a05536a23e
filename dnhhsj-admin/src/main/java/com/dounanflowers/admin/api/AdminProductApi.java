package com.dounanflowers.admin.api;

import com.dounanflowers.admin.service.AdminProductService;
import com.dounanflowers.common.bo.ProductBo;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Tag(name = "商品管理")
@RestController
@RequestMapping("/admin/product")
@RequiredArgsConstructor
public class AdminProductApi {

    private final AdminProductService adminProductService;

    @PostMapping("/pageList")
    @Operation(summary = "商品分页列表")
    public Page<ProductBo> pageList(@RequestBody PageRequest dto) {
        return adminProductService.productPageList(dto);
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "商品详情")
    public ProductBo detail(@PathVariable Long id) {
        return adminProductService.getProductDetail(id);
    }

    @PostMapping("/save")
    @Operation(summary = "保存商品")
    public void save(@RequestBody ProductBo dto) {
        adminProductService.saveProduct(dto);
    }

    @PostMapping("/delete/{id}")
    @Operation(summary = "删除商品")
    public void delete(@PathVariable Long id) {
        adminProductService.deleteProduct(id);
    }

    @PostMapping("/updateStock")
    @Operation(summary = "更新库存")
    public void updateStock(@RequestParam Long id, @RequestParam Integer stock) {
        adminProductService.updateProductStock(id, stock);
    }

    @PostMapping("/updateStatus")
    @Operation(summary = "更新状态")
    public void updateStatus(@RequestParam Long id, @RequestParam String status) {
        adminProductService.updateProductStatus(id, status);
    }

}
