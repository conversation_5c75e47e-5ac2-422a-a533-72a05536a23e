package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.bo.BrokerBo;
import com.dounanflowers.common.dto.BrokerCalledDto;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.service.BrokerService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/broker")
@Tag(name = "后台管理/经纪人")
@RequiredArgsConstructor
@Authorized("admin")
public class AdminBrokerApi {

    private final BrokerService brokerService;

    @PostMapping("/save")
    @Operation(summary = "经纪人创建或更新")
    public BrokerBo brokerCreateOrUpdate(@RequestBody BrokerBo brokerBo) {
        return brokerService.saveBroker(brokerBo);
    }

    @PostMapping("/page")
    @Operation(summary = "经纪人分页列表")
    public Page<BrokerBo> brokerPageList(@RequestBody PageRequest pageRequest) {
        return brokerService.pageBroker(pageRequest);
    }

    @PostMapping("/delete")
    @Operation(summary = "经济人删除")
    public Boolean brokerDeleteById(@RequestBody EditIdDto editIdDto) {
        return brokerService.deleteBroker(editIdDto.getId());
    }


    @PostMapping("/get")
    @Operation(summary = "加载一位经纪人", description = "随机加载一个经纪人，或根据条件优先选择一位经纪人")
    @Authorized({"admin", "shop"})
    public BrokerBo brokerGetOne() {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        return brokerService.getBrokerByUserId(userId);
    }

    @PostMapping("/called")
    @Operation(summary = "经纪人被打电话", description = "- 将brokerId写入用户boundBrokerId，表示用户和经纪人绑定\n- 商铺brokerCalledCount+1")
    @Authorized({"admin", "shop"})
    public Boolean brokerCalled(@RequestBody BrokerCalledDto dto) {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        dto.setUserId(userId);
        return brokerService.bindBroker(dto);
    }


}
