package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.InvoiceApplyDto;
import com.dounanflowers.common.dto.InvoiceProcessDto;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.enums.UserTypeEnum;
import com.dounanflowers.common.service.InvoiceService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 管理员发票API
 */
@RestController
@RequestMapping("/admin/invoice")
@Tag(name = "后台管理/发票")
@RequiredArgsConstructor
public class AdminInvoiceApi {

    private final InvoiceService invoiceService;

    @PostMapping("/pageList")
    @Authorized("admin")
    @Operation(summary = "发票分页列表(管理员)")
    public Page<InvoiceBo> pageListInvoices(@RequestBody PageRequest dto) {
        return invoiceService.pageInvoices(dto);
    }
    /**
     * 处理发票申请（上传发票或拒绝）
     */
    @PostMapping("/process")
    @Authorized("admin")
    public Boolean processInvoice(@RequestBody InvoiceProcessDto dto) {
        // 获取当前管理员信息
        SessionInfo<Long, AdminUserBo> session = SecurityHolder.session();
        invoiceService.processInvoice(dto, session.getUserId());
        return true;
    }


    @PostMapping("/getByBillId")
    @Authorized("admin")
    public InvoiceBo getByBillId(@RequestBody EditIdDto dto) {
        return invoiceService.fetchByBillId(dto.getId());
    }

    @PostMapping("/page")
    @Authorized("shop")
    @Operation(summary = "发票分页列表")
    public Page<InvoiceBo> pageInvoices(@RequestBody PageRequest dto) {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        dto.addFilter(new PageFilter().setField("userId").setType("eq").setValue(userId));
        return invoiceService.pageInvoices(dto);
    }

    @PostMapping("/apply")
    @Authorized("shop")
    @Operation(summary = "发票申请")
    public Boolean applyInvoice(@RequestBody InvoiceApplyDto dto) {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        invoiceService.applyInvoice(dto, userId, UserTypeEnum.ADMIN);
        return true;
    }

    /**
     * 获取发票详情
     */
    @PostMapping("/get")
    @Authorized({"admin", "shop"})
    @Operation(summary = "获取发票详情")
    public InvoiceBo getInvoiceDetail(@RequestBody EditIdDto dto) {
        return invoiceService.fetchById(dto.getId());
    }

    @PostMapping("/pageBillsForInvoice")
    @Authorized({ "shop"})
    @Operation(summary = "可开发票账单分页列表")
    public Page<BillBo> pageBillsForInvoice(@RequestBody PageRequest pageRequest) {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        return invoiceService.pageBillsForInvoice(pageRequest, userId, UserTypeEnum.ADMIN);
    }


    @PostMapping("/headerInfoList")
    @Authorized({ "shop"})
    @Operation(summary = "既往发票抬头列表")
    public List<InvoiceHeaderInfoBo> headerInfoList() {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        return invoiceService.invoiceHeaderInfoList( userId);
    }

    @PostMapping("/count")
    @Authorized({ "shop"})
    @Operation(summary = "发票统计")
    public InvoiceCountBo count() {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        return invoiceService.count( userId, UserTypeEnum.ADMIN);
    }

}
