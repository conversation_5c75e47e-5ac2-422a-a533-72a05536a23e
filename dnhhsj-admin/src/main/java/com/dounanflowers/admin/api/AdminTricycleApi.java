package com.dounanflowers.admin.api;

import com.dounanflowers.admin.dto.FakePayDto;
import com.dounanflowers.admin.dto.ManualPayDto;
import com.dounanflowers.admin.dto.TricycleApplyDto;
import com.dounanflowers.admin.dto.TricycleCheckDto;
import com.dounanflowers.admin.service.AdminTricycleService;
import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.CreateTricycleBatchDTO;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.dto.EditWithPasswordDto;
import com.dounanflowers.common.dto.IdsDto;
import com.dounanflowers.common.enums.TricycleCheckStatusEnum;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.bean.PageSort;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.bo.WxPayParamsBo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/admin/tricycle")
@Tag(name = "后台管理/三轮车")
@RequiredArgsConstructor
@Authorized({"admin", "shop"})
public class AdminTricycleApi {

    private final AdminTricycleService adminTricycleService;

    @PostMapping("/page")
    @Operation(summary = "三轮车分页列表")
    public TricyclePageBo tricyclePageList(@RequestBody PageRequest pageRequest) {
        if (!SecurityHolder.session().getName().equals("admin")) {
            pageRequest.addFilter(new PageFilter().setField("userId").setType("ne"));
        }
        if (CollectionUtils.isEmpty(pageRequest.getSort())) {
            pageRequest.addSort(new PageSort().setField("effective_at is not null").setOrder("desc"))
                    .addSort(new PageSort().setField("effectiveAt").setOrder("desc"))
                    .addSort(new PageSort().setField("createdAt").setOrder("desc"))
                    .addSort(new PageSort().setField("id").setOrder("desc"));
        }
        return adminTricycleService.pageList(pageRequest);
    }

    @PostMapping("/page/my")
    @Operation(summary = "三轮车分页列表")
    public TricyclePageBo myTricyclePageList(@RequestBody PageRequest pageRequest) {
        pageRequest.addFilter(new PageFilter().setField("userId").setType("eq").setValue(SecurityHolder.session().getUserId()));
        if (CollectionUtils.isEmpty(pageRequest.getSort())) {
            pageRequest.addSort(new PageSort().setField("effective_at is not null").setOrder("desc"))
                    .addSort(new PageSort().setField("effectiveAt").setOrder("desc"))
                    .addSort(new PageSort().setField("createdAt").setOrder("desc"))
                    .addSort(new PageSort().setField("id").setOrder("desc"));
        }
        return adminTricycleService.pageList(pageRequest);
    }

    @PostMapping("/by/user/list")
    @Operation(summary = "用户三轮车列表")
    public List<TricycleBo> tricycleListByUser(@RequestBody EditIdDto dto) {
        return adminTricycleService.tricycleListByUser(dto.getId());
    }

    @PostMapping("/delete")
    @Operation(summary = "删除三轮车")
    public Boolean deleteTricycle(@RequestBody EditWithPasswordDto dto) {
        return adminTricycleService.deleteTricycle(dto);
    }

    @PostMapping("/deposit/warn/list")
    @Operation(summary = "三轮车保证金警告列表")
    public List<TricycleBo> tricycleDepositWarnList() {
        return adminTricycleService.tricycleDepositWarnList();
    }

    @PostMapping("/check")
    @Operation(summary = "三轮车审核")
    public Boolean tricycleCheck(@RequestBody TricycleCheckDto dto) {
        return adminTricycleService.checkTricycle(dto);
    }


    @PostMapping("/check/agree")
    @Operation(summary = "三轮车审核")
    public Boolean tricycleCheckAgree(@RequestBody TricycleCheckDto dto) {
        dto.setCheckStatus(TricycleCheckStatusEnum.APPROVED.ordinal() + "");
        dto.setForce(true);
        return adminTricycleService.checkTricycle(dto);
    }


    @PostMapping("/check/cancel")
    @Operation(summary = "三轮车取消审核")
    public Boolean tricycleCheckCancel(@RequestBody EditIdDto dto) {
        return adminTricycleService.cancelCheckTricycle(dto.getId());
    }

    @PostMapping("/get")
    @Operation(summary = "三轮车详情")
    public TricycleBo tricycleGetById(@RequestBody EditIdDto editIdDto) {
        return adminTricycleService.getById(editIdDto.getId());
    }

    @PostMapping("/apply")
    @Operation(summary = "三轮车审核申请")
    public TricycleBo tricycleCheckApply(@RequestBody TricycleApplyDto dto) {
        if (CollectionUtils.isEmpty(dto.getImages())) {
            throw new BaseException("请上传三轮车照片");
        }
        if (dto.getId() == null) {
            return adminTricycleService.applyTricycle(dto);
        } else {
            return adminTricycleService.applyTricycleAgain(dto);
        }
    }

    @PostMapping("/update")
    @Operation(summary = "修改三轮车信息")
    public Boolean tricycleEdit(@RequestBody TricycleApplyDto dto) {
        return adminTricycleService.updateTricycle(dto);
    }

    @PostMapping("/qrcode")
    @Operation(summary = "三轮车二维码")
    public TricycleQrcodeBo tricycleQrcode(@RequestBody EditIdDto dto) {
        return adminTricycleService.qrcode(dto.getId());
    }

    @PostMapping("/make/pay")
    @Operation(summary = "三轮车号牌制作付款")
    public WxPayParamsBo tricycleMakePay(@RequestBody FakePayDto dto) {
        return adminTricycleService.makePay(dto);
    }

    @PostMapping("/make/pay/manual")
    @Operation(summary = "三轮车号牌制作手动付款")
    public BillBo manualMakePay(@RequestBody ManualPayDto dto) {
        return adminTricycleService.manualMakePay(dto);
    }

    @PostMapping("/make/send")
    @Operation(summary = "三轮车号牌制作发货")
    public Boolean tricycleMakeSend(@RequestBody TricycleApplyDto dto) {
        return adminTricycleService.makeSend(dto);
    }

    @PostMapping("/deposit/pay")
    @Operation(summary = "三轮车保证金付款")
    public WxPayParamsBo tricycleDepositPay(@RequestBody FakePayDto dto) {
        return adminTricycleService.depositPay(dto);
    }

    @PostMapping("/deposit/page")
    @Operation(summary = "三轮车保证金分页列表")
    public Page<TricycleDepositBo> tricycleDepositPage(@RequestBody PageRequest dto) {
        return adminTricycleService.tricycleDepositPage(dto);
    }

    @PostMapping("/deposit/refund")
    @Operation(summary = "三轮车保证金退款")
    public Boolean tricycleDepositRefund(@RequestBody EditIdDto dto) {
        return adminTricycleService.depositRefund(dto.getId());
    }

    @PostMapping("/deposit/refund/apply")
    @Operation(summary = "三轮车退保证金申请")
    public Boolean tricycleDepositRefundApply(@RequestBody EditIdDto dto) {
        return adminTricycleService.depositRefundApply(dto.getId());
    }

    @PostMapping("/batch/create")
    @Operation(summary = "批量创建三轮车")
    public TricycleBatchBo createBatch(@RequestBody CreateTricycleBatchDTO dto) {
        return adminTricycleService.createBatch(dto);
    }

    @PostMapping("/batch/page")
    @Operation(summary = "三轮车批次分页列表")
    public Page<TricycleBatchBo> tricycleBatchPage(@RequestBody PageRequest dto) {
        return adminTricycleService.tricycleBatchPage(dto);
    }

    @PostMapping("/batch/get")
    @Operation(summary = "三轮车批次详情")
    public TricycleBatchBo tricycleBatchGetById(@RequestBody EditIdDto dto) {
        return adminTricycleService.getBatchById(dto.getId());
    }

    @PostMapping("/make/batch")
    @Operation(summary = "三轮车批量制作")
    public TricycleBatchBo tricycleMakeBatch(@RequestBody IdsDto dto) {
        return adminTricycleService.makeBatch(dto.getIds());
    }

    @PostMapping("/done/batch")
    @Operation(summary = "三轮车批量完成")
    public TricycleBatchBo tricycleDoneBatch(@RequestBody IdsDto dto) {
        return adminTricycleService.doneBatch(dto.getIds());
    }

    @PostMapping("/record/page")
    @Operation(summary = "三轮车记录分页列表")
    public Page<TricycleInfoRecordBo> tricycleRecordPage(@RequestBody PageRequest dto) {
        return adminTricycleService.tricycleRecordPage(dto);
    }

}
