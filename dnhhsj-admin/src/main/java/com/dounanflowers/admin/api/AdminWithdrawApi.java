package com.dounanflowers.admin.api;

import com.dounanflowers.admin.bo.WithdrawAccountBo;
import com.dounanflowers.admin.bo.WithdrawAccountRecordBo;
import com.dounanflowers.admin.bo.WithdrawConfigBo;
import com.dounanflowers.admin.bo.WithdrawRecordBo;
import com.dounanflowers.admin.service.AdminWithdrawService;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.dto.WithdrawApproveDto;
import com.dounanflowers.common.dto.WithdrawCreateDto;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/withdraw")
@Tag(name = "后台管理/提现管理")
@RequiredArgsConstructor
public class AdminWithdrawApi {

    private final AdminWithdrawService withdrawService;

    @PostMapping("/account/get")
    @Operation(summary = "获取账户信息")
    @Authorized({"admin", "shop"})
    public WithdrawAccountBo getAccount() {
        return withdrawService.getAccount(SecurityHolder.<Long, AdminUserBo>session().getUserId());
    }

    @PostMapping("/account/page")
    @Operation(summary = "账户列表分页")
    @Authorized({"admin"})
    public Page<WithdrawAccountBo> getAccountPage(@RequestBody PageRequest pageRequest) {
        return withdrawService.getAccountPage(pageRequest);
    }

    @PostMapping("/account/record/page")
    @Operation(summary = "账户记录列表分页")
    @Authorized({"admin", "shop"})
    public Page<WithdrawAccountRecordBo> getAccountRecordPage(@RequestBody PageRequest pageRequest) {
        if (SecurityHolder.session().getName().equals("shop")) {
            pageRequest.addFilter(new PageFilter().setField("userId").setType("eq").setValue(SecurityHolder.session().getUserId()));
        }
        return withdrawService.getAccountRecordPage(pageRequest);
    }

    @PostMapping("/config/get")
    @Operation(summary = "获取提现配置")
    @Authorized({"admin", "shop"})
    public WithdrawConfigBo getConfig() {
        return withdrawService.getConfig(SecurityHolder.<Long, AdminUserBo>session().getUserId());
    }

    @PostMapping("/record/page")
    @Operation(summary = "提现记录分页")
    @Authorized({"admin", "shop"})
    public Page<WithdrawRecordBo> getRecordPage(@RequestBody PageRequest pageRequest) {
        if (SecurityHolder.session().getName().equals("shop")) {
            pageRequest.addFilter(new PageFilter().setField("userId").setType("eq").setValue(SecurityHolder.session().getUserId()));
        }
        return withdrawService.getRecordPage(pageRequest);
    }

    @PostMapping("/create")
    @Operation(summary = "创建提现记录")
    @Authorized({"admin", "shop"})
    public WithdrawRecordBo create(@RequestBody WithdrawCreateDto createDto) {
        return withdrawService.create(createDto);
    }

    @PostMapping("/approve")
    @Operation(summary = "提现审批")
    @Authorized({"admin"})
    public void approve(@RequestBody WithdrawApproveDto approveDto) {
        withdrawService.approve(approveDto);
    }

    @PostMapping("/reject")
    @Operation(summary = "提现拒绝")
    @Authorized({"admin"})
    public void reject(@RequestBody WithdrawApproveDto approveDto) {
        withdrawService.reject(approveDto);
    }
}
