package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.ArtworkBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.service.ArtworkService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/artwork")
@Tag(name = "后台管理/艺术品")
@RequiredArgsConstructor
public class AdminArtworkApi {

    private final ArtworkService artworkService;

    @PostMapping("/page")
    @Operation(summary = "艺术品分页列表")
    @Authorized("admin")
    public Page<ArtworkBo> artworkPageList(@RequestBody PageRequest pageRequest) {
        return artworkService.artworkPageList(pageRequest);
    }

    @PostMapping("/get")
    @Operation(summary = "艺术品详情")
    @Authorized("admin")
    public ArtworkBo artworkGetById(@RequestBody EditIdDto editIdDto) {
        return artworkService.getById(editIdDto.getId());
    }

    @PostMapping("/save")
    @Operation(summary = "艺术品新增或更新")
    @Authorized("admin")
    public ArtworkBo artworkCreateOrUpdate(@RequestBody ArtworkBo dto) {
        return artworkService.save(dto);
    }

    @PostMapping("/delete")
    @Operation(summary = "艺术品删除")
    @Authorized("admin")
    public Boolean artworkDeleteById(@RequestBody EditIdDto editIdDto) {
        return artworkService.deleteById(editIdDto.getId());
    }

}
