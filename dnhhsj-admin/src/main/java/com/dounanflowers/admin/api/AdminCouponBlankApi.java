package com.dounanflowers.admin.api;

import com.dounanflowers.admin.bo.CouponBlankBo;
import com.dounanflowers.admin.bo.CouponBlankDetailBo;
import com.dounanflowers.admin.dto.CouponBlankCreateDto;
import com.dounanflowers.admin.service.AdminCouponService;
import com.dounanflowers.common.bo.CouponBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/admin/coupon/blank")
@Tag(name = "后台管理/优惠券")
@RequiredArgsConstructor
@Authorized("admin")
public class AdminCouponBlankApi {

    private final AdminCouponService adminCouponService;

    @PostMapping("/create")
    @Operation(summary = "空白优惠券创建")
    @Authorized({"admin", "shop"})
    public CouponBlankDetailBo couponBlankCreate(@RequestBody CouponBlankCreateDto dto) {
        return adminCouponService.createBlank(dto);
    }

    @PostMapping("/get")
    @Operation(summary = "空白优惠券创建")
    @Authorized({"admin", "shop"})
    public CouponBlankDetailBo getCouponBlankInfo(@RequestBody EditIdDto dto) {
        return adminCouponService.getBlankInfo(dto.getId());
    }

    @PostMapping("/page")
    @Operation(summary = "空白券生成记录分页列表")
    @Authorized({"admin", "shop"})
    public Page<CouponBlankBo> couponBlankPageList(@RequestBody PageRequest pageRequest) {
        List<String> roles = SecurityHolder.session().getRoles();
        if(roles.isEmpty()) {
            return Page.empty();
        }
        if (!roles.contains("admin") && roles.contains("0")) {
            pageRequest.addFilter(new PageFilter().setField("belongUserId").setType("eq").setValue(SecurityHolder.session().getUserId()));
        }
        return adminCouponService.blankPage(pageRequest);
    }

    @PostMapping("/delete")
    @Operation(summary = "空白券生成记录删除")
    @Authorized({"admin", "shop"})
    public Boolean couponBlankDeleteById(@RequestBody EditIdDto editIdDto) {
        return adminCouponService.deleteBlank(editIdDto.getId());
    }

    @PostMapping("/update")
    @Operation(summary = "空白券生成记录更新")
    public Boolean couponBlankUpdate(@RequestBody CouponBlankBo dto) {
        return adminCouponService.updateBlank(dto);
    }

    @PostMapping("/coupon/list")
    @Operation(summary = "商家空白券记录包含优惠券")
    @Authorized({"admin", "shop"})
    public List<CouponBo> couponListInCouponBlank(@RequestBody EditIdDto dto) {
        return adminCouponService.couponListInCouponBlank(dto.getId());
    }

}
