package com.dounanflowers.admin.api;

import com.dounanflowers.admin.dto.ParkCarInDto;
import com.dounanflowers.admin.dto.ParkCarOutDto;
import com.dounanflowers.admin.dto.ParkHeartBeatDto;
import com.dounanflowers.admin.dto.ParkPassageWayUpsertDto;
import com.dounanflowers.admin.service.ParkService;
import com.dounanflowers.framework.bean.BlueCardResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/park")
@Tag(name = "停车场")
@RequiredArgsConstructor
public class ParkApi {

    private final ParkService parkService;

    @PostMapping("/heartBeat")
    @Operation(summary = "心跳接口")
    public BlueCardResult heartBeat(@RequestBody ParkHeartBeatDto parkHeartBeatDto) {
        return parkService.heartBeat(parkHeartBeatDto);
    }

    @PostMapping("/carIn")
    @Operation(summary = "实时发送入场记录", description = "场库实时发送最新的入场记录(每次发送1-10条入场记录)，若3次发送均失败，那么每5分钟发送一次此入场记录,直至发送成功或者车辆已出场为止。")
    public BlueCardResult carIn(@RequestBody ParkCarInDto parkCarInDto) {
        return parkService.carIn(parkCarInDto);
    }

    @PostMapping("/carOut")
    @Operation(summary = "实时发送出场记录", description = "场库按出场时间倒序把未发送的出场记录发送到第三方云，(每次发送1-10条)。若3次发送均失败，那么每5分钟发送一次此出场记录,直至成功为止。  \nCharge=onLineCharge+offLineCharge+profitChargeTotal  \nprofitChargeTotal=onLineProfitChargeValue+offLineProfitChargeValue+onLineProfitTimeValue +offLineProfitTimeValue  \n\n")
    public BlueCardResult carOut(@RequestBody ParkCarOutDto parkCarOutDto) {
        return parkService.carOut(parkCarOutDto);
    }

    @PostMapping("/passageWayUpsert")
    @Operation(summary = "通道信息上传", description = "场库将停车场信息发送给接口。注意:只有通道信息有变化才会调用此接口.")
    public BlueCardResult passageWayUpsert(@RequestBody ParkPassageWayUpsertDto parkPassageWayUpsertDto) {
        return parkService.passageWayUpsert(parkPassageWayUpsertDto);
    }

}
