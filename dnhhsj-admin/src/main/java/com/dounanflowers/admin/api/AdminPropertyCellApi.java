package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.*;
import com.dounanflowers.common.dto.*;
import com.dounanflowers.common.entity.PropertyCell;
import com.dounanflowers.common.service.PropertyCellService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.bo.WxPayParamsBo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/admin")
@Tag(name = "后台管理/物业单元")
@RequiredArgsConstructor
public class AdminPropertyCellApi {
    private final PropertyCellService propertyCellService;

    @PostMapping("/propertyCell/pageList")
    @Operation(summary = "物业单元分页列表")
    @Authorized({ "admin"})
    public Page<PropertyCellBo> shopPropertyCellPageList(@RequestBody PageRequest pageRequest) {
        return propertyCellService.page(pageRequest);
    }


    @PostMapping("/propertyCell/update")
    @Operation(summary = "更新物业单元")
    @Authorized({ "admin"})
    public void update(@RequestBody PropertyCellBo dto) {
        propertyCellService.update(dto);
    }

    @PostMapping("/propertyCell/add")
    @Operation(summary = "添加物业单元")
    @Authorized({ "admin"})
    public void add(@RequestBody PropertyCellBo dto) {
        propertyCellService.add(dto);
    }

    @PostMapping("/propertyCell/sameCellNoItems")
    @Operation(summary = "同位已折叠物业单元")
    @Authorized({ "admin"})
    public List<PropertyCellBo> sameCellNoItems(@RequestBody IdDto dto) {
        return propertyCellService.sameCellNoItems(dto.getId());
    }

    @PostMapping("/propertyCell/syncAll")
    @Operation(summary = "同步所有物业单元")
    @Authorized({ "admin"})
    public void syncAll() {
        propertyCellService.syncAllPropertyCell();
    }

    @PostMapping("/propertyCell/syncItem")
    @Operation(summary = "同步单个物业单元")
    @Authorized({ "admin"})
    public PropertyCell syncItem(@RequestBody IdDto dto) {
        return propertyCellService.syncPropertyCell(dto.getId());
    }

    @PostMapping("/propertyCell/checkIsExist")
    @Operation(summary = "是否存在同位物业单元")
    @Authorized({ "admin"})
    public PropertyCell checkIsExist(@RequestBody PropertyCellBo dto) {
        return propertyCellService.checkIsExist(dto);
    }

}
