package com.dounanflowers.admin.api;

import com.dounanflowers.admin.dto.AdminUserEditDto;
import com.dounanflowers.admin.dto.LoginDto;
import com.dounanflowers.admin.service.AdminUserService;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.dto.TwoFactorLoginDto;
import com.dounanflowers.common.dto.UserRealVerifyDto;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "管理员安全接口")
@RequiredArgsConstructor
@RequestMapping("/admin")
@RestController
public class AdminSecurityApi {

    private final AdminUserService userService;

    @PostMapping("/login")
    @Operation(summary = "账号登录")
    public SessionInfo<Long, AdminUserBo> login(@RequestBody LoginDto dto) {
        SessionInfo<Long, AdminUserBo> session = userService.loginByUsername(dto.getUsername(), dto.getPassword());
        return session.clearSensitive();
    }

    @PostMapping("/logout")
    @Operation(summary = "账号登出")
    @Authorized({"admin", "shop"})
    public void logout() {
        SecurityHolder.logout();
    }

    @PostMapping("/session")
    @Authorized({"admin", "shop"})
    @Operation(summary = "获取当前登录用户")
    public AdminUserBo getSessionUser() {
        return SecurityHolder.<Long, AdminUserBo>session().getUserInfo();
    }

    @PostMapping("/userRealnameVerify")
    @Operation(summary = "身份证实名认证", description = "身份证需要调取拍照，可以上传拍照的缩略图  \n返回的userInfo中realname字段为最新实名认证的姓名，可用于判断是否实名认证")
    @Authorized({"admin", "shop"})
    public SessionInfo<Long, AdminUserBo> userRealnameVerify(@RequestBody UserRealVerifyDto dto) {
        userService.realnameVerify(dto);
        return SecurityHolder.session();
    }

    @PostMapping("/myUserInfoUpdate")
    @Operation(summary = "修改自己用户信息")
    @Authorized({"admin", "shop"})
    public AdminUserBo myUserInfoUpdate(@RequestBody AdminUserEditDto dto) {
        return userService.updateSelfUserInfo(dto);
    }

    @PostMapping("/captcha")
    @Operation(summary = "验证码")
    public String captcha() {
        return null;
    }

    @PostMapping("/unbind/mobile")
    @Operation(summary = "解绑手机号")
    @Authorized("shop")
    public SessionInfo<Long, AdminUserBo> unbindMobile() {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        userService.unbindMobile(userId);
        return SecurityHolder.session();
    }

    @PostMapping("/unbind/openid")
    @Operation(summary = "解绑手机号")
    @Authorized("shop")
    public SessionInfo<Long, AdminUserBo> unbindOpenid() {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        userService.unbindOpenid(userId);
        return SecurityHolder.session();
    }

    @PostMapping("/loginBy2Fa")
    @Operation(summary = "2FA管理员登录", description = "使用手机号+2FA验证码登录任意账号")
    public SessionInfo<Long, AdminUserBo> loginBy2Fa(@RequestBody TwoFactorLoginDto dto) {
        SessionInfo<Long, AdminUserBo> session = userService.loginBy2Fa(dto);
        return session.clearSensitive();
    }

    @PostMapping("/user/shop/delete")
    @Operation(summary = "删除账号")
    @Authorized("shop")
    public void logoutAccount() {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        userService.deleteUserShop(userId);
        SecurityHolder.logout();
    }

}
