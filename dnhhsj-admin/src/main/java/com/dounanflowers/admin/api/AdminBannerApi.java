package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.BannerBo;
import com.dounanflowers.common.bo.BannerShowBo;
import com.dounanflowers.common.dto.BannerShowDto;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.service.SystemService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/admin/banner")
@Tag(name = "后台管理/横幅管理")
@RequiredArgsConstructor
public class AdminBannerApi {

    private final SystemService systemService;

    @PostMapping("/page")
    @Operation(summary = "横幅分页列表")
    @Authorized("admin")
    public Page<BannerBo> bannerPageList(@RequestBody PageRequest pageRequest) {
        return systemService.getBannerPage(pageRequest);
    }

    @PostMapping("/save")
    @Operation(summary = "横幅添加或修改")
    @Authorized("admin")
    public BannerBo bannerCreateOrUpdate(@RequestBody BannerBo dto) {
        return systemService.saveBanner(dto);
    }

    @PostMapping("/delete")
    @Operation(summary = "横幅删除")
    @Authorized("admin")
    public Boolean bannerDeleteById(@RequestBody EditIdDto editIdDto) {
        return systemService.deleteBanner(editIdDto.getId());
    }

    @PostMapping("/list")
    @Operation(summary = "轮播列表")
    public List<BannerShowBo> bannerList(@RequestBody BannerShowDto dto) {
        return systemService.getBannerShowList(dto.getPlace());
    }

}
