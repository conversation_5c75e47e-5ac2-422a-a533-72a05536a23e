package com.dounanflowers.admin.api;

import com.dounanflowers.admin.dto.TricycleIllegalFinePayDto;
import com.dounanflowers.admin.dto.TricycleIllegalGetDto;
import com.dounanflowers.admin.dto.TricycleIllegalHandleDto;
import com.dounanflowers.admin.dto.TricycleIllegalRecordDto;
import com.dounanflowers.admin.service.AdminTricycleIllegalService;
import com.dounanflowers.common.bo.TricycleIllegalBo;
import com.dounanflowers.common.bo.TricycleIllegalPageBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.third.bo.WxPayParamsBo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/tricycle/illegal")
@Tag(name = "后台管理/三轮车")
@RequiredArgsConstructor
@Authorized({"admin", "shop"})
public class AdminTricycleIllegalApi {

    private final AdminTricycleIllegalService adminTricycleIllegalService;

    @PostMapping("/create")
    @Operation(summary = "三轮车违规情况录入")
    public TricycleIllegalBo tricycleIllegalCreate(@RequestBody TricycleIllegalRecordDto dto) {
        return adminTricycleIllegalService.createIllegalRecord(dto);
    }

    @PostMapping("/handle")
    @Operation(summary = "三轮车违规处理")
    public TricycleIllegalBo tricycleIllegalHandle(@RequestBody TricycleIllegalHandleDto dto) {
        return adminTricycleIllegalService.handleIllegalRecord(dto);
    }

    @PostMapping("/fine/pay")
    @Operation(summary = "三轮车违规缴纳罚款")
    public WxPayParamsBo tricycleIllegalFinePay(@RequestBody TricycleIllegalFinePayDto dto) {
        return adminTricycleIllegalService.payFine(dto);
    }

    @PostMapping("/page")
    @Operation(summary = "三轮车违规记录列表", description = "```js\nfilter: [\n    {type: 'custom', field: 'uiHandleStatus', value: 'xxx'}, // xxx可以是 已警告/待处理/已处理\n]\n\n```")
    public TricycleIllegalPageBo tricycleIllegalPageList(@RequestBody PageRequest pageRequest) {
        return adminTricycleIllegalService.pageIllegalRecord(pageRequest);
    }

    @PostMapping("/get")
    @Operation(summary = "三轮车违规记录详情")
    public TricycleIllegalBo tricycleIllegalGet(@RequestBody TricycleIllegalGetDto editIdDto) {
        return adminTricycleIllegalService.getIllegalRecordById(editIdDto.getId());
    }

    @PostMapping("/pass")
    @Operation(summary = "三轮车违规出库放行")
    public Boolean tricycleIllegalPass(@RequestBody EditIdDto dto) {
        return adminTricycleIllegalService.passIllegalRecord(dto.getId());
    }

}
