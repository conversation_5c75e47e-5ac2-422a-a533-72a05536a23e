package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.ArticleBo;
import com.dounanflowers.common.dto.ArticleGetDto;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.service.ArticleService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/admin/article")
@Tag(name = "后台管理/文章管理")
@RequiredArgsConstructor
public class AdminArticleApi {

    private final ArticleService articleService;

    @PostMapping("/page")
    @Operation(summary = "文章分页列表")
    @Authorized({"admin", "shop"})
    public Page<ArticleBo> articlePageList(@RequestBody PageRequest pageRequest) {
        return articleService.articlePageList(pageRequest);
    }

    @PostMapping("/get")
    @Operation(summary = "文章详情")
    public ArticleBo articleGetById(@RequestBody ArticleGetDto editIdDto) {
        if (editIdDto.getId() != null) {
            return articleService.getById(editIdDto.getId());
        } else if (StringUtils.isNotBlank(editIdDto.getTitle())) {
            return articleService.getByTitle(editIdDto.getTitle());
        } else {
            return null;
        }
    }

    @PostMapping("/delete")
    @Operation(summary = "文章删除")
    @Authorized("admin")
    public Boolean articleDeleteById(@RequestBody EditIdDto editIdDto) {
        return articleService.deleteById(editIdDto.getId());
    }

    @PostMapping("/tags")
    @Operation(summary = "文章所有标签")
    @Authorized("admin")
    public List<String> articleAllTags() {
        return articleService.allTags();
    }

    @PostMapping("/save")
    @Operation(summary = "文章创建或修改")
    @Authorized("admin")
    public ArticleBo articleCreateOrUpdate(@RequestBody ArticleBo bo) {
        return articleService.save(bo);
    }

}
