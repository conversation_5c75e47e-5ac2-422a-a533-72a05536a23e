package com.dounanflowers.admin.api;

import com.dounanflowers.admin.dto.FeedbackHandleDto;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.bo.FeedbackBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.dto.FeedbackCreateDto;
import com.dounanflowers.common.enums.ChannelEnum;
import com.dounanflowers.common.service.SystemService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/feedback")
@Tag(name = "后台管理/投诉建议")
@RequiredArgsConstructor
public class AdminFeedbackApi {

    private final SystemService systemService;

    @PostMapping("/create")
    @Operation(summary = "投诉建议提交")
    public FeedbackBo feedbackCreate(@RequestBody FeedbackCreateDto dto) {
        SessionInfo<Long, AdminUserBo> session = SecurityHolder.session();
        if (session != null) {
            dto.setUserId(session.getUserId());
        }
        return systemService.feedbackCreate(dto, ChannelEnum.SHOP);
    }

    @PostMapping("/handle")
    @Operation(summary = "投诉建议处理")
    @Authorized("admin")
    public FeedbackBo feedbackUpdate(@RequestBody FeedbackHandleDto dto) {
        return systemService.feedbackHandle(dto.getId(), dto.getRemark(), dto.getHandled());
    }

    @PostMapping("/page")
    @Operation(summary = "投诉建议分页列表")
    @Authorized("admin")
    public Page<FeedbackBo> feedbackPageList(@RequestBody PageRequest pageRequest) {
        return systemService.feedbackPageList(pageRequest);
    }

    @PostMapping("/get")
    @Operation(summary = "投诉建议详情")
    @Authorized({"admin", "shop"})
    public FeedbackBo feedbackGet(@RequestBody EditIdDto dto) {
        return systemService.feedbackGet(dto.getId());
    }

}
