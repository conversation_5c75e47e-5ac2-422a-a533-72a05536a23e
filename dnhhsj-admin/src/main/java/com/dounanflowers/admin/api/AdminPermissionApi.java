package com.dounanflowers.admin.api;

import com.dounanflowers.admin.bo.AdminPermissionBo;
import com.dounanflowers.admin.dto.AdminPermissionDto;
import com.dounanflowers.admin.service.AdminPermissionService;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/permission")
@Tag(name = "后台管理/权限管理")
@RequiredArgsConstructor
@Authorized("admin")
public class AdminPermissionApi {

    private final AdminPermissionService adminPermissionService;

    @PostMapping("/page")
    @Operation(summary = "分页查询权限列表")
    public Page<AdminPermissionBo> page(@RequestBody PageRequest request) {
        return adminPermissionService.pageList(request);
    }

    @PostMapping("/save")
    @Operation(summary = "保存权限")
    public void save(@RequestBody AdminPermissionDto dto) {
        adminPermissionService.save(dto);
    }

    @PostMapping("/get")
    @Operation(summary = "获取权限详情")
    public AdminPermissionBo detail(@RequestBody EditIdDto dto) {
        return adminPermissionService.detail(dto.getId());
    }

    @PostMapping("/delete")
    @Operation(summary = "删除权限")
    public void delete(@RequestBody EditIdDto dto) {
        adminPermissionService.delete(dto.getId());
    }
}
