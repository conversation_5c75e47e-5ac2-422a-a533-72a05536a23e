package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.ModalBannerBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.enums.ClientTypeEnum;
import com.dounanflowers.common.service.ModalBannerService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/admin/modalBanner")
@Tag(name = "后台管理/弹窗横幅管理")
@RequiredArgsConstructor
public class AdminModalBannerApi {

    private final ModalBannerService modalBannerService;

    @PostMapping("/page")
    @Operation(summary = "弹窗横幅分页列表")
    @Authorized("admin")
    public Page<ModalBannerBo> pageList(@RequestBody PageRequest pageRequest) {
        return modalBannerService.pageList(pageRequest);
    }

    @PostMapping("/save")
    @Operation(summary = "弹窗横幅添加或修改")
    @Authorized("admin")
    public ModalBannerBo createOrUpdate(@RequestBody ModalBannerBo dto) {
        return modalBannerService.save(dto);
    }

    @PostMapping("/delete")
    @Operation(summary = "弹窗横幅删除")
    @Authorized("admin")
    public Boolean deleteById(@RequestBody EditIdDto editIdDto) {
        return modalBannerService.deleteModalBanner(editIdDto.getId());
    }

    @PostMapping("/showList")
    @Operation(summary = "弹窗横幅列表")
    @Authorized("shop")
    public List<ModalBannerBo> modalBannerList() {
        return modalBannerService.getModalBannerShowList(ClientTypeEnum.SHOP);
    }
}
