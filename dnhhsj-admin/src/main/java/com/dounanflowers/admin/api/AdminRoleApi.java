package com.dounanflowers.admin.api;

import com.dounanflowers.admin.bo.AdminRoleBo;
import com.dounanflowers.admin.dto.AdminRoleDto;
import com.dounanflowers.admin.service.AdminRoleService;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/admin/role")
@Tag(name = "后台管理/角色管理")
@RequiredArgsConstructor
@Authorized("admin")
public class AdminRoleApi {

    private final AdminRoleService adminRoleService;

    @PostMapping("/page")
    @Operation(summary = "分页查询角色列表")
    public Page<AdminRoleBo> page(@RequestBody PageRequest request) {
        return adminRoleService.pageList(request);
    }

    @PostMapping("/save")
    @Operation(summary = "保存角色")
    public void save(@RequestBody AdminRoleDto dto) {
        adminRoleService.save(dto);
    }

    @PostMapping("/get")
    @Operation(summary = "获取角色详情")
    public AdminRoleBo detail(@RequestBody EditIdDto dto) {
        return adminRoleService.detail(dto.getId());
    }

    @PostMapping("/delete")
    @Operation(summary = "删除角色")
    public void delete(@RequestBody EditIdDto dto) {
        adminRoleService.delete(dto.getId());
    }

    @PostMapping("/permissions")
    @Operation(summary = "获取角色的权限列表")
    public List<String> getRolePermissions(@RequestBody EditIdDto dto) {
        return adminRoleService.getRolePermissions(dto.getId());
    }

}
