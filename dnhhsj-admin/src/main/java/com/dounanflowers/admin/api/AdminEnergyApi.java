package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.bo.EnergyCellBalanceBo;
import com.dounanflowers.common.bo.EnergyCellBo;
import com.dounanflowers.common.bo.EnergyRoomCheckLogBo;
import com.dounanflowers.common.bo.EnergyRoomPayLogBo;
import com.dounanflowers.common.dto.*;
import com.dounanflowers.common.service.EnergyService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.bo.WxPayParamsBo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/admin")
@Tag(name = "后台管理/水电费")
@RequiredArgsConstructor
public class AdminEnergyApi {
    private final EnergyService energyService;

    @PostMapping("/energyCell/pageList")
    @Operation(summary = "水电费店铺分页列表")
    @Authorized({ "shop"})
    public Page<EnergyCellBo> shopEnergyCellPageList(@RequestBody PageRequest pageRequest) {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        pageRequest.addFilter(new PageFilter().setField("adminUserId").setType("eq").setValue(userId));
        return energyService.energyCellPage(pageRequest);
    }

    @PostMapping("/energyCell/adminPageList")
    @Operation(summary = "水电费店铺分页列表")
    @Authorized({ "admin"})
    public Page<EnergyCellBo> energyCellPageList(@RequestBody PageRequest pageRequest) {
        return energyService.energyCellPage(pageRequest);
    }

    @PostMapping("/energyCell/add")
    @Operation(summary = "水电费店铺分页列表")
    @Authorized({ "admin","shop"})
    public EnergyCellBo createEnergyCell(@RequestBody EnergyCellBo dto) {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        dto.setAdminUserId(userId);
        return energyService.energyCellCreate(dto);
    }

    @PostMapping("/energyCell/find")
    @Operation(summary = "水电费店铺查询")
    @Authorized({ "admin","shop"})
    public List<EnergyCellBo> fetchFromWgw(@RequestBody EnergyCellFindDto dto) {
        return energyService.energyCellFind(dto);
    }

    @PostMapping("/energyCell/searchFromWgw")
    @Operation(summary = "水电费物管王店铺查询")
    @Authorized({ "admin"})
    public List<EnergyCellBo> fetchFromWgw(@RequestBody SearchKeywordDto dto) {
        return energyService.energyCellFindByKeyword(dto);
    }

    @PostMapping("/energyCell/balance")
    @Operation(summary = "查询水电费余额")
    @Authorized({ "admin","shop"})
    public EnergyCellBalanceBo queryCellEnergyBalance(@RequestBody EnergyCellBalanceDto dto) {
        return energyService.queryCellEnergyBalance(dto.getId());
    }

    @PostMapping("/energyCell/checkLog")
    @Operation(summary = "查询店铺结算扣费记录")
    @Authorized({ "admin","shop"})
    public List<EnergyRoomCheckLogBo> queryRoomCheckLog(@RequestBody EnergyRoomCheckLogDto dto) {
        return energyService.queryRoomCheckLog(dto);
    }

    @PostMapping("/energyCell/payLog")
    @Operation(summary = "查询店铺历史充值记录")
    @Authorized({ "admin","shop"})
    public List<EnergyRoomPayLogBo> queryRoomPayLog(@RequestBody EnergyRoomPayLogDto dto) {
        return energyService.queryRoomPayLog(dto);
    }

    @PostMapping("/energyCell/prepay")
    @Operation(summary = "水电费店铺预存")
    @Authorized({ "admin","shop"})
    public WxPayParamsBo energyCellPrepay(@RequestBody EnergyCellPrepayDto dto) {
        return energyService.energyCellPrepay(dto);
    }

    @PostMapping("/energyCell/delete")
    @Operation(summary = "水电费店铺删除")
    @Authorized({ "admin","shop"})
    public boolean energyCellDelete(@RequestBody EditIdDto dto) {
        energyService.energyCellDelete(dto.getId());
        return true;
    }

//    energyCell/sendVerifySms
    @PostMapping("/energyCell/sendVerifySms")
    @Operation(summary = "发送验证码")
    @Authorized({ "shop"})
    public void sendVerifySms(@RequestBody IdDto dto) {
        energyService.energyCellSendVerifySms(dto.getId(), dto.getFake() != null && dto.getFake());
    }

//    /energyCell/verifyMobile
    @PostMapping("/energyCell/verifyMobile")
    @Operation(summary = "验证手机号")
    @Authorized({ "shop"})
    public void verifyMobile(@RequestBody EnergyCellVerifyMobileDto dto) {
        energyService.energyCellVerifyMobile(dto.getId(), dto.getCode());
    }

    @PostMapping("/energyCell/saveBusinessLicense")
    @Operation(summary = "保存营业执照")
    @Authorized({ "shop" })
    public void saveBusinessLicense(@RequestBody EnergyCellSaveBusinessLicenseDto dto) {
        energyService.energyCellSaveBusinessLicense(dto.getId(), dto.getBusinessLicense());
    }
}
