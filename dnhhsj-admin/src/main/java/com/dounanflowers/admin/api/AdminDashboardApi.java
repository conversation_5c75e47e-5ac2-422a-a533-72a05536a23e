package com.dounanflowers.admin.api;

import com.dounanflowers.admin.bo.DashboardCountBo;
import com.dounanflowers.admin.bo.StatisticInfoBo;
import com.dounanflowers.admin.dto.DateRangeDto;
import com.dounanflowers.admin.service.AdminDashboardService;
import com.dounanflowers.bpm.dto.PlaySourceCountDto;
import com.dounanflowers.common.bo.ClientActionCountBo;
import com.dounanflowers.common.bo.LcParkCountBo;
import com.dounanflowers.common.service.ClientActionCountService;
import com.dounanflowers.security.annotation.Authorized;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/admin/dashboard")
@Tag(name = "后台管理/仪表盘")
@RequiredArgsConstructor
@Authorized("admin")
public class AdminDashboardApi {
    private final AdminDashboardService adminDashboardService;
    private final ClientActionCountService clientActionCountService;

    @PostMapping("/statisticInfo")
    @Operation(summary = "统计信息")
    public StatisticInfoBo statisticInfo() {
        return adminDashboardService.statisticInfo();
    }

    @PostMapping("/lcPark")
    @Operation(summary = "停车统计")
    public List<LcParkCountBo> lcParkCountList(@RequestBody DateRangeDto dto) {
        return adminDashboardService.lcParkCountList(dto);
    }

    @PostMapping("/clientUser")
    @Operation(summary = "用户统计")
    public List<DashboardCountBo> clientUser(@RequestBody DateRangeDto dto) {
        return adminDashboardService.clientUser(dto);
    }

    @PostMapping("/merchantUser")
    @Operation(summary = "用户统计")
    public List<DashboardCountBo> merchantUser(@RequestBody DateRangeDto dto) {
        return adminDashboardService.merchantUser(dto);
    }

    @PostMapping("/tricycle")
    @Operation(summary = "三轮车统计")
    public List<DashboardCountBo> tricycle(@RequestBody DateRangeDto dto) {
        return adminDashboardService.tricycle(dto);
    }

    @PostMapping("/playSource")
    @Operation(summary = "吃喝玩乐来源统计")
    public List<ClientActionCountBo> playSource(@RequestBody PlaySourceCountDto dto) {
        return clientActionCountService.playSource(dto);
    }


}
