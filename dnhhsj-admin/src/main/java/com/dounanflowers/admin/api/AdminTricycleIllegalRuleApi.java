package com.dounanflowers.admin.api;

import com.dounanflowers.admin.service.AdminTricycleIllegalService;
import com.dounanflowers.common.bo.TricycleIllegalRuleBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/admin/tricycle/illegal/rule")
@Tag(name = "后台管理/三轮车")
@RequiredArgsConstructor
@Authorized({"admin", "shop"})
public class AdminTricycleIllegalRuleApi {

    private final AdminTricycleIllegalService adminTricycleIllegalService;

    @PostMapping("/page")
    @Operation(summary = "三轮车违规规则分页列表")
    public Page<TricycleIllegalRuleBo> tricycleIllegalRulePageList(@RequestBody PageRequest pageRequest) {
        return adminTricycleIllegalService.pageIllegalRule(pageRequest);
    }

    @PostMapping("/delete")
    @Operation(summary = "三轮车违规规则删除")
    public Boolean tricycleIllegalRuleDeleteById(@RequestBody EditIdDto editIdDto) {
        return adminTricycleIllegalService.deleteIllegalRule(editIdDto.getId());
    }

    @PostMapping("/save")
    @Operation(summary = "三轮车违规规则新增或更新")
    public TricycleIllegalRuleBo tricycleIllegalRuleCreateOrUpdate(@RequestBody TricycleIllegalRuleBo tricycleIllegalRuleBo) {
        return adminTricycleIllegalService.saveIllegalRule(tricycleIllegalRuleBo);
    }

    @PostMapping("/list")
    @Operation(summary = "三轮车违规规则列表")
    public List<TricycleIllegalRuleBo> tricycleIllegalRuleList() {
        return adminTricycleIllegalService.listIllegalRule();
    }

}
