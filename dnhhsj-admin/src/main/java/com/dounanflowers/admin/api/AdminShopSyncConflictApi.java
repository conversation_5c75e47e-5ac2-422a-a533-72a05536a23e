package com.dounanflowers.admin.api;

import com.dounanflowers.admin.dto.ShopSyncConflictHandleDto;
import com.dounanflowers.common.bo.ShopSyncConflictBo;
import com.dounanflowers.common.service.ShopSyncConflictService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/shopSyncConflict")
@Tag(name = "后台管理/商铺管理")
@RequiredArgsConstructor
public class AdminShopSyncConflictApi {

    private final ShopSyncConflictService shopSyncConflictService;

    @PostMapping("/page")
    @Operation(summary = "商铺同步冲突分页列表")
    public Page<ShopSyncConflictBo> shopSyncConflictPageList(@RequestBody PageRequest pageRequest) {
        return shopSyncConflictService.pageList(pageRequest);
    }

    @PostMapping("/handle")
    @Operation(summary = "商铺同步冲突处理")
    public Boolean shopSyncConflictHandle(@RequestBody ShopSyncConflictHandleDto dto) {
        return shopSyncConflictService.handle(dto.getId(), dto.getRightValue());
    }
}
