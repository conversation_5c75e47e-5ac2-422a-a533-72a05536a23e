package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.FileBo;
import com.dounanflowers.common.bo.ShopBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.service.ShopService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/admin/shop")
@Tag(name = "后台管理/商铺管理")
@RequiredArgsConstructor
@Authorized({"admin", "shop"})
public class AdminShopApi {

    private final ShopService shopService;

    @PostMapping("/get")
    @Operation(summary = "商铺详情")
    public ShopBo shopGetById(@RequestBody EditIdDto editIdDto) {
        if ("shop".equals(SecurityHolder.session().getName())) {
            shopService.updateViewCount(editIdDto.getId());
        }
        return shopService.getById(editIdDto.getId());
    }

    @PostMapping("/page")
    @Operation(summary = "商铺分页列表")
    public Page<ShopBo> shopPageList(@RequestBody PageRequest pageRequest) {
        return shopService.shopPageList(pageRequest);
    }

    @PostMapping("/save")
    @Operation(summary = "商铺创建或修改")
    public ShopBo shopCreateOrUpdate(@RequestBody ShopBo shopBo) {
        shopBo.setEditedAt(LocalDateTime.now());
        return shopService.save(shopBo);
    }

    @PostMapping("/tags")
    @Operation(summary = "商铺所有标签")
    public List<String> shopAllTags() {
        return shopService.allTags();
    }


    @PostMapping("/rank")
    @Operation(summary = "商铺重建排名")
    public Boolean shopRank() {
        return shopService.rank();
    }

    @PostMapping("/shareImage")
    public FileBo generateShopShareImage(@RequestBody EditIdDto request) {
        return shopService.generateShopShareImage(request.getId());
    }

}
