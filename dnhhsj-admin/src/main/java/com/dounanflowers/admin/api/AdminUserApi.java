package com.dounanflowers.admin.api;

import com.dounanflowers.admin.dto.AdminUserBatchCreateDto;
import com.dounanflowers.admin.dto.AdminUserEnableChangeDto;
import com.dounanflowers.admin.dto.AdminUserUpdateDto;
import com.dounanflowers.admin.service.AdminUserService;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.bo.ClientUserBo;
import com.dounanflowers.common.bo.UserCredentialBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.entity.AdminUserRoleRel;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import com.mybatisflex.core.query.QueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/admin/user")
@Tag(name = "后台管理/商家用户")
@RequiredArgsConstructor
@Authorized({"admin", "shop"})
public class AdminUserApi {

    private final AdminUserService adminUserService;

    @PostMapping("/page/shop")
    @Operation(summary = "商家用户分页列表")
    public Page<AdminUserBo> shopUserPageList(@RequestBody PageRequest pageRequest) {
        List<String> roles = SecurityHolder.session().getRoles();
        if (roles.contains("1")) {
            PageFilter filter = new PageFilter();
            QueryWrapper subWrapper = QueryWrapper.create().select("user_id").eq(AdminUserRoleRel::getRole, "1").from(AdminUserRoleRel.class);
            filter.setField("id");
            filter.setType("sub");
            pageRequest.addFilter(filter);
            pageRequest.addSubQuery("id", w -> w.in("id", subWrapper));
        } else if (roles.contains("0")) {
            return Page.empty();
        }
        PageFilter filter = new PageFilter();
        filter.setType("custom");
        filter.setField("client");
        filter.setValue("shop");
        pageRequest.addFilter(filter);
        return adminUserService.pageList(pageRequest);
    }

    @PostMapping("/page/admin")
    @Operation(summary = "商家用户分页列表")
    public Page<AdminUserBo> adminUserPageList(@RequestBody PageRequest pageRequest) {
        PageFilter filter = new PageFilter();
        filter.setType("custom");
        filter.setField("client");
        filter.setValue("admin");
        pageRequest.addFilter(filter);

        return adminUserService.pageList(pageRequest);
    }

    @PostMapping("/page")
    @Operation(summary = "商家用户分页列表")
    public Page<AdminUserBo> userPageList(@RequestBody PageRequest pageRequest) {
        return adminUserService.pageList(pageRequest);
    }

    @PostMapping("/save")
    @Operation(summary = "管理员用户保存")
    public void userSave(@RequestBody AdminUserBo adminUserBo) {
        adminUserService.saveAdmin(adminUserBo);
    }

    @PostMapping("/update")
    @Operation(summary = "商家用户编辑")
    public AdminUserBo userUpdate(@RequestBody AdminUserUpdateDto dto) {
        return adminUserService.updateUserInfo(dto);
    }

    @PostMapping("/create/batch")
    @Operation(summary = "商家用户批量创建")
    public Boolean userBatchCreate(@RequestBody AdminUserBatchCreateDto dto) {
        return adminUserService.batchCreateUser(dto);
    }

    @PostMapping("/user/credential")
    @Operation(summary = "商家实名认证信息")
    public UserCredentialBo userRealInfoGet(@RequestBody EditIdDto dto) {
        return null;
    }

    @PostMapping("/delete")
    @Operation(summary = "商家用户删除")
    public Boolean userDeleteById(@RequestBody EditIdDto editIdDto) {
        return adminUserService.deleteUser(editIdDto.getId());
    }

    @PostMapping("/enable/change")
    @Operation(summary = "商家用户启用/禁用")
    public Boolean userEnableChange(@RequestBody AdminUserEnableChangeDto dto) {
        return adminUserService.changeUserEnabled(dto.getId(), dto.getIsEnabled());
    }

}
