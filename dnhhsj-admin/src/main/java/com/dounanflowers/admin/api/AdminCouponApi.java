package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.CouponBo;
import com.dounanflowers.common.service.CouponService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/coupon")
@Tag(name = "后台管理/优惠券")
@RequiredArgsConstructor
@Authorized("admin")
public class AdminCouponApi {

    private final CouponService couponService;

    @PostMapping("/page")
    @Operation(summary = "优惠券分页列表")
    public Page<CouponBo> couponPageList(@RequestBody PageRequest pageRequest) {
        return couponService.couponPageList(pageRequest);
    }

}
