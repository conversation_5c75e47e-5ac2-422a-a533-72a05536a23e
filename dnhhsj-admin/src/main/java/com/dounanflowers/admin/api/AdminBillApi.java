package com.dounanflowers.admin.api;

import com.dounanflowers.admin.dto.AdminBillRelatListDto;
import com.dounanflowers.common.bo.BillBo;
import com.dounanflowers.common.dto.BillRefundDto;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.dto.HandleStatusBo;
import com.dounanflowers.common.service.BillService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/admin")
@Tag(name = "后台管理/订单")
@RequiredArgsConstructor
@Authorized("admin")
public class AdminBillApi {

    private final BillService billService;

    @PostMapping("/billGetByEntityId")
    @Operation(summary = "订单详情")
    @Authorized({"admin"})
    public BillBo billGetByEntityId(@RequestBody EditIdDto dto) {
        return billService.getByEntityId(dto.getId());
    }

    @PostMapping("/billRelatList")
    @Operation(summary = "订单(关联)列表")
    @Authorized({"admin", "shop"})
    public List<BillBo> billRelatList(@RequestBody AdminBillRelatListDto dto) {
        return billService.relatList(dto.getType(), dto.getEntityId(), dto.getEntityModel());
    }

    @PostMapping("/billRefundById")
    @Operation(summary = "订单退款")
    public HandleStatusBo billRefundById(@RequestBody BillRefundDto dto) {
        billService.refund(dto);
        return HandleStatusBo.success();
    }

    @PostMapping("/billPageList")
    @Operation(summary = "订单分页列表")
    @Authorized({"admin", "shop"})
    public Page<BillBo> billPageList(@RequestBody PageRequest pageRequest) {
        if (Objects.equals(SecurityHolder.session().getName(), "shop")) {
            pageRequest.addFilter(new PageFilter().setField("userId").setType("eq").setValue(SecurityHolder.session().getUserId()));
        }
        return billService.pageList(pageRequest);
    }

}
