package com.dounanflowers.admin.api;

import com.dounanflowers.admin.bo.CouponBlankQrcodeBo;
import com.dounanflowers.common.bo.CouponTplBo;
import com.dounanflowers.admin.dto.CouponCreateByTplDto;
import com.dounanflowers.admin.service.AdminCouponService;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.dto.CouponCodeDto;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.entity.CouponTplUserR;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import com.mybatisflex.core.query.QueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/admin/coupon/tpl")
@Tag(name = "后台管理/优惠券")
@RequiredArgsConstructor
@Authorized("admin")
public class AdminCouponTplApi {

    private final AdminCouponService adminCouponService;

    @PostMapping("/get")
    @Operation(summary = "优惠券模板详情")
    @Authorized({"admin", "shop"})
    public CouponTplBo couponTplGetById(@RequestBody EditIdDto editIdDto) {
        return adminCouponService.getTplById(editIdDto.getId());
    }

    @PostMapping("/get/code")
    @Operation(summary = "优惠券模板详情")
    @Authorized({"admin", "shop"})
    public CouponTplBo couponTplGetByCode(@RequestBody CouponCodeDto editIdDto) {
        return adminCouponService.getTplByCode(editIdDto.getCode());
    }

    @PostMapping("/delete")
    @Operation(summary = "优惠券模板删除")
    public Boolean couponTplDeleteById(@RequestBody EditIdDto editIdDto) {
        return adminCouponService.deleteTplById(editIdDto.getId());
    }

    @PostMapping("/qrcode")
    @Operation(summary = "商家优惠券模板二维码", description = "根据返回的code生成二维码，生成可使用uqrcodejs")
    @Authorized({"admin", "shop"})
    public CouponBlankQrcodeBo couponTplQrCode(@RequestBody EditIdDto dto) {
        return adminCouponService.getTplQrCode(dto.getId());
    }

    @PostMapping("/save")
    @Operation(summary = "优惠券模板新增或更新")
    public CouponTplBo couponTplCreateOrUpdate(@RequestBody CouponTplBo dto) {
        return adminCouponService.saveTpl(dto);
    }

    @PostMapping("/page")
    @Operation(summary = "优惠券模板分页列表")
    @Authorized({"admin", "shop"})
    public Page<CouponTplBo> couponTplPageList(@RequestBody PageRequest pageRequest) {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        List<String> roles = SecurityHolder.session().getRoles();
        if(roles.isEmpty()) {
            return Page.empty();
        }
        if (!roles.contains("admin") && roles.contains("0")) {
            PageFilter filter = new PageFilter();
            QueryWrapper subWrapper = QueryWrapper.create().select("coupon_tpl_id").eq(CouponTplUserR::getUserId, userId).from(CouponTplUserR.class);
            filter.setField("id");
            filter.setType("sub");
            pageRequest.addFilter(filter);
            pageRequest.addSubQuery("id", w -> w.in("id", subWrapper));
        }
        return adminCouponService.pageTplList(pageRequest);
    }

    @PostMapping("/coupon/create")
    @Operation(summary = "优惠券发放给用户")
    public Boolean couponCreateByTpl(@RequestBody CouponCreateByTplDto dto) {
        return adminCouponService.createCouponByTpl(dto);
    }


}
