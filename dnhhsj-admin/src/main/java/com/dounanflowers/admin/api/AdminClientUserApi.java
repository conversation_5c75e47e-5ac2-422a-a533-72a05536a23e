package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.ClientUserBo;
import com.dounanflowers.common.bo.UserCredentialBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.service.ClientUserService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/client/user")
@Tag(name = "后台管理/用户管理")
@RequiredArgsConstructor
@Authorized("admin")
public class AdminClientUserApi {

    private final ClientUserService clientUserService;

    @PostMapping("/page")
    @Operation(summary = "用户分页列表")
    public Page<ClientUserBo> userPageList(@RequestBody PageRequest pageRequest) {
        return clientUserService.page(pageRequest);
    }

    @PostMapping("/batchSetBusinessType")
    @Operation(summary = "上传用户商家类型")
    public String userBatchSetBusinessType() {
        return null;
    }

    @PostMapping("/realInfoGet")
    @Operation(summary = "用户实名认证信息")
    public UserCredentialBo userRealInfoGet(@RequestBody EditIdDto dto) {
        return null;
    }

}
