package com.dounanflowers.admin.api;

import com.dounanflowers.common.dto.HandleStatusBo;
import com.dounanflowers.common.service.BillService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/pay")
@Tag(name = "支付对接")
@RequiredArgsConstructor
public class PayApi {

    private final BillService billService;

    @PostMapping("/notify")
    @Operation(summary = "通知接口")
    public HandleStatusBo handleNotify(@RequestParam Map<String, String> dto) {
        billService.handleNotify(dto);
        return HandleStatusBo.success();
    }
}
