package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.LcOrderBo;
import com.dounanflowers.common.bo.LcParkBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.service.LcService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin")
@Tag(name = "后台管理/停车")
@RequiredArgsConstructor
public class AdminLcApi {

    private final LcService lcService;

    @PostMapping("/lcParkPageList")
    @Operation(summary = "停车场库分页列表")
    public Page<LcParkBo> lcParkPageList(@RequestBody PageRequest pageRequest) {
        return lcService.lcParkPage(pageRequest);
    }

    @PostMapping("/lcOrderPageList")
    @Operation(summary = "停车订单分页列表")
    public Page<LcOrderBo> lcOrderPageList(@RequestBody PageRequest pageRequest) {
        return lcService.lcOrderPage(pageRequest);
    }

    @PostMapping("/lcOrderGetById")
    @Operation(summary = "停车订单详情")
    public LcOrderBo lcOrderGetById(@RequestBody EditIdDto editIdDto) {
        return lcService.lcOrderGetById(editIdDto.getId());
    }

}
