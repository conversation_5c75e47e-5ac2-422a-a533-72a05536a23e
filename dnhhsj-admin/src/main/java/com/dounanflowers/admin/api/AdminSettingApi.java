package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.FileBo;
import com.dounanflowers.common.dto.UpdateSettingDto;
import com.dounanflowers.common.service.FileService;
import com.dounanflowers.common.service.SystemService;
import com.dounanflowers.security.annotation.Authorized;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@RestController
@RequestMapping("/admin")
@Tag(name = "后台管理/设置")
@RequiredArgsConstructor
public class AdminSettingApi {

    private final SystemService systemService;

    private final FileService fileService;

    @PostMapping("/upload")
    @Operation(summary = "上传")
    public FileBo upload(@RequestPart MultipartFile file) {
        return fileService.createFile(file);
    }

    @PostMapping("/settingMap")
    @Operation(summary = "所有设置的Map")
    public Map<String, Map<String, Object>> settingMap() {
        return systemService.settingMap();
    }

    @PostMapping("/settingSave")
    @Operation(summary = "设置新增或更新")
    @Authorized({"admin", "shop"})
    public boolean settingCreateOrUpdate(@RequestBody UpdateSettingDto dto) {
        return systemService.updateSetting(dto);
    }

    @PostMapping("/systemVar")
    @Operation(summary = "获取系统变量")
    public Map<String, Object> systemVar() {
        return systemService.settingMap().getOrDefault("Merchant", Map.of());
    }

}
