package com.dounanflowers.admin.api;

import com.dounanflowers.admin.bo.BiTradingCountBo;
import com.dounanflowers.admin.dto.AdminBillRelatListDto;
import com.dounanflowers.admin.dto.BiPeopleReportDto;
import com.dounanflowers.admin.dto.BiTradingCountDto;
import com.dounanflowers.admin.dto.BiTradingSyncDto;
import com.dounanflowers.admin.service.AdminBiService;
import com.dounanflowers.framework.bean.Result;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.third.service.BiTradingService;
import com.mybatisflex.core.row.Row;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/bi")
@Tag(name = "后台管理/大屏")
@RequiredArgsConstructor
public class AdminBiApi {

    private final AdminBiService biService;

    private final BiTradingService biTradingService;

    @PostMapping("/flowerPrice")
    @Operation(summary = "大屏花束价格")
    @Authorized({"admin", "shop"})
    public List<Row> flowerPrice() {
        return biService.flowerPrice();
    }

    @PostMapping("/truckCount")
    @Operation(summary = "大屏卡车统计")
    @Authorized({"admin", "shop"})
    public List<Row> truckCount(@RequestBody AdminBillRelatListDto dto) {
        return biService.truckCount();
    }

    @PostMapping("/trading/token")
    @Operation(summary = "获取token")
    public Result<String> token() {
        return Result.success(biTradingService.token());
    }

    @PostMapping("/trading/latest/count")
    public BiTradingCountBo getLatestBiTradingCount(@RequestBody BiTradingCountDto dto) {
        return biService.getLatestBiTradingCount(dto);
    }

    @PostMapping("/trading/sync")
    public void syncOnlineTradingCountByDay(@RequestBody BiTradingSyncDto dto) {
        biService.syncOnlineTradingCountByDay(dto.getDate());
    }

    @PostMapping("/peopleCount/regionInfo")
    public Map<String, Object> biPeopleCountGetRegionInfo() {
        return biService.biPeopleCountGetRegionInfo();
    }

    @PostMapping("/peopleCount/realTimeReport")
    public List<Map<String, Object>> biPeopleCountRealTimeReport(@RequestBody BiPeopleReportDto dto) {
        return biService.biPeopleCountRealTimeReport(dto);
    }

    @PostMapping("/peopleCount/inAndOutReport")
    public Map<String, Object> biPeopleCountInAndOutReport(@RequestBody BiPeopleReportDto dto) {
        return biService.biPeopleCountInAndOutReport(dto);
    }

}
