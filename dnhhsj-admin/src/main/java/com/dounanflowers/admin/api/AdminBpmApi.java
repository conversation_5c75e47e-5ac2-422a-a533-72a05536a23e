package com.dounanflowers.admin.api;

import com.dounanflowers.admin.bo.BpmActionBo;
import com.dounanflowers.admin.bo.BpmInstanceBo;
import com.dounanflowers.admin.dto.AdminBpmGetDto;
import com.dounanflowers.admin.dto.AdminBpmHandleDto;
import com.dounanflowers.admin.service.AdminBpmService;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/admin/bpm")
@Tag(name = "后台管理/工作流")
@RequiredArgsConstructor
@Authorized({"admin", "shop"})
public class AdminBpmApi {

    private final AdminBpmService bpmService;

    @PostMapping("/page")
    @Operation(summary = "后台工作流分页列表")
    public Page<BpmInstanceBo> workflowPageList(@RequestBody PageRequest pageRequest) {
        return bpmService.pageList(pageRequest);
    }

    @PostMapping("/get")
    @Operation(summary = "工作流详情")
    public BpmInstanceBo workflowGetById(@RequestBody AdminBpmGetDto dto) {
        if (dto.getId() != null) {
            return bpmService.getInstanceById(dto.getId());
        } else if (dto.getNodeInstanceId() != null) {
            return bpmService.getInstanceByNodeInstanceId(dto.getNodeInstanceId());
        } else {
            return null;
        }
    }

    @PostMapping("/action")
    @Operation(summary = "工作流操作")
    public Object action(@RequestBody AdminBpmHandleDto dto) {
        return bpmService.doAction(dto);
    }

    @PostMapping("/instance/actions")
    public List<BpmActionBo> getInstanceActions(@RequestBody EditIdDto dto) {
        return bpmService.getInstanceActions(dto.getId());
    }

}
