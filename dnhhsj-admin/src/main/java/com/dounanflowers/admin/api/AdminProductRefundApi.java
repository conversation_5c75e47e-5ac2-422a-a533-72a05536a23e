package com.dounanflowers.admin.api;

import com.dounanflowers.admin.service.AdminProductRefundService;
import com.dounanflowers.common.bo.ProductRefundApplyBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.dto.ProductOrderForceRefundDto;
import com.dounanflowers.common.dto.ProductRefundHandleDto;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.bean.Result;
import com.dounanflowers.security.annotation.Authorized;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/product/refund")
@RequiredArgsConstructor
@Authorized({"admin", "shop"})
public class AdminProductRefundApi {

    private final AdminProductRefundService refundService;

    @PostMapping("/page")
    public Result<Page<ProductRefundApplyBo>> page(@RequestBody PageRequest dto) {
        return Result.success(refundService.pageList(dto));
    }

    @PostMapping("/get")
    public Result<ProductRefundApplyBo> getById(@RequestBody EditIdDto dto) {
        return Result.success(refundService.getById(dto.getId()));
    }

    @PostMapping("/handle")
    public Result<Void> handleRefund(@RequestBody ProductRefundHandleDto dto) {
        refundService.handleRefund(dto);
        return Result.success();
    }

    @PostMapping("/forceRefund")
    public Result<Void> forceRefund(@RequestBody ProductOrderForceRefundDto dto) {
        refundService.forceRefund(dto);
        return Result.success();
    }

}
