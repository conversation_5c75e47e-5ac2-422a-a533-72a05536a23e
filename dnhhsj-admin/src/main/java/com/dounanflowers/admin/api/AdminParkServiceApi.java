package com.dounanflowers.admin.api;

import com.dounanflowers.admin.bo.ParkServiceAppointmentBo;
import com.dounanflowers.admin.bo.ParkServiceBo;
import com.dounanflowers.admin.bo.ParkServicePageBo;
import com.dounanflowers.admin.service.ParkServiceAppointmentService;
import com.dounanflowers.admin.service.ParkServiceService;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.dto.ParkServiceAppointmentCreateDto;
import com.dounanflowers.common.dto.ParkServiceAppointmentStatusDto;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/job/service")
@Tag(name = "园区服务管理")
@RequiredArgsConstructor
public class AdminParkServiceApi {

    private final ParkServiceService parkServiceService;
    private final ParkServiceAppointmentService parkServiceAppointmentService;

    @PostMapping("/page")
    @Operation(summary = "园区服务分页列表")
    @Authorized({"admin", "shop"})
    public ParkServicePageBo pageList(@RequestBody PageRequest pageRequest) {
        return parkServiceService.pageList(pageRequest);
    }

    @PostMapping("/get")
    @Operation(summary = "获取园区服务详情")
    @Authorized({"admin", "shop"})
    public ParkServiceBo get(@RequestBody EditIdDto dto) {
        return parkServiceService.get(dto.getId());
    }

    @PostMapping("/create")
    @Operation(summary = "创建园区服务")
    @Authorized("admin")
    public ParkServiceBo create(@RequestBody ParkServiceBo dto) {
        return parkServiceService.create(dto);
    }

    @PostMapping("/update")
    @Operation(summary = "更新园区服务")
    @Authorized("admin")
    public ParkServiceBo update(@RequestBody ParkServiceBo dto) {
        return parkServiceService.update(dto);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除园区服务")
    @Authorized("admin")
    public void delete(@RequestBody EditIdDto dto) {
        parkServiceService.delete(dto.getId());
    }

    @PostMapping("/appointment/page")
    @Operation(summary = "园区服务预约分页列表")
    @Authorized({"admin", "shop"})
    public Page<ParkServiceAppointmentBo> appointmentPageList(@RequestBody PageRequest pageRequest) {
        return parkServiceAppointmentService.pageList(pageRequest);
    }

    @PostMapping("/appointment/get")
    @Operation(summary = "获取预约详情")
    @Authorized({"admin", "shop"})
    public ParkServiceAppointmentBo getAppointment(@RequestBody EditIdDto dto) {
        return parkServiceAppointmentService.get(dto.getId());
    }

    @PostMapping("/appointment/create")
    @Operation(summary = "预约园区服务")
    @Authorized({"admin", "shop"})
    public ParkServiceAppointmentBo createAppointment(@RequestBody ParkServiceAppointmentCreateDto dto) {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        return parkServiceAppointmentService.create(userId, dto);
    }

    @PostMapping("/appointment/status")
    @Operation(summary = "更新预约状态")
    @Authorized({"admin", "shop"})
    public void updateAppointmentStatus(@RequestBody ParkServiceAppointmentStatusDto dto) {
        parkServiceAppointmentService.updateStatus(dto);
    }
}
