package com.dounanflowers.admin.api;

import com.dounanflowers.admin.service.AdminProductService;
import com.dounanflowers.common.bo.CheckOrderPageBo;
import com.dounanflowers.common.bo.ClientUserBo;
import com.dounanflowers.common.bo.ProductOrderBo;
import com.dounanflowers.common.dto.CheckOrderPageDto;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Tag(name = "商品订单管理")
@RestController
@RequestMapping("/admin/product/order")
@RequiredArgsConstructor
@Authorized("admin")
public class AdminProductOrderApi {

    private final AdminProductService adminProductService;

    @PostMapping("/page")
    @Operation(summary = "订单分页列表")
    public Page<ProductOrderBo> pageList(@RequestBody PageRequest dto) {
        return adminProductService.orderPageList(dto);
    }

    @PostMapping("/get")
    @Operation(summary = "订单详情")
    public ProductOrderBo detail(@RequestBody EditIdDto dto) {
        return adminProductService.getOrderDetail(dto.getId());
    }

    @PostMapping("/completed")
    @Operation(summary = "标记订单已完成")
    public void markAsCompleted(@RequestBody EditIdDto dto) {
        adminProductService.markOrderAsCompleted(dto.getId());
    }

    @PostMapping("/cancel/{id}")
    @Operation(summary = "取消订单")
    public void cancel(@RequestBody EditIdDto dto) {
        adminProductService.cancelOrder(dto.getId());
    }

    @PostMapping("/check/{code}")
    @Operation(summary = "核销订单")
    @Authorized({"admin", "shop"})
    public void check(@PathVariable("code") String code) {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        adminProductService.checkOrder(code, userId);
    }

    @PostMapping("/detail/{code}")
    @Operation(summary = "订单详情")
    @Authorized({"admin", "shop"})
    public ProductOrderBo detail(@PathVariable("code") String code) {
        return adminProductService.getOrderDetailByCode(code);
    }

    @PostMapping("/check/page")
    @Operation(summary = "核销订单分页")
    @Authorized({"admin", "shop"})
    public CheckOrderPageBo check(@RequestBody CheckOrderPageDto dto) {
        return adminProductService.checkOrderPageList(dto);
    }

}
