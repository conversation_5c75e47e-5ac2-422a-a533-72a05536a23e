package com.dounanflowers.admin.api;

import com.dounanflowers.admin.dto.WxLoginDto;
import com.dounanflowers.admin.service.AdminWxService;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.bo.ClientUserBo;
import com.dounanflowers.common.dto.CodeLoginDto;
import com.dounanflowers.framework.annotation.RepeatSubmit;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.bean.SessionInfo;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/wx")
@Tag(name = "商家小程序/登录")
@RequiredArgsConstructor
public class AdminWxApi {

    private final AdminWxService adminWxService;

    @PostMapping("/login")
    @RepeatSubmit
    @Operation(summary = "微信自动登录", description = "用户打开小程序执行一次\n如果没找到openId绑定的用户，返回{token:null,userInfo:null}")
    public SessionInfo<Long, AdminUserBo> login(@RequestBody WxLoginDto dto) {
        return adminWxService.login(dto.getCode(), dto.getPhoneNumberCode());
    }

    @PostMapping("/openId")
    @Operation(summary = "记录openId")
    @Authorized("shop")
    public SessionInfo<Long, AdminUserBo> openId(@RequestBody WxLoginDto dto) {
        return adminWxService.openId(dto.getCode());
    }

    @PostMapping("/loginByMobile")
    @Operation(summary = "单独通过手机号登录")
    public SessionInfo<Long, AdminUserBo> loginByMobile(@RequestBody WxLoginDto dto) {
        return adminWxService.loginByMobile(dto.getPhoneNumberCode());
    }

    @PostMapping("/mobile")
    @Operation(summary = "微信绑定手机号")
    @Authorized("shop")
    public SessionInfo<Long, AdminUserBo> updatePhone(@RequestBody WxLoginDto dto) {
        return adminWxService.updatePhone(dto.getPhoneNumberCode());
    }

    @PostMapping("/merchant/loginByMobileCode")
    @Operation(summary = "手机验证码登录(开发用)")
    public SessionInfo<Long, AdminUserBo> loginByMobileCode(@RequestBody CodeLoginDto codeLoginDto) {
        return null;
    }

    @PostMapping("/logout")
    @Operation(summary = "退出登录")
    @Authorized("shop")
    public void logout() {
        Long userId = SecurityHolder.<Long, ClientUserBo>session().getUserId();
        adminWxService.logout(userId);
        SecurityHolder.logout();
    }

    @PostMapping("/openid")
    @Operation(summary = "绑定微信")
    @Authorized("shop")
    public SessionInfo<Long, AdminUserBo> bindWx(@RequestBody WxLoginDto dto) {
        adminWxService.bindWx(dto.getCode());
        return SecurityHolder.session();
    }

}
