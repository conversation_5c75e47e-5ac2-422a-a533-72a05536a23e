package com.dounanflowers.admin.api;

import com.dounanflowers.admin.bo.VehicleLcWhitelistBo;
import com.dounanflowers.admin.dto.*;
import com.dounanflowers.admin.service.AdminVehicleService;
import com.dounanflowers.common.bo.BillBo;
import com.dounanflowers.common.bo.VehicleBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.dto.VehiclePlateChangeCheckDto;
import com.dounanflowers.common.dto.VehicleRefundCheckDto;
import com.dounanflowers.common.entity.Bill;
import com.dounanflowers.common.enums.VehicleCheckStatusEnum;
import com.dounanflowers.common.service.LcService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import com.dounanflowers.third.ThirdPartyHolder;
import com.dounanflowers.third.bo.ParkWhiteBo;
import com.dounanflowers.third.bo.WxPayParamsBo;
import com.dounanflowers.third.dto.ParkQueryWhiteDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/vehicle")
@Tag(name = "后台管理/机动车")
@RequiredArgsConstructor
@Authorized({"admin", "shop"})
public class AdminVehicleApi {

    private final AdminVehicleService adminVehicleService;
    private final LcService lcService;

    @PostMapping("/page")
    @Operation(summary = "机动车分页列表")
    @Authorized({"admin"})
    public Page<VehicleBo> vehiclePageList(@RequestBody PageRequest pageRequest) {
        return adminVehicleService.pageList(pageRequest);
    }

    @PostMapping("/page/my")
    @Operation(summary = "机动车分页列表")
    @Authorized({"shop"})
    public Page<VehicleBo> myVehiclePageList(@RequestBody PageRequest pageRequest) {
        pageRequest.addFilter(new PageFilter().setField("userId").setType("eq").setValue(SecurityHolder.session().getUserId()));
        return adminVehicleService.pageList(pageRequest);
    }

    @PostMapping("/by/user/list")
    @Operation(summary = "用户机动车列表")
    @Authorized({"admin"})
    public List<VehicleBo> vehicleListByUser(@RequestBody EditIdDto dto) {
        return adminVehicleService.vehicleListByUser(dto.getId());
    }

    @PostMapping("/check")
    @Operation(summary = "机动车审核")
    @Authorized({"admin"})
    public Boolean vehicleCheck(@RequestBody VehicleCheckDto dto) {
        return adminVehicleService.checkVehicle(dto);
    }

    @PostMapping("/check/agree")
    @Operation(summary = "机动车审核")
    @Authorized({"admin"})
    public Boolean vehicleCheckAgree(@RequestBody VehicleCheckDto dto) {
        dto.setCheckStatus(VehicleCheckStatusEnum.APPROVED.ordinal() + "");
        dto.setForce(true);
        return adminVehicleService.checkVehicle(dto);
    }

    @PostMapping("/entry")
    @Operation(summary = "机动车审核")
    @Authorized({"admin"})
    public Boolean vehicleEntryAgree(@RequestBody VehicleCheckDto dto) {
        return adminVehicleService.entryVehicle(dto);
    }

    @PostMapping("/buy")
    @Operation(summary = "购买月卡")
    @Authorized({"shop"})
    public WxPayParamsBo vehicleBuy(@RequestBody FakePayDto dto) {
        return adminVehicleService.vehicleBuy(dto);
    }

    @PostMapping("/check/cancel")
    @Operation(summary = "机动车取消审核")
    @Authorized({"shop"})
    public Boolean vehicleCheckCancel(@RequestBody EditIdDto dto) {
        return adminVehicleService.cancelCheckVehicle(dto.getId());
    }

    @PostMapping("/get")
    @Operation(summary = "机动车详情")
    @Authorized({"shop", "admin"})
    public VehicleBo vehicleGetById(@RequestBody EditIdDto editIdDto) {
        return adminVehicleService.getById(editIdDto.getId());
    }

    @PostMapping("/apply")
    @Operation(summary = "机动车审核申请")
    @Authorized({"shop"})
    public VehicleBo vehicleCheckApply(@RequestBody VehicleApplyDto dto) {
        dto.getImages().forEach(image -> {
            if(image == null || image.isEmpty()) {
                throw new BaseException("请上传图片");
            }
        });
        // Assert.notEmpty(dto.getImages(), "请上传图片");
        if (dto.getId() == null) {
            return adminVehicleService.applyVehicle(dto);
        } else {
            return adminVehicleService.applyVehicleAgain(dto);
        }
    }

    @PostMapping("/lcWhitelist")
    @Operation(summary = "机动车蓝卡白名单")
    @Authorized({"admin"})
    public VehicleLcWhitelistBo vehicleLcWhitelist(@RequestBody LcWhitelistDto dto) {

        ParkWhiteBo parkWhiteBo = lcService.whitelistQuery(dto.getPlate().trim());
        VehicleBo vehicleBo = adminVehicleService.getEffectiveVehicleByPlate(dto.getPlate().trim());
        VehicleLcWhitelistBo vehicleLcWhitelistBo = new VehicleLcWhitelistBo();
        vehicleLcWhitelistBo.setLcWhite(parkWhiteBo);
        vehicleLcWhitelistBo.setVehicle(vehicleBo);

        return vehicleLcWhitelistBo;

    }

    @PostMapping("/plateChangeApply")
    @Operation(summary = "修改车牌")
    @Authorized({"shop"})
    public WxPayParamsBo vehiclePlateChange(@RequestBody VehiclePlateChangeApplyDto dto) {
        return adminVehicleService.vehiclePlateChangeApply(dto.getVehicleId(), dto.getNewPlate().trim(), dto.getIsFake() != null && dto.getIsFake());
    }

    @PostMapping("/plateChangeCheck")
    @Operation(summary = "修改车牌")
    @Authorized({"admin"})
    public Boolean vehiclePlateChangeCheck(@RequestBody VehiclePlateChangeCheckDto dto) {
        return adminVehicleService.vehiclePlateChangeCheck(dto);
    }

    @PostMapping("/plateChangeCancel")
    @Operation(summary = "取消修改车牌")
    @Authorized({"shop"})
    public Boolean vehiclePlateCancel(@RequestBody EditIdDto dto) {
        return adminVehicleService.vehiclePlateChangeCancel(dto.getId());
    }

    @PostMapping("/plateChangePay")
    @Operation(summary = "支付换牌费")
    @Authorized({"shop"})
    public WxPayParamsBo vehiclePlatePay(@RequestBody EditIdDto dto) {
        return adminVehicleService.vehiclePlateChangePay(dto.getId());
    }

    @PostMapping("/refundApply")
    @Operation(summary = "退月卡申请")
    @Authorized({"shop"})
    public Boolean vehicleRefundApply(@RequestBody EditIdDto dto) {
        return adminVehicleService.vehicleRefundApply(dto.getId());
    }

    @PostMapping("/refundCheck")
    @Operation(summary = "退月卡审核")
    @Authorized({"admin"})
    public Boolean vehicleRefundCheck(@RequestBody VehicleRefundCheckDto dto) {
        return adminVehicleService.vehicleRefundCheck(dto);
    }

    @PostMapping("/monthlyCardBill")
    @Operation(summary = "月卡订单查询")
    @Authorized({"admin"})
    public BillBo vehicleMonthlyCardBill(@RequestBody EditIdDto dto) {
        Bill bill = adminVehicleService.vehicleMonthlyCardBill(dto.getId());
        return BeanUtils.copy(bill, BillBo.class);
    }

    @PostMapping("/samePlateHiddenList")
    @Operation(summary = "相同根ID的隐藏机动车")
    @Authorized({"admin"})
    public List<VehicleBo> vehicleSamePlateHiddenList(@RequestBody EditIdDto dto) {
        return adminVehicleService.sameRootHiddenVehicles(dto.getId());
    }

}
