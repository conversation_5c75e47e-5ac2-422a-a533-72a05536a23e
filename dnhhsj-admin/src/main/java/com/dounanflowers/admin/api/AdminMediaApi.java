package com.dounanflowers.admin.api;

import com.dounanflowers.admin.dto.MediaUpdateDto;
import com.dounanflowers.common.bo.MediaBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.service.FileService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/media")
@Tag(name = "后台管理/媒体管理")
@RequiredArgsConstructor
public class AdminMediaApi {

    private final FileService fileService;

    @PostMapping("/page")
    @Operation(summary = "媒体分页列表")
    public Page<MediaBo> mediaPageList(@RequestBody PageRequest pageRequest) {
        return fileService.mediaPageList(pageRequest);
    }

    @PostMapping("/update")
    @Operation(summary = "媒体信息更新")
    @Deprecated // 应该不需要更新信息了
    public MediaBo mediaUpdate(@RequestBody MediaUpdateDto dto) {
        return null;
    }

    @PostMapping("/delete")
    @Operation(summary = "媒体删除")
    public Boolean mediaDeleteById(@RequestBody EditIdDto editIdDto) {
        fileService.deleteById(editIdDto.getId());
        return true;
    }

}
