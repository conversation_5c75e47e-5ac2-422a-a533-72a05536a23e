package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.DictBo;
import com.dounanflowers.common.bo.DictOptionBo;
import com.dounanflowers.common.dto.DictGetDto;
import com.dounanflowers.common.dto.DictMapGetDto;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.service.SystemService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/dict")
@Tag(name = "后台管理/字典管理")
@RequiredArgsConstructor
@Authorized("admin")
public class AdminDictApi {

    private final SystemService systemService;

    @PostMapping("/page")
    @Operation(summary = "字典分页列表")
    public Page<DictBo> dictPageList(@RequestBody PageRequest pageRequest) {
        return systemService.dictPageList(pageRequest);
    }

    @PostMapping("/save")
    @Operation(summary = "字典创建或更新")
    public DictBo dictCreateOrUpdate(@RequestBody DictBo dictBo) {
        return systemService.saveDict(dictBo);
    }

    @PostMapping("/get")
    @Operation(summary = "获取字典选项", description = "根据name或key获取字典options")
    @Authorized({"admin", "shop"})
    public List<DictOptionBo> dictGet(@RequestBody DictGetDto dto) {
        if (StringUtils.isNotBlank(dto.getKey())) {
            return systemService.getDictOptions(dto.getKey());
        } else {
            return systemService.getDictOptionsByName(dto.getName());
        }
    }

    @PostMapping("/map")
    @Operation(summary = "批量获取字典选项", description = "根据key数组，或tags数组，获取字典Map对象  \nMap对象的key是字典的key，value是字典的options")
    @Authorized({"admin", "shop"})
    public Map<String, DictBo> dictGetMap(@RequestBody DictMapGetDto dto) {
        return systemService.getDictMap(dto);
    }

    @PostMapping("/tags")
    @Operation(summary = "字典所有标签", description = "获取字典表内字典的所有标签，用作筛选、设置标签的选项")
    public List<String> dictAllTags() {
        return systemService.dictAllTags();
    }

    @PostMapping("/delete")
    @Operation(summary = "删除字典")
    public Boolean dictDeleteById(@RequestBody EditIdDto editIdDto) {
        return systemService.deleteDictById(editIdDto.getId());
    }

}
