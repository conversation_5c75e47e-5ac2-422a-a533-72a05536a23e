package com.dounanflowers.admin.api;

import com.dounanflowers.admin.service.AdminStoreService;
import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.bo.StoreBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.dto.EditWithPasswordDto;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "后台管理/店铺管理")
@RestController
@RequestMapping("/admin/store")
@RequiredArgsConstructor
public class AdminStoreApi {

    private final AdminStoreService adminStoreService;

    @PostMapping("/create")
    @Operation(summary = "创建店铺")
    @Authorized("admin")
    public StoreBo create(@RequestBody StoreBo dto) {
        return adminStoreService.create(dto);
    }

    @PostMapping("/update")
    @Operation(summary = "更新店铺")
    @Authorized({"admin", "shop"})
    public StoreBo update(@RequestBody StoreBo dto) {
        return adminStoreService.update(dto);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除店铺")
    @Authorized("admin")
    public Boolean delete(@RequestBody EditWithPasswordDto dto) {
        if ("dnhhsj@123".equals(dto.getPassword())) {
            throw new BaseException("密码不正确");
        }
        adminStoreService.delete(dto.getId());
        return true;
    }

    @PostMapping("/get")
    @Operation(summary = "获取店铺")
    @Authorized({"admin", "shop"})
    public StoreBo get(@RequestBody EditIdDto dto) {
        return adminStoreService.get(dto.getId());
    }

    @PostMapping("/page")
    @Operation(summary = "分页查询店铺")
    @Authorized({"admin", "shop"})
    public Page<StoreBo> page(@RequestBody PageRequest pageRequest) {
        return adminStoreService.page(pageRequest);
    }

    @PostMapping("/getByShopId")
    @Operation(summary = "根据商铺ID获取店铺")
    @Authorized({"admin", "shop"})
    public StoreBo getByShopId(@RequestBody EditIdDto dto) {
        return adminStoreService.getByShopId(dto.getId());
    }

    @PostMapping("/updateStatus")
    @Operation(summary = "更新店铺状态")
    @Authorized({"admin", "shop"})
    public Boolean updateStatus(@RequestBody EditIdDto idDto, @RequestBody IsEnum status) {
        adminStoreService.updateStatus(idDto.getId(), status);
        return true;
    }

    @PostMapping("/my/list")
    @Operation(summary = "本人能看到的商铺")
    @Authorized({"admin", "shop"})
    public List<StoreBo> myStoreList() {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        return adminStoreService.storeListByUserId(userId);
    }
}
