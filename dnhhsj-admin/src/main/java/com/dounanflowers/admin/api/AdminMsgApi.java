package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.MsgCategoryBo;
import com.dounanflowers.common.bo.MsgDetailBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.service.MsgService;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/admin/msg")
@Tag(name = "后台管理/消息")
@RequiredArgsConstructor
public class AdminMsgApi {

    private final MsgService msgService;

    @PostMapping("/category/list")
    @Operation(summary = "获取所有消息分类")
    @Authorized({"admin", "shop"})
    public List<MsgCategoryBo> listAllCategories() {
        return msgService.listAllCategories();
    }

    @PostMapping("/page")
    @Operation(summary = "消息分页列表")
    @Authorized({"admin", "shop"})
    public Page<MsgDetailBo> messageList(@RequestBody PageRequest pageRequest) {
        pageRequest.addFilter(new PageFilter().setField("receiveUserId").setType("eq").setValue(SecurityHolder.session().getUserId()));
        return msgService.pageList(pageRequest);
    }

    @PostMapping("/read")
    @Operation(summary = "消息已读")
    @Authorized({"admin", "shop"})
    public void read(@RequestBody EditIdDto dto) {
        msgService.read(dto.getId());
    }

    @PostMapping("/retry")
    @Operation(summary = "消息重发")
    public void retry(@RequestBody EditIdDto dto) {
        msgService.retry(dto.getId());
    }

}
