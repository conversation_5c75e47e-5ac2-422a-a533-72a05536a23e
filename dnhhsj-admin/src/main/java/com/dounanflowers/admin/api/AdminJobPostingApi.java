package com.dounanflowers.admin.api;

import com.dounanflowers.common.bo.AdminUserBo;
import com.dounanflowers.common.bo.FavouriteBo;
import com.dounanflowers.common.bo.JobPostingBo;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.common.dto.JobPostingAuditDto;
import com.dounanflowers.admin.service.JobPostingService;
import com.dounanflowers.common.dto.JobPostingRecommendDto;
import com.dounanflowers.common.enums.JobPostingStatusEnum;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageFilter;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import com.dounanflowers.security.utils.SecurityHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin/job")
@Tag(name = "用工发布管理")
@RequiredArgsConstructor
public class AdminJobPostingApi {

    private final JobPostingService jobPostingService;

    @PostMapping("/page")
    @Operation(summary = "用工发布分页列表(管理员)")
    @Authorized({"admin", "shop"}) //TODO
    public Page<JobPostingBo> pageList(@RequestBody PageRequest pageRequest) {
        return jobPostingService.pageList(pageRequest);
    }

    @PostMapping("/shop/page")
    @Operation(summary = "用工发布分页列表(商铺)")
    @Authorized({"shop"})
    public Page<JobPostingBo> shopPageList(@RequestBody PageRequest pageRequest) {
        pageRequest.addFilter(new PageFilter().setField("status").setType("eq").setValue(JobPostingStatusEnum.APPROVED));
        return jobPostingService.pageList(pageRequest);
    }

    @PostMapping("/my/page")
    @Operation(summary = "我的用工发布分页列表")
    @Authorized({"admin", "shop"})
    public Page<JobPostingBo> myPageList(@RequestBody PageRequest pageRequest) {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        pageRequest.addFilter(new PageFilter().setField("userId").setType("eq").setValue(userId));
        return jobPostingService.singlePageList(pageRequest);
    }

    @PostMapping("/create")
    @Operation(summary = "创建用工发布")
    @Authorized({"admin", "shop"})
    public JobPostingBo create(@RequestBody JobPostingBo dto) {
        return jobPostingService.create(dto);
    }

    @PostMapping("/update")
    @Operation(summary = "更新用工发布")
    @Authorized({"admin", "shop"})
    public JobPostingBo update(@RequestBody JobPostingBo dto) {
        return jobPostingService.update(dto);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除用工发布")
    @Authorized({"admin", "shop"})
    public void delete(@RequestBody EditIdDto dto) {
        jobPostingService.delete(dto.getId());
    }

    @PostMapping("/audit")
    @Operation(summary = "审核用工发布")
    @Authorized("admin")
    public void audit(@RequestBody JobPostingAuditDto dto) {
        jobPostingService.audit(dto);
    }

    @PostMapping("/recommend")
    @Operation(summary = "用工置顶")
    @Authorized("admin")
    public JobPostingBo audit(@RequestBody JobPostingRecommendDto dto) {
        return jobPostingService.recommend(dto);
    }

    @PostMapping("/toggle/show")
    @Operation(summary = "显示/隐藏用工发布")
    @Authorized({"admin", "shop"})
    public void toggleShow(@RequestBody EditIdDto dto) {
        jobPostingService.toggleShow(dto.getId());
    }


//    jobFavouritePage
    @PostMapping("/favourite/page")
    @Operation(summary = "用工被收藏的列表")
    @Authorized({"shop"})
    public Page<FavouriteBo> jobFavouritePage(@RequestBody PageRequest pageRequest) {
        return jobPostingService.jobFavouritePage(pageRequest);
    }

}
