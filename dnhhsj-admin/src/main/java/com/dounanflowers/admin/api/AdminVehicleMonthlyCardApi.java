package com.dounanflowers.admin.api;

import com.dounanflowers.admin.service.AdminVehicleMonthlyCardService;
import com.dounanflowers.common.bo.VehicleMonthlyCardBo;
import com.dounanflowers.common.bo.VehicleMonthlyCardOrderBo;
import com.dounanflowers.common.dto.BuyMonthlyCardDTO;
import com.dounanflowers.common.dto.CreateMonthlyCardDTO;
import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.dounanflowers.security.annotation.Authorized;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@RestController
@RequestMapping("/admin/vehicle/monthly")
@Tag(name = "后台管理/车辆月卡")
@RequiredArgsConstructor
public class AdminVehicleMonthlyCardApi {

    private final AdminVehicleMonthlyCardService monthlyCardService;

    @PostMapping("/save")
    @Operation(summary = "创建月卡")
    @Authorized("admin")
    public void createMonthlyCard(@RequestBody CreateMonthlyCardDTO dto) {
        monthlyCardService.saveMonthlyCard(dto);
    }

    @PostMapping("/page")
    @Operation(summary = "月卡列表")
    @Authorized({"admin", "shop"})
    public Page<VehicleMonthlyCardBo> pageMonthlyCards(@RequestBody PageRequest pageRequest) {
        return monthlyCardService.pageMonthlyCards(pageRequest);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除月卡")
    @Authorized("admin")
    public void deleteMonthlyCard(@RequestBody EditIdDto dto) {
        monthlyCardService.deleteMonthlyCard(dto.getId());
    }

    @PostMapping("/get")
    @Operation(summary = "月卡详情")
    @Authorized({"admin", "shop"})
    public VehicleMonthlyCardBo getMonthlyCard(@RequestBody EditIdDto dto) {
        return monthlyCardService.getMonthlyCard(dto.getId());
    }

    @PostMapping("/order/page")
    @Operation(summary = "月卡订单列表")
    @Authorized("admin")
    public Page<VehicleMonthlyCardOrderBo> pageMonthlyCardOrders(@RequestBody PageRequest pageRequest) {
        return monthlyCardService.pageMonthlyCardOrders(pageRequest);
    }

    @PostMapping("/calc")
    @Operation(summary = "计算月卡价格")
    @Authorized({"admin", "shop"})
    public BigDecimal calculatePrice(@RequestBody BuyMonthlyCardDTO dto) {
        return monthlyCardService.calculatePrice(dto.getMonthlyCardId(), dto.getCouponId());
    }

}
