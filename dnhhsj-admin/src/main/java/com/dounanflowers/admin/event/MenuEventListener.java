package com.dounanflowers.admin.event;

import com.dounanflowers.admin.websocket.WebSocketServer;
import com.dounanflowers.common.event.AdminMenuEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


@Component
public class MenuEventListener {

    @Async
    @EventListener
    public void handleMyEventAsync(AdminMenuEvent event) {
        WebSocketServer.broadcastAdminMenuMessage(event.getMessage());
    }
}
