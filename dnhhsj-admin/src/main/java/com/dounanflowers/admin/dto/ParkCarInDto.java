package com.dounanflowers.admin.dto;

import com.dounanflowers.admin.bo.ParkOrderModifyBo;
import com.dounanflowers.admin.bo.ParkPlaceInfoBo;
import com.dounanflowers.admin.bo.ParkUserInfoBo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ParkCarInDto {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer size;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private List<ParkCarInDataDto> datas;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private String parkNumber;

    @Data
    public static class ParkCarInDataDto {

        @Schema(title = "入场记录编号")
        private String orderId;

        @Schema(title = "车牌", description = "无牌车取''")
        private String plate;

        @Schema(title = "无牌车票号", description = "牌车入场时从出票机取票的票号,唯一标识,有牌车时此字段填''")
        private String ticketCode;

        @Schema(title = "车牌颜色")
        private String plateColor;

        @Schema(title = "入场时间")
        private String inTime;

        @Schema(title = "入场通道名称")
        private String inChannel;

        @Schema(title = "入场图片名")
        private String inImage;

        @Schema(title = "访问事由")
        private String visitReason;

        @Schema(title = "放行类型", description = "自动抬杆/手动放行,无信息时填“”")
        private String openGateMode;

        @Schema(title = "匹配模式", description = "全字匹配、近似匹配、人工匹配、人工纠正,无信息时填“”")
        private String matchMode;

        @Schema(title = "证件号码")
        private String idCard;

        @Schema(title = "车牌识别可信度")
        private Integer confidence;

        @Schema(title = "车类型", description = "临时车/固定车/预约车")
        private String carType;

        private ParkUserInfoBo userInfo;

        private List<ParkPlaceInfoBo> placeInfo;

        @Schema(title = "是否开闸", description = "“开闸”“未开闸”")
        private String barriorOpen;

        @Schema(title = "开闸耗时", description = "压地感到抬杆时间")
        private String costTime;

        @Schema(title = "空位数")
        private String spaceCount;

        @Schema(title = "其他联动设备图片信息")
        private List<String> imageList;

        @Schema(title = "图片名")
        private String ImageName;

        @Schema(title = "修改车牌信息", description = "车牌修改信息，默认无此项")
        private ParkOrderModifyBo modifyMemo;

    }


}

