package com.dounanflowers.admin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ShopSyncConflictHandleDto {

    @JsonProperty("_id")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(title = "正确的值", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rightValue;

}

