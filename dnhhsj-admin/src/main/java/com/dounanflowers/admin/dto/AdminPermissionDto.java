package com.dounanflowers.admin.dto;

import com.dounanflowers.common.enums.PermissionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "权限DTO")
public class AdminPermissionDto {

    @Schema(description = "权限ID")
    private Long id;

    @NotBlank(message = "权限名称不能为空")
    @Schema(description = "权限名称")
    private String name;

    @NotBlank(message = "权限编码不能为空")
    @Schema(description = "权限编码")
    private String code;

    @Schema(description = "权限描述")
    private String description;

    @NotNull(message = "权限类型不能为空")
    @Schema(description = "权限类型")
    private PermissionTypeEnum type;
}
