package com.dounanflowers.admin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class VehicleApplyDto {

    @Schema(title = "图片", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> images;

    @Schema(title = "机动车Id", description = "创建不需要，重新审核需要")
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "车主姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String realname;

    @Schema(title = "月卡id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long monthlyCardId;

    @Schema(title = "优惠券id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long couponId;

    private String numCode;

    private String remark;
}

