package com.dounanflowers.admin.dto;

import com.dounanflowers.admin.bo.ParkPassageWayBo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ParkPassageWayUpsertDto {

    @Schema(title = "停车场编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String parkNumber;

    @Schema(title = "datas数据条数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer size;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private List<ParkPassageWayBo> datas;

}

