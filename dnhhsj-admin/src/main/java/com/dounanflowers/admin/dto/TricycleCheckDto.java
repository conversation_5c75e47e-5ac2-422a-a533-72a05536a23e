package com.dounanflowers.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class TricycleCheckDto {

    @Schema(title = "三轮车ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tricycleId;

    @Schema(title = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private String checkStatus;

    @Schema(title = "审核备注", requiredMode = Schema.RequiredMode.REQUIRED)
    private String checkRemark;

    @Schema(title = "拒绝理由", requiredMode = Schema.RequiredMode.REQUIRED)
    private String checkRejectReason;

    @Schema(hidden = true)
    private boolean force;

}

