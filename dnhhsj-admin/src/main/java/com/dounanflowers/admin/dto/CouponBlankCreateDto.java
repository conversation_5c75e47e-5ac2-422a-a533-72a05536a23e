package com.dounanflowers.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CouponBlankCreateDto {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private Long couponTplId;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer count;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

}

