package com.dounanflowers.admin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class FeedbackHandleDto {

    @Schema(title = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    private String remark;

    @Schema(title = "是否处理", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean handled;

}

