package com.dounanflowers.admin.dto;

import com.dounanflowers.common.dto.EditIdDto;
import com.dounanflowers.framework.enums.IsEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class StoreDto extends EditIdDto {

    @Schema(description = "商铺编号")
    private String cellNo;

    @Schema(description = "店铺名称")
    private String name;

    @Schema(description = "店铺状态")
    private IsEnum status;

    @Schema(description = "店铺地址")
    private String address;

    @Schema(description = "店铺坐标")
    private String location;

    @Schema(description = "管理员用户ID")
    private Long adminUserId;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "联系电话")
    private String mobile;

    private List<String> images;

    @Schema(description = "标签")
    private List<String> tags;

}
