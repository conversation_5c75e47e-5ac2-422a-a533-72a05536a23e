package com.dounanflowers.admin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MediaUpdateDto {

    @JsonProperty("_id")
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    @Schema(title = "占位文字")
    private String alt;

    @Schema(title = "描述")
    private String desc;

    @Schema(title = "byte大小")
    private String size;

    @Schema(title = "宽度")
    private String width;

    @Schema(title = "高度")
    private String height;

    @Schema(title = "视频时长")
    private String duration;

}

