package com.dounanflowers.admin.dto;

import com.dounanflowers.common.enums.PermissionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "角色DTO")
public class AdminRoleDto {

    @Schema(description = "角色ID")
    private Long id;

    @NotBlank(message = "角色名称不能为空")
    @Schema(description = "角色名称")
    private String name;

    @NotBlank(message = "角色编码不能为空")
    @Schema(description = "角色编码")
    private String code;

    @NotBlank(message = "角色首页不能为空")
    @Schema(description = "角色首页")
    private String home;

    @Schema(description = "角色描述")
    private String description;

    @NotNull(message = "角色类型不能为空")
    @Schema(description = "角色类型")
    private PermissionTypeEnum type;

    @Schema(description = "权限编码列表")
    private List<String> permissions;
}
