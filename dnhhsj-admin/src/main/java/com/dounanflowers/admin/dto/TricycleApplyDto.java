package com.dounanflowers.admin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class TricycleApplyDto {
    @Schema(title = "图片", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> images;

    @Schema(title = "三轮车ID", description = "创建不需要，重新审核需要")
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "车主姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String realname;

    private String mobile;

    private String numCode;

    private String remark;

}

