package com.dounanflowers.admin.dto;

import com.dounanflowers.admin.bo.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ParkCarOutDto {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer size;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private List<ParkCarOutDataDto> datas;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private String parkNumber;


    @Data
    public static class ParkCarOutDataDto {

        private String orderId;

        @Schema(title = "操作员Id")
        private String operatorId;

        @Schema(title = "操作员姓名")
        private String operatorName;

        @Schema(title = "发票号码")
        private String invoiceNo;

        private ParkCarInfoBo carInfo;

        @Schema(title = "车主信息")
        private ParkUserInfoBo userInfo;

        @Schema(title = "通行信息")
        private ParkPassInfoBo passInfo;

        @Schema(title = "收费统计信息")
        private ParkOrderChargeStatisticsBo chargeStatistics;

        @Schema(title = "优惠明细")
        private List<ParkOrderProfitBo> profitList;

        private List<ParkPlaceInfoBo> placeInfo;

        @Schema(title = "开闸耗时")
        private String costTime;

        @Schema(title = "空位数")
        private String spaceCount;

        @Schema(title = "收费明细")
        private List<ParkOrderChargeBo> chargeList;

    }

}

