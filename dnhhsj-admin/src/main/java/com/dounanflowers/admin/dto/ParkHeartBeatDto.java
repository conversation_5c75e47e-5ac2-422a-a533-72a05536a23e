package com.dounanflowers.admin.dto;

import com.dounanflowers.admin.bo.ParkAreaBo;
import com.dounanflowers.admin.bo.ParkDeviceBo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ParkHeartBeatDto {

    @Schema(title = "场库名称")
    private String parkName;

    @Schema(title = "场库编号", description = "每个场库的唯一编号(蓝卡云分配)")
    private String parkNumber;

    @Schema(title = "区域属性")
    private List<ParkAreaBo> areaList;

    @Schema(title = "设备信息")
    private List<ParkDeviceBo> deviceList;

    @Schema(title = "状态")
    private Integer state;

    @Schema(title = "场库在场预约数")
    private Integer bookInParkCount;

    @Schema(title = "场库总车位数")
    private Integer spaceCount;

    @Schema(title = "场库可预约数")
    private Integer bookSpaceCount;

    @Schema(title = "场库空车位数")
    private Integer freeSpaceCount;

}

//{
//        "areaList": [
//            {
//            "areaId": 1,
//            "areaName": "外层停车场",
//            "spaceCount": 800,
//            "bookSpaceCount": 0,
//            "lastSpaceCount": 391,
//            "bookInParkCount": 0
//            }
//        ],
//        "parkName": "云南昆明-斗南花花世界wpay",
//        "parkNumber": "p180930175706",
//        "spaceCount": 800,
//        "bookSpaceCount": 0,
//        "freeSpaceCount": 391,
//        "bookInParkCount": 0
//        }