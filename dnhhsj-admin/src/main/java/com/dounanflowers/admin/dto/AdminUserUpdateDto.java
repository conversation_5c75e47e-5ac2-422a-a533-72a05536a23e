package com.dounanflowers.admin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class AdminUserUpdateDto {

    @Schema(title = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("_id")
    private Long id;

    @Schema(title = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mobile;

    @Schema(title = "角色", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> roles;

}

