package com.dounanflowers.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class TricycleIllegalRecordDto {

    @Schema(title = "三轮车ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tricycleId;

    @Schema(title = "违规规则ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long ruleId;

    @Schema(title = "图片", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> images;

}

