package com.dounanflowers.third.service;

import com.dounanflowers.framework.annotation.Cached;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.DateUtils;
import com.dounanflowers.framework.utils.HttpUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.third.bo.TradingData;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Map;
import java.util.Objects;

@Service
public class BiTradingService {

    @Cached(value = "biTradingToken", expire = 60 * 60 * 24)
    public String token() {
        Map<String, Object> json = Map.of("auth", Map.of("account", "dnhh_screen", "passwd", "dnhh@123456"));

        HttpUtils.HttpResult response = HttpUtils.post("https://dc.dounanyun.com/api/screen/auth/login")
                .header("server", "1")
                .header("Content-Type", "application/json")
                .body(JsonUtils.toJson(json))
                .send();

        Map<String, Object> map = JsonUtils.toMap(new String(response.body()));

        if (!Objects.equals(map.get("code"), 1)) {
            throw new BaseException((String) map.get("msg"));
        }

        return ((Map<String, String>) map.get("data")).get("token");
    }

    public TradingData getTradingDataByDate(LocalDate start, LocalDate end) {
        String url = "https://dc.dounanyun.com/api/screen/bydate?limit=1&date=" + DateUtils.formatLocalDate(start, "yyyyMMdd") + "," + DateUtils.formatLocalDate(end, "yyyyMMdd");

        HttpUtils.HttpResult response = HttpUtils.get(url)
                .header("server", "1")
                .header("ba-user-token", token())
                .send();

        String body = new String(response.body());
        return JsonUtils.toObject(body, TradingData.class);
    }

}
