package com.dounanflowers.third.service;

import com.aliyun.teaopenapi.models.OpenApiRequest;
import com.aliyun.teaopenapi.models.Params;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.third.ThirdPartyHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class AliyunOpenapiService {

    public void mobile3MetaSimpleVerify(String identifyNum, String userName, String mobile) {
        try {
            Params params = new Params()
                    // 接口名称
                    .setAction("Mobile3MetaSimpleVerify")
                    // 接口版本
                    .setVersion("2019-03-07")
                    // 接口协议
                    .setProtocol("HTTPS")
                    // 接口 HTTP 方法
                    .setMethod("POST")
                    .setAuthType("AK")
                    .setStyle("RPC")
                    // 接口 PATH
                    .setPathname("/")
                    // 接口请求体内容格式
                    .setReqBodyType("json")
                    // 接口响应体内容格式
                    .setBodyType("json");
            // runtime options
            RuntimeOptions runtime = new RuntimeOptions();
            java.util.Map<String, Object> queries = new HashMap<>();
            queries.put("ParamType", "normal");
            queries.put("IdentifyNum", identifyNum);
            queries.put("UserName", userName);
            queries.put("Mobile", mobile);
            OpenApiRequest request = new OpenApiRequest().setQuery(com.aliyun.openapiutil.Client.query(queries));
            Map<String, ?> stringMap = ThirdPartyHolder.createAliOpenapi().callApi(params, request, runtime);
            log.info(JsonUtils.toJson(stringMap));
        } catch (Exception e) {
            throw new BaseException("阿里云OpenAPI调用失败");
        }
    }

    public void credentialVerify(String realName, String identifyNum, String identifyUrl) {
        try {
            com.aliyun.teaopenapi.models.Params params = new com.aliyun.teaopenapi.models.Params()
                    // 接口名称
                    .setAction("CredentialVerify")
                    // 接口版本
                    .setVersion("2019-03-07")
                    // 接口协议
                    .setProtocol("HTTPS")
                    // 接口 HTTP 方法
                    .setMethod("POST")
                    .setAuthType("AK")
                    .setStyle("RPC")
                    // 接口 PATH
                    .setPathname("/")
                    // 接口请求体内容格式
                    .setReqBodyType("formData")
                    // 接口响应体内容格式
                    .setBodyType("json");
            // query params
            java.util.Map<String, Object> queries = new java.util.HashMap<>();
            queries.put("CredType", "01");
            queries.put("CredName", "0101");
            queries.put("ImageUrl", identifyUrl);
            queries.put("UserName", realName);
            queries.put("IdentifyNum", identifyNum);
            // runtime options
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            OpenApiRequest request = new OpenApiRequest().setQuery(com.aliyun.openapiutil.Client.query(queries));
            // 复制代码运行请自行打印 API 的返回值
            // 返回值实际为 Map 类型，可从 Map 中获得三类数据：响应体 body、响应头 headers、HTTP 返回的状态码 statusCode。
            Map<String, ?> stringMap = ThirdPartyHolder.createAliOpenapi().callApi(params, request, runtime);
            log.info(JsonUtils.toJson(stringMap));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BaseException("阿里云OpenAPI调用失败");
        }
    }

}
