package com.dounanflowers.third.service;

import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.HttpUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.third.bo.RefundResultBo;
import com.dounanflowers.third.bo.WxPayParamsBo;
import com.dounanflowers.third.dto.PayParamsDto;
import com.dounanflowers.third.dto.RefundParamsDto;
import com.dounanflowers.third.entity.Allinpay;
import lombok.extern.slf4j.Slf4j;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class AllinpayService {

    static {
        java.security.Security.addProvider(
                new org.bouncycastle.jce.provider.BouncyCastleProvider()
        );
    }

    private final PrivateKey privateKey;

    private final PublicKey publicKey;

    private final String appid;

    private final String cusid;

    private final String subAppid;

    private final String notifyUrl;

    public AllinpayService(Allinpay config, String subAppid) {
        this.privateKey = loadPrivateKeyFromString(config.getKeyData());
        this.publicKey = loadPublicKeyFromString(config.getCertData());
        this.appid = config.getAppid();
        this.cusid = config.getCusid();
        this.subAppid = subAppid;
        this.notifyUrl = config.getNotifyUrl();
    }

    private PrivateKey loadPrivateKeyFromString(String keyString) {
        try {
            keyString = keyString.replace("-----BEGIN RSA PRIVATE KEY-----", "")
                    .replace("-----END RSA PRIVATE KEY-----", "")
                    .replaceAll("\\s+", "");
            return KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(Base64.getDecoder().decode(keyString)));
        } catch (NoSuchAlgorithmException e) {
            throw new UnsupportedOperationException(e);
        } catch (InvalidKeySpecException e) {
            throw new IllegalArgumentException(e);
        }
    }

    private PublicKey loadPublicKeyFromString(String keyString) {
        try {
            keyString = keyString.replace("-----BEGIN PUBLIC KEY-----", "")
                    .replace("-----END PUBLIC KEY-----", "")
                    .replaceAll("\\s+", "");
            return KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(Base64.getDecoder().decode(keyString)));
        } catch (NoSuchAlgorithmException e) {
            throw new UnsupportedOperationException(e);
        } catch (InvalidKeySpecException e) {
            throw new IllegalArgumentException(e);
        }
    }

    private String toUrlParams(Map<String, String> params) {
        return params.keySet().stream().sorted().map(key -> key + "=" + params.get(key)).collect(Collectors.joining("&"));
    }

    private String sign(String paramsStr) {
        try {
            // 创建一个Signature实例
            Signature signature = Signature.getInstance("SHA1withRSA");
            // 初始化Signature对象
            signature.initSign(privateKey);
            // 更新要签名的数据
            signature.update(paramsStr.getBytes());
            // 生成签名
            byte[] signatureBytes = signature.sign();
            // 将签名转换为Base64编码的字符串
            return URLEncoder.encode(Base64.getEncoder().encodeToString(signatureBytes), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("签名失败", e);
            return null;
        }

    }

    public Boolean verify(Map<String, String> params) {
        try {
            String sign = params.get("sign");
            params.remove("sign");
            String paramsStr = toUrlParams(params);
            // 创建一个Signature实例
            Signature signature = Signature.getInstance("SHA1withRSA");
            // 初始化Signature对象
            signature.initVerify(publicKey);
            // 更新要签名的数据
            signature.update(paramsStr.getBytes());
            // 验证签名
            return signature.verify(Base64.getDecoder().decode(sign));
        } catch (Exception e) {
            log.error("验签失败", e);
            return false;
        }
    }

    private Map<String, String> request(String url, Map<String, String> fullParams) {
        String fullParamsStr = toUrlParams(fullParams);
        System.out.println("请求原字符串" + fullParamsStr);
        String signStr = sign(fullParamsStr);
        System.out.println("请求签名" + signStr);
        String urlParams = fullParamsStr + "&sign=" + signStr;
        System.out.println("请求字符串" + urlParams);

        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
        HttpUtils.HttpResult response = HttpUtils.post(url).header(header).body(urlParams).send();
        String jsonText = new String(response.body());
        System.out.println("响应字符串" + jsonText);
        Map<String, String> map = JsonUtils.toObject(jsonText, Map.class);
        if (!Objects.equals(map.get("retcode"), "SUCCESS")) {
            throw new BaseException(map.get("retmsg"));
        }

        Boolean check = verify(map);

        if (!check) {
            throw new BaseException("响应验签失败");
        }

        if (!Objects.equals(map.get("trxstatus"), "0000")) {
            throw new BaseException(map.get("errmsg"));
        }
        System.out.println(map);
        return map;
    }

    public WxPayParamsBo pay(PayParamsDto params) {
        Map<String, String> fullParams = new HashMap<>();
        fullParams.put("cusid", cusid);
        fullParams.put("appid", appid);
        fullParams.put("version", "11"); // 版本号
        fullParams.put("paytype", "W06"); // 支付类型，微信小程序
        fullParams.put("randomstr", UUID.randomUUID().toString()); // 随机字符串
        fullParams.put("sub_appid", subAppid);
        fullParams.put("signtype", "RSA"); // 签名类型

        fullParams.put("trxamt", String.valueOf(params.getTrxamt()));
        fullParams.put("reqsn", params.getUnireqsn());
        fullParams.put("acct", params.getAcct());

        fullParams.put("notify_url", notifyUrl);
        fullParams.put("body", params.getBody());
        fullParams.put("remark", params.getRemark());
        if (params.getValidtime() != null) {
            fullParams.put("validtime", params.getValidtime());
        }

        System.out.println("支付");
        Map<String, String> map = request("https://vsp.allinpay.com/apiweb/unitorder/pay", fullParams);
        return JsonUtils.toObject(map.get("payinfo"), WxPayParamsBo.class);
    }

    public RefundResultBo refund(RefundParamsDto params) {
        Map<String, String> fullParams = new HashMap<>();
        fullParams.put("cusid", cusid);
        fullParams.put("appid", appid);
        fullParams.put("version", "11"); // 版本号
        fullParams.put("randomstr", UUID.randomUUID().toString()); // 随机字符串
        fullParams.put("signtype", "RSA"); // 签名类型

        fullParams.put("trxamt", String.valueOf(params.getTrxamt()));
        fullParams.put("reqsn", params.getReqsn());
        fullParams.put("oldreqsn", params.getOldreqsn());
        fullParams.put("oldtrxid", params.getOldtrxid());

        System.out.println("退款");
        Map<String, String> map = request("https://vsp.allinpay.com/apiweb/tranx/refund", fullParams);

        return BeanUtils.copy(map, RefundResultBo.class);
    }


}
