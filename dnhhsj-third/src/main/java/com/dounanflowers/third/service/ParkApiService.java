package com.dounanflowers.third.service;

import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.BeanUtils;
import com.dounanflowers.framework.utils.HttpUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.security.utils.MD5Utils;
import com.dounanflowers.third.bo.ParkQueryImgByNameResBo;
import com.dounanflowers.third.bo.ParkWhiteBo;
import com.dounanflowers.third.dto.*;
import com.dounanflowers.third.entity.ParkApi;

import java.util.*;
import java.util.stream.Collectors;

public class ParkApiService {
    private final String parkApiKey;
    private final String parkApiPrefix;
    private final String defaultParkNumber = "p180930175706";

    public ParkApiService(ParkApi config) {
        this.parkApiKey = config.getApiKey();
        this.parkApiPrefix = config.getApiPrefix();
    }

    public Object fetchParkApiBase(String url, String parkNumber, String json) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("sign", MD5Utils.encrypt(parkApiKey + parkNumber));
        HttpUtils.HttpResult response = HttpUtils.post(parkApiPrefix + url).header(headers).body(json).send();
        String jsonText = new String(response.body());
        Map<String, Object> map = JsonUtils.toMap(jsonText);
        if (!Objects.equals(map.get("status"), "success")) {
            throw new BaseException((String) map.get("errorCode"));
        }
        return map.get("datas");

    }


    public <T> T fetchParkApi(String url, String parkNumber, String json, Class<T> targetClass) {
        Object obj = fetchParkApiBase(url, parkNumber, json);
        return BeanUtils.copy(obj, targetClass);
    }

    public <T> List<T> fetchListParkApi(String url, String parkNumber, String json, Class<T> targetClass) {
        Object obj = fetchParkApiBase(url, parkNumber, json);
        return JsonUtils.toList(JsonUtils.toJson(obj), targetClass);
    }

    public ParkChargebfResDto fetchChargebf(ParkChargebfReqDto dto) {
        return fetchParkApi("getChargebf", dto.getParkNumber(), JsonUtils.toJson(dto), ParkChargebfResDto.class);
    }

    public void fetchCouponSend(ParkCouponSendReqDto dto) {
        fetchParkApi("coupon", dto.getParkNumber(), JsonUtils.toJson(dto), ParkCouponSendResDto.class);
    }

    public void fetchPayResultSend(ParkPayResultReqDto dto) {
        fetchParkApi("sendPayRes", dto.getParkNumber(), JsonUtils.toJson(dto), Object.class);
    }

    public void fetchSynWhite(ParkSyncWhiteDto dto) {
        fetchParkApi("synWhite", dto.getParkNumber(), JsonUtils.toJson(dto), Object.class);
    }

    public List<ParkWhiteBo> fetchSynWhiteQuery(ParkQueryWhiteDto dto) {
        return fetchListParkApi("queryWhite", dto.getParkNumber(), JsonUtils.toJson(dto), ParkWhiteBo.class);
    }

    public Map<String, String> getImageUrlMap(List<String> imageList) {
        if (imageList == null || imageList.isEmpty()) {
            return Collections.emptyMap();
        }
        ParkQueryImgByNameReqDto dto = new ParkQueryImgByNameReqDto();
        dto.setParkNumber(defaultParkNumber);
        dto.setDatas(imageList);
        Object res = fetchParkApiBase("queryImgByName", dto.getParkNumber(), JsonUtils.toJson(dto));
        ParkQueryImgByNameResBo object = JsonUtils.toObject(JsonUtils.toJson(res), ParkQueryImgByNameResBo.class);
        return object.getImageList().stream().collect(Collectors.toMap(ParkQueryImgByNameResBo.ImageListItem::getImageName, v -> {
            String imageURL = v.getImageURL();
            String[] split = imageURL.split(";");
            return split[0];
        }));
    }

}
