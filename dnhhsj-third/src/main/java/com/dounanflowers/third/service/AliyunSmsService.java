package com.dounanflowers.third.service;
import com.aliyun.ocr_api20210707.models.RecognizeBusinessLicenseResponse;
import com.aliyun.tea.*;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.third.bo.OcrBusinessLicenseBo;
import com.dounanflowers.third.entity.AliyunSms;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AliyunSmsService {

    private final AliyunSms aliyunSms;

    public AliyunSmsService(AliyunSms aliyunSms) {
        this.aliyunSms = aliyunSms;
    }

    public com.aliyun.dysmsapi20170525.Client createSmsClient() {

        try {
            com.aliyun.credentials.models.Config credentialConfig = new com.aliyun.credentials.models.Config();
            credentialConfig.setType("access_key");
            credentialConfig.setAccessKeyId(aliyunSms.getAccessKey());
            credentialConfig.setAccessKeySecret(aliyunSms.getAccessSecret());
            // 工程代码建议使用更安全的无AK方式，凭据配置方式请参见：https://help.aliyun.com/document_detail/378657.html。
            com.aliyun.credentials.Client credential = new com.aliyun.credentials.Client(credentialConfig);
            com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                    .setCredential(credential);
            // Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
            config.endpoint = "dysmsapi.aliyuncs.com";
            return new com.aliyun.dysmsapi20170525.Client(config);
        } catch (Exception e) {
            log.error("短信初始化失败", e);
            throw new BaseException("短信发送失败");
        }

    }

    /**
     * 发送验证码
     * @param mobile
     * @param code
     */
    public void sendVerifyCode(String mobile, String code) {

        com.aliyun.dysmsapi20170525.Client client = createSmsClient();
        com.aliyun.dysmsapi20170525.models.SendSmsRequest sendSmsRequest = new com.aliyun.dysmsapi20170525.models.SendSmsRequest()
                .setSignName("斗南花卉")
                .setTemplateCode("SMS_220025159")
                .setPhoneNumbers(mobile)
                .setTemplateParam("{\"code\":\""+code+"\"}");
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            client.sendSmsWithOptions(sendSmsRequest, runtime);
        } catch (TeaException error) {
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);


        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            log.error("短信发送失败", error);
            throw new BaseException("短信发送失败");
        }
    }

    public com.aliyun.ocr_api20210707.Client createOcrClient() {
        try {
            com.aliyun.credentials.models.Config credentialConfig = new com.aliyun.credentials.models.Config();
            credentialConfig.setType("access_key");
            credentialConfig.setAccessKeyId(aliyunSms.getAccessKey());
            credentialConfig.setAccessKeySecret(aliyunSms.getAccessSecret());
            // 工程代码建议使用更安全的无AK方式，凭据配置方式请参见：https://help.aliyun.com/document_detail/378657.html。
            com.aliyun.credentials.Client credential = new com.aliyun.credentials.Client(credentialConfig);
            com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                    .setCredential(credential);
            // Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
            config.endpoint = "ocr-api.cn-hangzhou.aliyuncs.com";
            return new com.aliyun.ocr_api20210707.Client(config);
        } catch (Exception e) {
            log.error("短信初始化失败", e);
            throw new BaseException("短信发送失败");
        }
    }

    /*
     * 识别营业执照
     */
     public OcrBusinessLicenseBo ocrBusinessLicense(String imageUrl) {
         com.aliyun.ocr_api20210707.Client client = createOcrClient();
         com.aliyun.ocr_api20210707.models.RecognizeBusinessLicenseRequest recognizeBusinessLicenseRequest = new com.aliyun.ocr_api20210707.models.RecognizeBusinessLicenseRequest();
         com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
         recognizeBusinessLicenseRequest.setUrl(imageUrl);
         try {
             // 复制代码运行请自行打印 API 的返回值
             RecognizeBusinessLicenseResponse res = client.recognizeBusinessLicenseWithOptions(recognizeBusinessLicenseRequest, runtime);
             String data = res.getBody().getData();
             System.out.println(data);
             return JsonUtils.toObject(data, OcrBusinessLicenseBo.class);
         } catch (TeaException error) {
             // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
             // 错误 message
             System.out.println(error.getMessage());
             // 诊断地址
             System.out.println(error.getData().get("Recommend"));
             com.aliyun.teautil.Common.assertAsString(error.message);
             log.error("识别营业执照失败", error);
             throw new BaseException("识别营业执照失败");
         } catch (Exception _error) {
             TeaException error = new TeaException(_error.getMessage(), _error);
             // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
             // 错误 message
             System.out.println(error.getMessage());
             // 诊断地址
             System.out.println(error.getData().get("Recommend"));
             com.aliyun.teautil.Common.assertAsString(error.message);
             log.error("识别营业执照失败", error);
             throw new BaseException("识别营业执照失败");
         }
     }
}
