package com.dounanflowers.third.service;

import com.aliyun.credentials.utils.StringUtils;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.HttpUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.third.bo.*;
import org.bouncycastle.util.Strings;
import org.yaml.snakeyaml.util.UriEncoder;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.mybatisflex.core.audit.http.HashUtil.md5;

/**
 *
 *
 * <AUTHOR>
 * @version [版本号, 2022-11-15]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
public class WuguanwangService {
    /**
     * 查询住户
     * @param mobile
     * @param idCardNum
     */
    public WgwUserBo getUser(String mobile, String idCardNum) {
        String url = "http://36.138.249.45:8401/wxcell?value=" + idCardNum + "&type=login";
        HttpUtils.HttpResult response = HttpUtils.get(url).send();
        String body = new String(response.body());
        System.out.println(body);
        if(StringUtils.isEmpty(body)) {
            throw new BaseException("住户不存在");
        }
        List<WgwUserBo> users = JsonUtils.toList(body, WgwUserBo.class);

        WgwUserBo user = users.getFirst();
        if(user == null) {
            throw new BaseException("住户不存在");
        }
        if(!Objects.equals(mobile, user.getF_MainTel())) {
            throw new BaseException("手机号不匹配");
        }
        return user;
    }

    /**
     * 查询店铺
     * @param mobile
     */
    public List<WgwCellBo> getCell(String mobile, String idCardNum) {
        String url = "";
        if(!StringUtils.isEmpty(mobile)) {
            url = "http://36.138.249.45:8401/wxcell?value=" + mobile;
        }
        if(!StringUtils.isEmpty(idCardNum)) {
            url = "http://36.138.249.45:8401/wxcell?value=" + idCardNum;
        }
        if(StringUtils.isEmpty(url)) {
            throw new BaseException("手机号或身份证号至少一个");
        }

        HttpUtils.HttpResult response = HttpUtils.get(url).send();
        String body = new String(response.body());
        System.out.println(body);
        if(StringUtils.isEmpty(body)) {
            throw new BaseException("店铺不存在");
        }
        List<WgwCellBo> cells = JsonUtils.toList(body, WgwCellBo.class);
        return cells;
    }
    /**
     * 查询店铺
     */
    public List<WgwCellBo> searchCell(String keyword) {
        String url = "http://36.138.249.45:3000/dnhhsj/cell-find?keyword=" + UriEncoder.encode(keyword);

        HttpUtils.HttpResult response = HttpUtils.get(url).send();
        String body = new String(response.body());
        System.out.println(body);
        if(StringUtils.isEmpty(body)) {
            throw new BaseException("店铺不存在");
        }
        List<WgwCellBo> cells = JsonUtils.toList(body, WgwCellBo.class);
        return cells;
    }



    /**
     * 预充值水电费
     * @param companyCode
     * @param cellId
     * @param money
     * @param orderNo
     */
    public void prepayEnergy(String companyCode, String cellId, Integer money, String orderNo) {

        String url = "http://36.138.249.45:8401/Notes?Type=充值&ids="+companyCode+"-W-"+cellId+"-预收水电费-"+money+"&Style=7&memo=" + orderNo;
        System.out.println("预充值水电费:"+url);
        HttpUtils.HttpResult response = HttpUtils.get(url).send();
        String body = new String(response.body());
        System.out.println("prepayEnergy" + body);
        if(StringUtils.isEmpty(body)) {
            throw new BaseException("充值错误，请联系客服查询");
        }
        WgxPrepayEnergyResBo res = JsonUtils.toObject(body, WgxPrepayEnergyResBo.class);
        if(res == null) {
            throw new BaseException("充值错误，请联系客服查询");
        }
        if(!res.getResult().equals("OK")) {
            throw new BaseException(res.getResult());
        }

    }

    /**
     * 查询水电费余额
     * @param cellNO
     * @return
     */
    public WgwEnergyLeftBo queryCellEnergyLeft(String companyCode, String cellNO) {
        String url = "http://yun.hokoemc.com:8093/Merchant_API.aspx";
        Map<String, String> params = new HashMap<>(Map.of(
                "action", "wy_qroominfo",
                "roomcode", companyCode + cellNO
        ));
        String data = this.signedParams(params);
        HttpUtils.HttpResult response = HttpUtils.post(url).body(data).send();
        String body = new String(response.body());
        WgwEnergyLeftResBo res = JsonUtils.toObject(body, WgwEnergyLeftResBo.class);
        System.out.println(res);
        if(res == null) {
            throw new BaseException("查询失败");
        }
        if(!res.getCode().equals("0")) {
            throw new BaseException(res.getMsg());
        }
        if(res.getObjSet() == null || res.getObjSet().isEmpty()) {
            throw new BaseException("查询不到此商铺");
        }
        return res.getObjSet().getFirst();
    }

    private String toUrlParams(Map<String, String> params) {
        return params.keySet().stream().sorted().map(key -> key + "=" + params.get(key)).collect(Collectors.joining("&"));
    }

    private String signedParams(Map<String, String> params) {
        params.put("syscode", "5387f99681ae4139bf14053c784cea30");
        params.put("username", "10000");
        String data = toUrlParams(params);
        String dataForHash = Strings.toLowerCase(data) + "&key=4314373B8DAC4CD6BF7722D4291C888F";
        System.out.println(data);
        System.out.println(dataForHash);

        return data + "&sign=" + Strings.toUpperCase(md5(dataForHash));
    }

    /**
     * 获取单元/商铺结算扣费记录
     * @param companyCode 物业公司表
     * @param cellNO 单元编号/商铺编号，ALL表示时间段内所有
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @return 结算扣费记录列表
     */
    public List<WgwEnergyCheckLogBo> queryRoomCheckLog(String companyCode, String cellNO, LocalDate startDate, LocalDate endDate) {
        String url = "http://yun.hokoemc.com:8093/Merchant_API.aspx";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String sTime = startDate.format(formatter);
        String eTime = endDate.format(formatter);

        Map<String, String> params = new HashMap<>(Map.of(
                "action", "Wy_QRoomCheckLog",
                "roomcode", companyCode + cellNO,
                "stime", sTime,
                "etime", eTime
        ));
        String data = this.signedParams(params);


        HttpUtils.HttpResult response = HttpUtils.post(url).body(data).send();
        String body = new String(response.body());
        System.out.println(body);
        WgwEnergyCheckLogResBo res = JsonUtils.toObject(body, WgwEnergyCheckLogResBo.class);
        System.out.println(res);

        if(res == null) {
            throw new BaseException("查询失败");
        }
        if(!res.getCode().equals("0")) {
            throw new BaseException(res.getMsg());
        }
        if(res.getObjSet() == null || res.getObjSet().isEmpty()) {
            return List.of();
        }
        return res.getObjSet();
    }

    /**
     * 查询单元/商铺历史充值记录
     * @param companyCode 物业公司表
     * @param cellNo 单元编号/商铺编号，ALL表示时间段内所有
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @return 充值记录列表
     */
    public List<WgwEnergyPayLogBo> queryRoomPayLog(String companyCode, String cellNo, LocalDate startDate, LocalDate endDate) {
        String url = "http://yun.hokoemc.com:8093/Merchant_API.aspx";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String sTime = startDate.format(formatter);
        String eTime = endDate.format(formatter);
        Map<String, String> params = new HashMap<>(Map.of(
                "action", "wy_qroompaylog",
                "roomcode", companyCode + cellNo,
                "stime", sTime,
                "etime", eTime
        ));

        String data = this.signedParams(params);
        System.out.println(data);

        HttpUtils.HttpResult response = HttpUtils.post(url).body(data).send();
        String body = new String(response.body());
        System.out.println(body);
        WgwEnergyPayLogResBo res = JsonUtils.toObject(body, WgwEnergyPayLogResBo.class);
        System.out.println(res);

        if(res == null) {
            throw new BaseException("查询失败");
        }
        if(!res.getCode().equals("0")) {
            throw new BaseException(res.getMsg());
        }
        if(res.getObjSet() == null || res.getObjSet().isEmpty()) {
            return List.of();
        }
        return res.getObjSet();
    }

    public List<WgwCellBo> syncCell(List<String> cellKeys) {
        String url = "http://36.138.249.45:3000/dnhhsj/cell-sync";
        Map<String, List<String>> cellKeysMap = Map.of("cellKeys", cellKeys);
        HttpUtils.HttpResult response = HttpUtils.post(url).body(JsonUtils.toJson(cellKeysMap)).send();
        String body = new String(response.body());
        System.out.println(body);
        if(StringUtils.isEmpty(body)) {
            return List.of();
        }
        return JsonUtils.toList(body, WgwCellBo.class);
    }
}
