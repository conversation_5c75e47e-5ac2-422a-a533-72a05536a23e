package com.dounanflowers.third.service;

import com.dounanflowers.framework.annotation.Cached;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.utils.HttpUtils;
import com.dounanflowers.framework.utils.JsonUtils;
import com.dounanflowers.third.bo.BiPeopleCountLoginDataBo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

@Service
public class BiPeopleCountService {

    @Value("${bi.people.login.name}")
    private String loginName;
    @Value("${bi.people.login.password}")
    private String loginPassword;
    @Value("${bi.people.url}")
    private String biPeopleUrl;

    public Map<String, Object> getRegionInfo() {
        Map<String, String> loginData = this.loginData();

        HttpUtils.HttpResult response = HttpUtils.get(biPeopleUrl + "/CardSolution/statistics/regionConfig/getRegionInfo")
                .header("Cookie", loginData.get("cookie"))
                .header("Content-Type", "application/json")
                .send();

        String body = new String(response.body());
        Map<String, Object> map = JsonUtils.toMap(body);
        if (!Objects.equals(map.get("success"), true)) {
            throw new BaseException((String) map.get("errMsg"));
        }
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) map.get("data");
        return dataList.getFirst();
    }

    public List<Map<String, Object>> realTimeReport(String timeUnit, String statisticStartTime, String statisticEndTime) {

        Map<String, String> loginData = this.loginData();


        HttpUtils.HttpResult response = HttpUtils.post(biPeopleUrl + "/CardSolution/statistics/realTimeReport/chart/"
                        + timeUnit
                        + "?userId=" + loginData.get("apiUserId")
                        + "&userName=" + loginData.get("apiUserName")
                        + "&token=" + loginData.get("apiToken"))
                .header("Content-Type", "application/json")
                .header("Cookie", loginData.get("cookie"))
                .body(JsonUtils.toJson(Map.of("regionCode", "1", "regionType", "2", "statisticEndTime", statisticEndTime, "statisticStartTime", statisticStartTime)))
                .send();

        String body = new String(response.body());
        Map<String, Object> map = JsonUtils.toMap(body);
        if (!Objects.equals(map.get("success"), true)) {
            throw new BaseException((String) map.get("errMsg"));
        }
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) map.get("data");
       return dataList;
    }

    public Map<String, Object> inAndOutReport(String timeUnit, String statisticStartTime, String statisticEndTime) {
        Map<String, String> loginData = this.loginData();


        HttpUtils.HttpResult response = HttpUtils.post(biPeopleUrl + "/CardSolution/statistics/inAndOutReport/chart/" + timeUnit
                        + "?userId=" + loginData.get("apiUserId")
                        + "&userName=" + loginData.get("apiUserName")
                        + "&token=" + loginData.get("apiToken"))
                .header("Content-Type", "application/json")
                .header("Cookie", loginData.get("cookie"))
                .body(JsonUtils.toJson(Map.of("regionCode", "1", "regionType", "2", "statisticEndTime", statisticEndTime, "statisticStartTime", statisticStartTime)))
                .send();

        String body = new String(response.body());
        Map<String, Object> map = JsonUtils.toMap(body);
        if (!Objects.equals(map.get("success"), true)) {
            throw new BaseException((String) map.get("errMsg"));
        }
        return (Map<String, Object>) map.get("data");
    }





    @Cached(value = "biPeopleCount:loginData", expire = 60 * 30)
    public Map<String, String> loginData() {

        BiPeopleCountLoginDataBo.PublicKeyData publicKeyRes = this.getPublicKey();
        String cookie = publicKeyRes.getCookie();
        String publicKey = publicKeyRes.getPublicKey();
        String token = this.login(cookie, publicKey);
        String apiPublicKey = this.getApiPublicKey(cookie);
        Map<String, Object> apiLoginData = this.apiLogin(cookie, apiPublicKey);

        Map<String, String> map = new HashMap<>();
        map.put("cookie", cookie);
        map.put("publicKey", publicKey);
        map.put("token", token);
        map.put("apiPublicKey", apiPublicKey);
        map.put("apiUserName", (String) apiLoginData.get("loginName"));
        map.put("apiUserId", (String) apiLoginData.get("id"));
        map.put("apiToken", (String) apiLoginData.get("token"));
        return map;

    }

    private Map<String, Object> apiLogin(String cookie, String apiPublicKey) {

        HttpUtils.HttpResult response = HttpUtils.post(biPeopleUrl + "/WPMS/apiLogin")
                .header("Content-Type", "application/json")
                .header("Cookie", cookie)
                .body(JsonUtils.toJson(Map.of("loginName", this.loginName, "loginPass", this.base64Encode(this.loginPassword + apiPublicKey))))
                .send();

        String body = new String(response.body());
        Map<String, Object> map = JsonUtils.toMap(body);
        if (!Objects.equals(map.get("success"), "true")) {
            throw new BaseException((String) map.get("errMsg"));
        }

       return map;

    }

    private String base64Encode(String s) {
        return Base64.getEncoder().encodeToString(s.getBytes(StandardCharsets.UTF_8));
    }

    private String getApiPublicKey(String cookie) {
        HttpUtils.HttpResult response = HttpUtils.post(biPeopleUrl + "/WPMS/getCryptKey")
                .header("Content-Type", "application/json")
                .header("Cookie", cookie)
                .body(JsonUtils.toJson(Map.of("loginName", this.loginName)))
                .send();

        String body = new String(response.body());
        Map<String, Object> map = JsonUtils.toMap(body);
        if (!Objects.equals(map.get("success"), "true")) {
            throw new BaseException((String) map.get("errMsg"));
        }
        return (String) map.get("publicKey");
    }

    private  BiPeopleCountLoginDataBo.PublicKeyData getPublicKey() {

        HttpUtils.HttpResult response = HttpUtils.post(biPeopleUrl + "/WPMS/getPublicKey")
                .header("Content-Type", "application/json")
                .body(JsonUtils.toJson(Map.of("loginName", this.loginName)))
                .send();

        String body = new String(response.body());
        Map<String, Object> map = JsonUtils.toMap(body);
        if (!Objects.equals(map.get("success"), "true")) {
            throw new BaseException((String) map.get("errMsg"));
        }
        String cookie = String.valueOf(response.headers().get("Set-Cookie"));
        String publicKey = (String) map.get("publicKey");
        BiPeopleCountLoginDataBo.PublicKeyData publicKeyData = new BiPeopleCountLoginDataBo.PublicKeyData();
        publicKeyData.setCookie(cookie);
        publicKeyData.setPublicKey(publicKey);
        return publicKeyData;
    }

    private String login(String cookie, String publicKey) {
        HttpUtils.HttpResult response = HttpUtils.post(biPeopleUrl + "/WPMS/login")
                .header("Cookie", cookie)
                .header("Content-Type", "application/json")
                .body(JsonUtils.toJson(Map.of("loginName", this.loginName, "loginPass", this.rsaEncrypt(this.loginPassword, publicKey))))
                .send();

        String body = new String(response.body());
        Map<String, Object> map = JsonUtils.toMap(body);
        if (!Objects.equals(map.get("success"), "true")) {
            throw new BaseException((String) map.get("errMsg"));
        }
        return (String) map.get("token");
    }

    private String rsaEncrypt(String data, String publicKey) {
        try {
            Cipher cipher = Cipher.getInstance("RSA");
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            byte[] publicKeyBytes = Base64.getDecoder().decode(publicKey);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
            PublicKey pubKey = keyFactory.generatePublic(keySpec);
            cipher.init(Cipher.ENCRYPT_MODE, pubKey);
            byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException | IllegalBlockSizeException |
                 BadPaddingException | InvalidKeySpecException e) {
            throw new RuntimeException("Error while encrypting data", e);
        }
    }





}
