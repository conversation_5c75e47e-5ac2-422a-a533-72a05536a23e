package com.dounanflowers.third;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.teaopenapi.Client;
import com.aliyun.teaopenapi.models.Config;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.third.entity.*;
import com.dounanflowers.third.manager.ConfigManager;
import com.dounanflowers.third.service.AliyunSmsService;
import com.dounanflowers.third.service.AllinpayService;
import com.dounanflowers.third.service.ParkApiService;
import com.dounanflowers.third.service.WuguanwangService;
import com.google.common.collect.Maps;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
public class ThirdPartyHolder {

    private static final Map<String, ParkApiService> parkApiServiceCache = Maps.newConcurrentMap();
    private static final Map<String, WuguanwangService> wuguanwangServiceCache = Maps.newConcurrentMap();
    private static final Map<String, AliyunSmsService> aliyunSmsServiceCache = Maps.newConcurrentMap();

    private static final Map<String, WxMaService> wxMaServiceCache = Maps.newConcurrentMap();

    private static final Map<String, OSSClient> ossClientCache = Maps.newConcurrentMap();

    private static final Map<String, com.aliyun.teaopenapi.Client> teaOpenApiClientCache = Maps.newConcurrentMap();

    private static final Map<String, AliyunOss> ossConfigCache = Maps.newConcurrentMap();

    private static final Map<String, AllinpayService> allinpayServiceCache = Maps.newConcurrentMap();

    private static ConfigManager configManager;

    public ThirdPartyHolder(ConfigManager configManager) {
        ThirdPartyHolder.configManager = configManager;
    }

    public static Client createAliOpenapi() {
        if (teaOpenApiClientCache.get("aliyun") == null) {
            try {
                AliyunOpenapi aliyunOpenapi = configManager.getAliyunOpenapi();
                // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
                // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html。
                Config config = new Config()
                        // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                        .setAccessKeyId(aliyunOpenapi.getAccessKey())
                        // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                        .setAccessKeySecret(aliyunOpenapi.getSecret());
                // Endpoint 请参考 https://api.aliyun.com/product/Cloudauth
                config.endpoint = aliyunOpenapi.getEndpoint();
                Client client = new Client(config);
                teaOpenApiClientCache.put("aliyun", client);
            } catch (Exception e) {
                throw new BaseException("阿里云OpenAPI配置错误");
            }
        }
        return teaOpenApiClientCache.get("aliyun");
    }

    public static synchronized AllinpayService allinpayService() {
        return allinpayService("default", "default");
    }


    public static synchronized AllinpayService allinpayService(String env) {
        return allinpayService(env, "default");
    }

    public static synchronized AllinpayService allinpayService(String env, String payEnv) {
        String key = env + "/" + payEnv;
        if (allinpayServiceCache.get(key) == null) {
            Allinpay allinpay = configManager.getAllinpay(payEnv);
            if (allinpay == null) {
                throw new BaseException("未找到通联支付配置");
            }
            WxMiniapp wxMiniapp = configManager.getWxMiniapp(env);
            AllinpayService allinpayService = new AllinpayService(allinpay, wxMiniapp == null ? null : wxMiniapp.getAppId());
            allinpayServiceCache.put(key, allinpayService);
        }
        return allinpayServiceCache.get(key);
    }

    public static synchronized WxMaService wxMaService(String env) {
        if (wxMaServiceCache.get(env) == null) {
            WxMiniapp wxMiniapp = configManager.getWxMiniapp(env);
            if (wxMiniapp == null) {
                throw new BaseException("未找到相关环境小程序配置");
            }
            WxMaService wxMaService = new WxMaServiceImpl();
            WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
            config.setAppid(wxMiniapp.getAppId());
            config.setSecret(wxMiniapp.getAppSecret());
            wxMaService.setWxMaConfig(config);
            wxMaServiceCache.put(env, wxMaService);
        }
        return wxMaServiceCache.get(env);
    }

    public static OSSClient ossClient() {
        cacheAliyunOss();
        return ossClientCache.get("aliyun");
    }

    public static AliyunOss ossConfig() {
        cacheAliyunOss();
        return ossConfigCache.get("aliyun");
    }

    private static synchronized void cacheAliyunOss() {
        if (ossConfigCache.get("aliyun") == null || ossClientCache.get("aliyun") == null) {
            AliyunOss aliyunOss = configManager.getAliyunOss();
            if (aliyunOss == null) {
                throw new BaseException("未找到阿里云OSS配置");
            }
            OSSClient ossClient = new OSSClient(aliyunOss.getEndpoint(), new DefaultCredentialProvider(aliyunOss.getAccessKey(), aliyunOss.getSecret()), null);
            ossClientCache.put("aliyun", ossClient);
            ossConfigCache.put("aliyun", aliyunOss);
        }
    }

    public static synchronized ParkApiService parkApiService() {
        if (parkApiServiceCache.get("default") == null) {
            ParkApi parkApi = configManager.getParkApi();
            if (parkApi == null) {
                throw new BaseException("未找到停车场API配置");
            }
            ParkApiService parkApiService = new ParkApiService(parkApi);
            parkApiServiceCache.put("default", parkApiService);
        }
        return parkApiServiceCache.get("default");
    }

    public static synchronized WuguanwangService wuguanwangService() {
        if (wuguanwangServiceCache.get("default") == null) {
            WuguanwangService wuguanwangService = new WuguanwangService();
            wuguanwangServiceCache.put("default", wuguanwangService);
        }
        return wuguanwangServiceCache.get("default");
    }

    public static synchronized AliyunSmsService aliyunSmsService() {
        if (aliyunSmsServiceCache.get("default") == null) {
            AliyunSms aliyunSms = configManager.getAliyunSms();
            AliyunSmsService aliyunSmsService = new AliyunSmsService(aliyunSms);
            aliyunSmsServiceCache.put("default", aliyunSmsService);
        }
        return aliyunSmsServiceCache.get("default");
    }

}
