package com.dounanflowers.third.manager;

import com.dounanflowers.third.entity.*;
import com.dounanflowers.third.repo.*;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ConfigManager {

    private final WxMiniappRepo wxMiniappRepo;

    private final AliyunOssRepo aliyunOssRepo;

    private final AliyunOpenapiRepo aliyunOpenapiRepo;

    private final AllinpayRepo allinpayRepo;

    private final ParkApiRepo parkApiRepo;

    private final AliyunSmsRepo aliyunSmsRepo;

    public WxMiniapp getWxMiniapp(String env) {
        return wxMiniappRepo.selectOneByQuery(QueryWrapper.create().eq(WxMiniapp::getEnv, env));
    }

    public AliyunOss getAliyunOss() {
        return aliyunOssRepo.selectOneByQuery(QueryWrapper.create().limit(1));
    }

    public AliyunOpenapi getAliyunOpenapi() {
        return aliyunOpenapiRepo.selectOneByQuery(QueryWrapper.create().limit(1));
    }

    public Allinpay getAllinpay(String env) {
        return allinpayRepo.selectOneByQuery(QueryWrapper.create().eq(Allinpay::getEnv, env));
    }

    public ParkApi getParkApi() {
        return parkApiRepo.selectOneByQuery(QueryWrapper.create().limit(1));
    }

    public AliyunSms getAliyunSms() {
        return aliyunSmsRepo.selectOneByQuery(QueryWrapper.create().limit(1));
    }

}
