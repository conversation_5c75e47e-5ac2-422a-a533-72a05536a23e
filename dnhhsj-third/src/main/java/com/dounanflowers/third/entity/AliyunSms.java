package com.dounanflowers.third.entity;

import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("config_aliyun_sms")
@Accessors(chain = true)
public class AliyunSms extends BaseEntity {

    private String accessKey;

    private String accessSecret;

}
