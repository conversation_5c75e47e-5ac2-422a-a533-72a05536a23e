package com.dounanflowers.third.entity;

import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("config_wx_miniapp")
@Accessors(chain = true)
public class WxMiniapp extends BaseEntity {

    private String env;

    private String appId;

    private String appSecret;

    private String mchId;

    private String mchKey;

    private String notifyUrl;

    private byte[] certData;

    private byte[] keyData;

}
