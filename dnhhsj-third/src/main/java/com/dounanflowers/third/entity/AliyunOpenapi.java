package com.dounanflowers.third.entity;

import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table("config_aliyun_openapi")
public class AliyunOpenapi extends BaseEntity {

    private String endpoint;

    private String accessKey;

    private String secret;

}
