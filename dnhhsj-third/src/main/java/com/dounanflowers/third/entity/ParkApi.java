package com.dounanflowers.third.entity;

import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("config_park_api")
@Accessors(chain = true)
public class ParkApi extends BaseEntity {

    private String apiPrefix;

    private String apiKey;

}
