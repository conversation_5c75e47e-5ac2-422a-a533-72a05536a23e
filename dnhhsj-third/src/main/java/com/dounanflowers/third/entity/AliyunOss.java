package com.dounanflowers.third.entity;

import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("config_aliyun_oss")
@Accessors(chain = true)
public class AliyunOss extends BaseEntity {

    private String endpoint;

    private String accessKey;

    private String secret;

    private String bucketName;

    private String domain;

}
