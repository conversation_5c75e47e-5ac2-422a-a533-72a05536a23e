package com.dounanflowers.third.entity;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.bean.BaseEntity;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Table("config_allinpay")
@Accessors(chain = true)
public class Allinpay extends BaseEntity {

    private String env;

    private String notifyUrl;

    private String appid;

    private String cusid;

    @ColumnDef(type = "text")
    private String certData;

    @ColumnDef(type = "text")
    private String keyData;

}
