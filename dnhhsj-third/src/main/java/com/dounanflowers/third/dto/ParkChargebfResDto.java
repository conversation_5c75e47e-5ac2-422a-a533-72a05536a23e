package com.dounanflowers.third.dto;

import lombok.Data;

@Data
public class ParkChargebfResDto {

    /**
     * 应收总金额
     */
    private String charge;

    /**
     * 本次结算时间
     */
    private String getTime;

    /**
     * 图片名
     */
    private String imgName;

    /**
     * 入场时间
     */
    private String inTime;

    /**
     * 备注
     */
    private String memo;

    /**
     * 唯一订单号
     */
    private String orderId;

    /**
     * 支付流水号
     */
    private String outTradeNo;

    /**
     * 支付后免费时长
     */
    private String paidfreeTime;

    /**
     * 已支付总金额
     */
    private String paidTotal;

    /**
     * 本次应收金额
     */
    private String payCharge;

    /**
     * 车牌号
     */
    private String plate;

    /**
     * 优惠总金额
     */
    private String profitChargeTotal;

    /**
     * 免费停车总时长
     */
    private String profitTimeTotal;

    /**
     * 停车分钟数
     */
    private Integer stopTimeMin;

    /**
     * 总停车时长
     */
    private String stopTimeTotal;

    /**
     * 时间戳
     */
    private String timeStamp;
}
