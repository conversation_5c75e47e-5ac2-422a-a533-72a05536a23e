package com.dounanflowers.third.dto;

import lombok.Data;

import java.util.List;

@Data
public class ParkCouponSendReqDto {
    /**
     * 停车场编号
     */
    private String parkNumber;
    /**
     * 优惠列表
     */
    private List<ParkCoupon> datas;

    @Data
    public static class ParkCoupon {
        /**
         * 优惠券编码，第三方定义,每笔优惠不可重复
         */
        private String couponCode;
        /**
         * 优惠类型，0”小时券,”1”金额券
         */
        private String couponModeId;
        /**
         * 优惠截止时间，格式yyyy-MM-dd HH:mm:ss
         */
        private String end;
        /**
         * 优惠序列号，从1开始递增
         */
        private String id;
        /**
         * 入场记录编号，车辆入场时由场库生成的唯一编号
         */
        private String orderId;
        /**
         * 车牌号
         */
        private String plate;
        /**
         * 面值，小时券,单位:分钟，  金额券,单位:分  折扣券0表示免费  10表示1折  85表示85折  90表示9折
         */
        private String position;
        /**
         * 商户ID，从1000开始（1000以内为系统占用）
         */
        private Long shopId;
        /**
         * 商户名称，下发优惠券的商户名称，便于后期优惠券统计查询
         */
        private String shopName;


    }
}
