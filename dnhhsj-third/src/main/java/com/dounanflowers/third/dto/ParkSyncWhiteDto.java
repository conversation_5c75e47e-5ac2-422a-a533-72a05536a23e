package com.dounanflowers.third.dto;

import lombok.Data;

import java.util.List;

@Data
public class ParkSyncWhiteDto {
    /**
     * 停车场编号
     */
    private String parkNumber;

    /**
     * 白名单数量
     */
    private Integer size;

    /**
     * 白名单列表
     */
    private List<Whitelist> datas;


    @Data
    public static class Whitelist {
        /**
         * 车牌号
         */
        private String plate;
        /**
         * 收费类型 不收费/挂账
         */
        private String chargeType;
        /**
         * 车主姓名
         */
        private String name;
        /**
         * 车主身份证号
         */
        private String certificate;
        /**
         * 车主地址
         */
        private String address;
        /**
         * 车主联系电话
         */
        private String phone;
        /**
         * 车牌颜色 “蓝牌”，“黄牌”等
         */
        private String plateColor;
        /**
         * 生效日期 yyyy-MM-dd格式字符串
         */
        private String start;
        /**
         * 失效日期  yyyy-MM-dd格式字符串
         */
        private String end;
        /**
         * 部门 默认填“”
         */
        private String dept;
        /**
         * 车辆类型 错峰/月租
         */
        private String carType;
        /**
         * 备注 默认填“”
         */
        private String memo;

        /**
         * 区域信息 无区域概念则为[]
         */
        private List<Object> areasAndValidity;

        /**
         * 车牌匹配等级 1全字 2严格 3特高 默认1
         */
        private Integer levelId;
    }

}
