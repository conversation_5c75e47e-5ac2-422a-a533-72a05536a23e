package com.dounanflowers.third.dto;

import lombok.Data;

import java.util.List;

@Data
public class ParkPayResultReqDto {


    /**
     * 结算时间
     */
    private String getTime;
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 算费流水号，算费时有返回此字段，下发时需要带回
     */
    private String outTradeNo;
    /**
     * 停车场编号
     */
    private String parkNumber;
    /**
     * 支付渠道
     */
    private String payChannel;
    /**
     * 应收金额
     */
    private String payCharge;
    /**
     * 支付时间
     */
    private String payTime;
    /**
     * 支付类型
     */
    private String payType;
    /**
     * 车牌号
     */
    private String plate;
    /**
     * 实收金额
     */
    private String realCharge;
    /**
     * 每笔交易生成唯一流水号
     */
    private String transactionID;
    /**
     * 当第三方存在多笔支付时,可以传此参数来对应支付记录；payList 中的 realCharge 总和需等于 payCharge
     */
    private List<PayList> payList;

    @Data
    public static class PayList {
        /**
         * 支付类型
         */
        private String payType;
        /**
         * 支付金额
         */
        private String realCharge;
        /**
         * 第三方支付流水号
         */
        private String transactionID;
    }
}
