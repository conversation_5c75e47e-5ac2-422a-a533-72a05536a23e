package com.dounanflowers.third.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "微信支付参数")
public class WxPayParamsBo {

    @Schema(title = "是否已支付", description = "一般是0元订单，自动支付，不再调用微信支付")
    private Boolean paid;

    @Schema(title = "AppId", description = "微信开放平台 - 应用 - AppId，注意和微信小程序、公众号 AppId 可能不一致")
    private String appId;

    @Schema(title = "时间戳", description = "单位：秒")
    private String timeStamp;

    @Schema(title = "随机字符串")
    private String nonceStr;

    @JsonProperty("package")
    @Schema(title = "固定值")
    private String pack;

    @Schema(title = "签名类型")
    private String signType;

    @Schema(title = "签名", description = "这里用的 MD5/RSA 签名")
    private String paySign;

}

