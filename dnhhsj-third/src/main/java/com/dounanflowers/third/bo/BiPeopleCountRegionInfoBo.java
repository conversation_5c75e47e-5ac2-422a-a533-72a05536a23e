package com.dounanflowers.third.bo;

import lombok.Data;

//{"alarmChannelCode":"1000000$1$0$0","createTime":1695623002000,"curNumber":839,"enterNumber":6877,"existPersonOrNot":1,"id":1,"isHappenedLowerAlarm":0,"isHappenedNotPlanAlarm":0,"isHappenedUnmannedAlarm":0,"isHappenedUpperAlarm":0,"lowerFlag":0,"outNumber":6038,"planFlag":0,"regionCode":"1","regionName":"斗南花卉产业园","regionType":2,"statisticModel":1,"unmannedFlag":0,"updateTime":1695623002000,"upperFlag":0}
@Data
public class BiPeopleCountRegionInfoBo {

    private String alarmChannelCode;
    private Long createTime;
    private Integer curNumber;
    private Integer enterNumber;
    private Integer existPersonOrNot;
    private Integer id;
    private Integer isHappenedLowerAlarm;
    private Integer isHappenedNotPlanAlarm;
    private Integer isHappenedUnmannedAlarm;
    private Integer isHappenedUpperAlarm;
    private Integer lowerFlag;
    private Integer outNumber;
    private Integer planFlag;
    private String regionCode;
    private String regionName;
    private Integer regionType;
    private Integer statisticModel;
    private Integer unmannedFlag;
    private Long updateTime;
    private Integer upperFlag;
}
