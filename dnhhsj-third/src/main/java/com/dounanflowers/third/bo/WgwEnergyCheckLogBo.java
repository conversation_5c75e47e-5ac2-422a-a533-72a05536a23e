package com.dounanflowers.third.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

//{"CostID":"198b95ab-a39d-4e2f-960d-6f2f321158d3",
//        "RoomCode":"PWGL_11-1-1008",
//        "RoomName":"1-1-1008",
//        "CostMethod":"1",
//        "CostMoney":"364.2116",
//        "UName":"东皇超市",
//        "ElectrcUse":"620.7800",
//        "ElectrcPrice":"0.5867",
//        "ElectrcCostMoney":"364.2116",
//        "WaterUse":"0.0000",
//        "WaterPrice":"7.5000",
//        "WaterCostMoney":"0.0000",
//        "MoneyLeft":"761.2061",
//        "CreateTime":"2025-04-02 12:37:55",
//        "Status":"1"
//}

@Data
public class WgwEnergyCheckLogBo {
    @JsonProperty("MSG")
    private String msg;

    @JsonProperty("CostID")
    private String costId;

    @JsonProperty("RoomCode")
    private String roomCode;

    @JsonProperty("RoomName")
    private String roomName;

    @JsonProperty("CostMethod")
    private Integer costMethod;

    @JsonProperty("CostMoney")
    private BigDecimal costMoney;

    @JsonProperty("UName")
    private String uName;

    @JsonProperty("ElectrcUse")
    private BigDecimal electrcUse;

    @JsonProperty("ElectrcPrice")
    private BigDecimal electrcPrice;

    @JsonProperty("ElectrcCostMoney")
    private BigDecimal electrcCostMoney;

    @JsonProperty("WaterUse")
    private BigDecimal waterUse;

    @JsonProperty("WaterPrice")
    private BigDecimal waterPrice;

    @JsonProperty("WaterCostMoney")
    private BigDecimal waterCostMoney;

    @JsonProperty("MoneyLeft")
    private BigDecimal moneyLeft;

    @JsonProperty("CreateTime")
    private String createTime;

    @JsonProperty("Status")
    private String status;

}
