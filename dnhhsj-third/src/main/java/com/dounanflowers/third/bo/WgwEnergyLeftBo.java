package com.dounanflowers.third.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class WgwEnergyLeftBo {
    @JsonProperty("RoomCode")
    private String RoomCode;

    @JsonProperty("RoomName")
    private String RoomName;

    @JsonProperty("MerchantName")
    private String MerchantName;

    @JsonProperty("RoomState")
    private String RoomState;

    @JsonProperty("Telphone")
    private String Telphone;

    @JsonProperty("MoneyLeft")
    private String MoneyLeft;

    @JsonProperty("PreCost")
    private String PreCost;

    @JsonProperty("ChargeTotal")
    private String ChargeTotal;

    @JsonProperty("StartTime")
    private String StartTime;

    @JsonProperty("UpdateTime")
    private String UpdateTime;
}