package com.dounanflowers.third.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

//{"ChargeID":"05f14679-5913-f011-92ab-9c504b40dcc2",
// "RoomCode":"PWGL_11-1-1008",
// "RoomName":"1-1-1008",
// "ChargeMethod":"1",
// "ChargeMoney":"1.0000",
// "UName":"东皇超市",
// "Remark":"接口充值",
// "CreateTime":"2025-04-07 10:39:12",
// "OrderID":"11120250420011940840",
// "Status":"1"
// }

@Data
public class WgwEnergyPayLogBo {
    @JsonProperty("MSG")
    private String msg;

    @JsonProperty("ChargeID")
    private String chargeId;

    @JsonProperty("RoomCode")
    private String roomCode;

    @JsonProperty("RoomName")
    private String roomName;

    @JsonProperty("UName")
    private String uName;

    @JsonProperty("ChargeMethod")
    private Integer chargeMethod;

    @JsonProperty("ChargeMoney")
    private BigDecimal chargeMoney;

    @JsonProperty("UserName")
    private String userName;

    @JsonProperty("TelPhone")
    private String telPhone;

    @JsonProperty("Remark")
    private String remark;

    @JsonProperty("CreateTime")
    private String createTime;

    @JsonProperty("OrderID")
    private String orderId;

    @JsonProperty("Status")
    private String status;
}
