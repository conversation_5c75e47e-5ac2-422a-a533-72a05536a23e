package com.dounanflowers.third.bo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class WgwCellBo {
    @JsonProperty("F_GSName")
    private String gsName;

    @JsonProperty("F_GSCode")
    private String gsCode;

    @JsonProperty("F_CellName")
    private String cellName;

    @JsonProperty("F_CellNO")
    private String cellNo;

    @JsonProperty("F_CellID")
    private String cellId;

    @JsonProperty("F_AreaJZ")
    private String areaJz;

    @JsonProperty("F_Area")
    private String area;

    @JsonProperty("F_Begindate")
    private String begindate;

    @JsonProperty("F_EndDate")
    private String endDate;

    @JsonProperty("F_HTBH")
    private String htbh;

    @JsonProperty("F_Name")
    private String name;

    @JsonProperty("F_Tel")
    private String tel;

    @JsonProperty("F_ZJHM")
    private String zhhm;

    @JsonProperty("F_Address")
    private String address;

    @JsonProperty("F_BankNO")
    private String bankNo;

    @JsonProperty("F_LayerNO")
    private String layerNo;

    @JsonProperty("F_LayerName")
    private String layerName;
}


