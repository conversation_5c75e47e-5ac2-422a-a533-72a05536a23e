package com.dounanflowers.third.bo;

import lombok.Data;

import java.util.List;

@Data
public class BiPeopleCountReportBo {
//    {"keyTime":"2024-12-31","remainder":17747,"showTime":"01:00","timeIsRange":true,"type":"hour","useTime":1735578000000}
    private List<ListItem> data;
    private String time;

    @Data
    public static class ListItem {
        private String keyTime;
        private Integer remainder;
        private String showTime;
        private Boolean timeIsRange;
        private String type;
        private Long useTime;
    }


}
