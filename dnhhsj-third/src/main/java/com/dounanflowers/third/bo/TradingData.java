package com.dounanflowers.third.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class TradingData {
    private int code;
    private String msg;
    private long time;
    private TradingDataDetail data;

    @Data
    public static class TradingDataDetail {
        private int count;
        private List<String> dateRange;
        private int limit;
        @JsonProperty("list")
        private List<TradingDataItem> itemList;
        private int page;
        @JsonProperty("total_payment")
        private String totalPayment;
    }

    @Data
    public static class TradingDataItem {
        @JsonProperty("order_id")
        private long orderId;
        @JsonProperty("group_order_id")
        private long groupOrderId;
        @JsonProperty("store_name")
        private String storeName;
        @JsonProperty("order_sn")
        private String orderSn;
        @JsonProperty("real_name")
        private String realName;
        @JsonProperty("user_address")
        private String userAddress;
        @JsonProperty("total_num")
        private int totalNum;
        @JsonProperty("pay_price")
        private String payPrice;
        @JsonProperty("create_time")
        private String createTime;
        @JsonProperty("pay_time")
        private String payTime;
    }
}