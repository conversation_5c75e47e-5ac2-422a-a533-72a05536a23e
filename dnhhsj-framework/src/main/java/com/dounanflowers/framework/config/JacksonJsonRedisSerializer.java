package com.dounanflowers.framework.config;

import com.dounanflowers.framework.utils.SerializableUtils;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.lang.Nullable;

public class JacksonJsonRedisSerializer implements RedisSerializer<Object> {

    @Override
    public byte[] serialize(@Nullable Object value) {
        if (value == null) {
            return new byte[0];
        }
        return SerializableUtils.toBytes(value);
    }

    @Override
    public Object deserialize(@Nullable byte[] source) {
        if (source == null || source.length == 0) {
            return null;
        }
        return SerializableUtils.fromBytes(source);
    }

    /**
     * Deserialized the array of bytes containing {@literal JSON} as an {@link Object} of the given, required {@link Class
     * type}.
     *
     * @param source array of bytes containing the {@literal JSON} to deserialize; can be {@literal null}.
     * @param type   {@link Class type} of {@link Object} from which the {@literal JSON} will be deserialized; must not be
     *               {@literal null}.
     * @return {@literal null} for an empty source, or an {@link Object} of the given {@link Class type} deserialized from
     * the array of bytes containing {@literal JSON}.
     * @throws IllegalArgumentException if the given {@link Class type} is {@literal null}.
     * @throws SerializationException   if the array of bytes cannot be deserialized as an instance of the given
     *                                  {@link Class type}
     */
    @Nullable
    @SuppressWarnings("unchecked")
    public <T> T deserialize(@Nullable byte[] source, Class<T> type) throws SerializationException {
        if (source == null || source.length == 0) {
            return null;
        }
        return SerializableUtils.fromBytes(source, type);
    }

}
