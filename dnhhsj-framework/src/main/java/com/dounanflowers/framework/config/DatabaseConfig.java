package com.dounanflowers.framework.config;

import com.dounanflowers.framework.bean.BaseAuditor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableTransactionManagement
@Configuration
@MapperScan("com.dounanflowers.**.repo")
public class DatabaseConfig {

    @Bean
    @ConditionalOnMissingBean(BaseAuditor.class)
    public BaseAuditor baseAuditorAware() {
        return new BaseAuditor();
    }


}
