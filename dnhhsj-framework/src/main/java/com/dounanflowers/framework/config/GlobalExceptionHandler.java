package com.dounanflowers.framework.config;

import com.dounanflowers.framework.bean.Result;
import com.dounanflowers.framework.exception.BaseException;
import com.dounanflowers.framework.filter.LogFilter;
import com.dounanflowers.framework.utils.ServletUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.ValidationException;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.internal.engine.path.PathImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.Map;
import java.util.Set;

@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Result<?> methodArgumentNotValidHandler(MethodArgumentNotValidException e) {
        log.error(e.getMessage());
        setErrorCode("400");
        return Result.error(400, e.getBindingResult().getFieldError().getDefaultMessage());
    }

    @ExceptionHandler(NoResourceFoundException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Result<?> methodArgumentNotValidHandler(NoResourceFoundException e) {
        setErrorCode("404");
        return Result.error(404, e.getMessage());
    }

    private static void setErrorCode(String number) {
        HttpServletRequest request = ServletUtils.getRequest();
        Map<String, String> extraMap = (Map<String, String>) request.getAttribute(LogFilter.LOG_EXTRA_MAP);
        extraMap.put("errorCode", number);
    }

    @ExceptionHandler(BaseException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Result<?> baseExceptionHandler(BaseException e) {
        setErrorCode(Integer.toString(e.getCode()));
        log.error(e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler({
            IllegalArgumentException.class,
            MethodArgumentTypeMismatchException.class,
            MissingServletRequestParameterException.class,
            HttpMessageNotReadableException.class
    })
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Result<?> badArgumentHandler(IllegalArgumentException e) {
        setErrorCode("400");
        log.error(e.getMessage(), e);
        return Result.badArgumentValue();
    }

    @ExceptionHandler(ValidationException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Result<?> badArgumentHandler(ValidationException e) {
        log.error(e.getMessage(), e);
        setErrorCode("400");
        if (e instanceof ConstraintViolationException exs) {
            Set<ConstraintViolation<?>> violations = exs.getConstraintViolations();
            for (ConstraintViolation<?> item : violations) {
                String message = ((PathImpl) item.getPropertyPath()).getLeafNode().getName() + item.getMessage();
                return Result.error(400, message);
            }
        }
        return Result.badArgumentValue();
    }

    @ExceptionHandler(Exception.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Result<?> seriousHandler(Exception e) {
        setErrorCode("500");
        log.error(e.getMessage(), e);
        return Result.error();
    }

}
