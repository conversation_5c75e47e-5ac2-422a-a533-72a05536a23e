package com.dounanflowers.framework.filter;

import com.dounanflowers.framework.utils.TraceHelper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.WebUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class LogFilter extends OncePerRequestFilter {

    public final static String LOG_EXTRA_MAP = "LOG_EXTRA_MAP";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(request);
        TraceHelper.set(request.getHeader("Trace-Id"));
        long startTime = System.currentTimeMillis();
        Map<String, String> map = Maps.newConcurrentMap();
        request.setAttribute(LOG_EXTRA_MAP, map);
        try {
            filterChain.doFilter(wrappedRequest, response);
            String uri = request.getRequestURI();
            String method = request.getMethod();
            String headerLog = buildHeaderLog(request);
            String queryString = request.getQueryString();
            String payload = getBody(wrappedRequest);
            HttpStatus httpStatus = HttpStatus.valueOf(response.getStatus());
            log.info(formatLog(httpStatus.getReasonPhrase(), method, uri, startTime, headerLog, queryString, payload, map));
        } catch (Exception e) {
            String uri = request.getRequestURI();
            String method = request.getMethod();
            String headerLog = buildHeaderLog(request);
            String queryString = request.getQueryString();
            String payload = getBody(wrappedRequest);
            log.info(formatLog("ERROR", method, uri, startTime, headerLog, queryString, payload, map));
            throw e;
        } finally {
            TraceHelper.clear();
        }
    }

    private static String buildHeaderLog(HttpServletRequest request) {
        Map<String, String> headerMap = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headerMap.put(headerName, headerValue);
        }
        String ipAddress = request.getRemoteAddr();
        headerMap.put("remoteClient", ipAddress);
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(headerMap);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private static String getBody(ContentCachingRequestWrapper request) {
        // wrap request to make sure we can read the body of the request (otherwise it will be consumed by the actual
        // request handler)
        ContentCachingRequestWrapper wrapper = WebUtils.getNativeRequest(request, ContentCachingRequestWrapper.class);
        if (wrapper != null) {
            if (request.getContentType() != null && request.getContentType().startsWith("multipart/form-data")) {
                return "[multipart/form-data]";
            }
            byte[] buf = wrapper.getContentAsByteArray();
            if (buf.length > 0) {
                String payload;
                try {
                    payload = new String(buf, wrapper.getCharacterEncoding());
                } catch (UnsupportedEncodingException ex) {
                    payload = "[unknown]";
                }
                return payload;
            }
        }
        return null;
    }

    private static String formatLog(String result, String method, String uri, long startTime, String headerLog,
                                    String queryString, String payload, Map<String, String> extraMap) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS");
        String requestTime = df.format(new Date(startTime));
        long executeTime = (new Date()).getTime() - startTime;
        String extra = extraMap.entrySet().stream().map(entry -> entry.getKey() + ":" + entry.getValue())
                .collect(Collectors.joining(";"));
        if (!extra.isEmpty()) {
            extra = ";" + extra;
        }
        return result + ";"
                + method + ";"
                + uri + ";"
                + requestTime + ";"
                + executeTime + "ms" + ";"
                // + "header:" + headerLog + ";"
                + "query:" + queryString + ";"
                + "payload:" + payload + extra;
    }

}
