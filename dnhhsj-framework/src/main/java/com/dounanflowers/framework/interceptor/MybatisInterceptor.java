package com.dounanflowers.framework.interceptor;

import com.dounanflowers.framework.bean.BaseAuditor;
import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.utils.IdUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.binding.MapperMethod.ParamMap;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collection;

/**
 * @description: MybatisInterceptor
 * @author: Darren
 * @create: 2019/12/31 09:46
 */
@Slf4j
@Component
@Intercepts({
        @Signature(
                type = Executor.class,
                method = "update",
                args = {MappedStatement.class, Object.class}
        )
})
@RequiredArgsConstructor
public class MybatisInterceptor implements Interceptor {

    private final BaseAuditor baseAuditor;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        Object parameter = invocation.getArgs()[1];
        if (parameter == null) {
            return invocation.proceed();
        }
        if (SqlCommandType.INSERT == sqlCommandType) {
            if (parameter instanceof ParamMap<?> p) {
                if (p.containsKey("$$entity")) {
                    parameter = p.get("$$entity");
                } else {
                    parameter = p.get("param1");
                }
                if (parameter instanceof BaseEntity entity) {
                    createBaseInfo(entity);
                } else if (parameter instanceof Collection<?> collection) {
                    for (Object o : collection) {
                        if (o instanceof BaseEntity entity) {
                            createBaseInfo(entity);
                        }
                    }
                }
            } else {
                if (parameter instanceof BaseEntity entity) {
                    createBaseInfo(entity);
                } else if (parameter instanceof Collection<?> collection) {
                    for (Object o : collection) {
                        if (o instanceof BaseEntity entity) {
                            createBaseInfo(entity);
                        }
                    }
                }
            }
        } else if (SqlCommandType.UPDATE == sqlCommandType) {
            if (parameter instanceof ParamMap<?> p) {
                if (p.containsKey("$$entity")) {
                    parameter = p.get("$$entity");
                } else {
                    parameter = p.get("param1");
                }
                if (parameter == null) {
                    return invocation.proceed();
                }
            }
            if (parameter instanceof BaseEntity entity) {
                entity.setUpdatedBy(baseAuditor.getCurrentAuditor());
                entity.setUpdatedAt(LocalDateTime.now());
            }
        }
        return invocation.proceed();
    }

    private void createBaseInfo(BaseEntity entity) {
        if (entity.getId() == null) {
            entity.setId(IdUtils.nextId());
        }
        entity.setCreatedBy(baseAuditor.getCurrentAuditor());
        entity.setUpdatedBy(baseAuditor.getCurrentAuditor());
        LocalDateTime now = LocalDateTime.now();
        if (entity.getCreatedAt() == null) {
            entity.setCreatedAt(now);
        }
        if (entity.getUpdatedAt() == null) {
            entity.setUpdatedAt(now);
        }
    }

}
