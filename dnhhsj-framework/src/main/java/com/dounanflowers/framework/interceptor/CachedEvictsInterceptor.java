package com.dounanflowers.framework.interceptor;

import com.dounanflowers.framework.annotation.CachedEvict;
import com.dounanflowers.framework.annotation.CachedEvicts;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Set;


@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class CachedEvictsInterceptor {

    public final static String CACHE_PREFIX = "cache:";

    private final RedisTemplate<String, String> redisTemplate;

    @Pointcut("@annotation(com.dounanflowers.framework.annotation.CachedEvicts)")
    public void cut() {
    }

    @Around("cut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Object target = joinPoint.getTarget();
        Method currentMethod = target.getClass()
                .getMethod(methodSignature.getName(), methodSignature.getParameterTypes());
        CachedEvicts annotation = currentMethod.getAnnotation(CachedEvicts.class);

        Set<String> beforeKeys = Sets.newHashSet();
        Set<String> afterKeys = Sets.newHashSet();
        for (CachedEvict cachedEvict : annotation.value()) {
            StringBuilder cacheName = new StringBuilder(cachedEvict.value());
            boolean all = cachedEvict.all();
            if (all) {
                StringBuilder append = cacheName.append(":*");
                Set<String> keySet = redisTemplate.keys(CACHE_PREFIX + append);
                if (keySet != null) {
                    if (cachedEvict.before()) {
                        beforeKeys.addAll(keySet);
                    } else {
                        afterKeys.addAll(keySet);
                    }
                }
            } else {
                String key = cachedEvict.key();
                if (!key.isEmpty()) {
                    String[] keys = key.split("\\.");
                    for (Parameter parameter : currentMethod.getParameters()) {
                        String name = parameter.getName();
                        if (name.equals(keys[0])) {
                            if (keys.length == 1) {
                                Field field = target.getClass().getDeclaredField(name);
                                field.setAccessible(true);
                                Object value = field.get(target);
                                cacheName.append(":").append(value);
                            } else {
                                Object value = parameter.getType().getMethod("get" + keys[1].substring(0, 1).toUpperCase() + keys[1].substring(1)).invoke(target);
                                cacheName.append(":").append(value);
                            }
                        }
                    }
                } else {
                    cacheName.append(":default");
                }
                if (cachedEvict.before()) {
                    beforeKeys.add(CACHE_PREFIX + cacheName);
                } else {
                    afterKeys.add(CACHE_PREFIX + cacheName);
                }
            }
        }
        if (!beforeKeys.isEmpty()) {
            redisTemplate.delete(beforeKeys);
        }
        Object proceed = joinPoint.proceed();
        if (!afterKeys.isEmpty()) {
            redisTemplate.delete(afterKeys);
        }
        return proceed;
    }

}