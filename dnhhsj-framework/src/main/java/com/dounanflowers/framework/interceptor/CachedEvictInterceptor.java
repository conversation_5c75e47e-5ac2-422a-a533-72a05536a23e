package com.dounanflowers.framework.interceptor;

import com.dounanflowers.framework.annotation.CachedEvict;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Set;


@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class CachedEvictInterceptor {

    public final static String CACHE_PREFIX = "cache:";

    private final RedisTemplate<String, String> redisTemplate;

    @Pointcut("@annotation(com.dounanflowers.framework.annotation.CachedEvict)")
    public void cut() {
    }

    @Around("cut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Object target = joinPoint.getTarget();
        Method currentMethod = target.getClass()
                .getMethod(methodSignature.getName(), methodSignature.getParameterTypes());
        CachedEvict annotation = currentMethod.getAnnotation(CachedEvict.class);
        StringBuilder cacheName = new StringBuilder(annotation.value());
        Set<String> beforeKeys = Sets.newHashSet();
        Set<String> afterKeys = Sets.newHashSet();
        boolean all = annotation.all();
        boolean isBefore = annotation.before();
        if (all) {
            cacheName.append(":*");
            Set<String> keys = redisTemplate.keys(CACHE_PREFIX + cacheName);
            if (keys != null) {
                if (isBefore) {
                    beforeKeys.addAll(keys);
                } else {
                    afterKeys.addAll(keys);
                }
            }
        } else {
            String key = annotation.key();
            if (!key.isEmpty()) {
                String[] keys = key.split("\\.");
                Parameter[] parameters = currentMethod.getParameters();
                for (int i = 0; i < parameters.length; i++) {
                    Parameter parameter = parameters[i];
                    String name = parameter.getName();
                    if (name.equals(keys[0])) {
                        if (keys.length == 1) {
                            Object value = joinPoint.getArgs()[i];
                            cacheName.append(":").append(value);
                        } else {
                            Object arg = joinPoint.getArgs()[i];
                            Object value = parameter.getType().getMethod("get" + keys[1].substring(0, 1).toUpperCase() + keys[1].substring(1)).invoke(arg);
                            cacheName.append(":").append(value);
                        }
                    }
                }
            } else {
                cacheName.append(":default");
            }
            if (isBefore) {
                beforeKeys.add(CACHE_PREFIX + cacheName);
            } else {
                afterKeys.add(CACHE_PREFIX + cacheName);
            }
        }
        redisTemplate.delete(beforeKeys);
        Object proceed = joinPoint.proceed();
        redisTemplate.delete(afterKeys);
        return proceed;
    }

}