package com.dounanflowers.framework.interceptor;

import com.dounanflowers.framework.annotation.Cached;
import com.dounanflowers.framework.exception.BaseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.concurrent.TimeUnit;

/**
 * 
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class CachedInterceptor {

    public final static String CACHE_PREFIX = "cache:";

    private final RedisTemplate<Object, Object> redisTemplate;

    @Pointcut("@annotation(com.dounanflowers.framework.annotation.Cached)")
    public void cut() {
    }

    @Around("cut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Object target = joinPoint.getTarget();
        Method currentMethod = target.getClass()
                .getMethod(methodSignature.getName(), methodSignature.getParameterTypes());
        Cached annotation = currentMethod.getAnnotation(Cached.class);
        StringBuilder cacheName = new StringBuilder(annotation.value());
        String key = annotation.key();
        if (!key.isEmpty()) {
            String[] keys = key.split("\\.");
            Parameter[] parameters = currentMethod.getParameters();
            for (int i = 0; i < parameters.length; i++) {
                Parameter parameter = parameters[i];
                String name = parameter.getName();
                if (name.equals(keys[0])) {
                    if (keys.length == 1) {
                        Object value = joinPoint.getArgs()[i];
                        cacheName.append(":").append(value);
                    } else {
                        Object arg = joinPoint.getArgs()[i];
                        Object value = parameter.getType().getMethod("get" + keys[1].substring(0, 1).toUpperCase() + keys[1].substring(1)).invoke(arg);
                        cacheName.append(":").append(value);
                    }
                }
            }
        } else {
            cacheName.append(":default");
        }
        try {
            Object s = redisTemplate.opsForValue().get(CACHE_PREFIX + cacheName);
            if (s != null) {
                if (currentMethod.getReturnType() != s.getClass() && !currentMethod.getReturnType().isAssignableFrom(s.getClass())) {
                    throw new BaseException("缓存类型不匹配");
                }
                return s;
            }
        } catch (Exception e) {
            log.error("获取缓存失败{},{}", cacheName, e.getMessage());
        }
        Object proceed = joinPoint.proceed();
        long expire = annotation.expire();
        if (expire > 0) {
            redisTemplate.opsForValue().set(CACHE_PREFIX + cacheName, proceed, expire, TimeUnit.SECONDS);
        } else {
            redisTemplate.opsForValue().set(CACHE_PREFIX + cacheName, proceed);
        }
        return proceed;
    }

}