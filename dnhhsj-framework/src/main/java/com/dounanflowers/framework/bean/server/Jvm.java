package com.dounanflowers.framework.bean.server;


import com.dounanflowers.framework.utils.Arith;
import com.dounanflowers.framework.utils.DateUtils;
import lombok.Getter;
import lombok.Setter;

import java.lang.management.ManagementFactory;
import java.time.LocalDateTime;

/**
 * JVM相关信息
 */
@Setter
public class Jvm {
    /**
     * 当前JVM占用的内存总数(M)
     */
    private double total;

    /**
     * JVM最大可用内存总数(M)
     */
    private double max;

    /**
     * JVM空闲内存(M)
     */
    private double free;

    /**
     * JDK版本
     */
    @Getter
    private String version;

    /**
     * JDK路径
     */
    @Getter
    private String home;

    public double getTotal() {
        return Arith.div(total, (1024 * 1024), 2);
    }

    public double getMax() {
        return Arith.div(max, (1024 * 1024), 2);
    }

    public double getFree() {
        return Arith.div(free, (1024 * 1024), 2);
    }

    public double getUsed() {
        return Arith.div(total - free, (1024 * 1024), 2);
    }

    public double getUsage() {
        return Arith.mul(Arith.div(total - free, total, 4), 100);
    }

    /**
     * 获取JDK名称
     */
    public String getName() {
        return ManagementFactory.getRuntimeMXBean().getVmName();
    }

    /**
     * JDK启动时间
     */
    public String getStartTime() {
        long startTime = ManagementFactory.getRuntimeMXBean().getStartTime();
        LocalDateTime localDateTime = DateUtils.parseLocalDateTime(startTime);
        return DateUtils.formatDateTime(localDateTime, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * JDK运行时间
     */
    public String getRunTime() {
        long startTime = ManagementFactory.getRuntimeMXBean().getStartTime();
        LocalDateTime start = DateUtils.parseLocalDateTime(startTime);
        return DateUtils.timeDistance(LocalDateTime.now(), start);
    }

    /**
     * 运行参数
     */
    public String getInputArgs() {
        return ManagementFactory.getRuntimeMXBean().getInputArguments().toString();
    }
}
