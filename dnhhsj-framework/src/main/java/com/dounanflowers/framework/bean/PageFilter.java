package com.dounanflowers.framework.bean;

import com.dounanflowers.framework.enums.IsEnum;
import com.dounanflowers.framework.utils.DateUtils;
import com.google.common.base.CaseFormat;
import com.mybatisflex.core.query.QueryWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class PageFilter {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private String field;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private String type;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private Object value;

    public QueryWrapper wrapper(QueryWrapper wrapper) {
        String field = this.field;
        if (("_id".equals(field) || field.endsWith("Id")) && !"openId".equals(field)) {
            if ("_id".equals(field)) {
                field = "id";
            }
            if (value instanceof List<?> list) {
                value = list.stream().map(v -> {
                    if (v instanceof String str) {
                        return Long.parseLong(str);
                    }
                    return v;
                }).toList();
            } else if (value instanceof String str && str.endsWith("!s")) {
                value = str.substring(0, str.length() - 2);
            } else if (value instanceof String str) {
                value = Long.parseLong(str);
            }
        }
        return wrapper(wrapper, field);
    }

    public QueryWrapper wrapper(QueryWrapper wrapper, String field) {
        field = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, field);
        if (value == null) {
            switch (type) {
                case "eq":
                    wrapper.isNull(field);
                    break;
                case "ne":
                    wrapper.isNotNull(field);
                    break;
            }
            return wrapper;
        }
        if (type == null) {
            return wrapper;
        }
        String finalField = field;
        switch (value) {
            case List<?> objects when objects.isEmpty() -> {
                return wrapper;
            }
            case String s when s.isEmpty() -> {
                return wrapper;
            }
            case List<?> list -> {
                list = list.stream().map(v -> {
                    if (v instanceof Boolean) {
                        return IsEnum.of((Boolean) v);
                    }
                    return v;
                }).toList();
                switch (type) {
                    case "in":
                        list = list.stream().filter(Objects::nonNull).toList();
                        wrapper.in(field, list.toArray());
                        break;
                    case "nin":
                        list = list.stream().filter(Objects::nonNull).toList();
                        wrapper.notIn(field, list.toArray());
                        break;
                    case "range":
                        if ("created_at".equals(field) || "updated_at".equals(field)) {
                            list = list.stream().map(v -> {
                                if (v instanceof LocalDateTime) {
                                    return v;
                                }
                                return DateUtils.parseLocalDateTime(Long.parseLong(v.toString()));
                            }).toList();
                        }
                        wrapper.between(field, list.getFirst(), list.getLast());
                        break;
                    case "inRange":
                        List<List<?>> inrange = (List<List<?>>) list;
                        wrapper.and(w -> {
                            inrange.forEach(r -> w.or(ws -> ws.between(finalField, r.getFirst(), r.getLast()), true));
                        });
                }
            }
            default -> {
                if (value instanceof Boolean) {
                    value = IsEnum.of((Boolean) value);
                }
                switch (type) {
                    case "eq":
                        wrapper.eq(field, value);
                        break;
                    case "ne":
                        wrapper.ne(field, value);
                        break;
                    case "gt":
                        wrapper.gt(field, value);
                        break;
                    case "ge":
                        wrapper.ge(field, value);
                        break;
                    case "lt":
                        wrapper.lt(field, value);
                        break;
                    case "le":
                        wrapper.le(field, value);
                        break;
                    case "like":
                        wrapper.like(field, value);
                        break;
                    case "llike":
                        wrapper.likeLeft(field, value);
                        break;
                    case "rlike":
                        wrapper.likeRight(field, value);
                        break;
                }
            }
        }
        return wrapper;
    }

    public boolean isCustom() {
        return "custom".equals(type);
    }

    public boolean isSubQuery() {
        return "sub".equals(type);
    }

}

