package com.dounanflowers.framework.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "蓝卡响应")
public class BlueCardResult {

    private String status;

    @Schema(title = "失败原因")
    private String errorCode;

    @Schema(title = "数据")
    private DataInfo data;

    @Data
    @Schema(title = "数据")
    public static class DataInfo {

        public DataInfo() {
            this.timeStamp = String.valueOf(System.currentTimeMillis());
        }

        @Schema(title = "时间戳")
        private String timeStamp;

    }

    public static BlueCardResult success() {
        BlueCardResult resp = new BlueCardResult();
        resp.setStatus("success");
        resp.setData(new DataInfo());
        return resp;
    }

    public static BlueCardResult fail(String errorCode) {
        BlueCardResult resp = new BlueCardResult();
        resp.setStatus("fail");
        resp.setErrorCode(errorCode);
        return resp;
    }
}
