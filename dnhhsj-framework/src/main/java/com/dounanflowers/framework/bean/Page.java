package com.dounanflowers.framework.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.function.Function;

@Data
@Schema(title = "分页返回数据")
public class Page<T> {

    @Schema(title = "总数", requiredMode = Schema.RequiredMode.REQUIRED)
    private int total;

    @Schema(title = "列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<T> list;

    public static <T> Page<T> of(int total, List<T> records) {
        Page<T> page = new Page<>();
        page.setList(records);
        page.setTotal(total);
        return page;
    }

    public static <T> Page<T> of(long total, List<T> records) {
        Page<T> page = new Page<>();
        page.setList(records);
        page.setTotal((int) total);
        return page;
    }

    public Page<T> ofResult(int total, List<T> records) {
        this.total = total;
        this.list = records;
        return this;
    }

    public Page<T> ofResult(long total, List<T> records) {
        this.total = (int) total;
        this.list = records;
        return this;
    }

    public static <T> Page<T> empty() {
        Page<T> tPage = new Page<>();
        tPage.setList(List.of());
        return tPage;
    }

    public Page<T> emptyResult() {
        this.list = List.of();
        return this;
    }

    public <O> Page<O> convert(Function<T, O> function) {
        List<O> collect = this.list.stream().map(function).toList();
        return Page.of(this.total, collect);
    }

}
