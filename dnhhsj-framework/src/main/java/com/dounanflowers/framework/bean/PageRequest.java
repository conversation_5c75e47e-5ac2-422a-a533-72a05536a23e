package com.dounanflowers.framework.bean;

import com.google.common.collect.Lists;
import com.mybatisflex.core.query.QueryWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

@Data
@Schema(title = "分页请求")
public class PageRequest {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer pageNum;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer pageSize;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private List<PageFilter> filter;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private List<PageSort> sort;

    @Schema(hidden = true)
    private Map<String, Consumer<QueryWrapper>> customMap = new HashMap<>();

    @Schema(hidden = true)
    private Map<String, Consumer<QueryWrapper>> subQueryMap = new HashMap<>();

    public PageRequest addFilter(PageFilter filter) {
        if (this.filter == null) {
            this.filter = Lists.newArrayList(filter);
            return this;
        }
        this.filter.add(filter);
        return this;
    }

    public PageRequest addSort(PageSort sort) {
        if (this.sort == null) {
            this.sort = Lists.newArrayList(sort);
            return this;
        }
        this.sort.add(sort);
        return this;
    }

    public QueryWrapper filterWrapper() {
        QueryWrapper wrapper = QueryWrapper.create();
        return filterWrapper(wrapper);
    }

    public QueryWrapper filterWrapper(QueryWrapper wrapper) {
        Set<String> subFields = subQueryMap.keySet();
        if (CollectionUtils.isEmpty(filter)) {
            return wrapper;
        }
        for (PageFilter dto : filter) {
            if (dto.isCustom() && customMap.containsKey(dto.getField())) {
                customMap.get(dto.getField()).accept(wrapper);
            } else if (dto.isSubQuery() && subFields.contains(dto.getField())) {
                subQueryMap.get(dto.getField()).accept(wrapper);
                subFields.remove(dto.getField());
            } else {
                dto.wrapper(wrapper);
            }
        }
        return wrapper;
    }

    public QueryWrapper pageWrapper(QueryWrapper wrapper) {
        wrapper.limit((pageNum - 1) * pageSize, pageSize);
        return wrapper;
    }

    public QueryWrapper sortWrapper(QueryWrapper wrapper) {
        if (CollectionUtils.isEmpty(sort)) {
            wrapper.orderBy("created_at", false).orderBy("id", false);
            return wrapper;
        }
        for (PageSort dto : sort) {
            dto.wrapper(wrapper);
        }
        return wrapper;
    }

    public void addCustom(String field, Consumer<QueryWrapper> consumer) {
        if (field == null) {
            return;

        }
        if (customMap.containsKey(field)) {
            customMap.put(field, customMap.get(field).andThen(consumer));
        } else {
            customMap.put(field, consumer);
        }
    }

    public void addSubQuery(String field, Consumer<QueryWrapper> consumer) {
        if (field == null) {
            return;
        }
        if (subQueryMap.containsKey(field)) {
            subQueryMap.put(field, subQueryMap.get(field).andThen(consumer));
        } else {
            subQueryMap.put(field, consumer);
        }
    }

}
