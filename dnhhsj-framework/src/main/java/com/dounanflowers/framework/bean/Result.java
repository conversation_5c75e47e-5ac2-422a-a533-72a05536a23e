package com.dounanflowers.framework.bean;

import com.dounanflowers.framework.utils.TraceHelper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class Result<E> {

    @Schema(title = "错误码", description = "大于0表示错误")
    private int code;

    @Schema(title = "错误信息")
    private String msg = "success";

    @Schema(title = "返回数据")
    private E data;

    @Schema(title = "追溯ID", description = "方便在日志里定位的ID")
    private String traceId;

    public Result() {
        this.traceId = TraceHelper.get();
    }

    public Result(int code, String msg) {
        this.code = code;
        this.msg = msg;
        this.traceId = TraceHelper.get();
    }

    public Result(E data) {
        this.data = data;
        this.traceId = TraceHelper.get();
    }

    public static <E> Result<E> success() {
        return new Result<>();
    }

    public static <E> Result<E> success(E data) {
        return new Result<>(data);
    }

    public static <E> Result<E> error() {
        return new Result<>(500, "Internal Server Error");
    }

    public static <E> Result<E> error(int code, String message) {
        return new Result<>(code, message);
    }

    public static <E> Result<E> badArgument() {
        return error(400, "Bad Argument");
    }

    public static <E> Result<E> unAuth() {
        return error(401, "Unauthorized");
    }

    public static <E> Result<E> badArgumentValue() {
        return error(400, "Bad Argument Value");
    }

    public static <E> Result<E> notFound() {
        return error(404, "Not Found");
    }

}
