package com.dounanflowers.framework.bean;

import com.google.common.base.CaseFormat;
import com.mybatisflex.core.query.QueryWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.function.Consumer;

@Data
@Accessors(chain = true)
public class PageSort {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private String field;

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private String order;

    private Consumer<QueryWrapper> consumer;

    public void wrapper(QueryWrapper wrapper) {
        if (consumer != null) {
            consumer.accept(wrapper);
            return;
        }
        if (field == null) {
            return;
        }
        if (order == null) {
            return;
        }
        field = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, field);
        switch (order) {
            case "asc":
            case "ASC":
                wrapper.orderBy(field, true);
                break;
            case "desc":
            case "DESC":
                wrapper.orderBy(field, false);
                break;
        }
    }

}

