package com.dounanflowers.framework.bean;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public abstract class BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @ColumnDef(comment = "ID", primary = true)
    private Long id;

    @ColumnDef(nullable = false, defaultValue = "0", comment = "创建时间")
    private Long createdBy;

    @ColumnDef(nullable = false, defaultValue = "CURRENT_TIMESTAMP", comment = "创建人")
    private LocalDateTime createdAt;

    @ColumnDef(nullable = false, defaultValue = "0", comment = "更新人")
    private Long updatedBy;

    @ColumnDef(nullable = false, defaultValue = "CURRENT_TIMESTAMP", comment = "更新时间")
    private LocalDateTime updatedAt;

    @Column(version = true)
    @ColumnDef(nullable = false, defaultValue = "0", comment = "版本号")
    private Long ver;

}
