package com.dounanflowers.framework.bean;

import com.dounanflowers.framework.annotation.ColumnDef;
import com.dounanflowers.framework.enums.IsEnum;
import com.mybatisflex.annotation.Column;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public abstract class SoftDeletedEntity extends BaseEntity {

    @Column(isLogicDelete = true)
    @ColumnDef(nullable = false, defaultValue = "0", comment = "是否删除 0:否 1:是")
    private IsEnum isDeleted;

}
