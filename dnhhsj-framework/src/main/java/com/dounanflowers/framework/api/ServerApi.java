package com.dounanflowers.framework.api;

import com.dounanflowers.framework.bean.Server;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务器监控
 */
@RestController
@RequestMapping("/monitor/server")
public class ServerApi {
    @GetMapping()
    public Server getInfo() throws Exception {
        Server server = new Server();
        server.copyTo();
        return server;
    }
}
