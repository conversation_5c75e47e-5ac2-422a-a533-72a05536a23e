package com.dounanflowers.framework.utils;

import org.apache.commons.io.IOUtils;

import java.io.ByteArrayInputStream;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.zip.GZIPInputStream;

public class HttpUtils {

    private static HttpClient httpClient;

    public static synchronized HttpClient getClient() {
        if (httpClient == null) {
            httpClient = HttpClient.newHttpClient();
        }
        return httpClient;
    }

    public static HttpSender get(String url) {
        return new HttpSender(getClient()).url(url).get();
    }

    public static HttpSender post(String url) {
        return new HttpSender(getClient()).url(url).post();
    }


    public static class HttpSender {

        private final HttpClient client;

        private String url;

        private final Map<String, String> query = new HashMap<>();

        private final HttpRequest.Builder builder = HttpRequest.newBuilder();

        private String method;

        private String body;

        private HttpSender(HttpClient client) {
            this.client = client;
        }

        public HttpSender url(String url) {
            this.url = url;
            return this;
        }

        public HttpSender get() {
            this.method = "GET";
            return this;
        }

        public HttpSender post() {
            this.method = "POST";
            return this;
        }

        public HttpSender put() {
            this.method = "PUT";
            return this;
        }

        public HttpSender delete() {
            this.method = "DELETE";
            return this;
        }

        public HttpSender patch() {
            this.method = "PATCH";
            return this;
        }

        public HttpSender query(String key, String value) {
            this.query.put(key, value);
            return this;
        }

        public HttpSender query(Map<String, String> query) {
            this.query.putAll(query);
            return this;
        }

        public HttpSender body(String body) {
            this.body = body;
            return this;
        }

        public HttpSender header(String key, String value) {
            this.builder.header(key, value);
            return this;
        }

        public HttpSender header(String key, String... values) {
            for (String value : values) {
                this.builder.header(key, value);
            }
            return this;
        }

        public HttpSender header(Map<String, String> headers) {
            headers.forEach(this.builder::header);
            return this;
        }

        public HttpResult send() {
            try {
                String url = this.url;
                if (!this.query.isEmpty()) {
                    url += "?";
                    StringBuilder urlBuilder = new StringBuilder(url);
                    for (Map.Entry<String, String> entry : this.query.entrySet()) {
                        urlBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                    }
                    url = urlBuilder.toString();
                    url = url.substring(0, url.length() - 1);
                }
                HttpRequest.BodyPublisher body = this.body == null ? HttpRequest.BodyPublishers.noBody() : HttpRequest.BodyPublishers.ofString(this.body);
                this.builder.uri(URI.create(url)).method(this.method, body);
                HttpResponse<byte[]> send = this.client.send(this.builder.build(), HttpResponse.BodyHandlers.ofByteArray());
                return new HttpResult(send.statusCode(), send.body(), send.headers().map());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            HttpSender that = (HttpSender) o;
            return Objects.equals(url, that.url) && Objects.equals(query, that.query) && Objects.equals(method, that.method) && Objects.equals(body, that.body);
        }

        @Override
        public int hashCode() {
            return Objects.hash(url, query, method, body);
        }

        @Override
        public String toString() {
            return "HttpSender{" +
                    "url='" + url + '\'' +
                    ", method='" + method + '\'' +
                    ", query=" + query +
                    ", body='" + body + '\'' +
                    '}';
        }
    }

    public static final class HttpResult {
        private final int statusCode;
        private final byte[] body;
        private final Map<String, List<String>> headers;

        private HttpResult(int statusCode, byte[] body, Map<String, List<String>> headers) {
            this.statusCode = statusCode;
            this.body = body;
            this.headers = headers;
        }

        public String getHeader(String key) {
            return String.join(",", headers.get(key));
        }

        public int statusCode() {
            return statusCode;
        }

        public byte[] body() {
            return body;
        }

        public String bodyAsString() {
            if (headers.containsKey("Content-Encoding")) {
                String encoding = headers.get("Content-Encoding").getFirst();
                if (encoding.equals("gzip")) {
                    try {
                        GZIPInputStream gzipInputStream = new GZIPInputStream(new ByteArrayInputStream(body));
                        return IOUtils.toString(gzipInputStream, StandardCharsets.UTF_8);
                    } catch (java.io.IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
            return new String(body, StandardCharsets.UTF_8);
        }

        public String bodyAsString(Charset charset) {
            return new String(body, charset);
        }

        public Map<String, List<String>> headers() {
            return headers;
        }

        @Override
        public boolean equals(Object obj) {
            if (obj == this) return true;
            if (obj == null || obj.getClass() != this.getClass()) return false;
            var that = (HttpResult) obj;
            return this.statusCode == that.statusCode &&
                    Arrays.equals(this.body, that.body) &&
                    Objects.equals(this.headers, that.headers);
        }

        @Override
        public int hashCode() {
            return Objects.hash(statusCode, body, headers);
        }

        @Override
        public String toString() {
            return "HttpResult[" +
                    "statusCode=" + statusCode + ", " +
                    "body=" + body + ", " +
                    "headers=" + headers + ']';
        }


    }

}
