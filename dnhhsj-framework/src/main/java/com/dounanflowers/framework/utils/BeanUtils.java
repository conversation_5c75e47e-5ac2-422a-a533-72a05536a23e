package com.dounanflowers.framework.utils;

import com.dounanflowers.framework.enums.BaseEnum;
import com.dounanflowers.framework.enums.IsEnum;
import com.google.common.base.CaseFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.lang.constant.ConstantDesc;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.Temporal;
import java.util.*;

public abstract class BeanUtils {

    private static final Logger log = LoggerFactory.getLogger(BeanUtils.class);

    @SuppressWarnings("unchecked")
    public static <T> T copy(Object source, Class<T> targetClass, String... ignoreProperties) {
        Assert.notNull(source, "source must not be null");
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            if (source instanceof Map) {
                copyFromMap((Map<String, Object>) source, target, ignoreProperties);
                return target;
            }
            copyFromObject(source, target, ignoreProperties);
            return target;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> List<T> copyList(List<?> source, Class<T> targetClass) {
        return source.stream().map(s -> copy(s, targetClass)).toList();
    }

    private static Method getMethod(Class<?> clz, String methodName, Class<?>... paramTypes) {
        Method method;
        try {
            method = clz.getMethod(methodName, paramTypes);
        } catch (NoSuchMethodException e) {
            return null;
        }
        return method;
    }

    @SuppressWarnings("unchecked")
    public static void copyProperties(Object source, Object target, String... ignoreProperties) {
        Assert.notNull(source, "source must not be null");
        Assert.notNull(target, "target must not be null");
        try {
            if (source instanceof Map) {
                copyFromMap((Map<String, Object>) source, target, ignoreProperties);
            }
            copyFromObject(source, target, ignoreProperties);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void copyProperty(Object target, String fieldName, Object value) {
        if (target == null || value == null || fieldName == null) {
            return;
        }
        try {
            Method set = getSetMethod(fieldName, target.getClass(), value.getClass());
            if (set == null) {
                return;
            }
            Class<?> parameterType = set.getParameterTypes()[0];
            value = transValue(value, parameterType);
            if (value instanceof List) {
                set.invoke(target, value);
                return;
            }
            if (!isPrimitive(value.getClass())) {
                value = copy(value, value.getClass());
            }
            if (value.getClass().isAssignableFrom(parameterType)) {
                set.invoke(target, value);
            }
        } catch (Exception e) {
            System.out.println("target:" + target.getClass().getName() + ", field: " + fieldName + ", value: " + value);
            throw new RuntimeException(e);
        }
    }

    private static void copyFromMap(Map<String, Object> source, Object target, String... ignoreProperties) {
        Class<?> targetClass = target.getClass();
        for (Map.Entry<String, Object> entry : source.entrySet()) {
            String fieldName = entry.getKey();
            if (fieldName.indexOf('_') > 1) {
                fieldName = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, fieldName);
            }
            Object value = entry.getValue();
            if (value == null) {
                continue;
            }
            if (ignoreProperties.length > 0 && !List.of(ignoreProperties).contains(fieldName)) {
                continue;
            }
            try {
                value = isPrimitive(value.getClass()) ? value : copy(value, value.getClass());
                Class<?> fieldType = getFieldType(targetClass, fieldName);
                if (fieldType == null) {
                    continue;
                }
                value = transValue(value, fieldType);
                Method set = getSetMethod(fieldName, targetClass, value.getClass());
                if (set == null) {
                    continue;
                }
                set.invoke(target, value);
            } catch (Exception e) {
                log.warn("field {} mismatch", fieldName, e);
            }
        }
    }

    public static <T> T ordinalOf(Class<T> enumClass, Integer value) {
        if (value == null) {
            return null;
        }
        T[] values = enumClass.getEnumConstants();
        if (value < 0 || value >= values.length) {
            return null;
        }
        return values[value];
    }

    private static Object transValue(Object value, Class<?> targetClass) {
        if (targetClass.isEnum() && (value instanceof Integer || value instanceof Long || value instanceof Short || value instanceof String)) {
            return ordinalOf(targetClass, Integer.valueOf(value.toString()));
        } else if (value instanceof BigDecimal) {
            if ((targetClass == Double.class || targetClass == double.class)) {
                return ((BigDecimal) value).doubleValue();
            } else if ((targetClass == Float.class || targetClass == float.class)) {
                return ((BigDecimal) value).floatValue();
            } else if (targetClass == String.class) {
                return value.toString();
            }
        } else if (value instanceof Timestamp) {
            if (targetClass == Date.class) {
                return new Date(((Timestamp) value).getTime());
            } else if (targetClass == Long.class || targetClass == long.class) {
                return ((Timestamp) value).getTime();
            } else if (targetClass == LocalDateTime.class) {
                return ((Timestamp) value).toLocalDateTime();
            } else if (targetClass == LocalDate.class) {
                return ((Timestamp) value).toLocalDateTime().toLocalDate();
            }
        } else if (value instanceof IsEnum && (targetClass == boolean.class || targetClass == Boolean.class)) {
            return ((IsEnum) value).isTrue();
        } else if (value instanceof BaseEnum<?> && (targetClass == int.class || targetClass == Integer.class)) {
            return ((BaseEnum<?>) value).ordinal();
        } else if (value.getClass() == Boolean.class && targetClass == IsEnum.class) {
            return IsEnum.of((Boolean) value);
        } else if (targetClass == BigDecimal.class) {
            return new BigDecimal(value.toString());
        } else if (targetClass == String.class) {
            if (value instanceof BaseEnum<?>) {
                return ((BaseEnum<?>) value).ordinal() + "";
            }
            return value.toString();
        } else if (value instanceof Long && targetClass == Integer.class) {
            return ((Long) value).intValue();
        } else if (value instanceof Integer && targetClass == Long.class) {
            return ((Integer) value).longValue();
        }
        return value;
    }

    private static Class<?> getFieldType(Class<?> targetClass, String fieldName) {
        try {
            return targetClass.getDeclaredField(fieldName).getType();
        } catch (NoSuchFieldException ignored) {
            while (targetClass.getSuperclass() != null) {
                targetClass = targetClass.getSuperclass();
                try {
                    return targetClass.getDeclaredField(fieldName).getType();
                } catch (NoSuchFieldException ignored1) {
                }
            }
        }
        return null;
    }

    private static Method getSetMethod(String fieldName, Class<?> targetClass, Class<?> paramType) {
        String setMethodName = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
        Method set = getMethod(targetClass, setMethodName, paramType);
        if (set == null) {
            set = getMethod(targetClass, fieldName, paramType);
        }
        if (set == null) {
            set = getMethod(targetClass, setMethodName, Object.class);
        }
        if (set == null) {
            for (Method method : targetClass.getMethods()) {
                if (method.getName().equals(setMethodName) && method.getParameterCount() == 1) {
                    return method;
                }
            }
        }
        return set;
    }

    public static void copyFromObject(Object source, Object target, String[] ignoreProperties) {
        Class<?> targetClass = target.getClass();
        for (Method get : source.getClass().getMethods()) {
            if (get.getParameterCount() != 0) {
                continue;
            }
            if (get.getReturnType() == void.class) {
                continue;
            }
            if (get.getName().equals("getClass") || get.getName().equals("hashCode") || get.getName().equals("toString")
                    || get.getName().equals("to") || get.getName().equals("clone") || get.getName().equals("notify")
                    || get.getName().equals("notifyAll") || get.getName().equals("wait") || get.getName().equals("finalize")
                    || get.getName().equals("from")
            ) {
                continue;
            }
            try {
                String fieldName = getFieldName(get);
                if (ignoreProperties.length > 0 && !List.of(ignoreProperties).contains(fieldName)) {
                    continue;
                }
                Method set = getSetMethod(fieldName, targetClass, get.getReturnType());
                if (set == null) {
                    continue;
                }
                Object value = get.invoke(source);
                if (value == null) {
                    continue;
                }
                Class<?> parameterType = set.getParameterTypes()[0];
                value = transValue(value, parameterType);
                if (value instanceof List) {
                    continue;
                }
                if (!isPrimitive(value.getClass())) {
                    value = copy(value, value.getClass());
                }
                if (value.getClass().isAssignableFrom(parameterType)) {
                    set.invoke(target, value);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    private static String getFieldName(Method get) {
        String methodName = get.getName();
        String fieldName;
        if (methodName.startsWith("get")) {
            fieldName = methodName.substring(3);
            fieldName = fieldName.substring(0, 1).toLowerCase() + fieldName.substring(1);
        } else if (methodName.startsWith("is")) {
            fieldName = methodName.substring(2);
            fieldName = fieldName.substring(0, 1).toLowerCase() + fieldName.substring(1);
        } else {
            fieldName = methodName;
        }
        return fieldName;
    }

    public static boolean isPrimitive(Class<?> clz) {
        return clz.isPrimitive() || ConstantDesc.class.isAssignableFrom(clz) || clz == Boolean.class || clz == Short.class
                || clz == Byte.class || clz == Character.class || clz == Void.class || clz == Object.class
                || clz == BigDecimal.class || clz == BigInteger.class || Enum.class.isAssignableFrom(clz)
                || Date.class.isAssignableFrom(clz) || Temporal.class.isAssignableFrom(clz);
    }

    public static boolean isCollections(Class<?> clz) {
        return Collection.class.isAssignableFrom(clz);
    }

    @SuppressWarnings("unchecked")
    public static Map<String, Object> toMap(Object source, String... ignoreProperties) {
        if (source instanceof Map) {
            return (Map<String, Object>) source;
        }
        assertSource(source);
        Map<String, Object> map = new HashMap<>();
        for (Method get : source.getClass().getMethods()) {
            if (get.getParameterCount() != 0) {
                continue;
            }
            if (get.getReturnType() == void.class) {
                continue;
            }
            try {
                String fieldName = getFieldName(get);
                if (ignoreProperties.length > 0 && !List.of(ignoreProperties).contains(fieldName)) {
                    continue;
                }
                Object value = get.invoke(source);
                if (value == null) {
                    continue;
                }
                map.put(fieldName, value);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return map;
    }

    private static void assertSource(Object source) {
        Assert.notNull(source, "source must not be null");
        if (source instanceof List) {
            throw new IllegalArgumentException("source is a list");
        }
    }

}
