package com.dounanflowers.framework.utils;


import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;


public class SpELUtil {

    public static String parse(String spEL, Object object) {
        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setRootObject(object);
        return parser.parseExpression(spEL).getValue(context, String.class);
    }

}
