package com.dounanflowers.framework.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.util.UUID;

public class TraceHelper {

    public static void set() {
        set(TraceHelper.get());
    }

    public static void set(String tid) {
        if (StringUtils.isBlank(tid)) {
            tid = UUID.randomUUID().toString();
        }
        MDC.put("trace-id", tid);
    }

    public static String get() {
        return MDC.get("trace-id");
    }

    public static void clear() {
        MDC.remove("trace-id");
    }

}
