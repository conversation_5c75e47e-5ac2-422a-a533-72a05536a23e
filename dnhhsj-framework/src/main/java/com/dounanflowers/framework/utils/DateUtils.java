package com.dounanflowers.framework.utils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

public abstract class DateUtils {

    public static LocalDateTime parseLocalDateTime(Long timestamp) {
        return Instant.ofEpochMilli(timestamp).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static String formatDateTime(LocalDateTime dateTime, String formatter) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(formatter);
        return dtf.format(dateTime);
    }

    public static LocalDateTime parseLocalDateTime(String dateTime, String formatter) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(formatter);
        return LocalDateTime.parse(dateTime, dtf);
    }

    public static LocalDate parseLocalDate(String date, String formatter) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(formatter);
        return LocalDate.parse(date, dtf);
    }

    public static String formatLocalDate(LocalDate dateTime, String formatter) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(formatter);
        return dtf.format(dateTime);
    }

    public static long getTimestamp(LocalDateTime dateTime) {
        return dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static String timeDistance(LocalDateTime endDate, LocalDateTime startTime) {
        long nd = 24 * 60 * 60;
        long nh = 60 * 60;
        long nm = 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = startTime.until(endDate, ChronoUnit.SECONDS);
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }
}
