package com.dounanflowers.framework.utils;


public class IdUtils {

    private static SnowflakeId snowflakeId;

    private static boolean isInit = false;

    public static void init(long workerId, long datacenterId) {
        if (isInit) {
            return;
        }
        isInit = true;
        snowflakeId = new SnowflakeId(workerId, datacenterId);
    }

    public static Long nextId() {
        return snowflakeId.nextId();
    }

}
