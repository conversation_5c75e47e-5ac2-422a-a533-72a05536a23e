package com.dounanflowers.framework.utils;


import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.core.TreeNode;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.jsontype.NamedType;
import com.fasterxml.jackson.databind.jsontype.TypeDeserializer;
import com.fasterxml.jackson.databind.jsontype.TypeSerializer;
import com.fasterxml.jackson.databind.jsontype.impl.StdTypeResolverBuilder;
import com.fasterxml.jackson.databind.node.TextNode;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Collection;
import java.util.List;

import static com.dounanflowers.framework.utils.JsonUtils.configMapper;

@Slf4j
public class SerializableUtils {

    private final static ObjectMapper objectMapper = new ObjectMapper();

    static {
        configMapper(objectMapper);
        configMapperSerializable(objectMapper);
    }

    public static byte[] toBytes(Object o) {
        try {
            return objectMapper.writeValueAsBytes(o);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static String toString(Object o) {
        return new String(toBytes(o));
    }

    public static <T> T fromBytes(byte[] source) {
        return fromBytes(source, Object.class);
    }

    public static <T> T fromBytes(byte[] source, Class<?> clazz) {
        try {
            String s = new String(source);
            JavaType javaType;
            if (clazz == Object.class && s.startsWith("[")) {
                javaType = objectMapper.getTypeFactory().constructType(List.class);
            } else if (clazz == Object.class) {
                JsonNode root = objectMapper.readTree(source);
                JsonNode jsonNode = root.get("@class");
                if (jsonNode instanceof TextNode && jsonNode.asText() != null) {
                    javaType = objectMapper.getTypeFactory().constructFromCanonical(jsonNode.asText());
                } else {
                    javaType = objectMapper.getTypeFactory().constructType(Object.class);
                }
            } else {
                javaType = objectMapper.getTypeFactory().constructType(clazz);
            }
            return objectMapper.readValue(source, javaType);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T fromString(String source) {
        return fromBytes(source.getBytes());
    }

    public static void configMapperSerializable(ObjectMapper objectMapper) {
        com.fasterxml.jackson.databind.jsontype.TypeResolverBuilder<?> typer = new TypeResolverBuilder()
                .init(JsonTypeInfo.Id.CLASS, null)
                .inclusion(JsonTypeInfo.As.PROPERTY)
                .typeProperty("@class");
        objectMapper.setDefaultTyping(typer);
    }

    public static class TypeResolverBuilder extends StdTypeResolverBuilder {

        public TypeResolverBuilder() {
            super();
        }

        @Override
        public TypeDeserializer buildTypeDeserializer(DeserializationConfig config,
                                                      JavaType baseType, Collection<NamedType> subtypes) {
            return useForType(baseType) ? super.buildTypeDeserializer(config, baseType, subtypes) : null;
        }

        @Override
        public TypeSerializer buildTypeSerializer(SerializationConfig config,
                                                  JavaType baseType, Collection<NamedType> subtypes) {
            return useForType(baseType) ? super.buildTypeSerializer(config, baseType, subtypes) : null;
        }

        public boolean useForType(JavaType javaType) {
            if (javaType.isJavaLangObject()) {
                return true;
            }
            if (BeanUtils.isPrimitive(javaType.getRawClass())) {
                return false;
            }
            if (BeanUtils.isCollections(javaType.getRawClass())) {
                return false;
            }
            return !TreeNode.class.isAssignableFrom(javaType.getRawClass());
        }
    }

}
