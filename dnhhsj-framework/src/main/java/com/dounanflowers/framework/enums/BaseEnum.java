package com.dounanflowers.framework.enums;


import com.dounanflowers.framework.utils.BeanUtils;
import com.mybatisflex.annotation.EnumValue;

public interface BaseEnum<E extends Enum<E>> {

    static <T> T ordinalOf(Class<T> enumClass, Integer value) {
        return BeanUtils.ordinalOf(enumClass, value);
    }

    static <T> T ordinalOf(Class<T> enumClass, String value) {
        return BeanUtils.ordinalOf(enumClass, Integer.parseInt(value));
    }

    int ordinal();

    @EnumValue
    default int getOrdinal() {
        return ordinal();
    }

}
