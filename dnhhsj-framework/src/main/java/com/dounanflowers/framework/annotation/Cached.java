package com.dounanflowers.framework.annotation;

import org.springframework.aot.hint.annotation.Reflective;

import java.lang.annotation.*;

/**
 *
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
@Reflective
public @interface Cached {

    String value();

    String key() default "";

    /**
     * Cache expiration time in seconds
     *
     * @return expiration time in seconds, -1 means never expire
     */
    long expire() default -1;
}
