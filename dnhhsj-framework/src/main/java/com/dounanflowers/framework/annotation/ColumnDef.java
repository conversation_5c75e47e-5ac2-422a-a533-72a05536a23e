package com.dounanflowers.framework.annotation;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({METHOD, FIELD})
@Retention(RUNTIME)
public @interface ColumnDef {

    boolean primary() default false;

    String name() default "";

    boolean nullable() default true;

    String type() default "";

    boolean ignore() default false;

    String comment() default "";

    String defaultValue() default "";

}
