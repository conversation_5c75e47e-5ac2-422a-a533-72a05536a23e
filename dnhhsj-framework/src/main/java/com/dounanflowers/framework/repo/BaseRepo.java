package com.dounanflowers.framework.repo;

import com.dounanflowers.framework.bean.BaseEntity;
import com.dounanflowers.framework.bean.Page;
import com.dounanflowers.framework.bean.PageRequest;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;

import java.util.List;
import java.util.function.Consumer;

public interface BaseRepo<T extends BaseEntity> extends BaseMapper<T> {

    default void save(T entity) {
        if (entity.getId() == null) {
            insertSelective(entity);
        } else {
            if (entity.getVer() == null) {
                T t = selectOneById(entity.getId());
                if (t == null) {
                    insertSelective(entity);
                    return;
                }
                entity.setVer(t.getVer());
            }
            update(entity);
            entity.setVer(entity.getVer() + 1);
        }
    }

    default Page<T> selectPageByQuery(PageRequest request) {
        QueryWrapper wrapper = request.filterWrapper();
        long l = this.selectCountByQuery(wrapper);
        if (l == 0) {
            return Page.empty();
        }
        wrapper = request.pageWrapper(wrapper);
        wrapper = request.sortWrapper(wrapper);
        List<T> list = this.selectListByQuery(wrapper);
        return Page.of(l, list);
    }

    default Page<T> selectPageByQuery(PageRequest request, Consumer<QueryWrapper> consumer) {
        QueryWrapper wrapper = request.filterWrapper();
        long l = this.selectCountByQuery(wrapper);
        if (l == 0) {
            return Page.empty();
        }
        consumer.accept(wrapper);
        wrapper = request.pageWrapper(wrapper);
        wrapper = request.sortWrapper(wrapper);
        List<T> list = this.selectListByQuery(wrapper);
        return Page.of(l, list);
    }

    default Page<T> selectPageWithRelations(PageRequest request) {
        QueryWrapper wrapper = request.filterWrapper();
        long l = this.selectCountByQuery(wrapper);
        if (l == 0) {
            return Page.empty();
        }
        wrapper = request.pageWrapper(wrapper);
        wrapper = request.sortWrapper(wrapper);
        List<T> list = this.selectListWithRelationsByQuery(wrapper);
        return Page.of(l, list);
    }

    default Page<T> selectPageWithRelations(PageRequest request, Consumer<QueryWrapper> consumer) {
        QueryWrapper wrapper = request.filterWrapper();
        long l = this.selectCountByQuery(wrapper);
        if (l == 0) {
            return Page.empty();
        }
        consumer.accept(wrapper);
        wrapper = request.pageWrapper(wrapper);
        wrapper = request.sortWrapper(wrapper);
        List<T> list = this.selectListWithRelationsByQuery(wrapper);
        return Page.of(l, list);
    }

}
